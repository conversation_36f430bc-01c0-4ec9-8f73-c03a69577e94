<template>
  <div>
    <div class="row items-center q-ma-md q-gutter-md">
      <div class="text-h5 text-weight-regular">
        {{ t('history.title') }}
        <span v-show="pagination.total_items">
          ({{pagination.total_items}})
        </span>
      </div>

      <!-- Sorting options -->
      <q-select
        v-model="sortOption"
        :options="sortOptions"
        dense
        outlined
        options-dense
        map-options
        emit-value
        :label="t('reviewsPage.sortBy')"
        class="q-ml-auto"
        style="min-width: 220px"
      />
    </div>

    <div class="q-mx-md">
      <q-infinite-scroll @load="onLoad" :offset="250" :disable="stopLoad" class="col">
        <q-list bordered separator class="shadow-2">
          <PlayHistoryItem v-for="(history, index) in historyList" :key="`${history.id}-${index}`" :history="history" @delete="removeHistory" />
        </q-list>

        <div v-show="stopLoad" class="q-mt-lg q-mb-xl text-h6 text-bold text-center">{{ t('history.endOfList') }}</div>

        <template v-slot:loading>
          <div class="row justify-center q-my-md">
            <q-spinner-dots color="primary" size="40px" />
          </div>
        </template>
      </q-infinite-scroll>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onActivated, onDeactivated, getCurrentInstance } from 'vue'
import { useI18n } from 'vue-i18n'
import PlayHistoryItem from '../components/PlayHistoryItem.vue'
import { useNotification } from '../composables/useNotification'

defineOptions({
  name: 'PlayHistoryPage'
})

const { t } = useI18n()
const { proxy } = getCurrentInstance()
const { showErrorNotification } = useNotification()

// Reactive data
const stopLoad = ref(false)
const historyList = ref([])
const pagination = ref({ current_page: 0, per_page: 20, total_items: 0 })

// Computed properties
const sortOption = computed({
  get() {
    const defaultSort = {
      label: t('history.sortOptions.updatedDesc'),
      order: 'updated_at',
      sort: 'desc'
    }
    if (localStorage.historySortOption) {
      try {
        return JSON.parse(localStorage.historySortOption)
      } catch {
        localStorage.removeItem('historySortOption')
        return defaultSort
      }
    }
    return defaultSort
  },
  set(newVal) {
    localStorage.historySortOption = JSON.stringify(newVal)
    reset()
  }
})

const sortOptions = computed(() => [
  {
    label: t('history.sortOptions.updatedDesc'),
    order: 'updated_at',
    sort: 'desc'
  },
  {
    label: t('history.sortOptions.updatedAsc'),
    order: 'updated_at',
    sort: 'asc'
  },
  {
    label: t('history.sortOptions.progressDesc'),
    order: 'progress_percentage',
    sort: 'desc'
  },
  {
    label: t('history.sortOptions.progressAsc'),
    order: 'progress_percentage',
    sort: 'asc'
  }
])

const requestParams = computed(() => ({
  page: pagination.value.current_page + 1 || 1,
  page_size: 20,
  order: sortOption.value.order,
  sort: sortOption.value.sort
}))

// Methods
const onLoad = (index, done) => {
  requestHistory()
    .then(() => done())
}

const requestHistory = async () => {
  try {
    const response = await proxy.$api.get('/api/v1/me/history', {
      params: requestParams.value
    })

    const data = response.data
    const history = data.items || []

    historyList.value = (requestParams.value.page === 1) ? history.concat() : historyList.value.concat(history)
    pagination.value = data.pagination || { current_page: 0, per_page: 20, total_items: 0 }

    if (historyList.value.length >= pagination.value.total_items) {
      stopLoad.value = true
    }
  } catch (error) {
    if (error.response?.status !== 401) {
      showErrorNotification(error.response?.data?.error || error.message || t('notification.loadHistoryFailed'))
    }
    stopLoad.value = true
  }
}

const removeHistory = (historyId) => {
  historyList.value = historyList.value.filter(item => item.id !== historyId)
  pagination.value.total_items -= 1
}

const reset = () => {
  stopLoad.value = true
  pagination.value = { current_page: 0, per_page: 20, total_items: 0 }
  requestHistory()
    .then(() => {
      stopLoad.value = false
    })
}

// Lifecycle hooks
onActivated(() => {
  stopLoad.value = false
})

onDeactivated(() => {
  stopLoad.value = true
})


</script>
