import { defineStore } from 'pinia'

export const useWorkInfoStore = defineStore('workInfo', {
  state: () => ({
    // Store tree data by originalId for quick access
    treeDataCache: {
      // Format: { [originalId]: { storageId, treeData, timestamp } }
    }
  }),

  getters: {
    getTreeData: (state) => (originalId) => {
      return state.treeDataCache[originalId] || null
    },

    hasTreeData: (state) => (originalId) => {
      return !!state.treeDataCache[originalId]
    }
  },

  actions: {
    setTreeData({ originalId, storageId, treeData, pathInStorage }) {
      this.treeDataCache[originalId] = {
        storageId,
        treeData,
        pathInStorage: pathInStorage || '',
        timestamp: Date.now()
      }
    },

    clearTreeData(originalId) {
      if (originalId) {
        delete this.treeDataCache[originalId]
      } else {
        // Clear all if no specific originalId provided
        this.treeDataCache = {}
      }
    }
  }
})
