package handler

import (
	"encoding/json"
	"io"
	"net/http"
	"os"
	"path/filepath"

	"github.com/Sakura-Byte/kikoeru-go/pkg/config"
	user_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/user"
	"github.com/Sakura-Byte/kikoeru-go/pkg/log"
	"github.com/Sakura-Byte/kikoeru-go/pkg/ports"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/database"
	"github.com/gin-gonic/gin"
)

type AdminDataHandler struct {
	appConfig   *config.AppConfig
	userService ports.UserService
	workService ports.WorkService
}

func NewAdminDataHandler(
	appConfig *config.AppConfig,
	userService ports.UserService,
	workService ports.WorkService,
) *AdminDataHandler {
	return &AdminDataHandler{
		appConfig:   appConfig,
		userService: userService,
		workService: workService,
	}
}

// ExportWorks exports all works data to a JSON file
func (h *AdminDataHandler) ExportWorks(c *gin.Context) {
	// Create a temporary file to store the export
	tempDir := os.TempDir()
	tempFile, err := os.CreateTemp(tempDir, "works_export_*.json")
	if err != nil {
		log.Error(c.Request.Context(), "Failed to create temporary file for works export", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create export file"})
		return
	}
	defer os.Remove(tempFile.Name()) // Clean up temp file when done

	// Get all works using database ListWorksParams structure
	// Using large page size to get all works
	params := database.ListWorksParams{}
	params.Page = 1
	params.PageSize = 10000 // Very large number to get all works

	works, _, err := h.workService.ListWorks(c, params)
	if err != nil {
		log.Error(c.Request.Context(), "Failed to fetch works for export", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch works data"})
		return
	}

	// Write the works data to the temporary file
	encoder := json.NewEncoder(tempFile)
	if err := encoder.Encode(works); err != nil {
		log.Error(c.Request.Context(), "Failed to encode works data", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to encode works data"})
		return
	}
	tempFile.Close() // Close before reading

	// Set appropriate headers for file download
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Disposition", "attachment; filename=works_export.json")
	c.Header("Content-Type", "application/json")

	// Serve the file
	c.File(tempFile.Name())
}

// ExportUsers exports all user data to a JSON file
func (h *AdminDataHandler) ExportUsers(c *gin.Context) {
	// Create a temporary file to store the export
	tempDir := os.TempDir()
	tempFile, err := os.CreateTemp(tempDir, "users_export_*.json")
	if err != nil {
		log.Error(c.Request.Context(), "Failed to create temporary file for users export", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create export file"})
		return
	}
	defer os.Remove(tempFile.Name()) // Clean up temp file when done

	// Get all users using user_dto.ListUsersParams structure
	params := user_dto.ListUsersParams{}
	params.Page = 1
	params.PageSize = 10000 // Very large number to get all users

	users, _, err := h.userService.ListUsers(c, params)
	if err != nil {
		log.Error(c.Request.Context(), "Failed to fetch users for export", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch users data"})
		return
	}

	// Write the users data to the temporary file
	encoder := json.NewEncoder(tempFile)
	if err := encoder.Encode(users); err != nil {
		log.Error(c.Request.Context(), "Failed to encode users data", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to encode users data"})
		return
	}
	tempFile.Close() // Close before reading

	// Set appropriate headers for file download
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Disposition", "attachment; filename=users_export.json")
	c.Header("Content-Type", "application/json")

	// Serve the file
	c.File(tempFile.Name())
}

// ImportData imports data from a JSON file
func (h *AdminDataHandler) ImportData(c *gin.Context) {
	// Get the uploaded file
	file, err := c.FormFile("file")
	if err != nil {
		log.Error(c.Request.Context(), "Failed to get uploaded file", "error", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "No file uploaded or invalid form"})
		return
	}

	// Create a temporary directory to store the uploaded file
	tempDir := os.TempDir()
	tempFilePath := filepath.Join(tempDir, file.Filename)

	// Save the uploaded file to the temporary directory
	if err := c.SaveUploadedFile(file, tempFilePath); err != nil {
		log.Error(c.Request.Context(), "Failed to save uploaded file", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save uploaded file"})
		return
	}
	defer os.Remove(tempFilePath) // Clean up temp file when done

	// Open the saved file
	importFile, err := os.Open(tempFilePath)
	if err != nil {
		log.Error(c.Request.Context(), "Failed to open imported file", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process import file"})
		return
	}
	defer importFile.Close()

	// Read the file content
	content, err := io.ReadAll(importFile)
	if err != nil {
		log.Error(c.Request.Context(), "Failed to read import file", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to read import file"})
		return
	}

	// Try to determine the data type (works or users)
	var importedData interface{}
	if err := json.Unmarshal(content, &importedData); err != nil {
		log.Error(c.Request.Context(), "Failed to parse import file", "error", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid JSON format in import file"})
		return
	}

	// Here you would implement the actual import logic
	// This would depend on the data structure and your service implementations
	// For now, we'll just return a success response
	log.Info(c.Request.Context(), "Data imported successfully", "filename", file.Filename)
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Data imported successfully",
	})
}
