// Chinese (Simplified) translations

export default {
  failed: '操作失败',
  success: '操作成功',
  settings: '设置',

  // Admin profile page translations
  adminProfile: {
    title: '管理员资料',
    username: '用户名',
    newPassword: '新密码',
    confirmPassword: '确认密码',
    passwordsMustMatch: '密码必须匹配',
    save: '保存更改',
    updateSuccess: '资料更新成功',
    updateError: '更新资料失败',
    loadError: '加载资料失败',
    noChanges: '未检测到更改'
  },

  // Common text used across the application
  common: {
    confirm: '确定',
    cancel: '取消',
    delete: '删除',
    save: '保存',
    edit: '编辑',
    create: '创建',
    add: '添加',
    remove: '移除',
    file: '文件',
    next: '下一步',
    back: '返回'
  },

  // Common validation messages
  validation: {
    required: '此字段为必填项',
    minLength: '至少需要 {length} 个字符',
    usernameMinLength: '用户名至少{length}个字符',
    passwordMinLength: '密码至少{length}个字符',
    invalidEmail: '请输入有效的电子邮箱',
    passwordMismatch: '密码不匹配',
    playlistNameRequired: '请输入播放列表名称'
  },

  // Language switcher
  languageSwitcher: {
    language: '语言'
  },

  // Navigation items
  nav: {
    mediaLibrary: '媒体库',
    myReviews: '我的评价',
    playlists: '播放列表',
    history: '播放历史',
    circles: '社团',
    tags: '标签',
    voiceActors: '声优',
    randomListen: '随心听',
    sleepMode: '睡眠模式',
    adminPanel: '管理面板',
    darkMode: '夜间模式',
    lightMode: '日间模式',
    logout: '登出',
    login: '登录'
  },

  // Dialog messages
  dialog: {
    logout: '是否退出登录？',
    cancel: '取消',
    confirm: '退出',
    delete: '删除'
  },

  // Notifications
  notification: {
    darkModeEnabled: '已切换到夜间模式',
    lightModeEnabled: '已切换到日间模式',
    loggedOut: '已退出登录',
    randomPlayStart: '随机播放 {count} 首音频',
    noPlayableAudio: '没有找到可播放的音频',
    randomPlayError: '获取随机作品失败',
    loginSuccess: '登录成功',
    loginFailed: '用户名或密码错误',
    networkError: '网络错误',
    registerSuccess: '注册成功',
    registerSuccessLogin: '注册成功！请登录',
    registerFailed: '注册失败: {status}',
    passwordChanged: '密码修改成功',
    emailSent: '邮件已发送',
    reviewSubmitted: '评论已提交',
    reviewSubmitFailed: '评论提交失败',
    reviewDeleted: '评论已删除',
    reviewDeleteFailed: '删除评论失败',
    playlistUpdated: '播放列表已更新',
    playlistUpdateFailed: '更新播放列表失败',
    playlistDeleted: '播放列表已删除',
    playlistDeleteFailed: '删除播放列表失败',
    loadFolderFailed: '加载文件夹失败',
    downloadFailed: '下载失败',
    openFileFailed: '打开文件失败',
    loadListFailed: '加载{type}失败',
    loadHistoryFailed: '加载播放历史失败',
    loadPlaylistFailed: '加载播放列表失败',
    playlistItemRemoved: '项目已从播放列表移除',
    playlistItemRemoveFailed: '从播放列表移除项目失败',
    playlistOrderUpdated: '播放列表顺序已更新',
    playlistOrderUpdateFailed: '更新播放列表顺序失败',
    searchWorksFailed: '搜索作品失败',
    workAlreadyInPlaylist: '作品已在该播放列表中',
    playlistItemAdded: '项目已添加到播放列表',
    playlistItemAddFailed: '添加项目到播放列表失败',
    loadPlaylistsFailed: '加载播放列表失败',
    playlistCreated: '播放列表创建成功',
    playlistCreateFailed: '创建播放列表失败',
    loadReviewsFailed: '加载评论失败',
    loadWorksFailed: '加载作品失败',
    loadWorkDetailsFailed: '加载作品详情失败',
    adminLoadStoragesFailed: '加载存储源失败',
    adminLoadDriverDefinitionsFailed: '加载驱动定义失败',
    adminDriversReloaded: '驱动重载成功',
    adminDriversReloadFailed: '驱动重载失败',
    adminConnectionTestSuccess: '连接测试成功',
    adminConnectionTestFailed: '连接测试失败',
    adminStorageDeleted: '存储源已删除',
    adminStorageDeleteFailed: '删除存储源失败',
    adminLoadTasksFailed: '加载任务失败',
    adminLoadStoragesForTasksFailed: '加载用于任务表单的存储源失败',
    adminScanTaskSubmitted: '扫描任务已提交',
    adminScanTaskSubmitFailed: '提交扫描任务失败',
    adminScrapeAllTaskSubmitted: '刮削任务已提交',
    adminScrapeAllTaskSubmitFailed: '提交刮削任务失败',
    adminTaskCancelled: '任务已取消',
    adminTaskCancelFailed: '取消任务失败',
    adminSystemConfigSaved: '系统配置已保存',
    adminSystemConfigSaveFailed: '保存系统配置失败',
    adminLoadCacheInfoFailed: '加载缓存信息失败',
    adminCoverCacheCleared: '封面缓存已清理',
    adminClearCoverCacheFailed: '清理封面缓存失败',
    adminFileCacheCleared: '文件缓存已清理',
    adminClearFileCacheFailed: '清理文件缓存失败',
    adminAllCacheCleared: '所有缓存已清理',
    adminClearAllCacheFailed: '清理所有缓存失败',
    adminWorksExported: '作品数据导出成功',
    adminExportWorksFailed: '导出作品数据失败',
    adminUsersExported: '用户数据导出成功',
    adminExportUsersFailed: '导出用户数据失败',
    adminDataImported: '数据导入成功',
    adminImportDataFailed: '导入数据失败',
    adminLoadSystemInfoFailed: '加载系统信息失败',
    adminLoadScannerStatusFailed: '加载扫描器状态失败',
    adminScanSpecificStarted: '选定存储源的扫描已开始',
    adminScanAllStarted: '所有存储源的扫描已开始',
    adminScanSpecificFailed: '启动选定存储源的扫描失败',
    adminScanAllFailed: '启动所有存储源的扫描失败',
    subtitleUploaded: '字幕上传成功',
    multipleTitlesUploaded: '{count} 个字幕上传成功',
    uploadFailed: '字幕上传失败',
    missingStorageInfo: '缺少存储信息',
    loadAudioFilesFailed: '加载音频文件失败，请重试',
    encodingChanged: '归档编码已成功更改',
    loadFailed: '加载数据失败',
    createSuccess: '创建成功',
    updateSuccess: '更新成功',
    deleteSuccess: '删除成功',
    saveFailed: '保存失败',
    deleteFailed: '删除失败',
    importFailed: '导入失败'
  },

  // Work card component
  workCard: {
    soldCount: '售出数',
    ageRating: {
      adult: 'R-18',
      r15: 'R-15',
      general: '全年龄'
    },
    dlsiteLink: 'DLsite',
    rating: '平均',
    commentCount: '评论数'
  },

  // Auth related translations
  auth: {
    login: '登录',
    register: '注册',
    username: '用户名',
    usernameOrEmail: '用户名或邮箱',
    password: '密码',
    confirmPassword: '确认密码',
    email: '邮箱',
    forgotPassword: '忘记密码？',
    resetPassword: '重置密码',
    sendResetEmail: '发送重置邮件',
    createAccount: '注册新账户',
    adminLogin: '管理员登录成功！管理面板已在侧边栏中显示',
    registerNewAccount: '注册新账户',
    alreadyHaveAccount: '已有账户？立即登录'
  },

  // Settings page
  settingsPage: {
    personalInfo: '个人信息',
    playbackSettings: '播放设置',
    interfaceSettings: '界面设置',
    username: '用户名',
    userGroup: '用户组',
    registrationDate: '注册日期',
    lastUpdate: '最后更新',
    forwardJump: '快进跳秒数',
    rewindJump: '倒带跳秒数',
    seconds: '秒',
    guestBanner: '您现在以访客身份浏览。仅显示播放器设置。',
    emailSettings: '邮箱设置',
    currentEmail: '当前邮箱',
    notSet: '未设置',
    verified: '已验证',
    unverified: '未验证',
    changeEmail: '更改邮箱',
    setEmail: '设置邮箱',
    resendVerification: '重新发送验证邮件',
    unlinkEmail: '解除邮箱绑定',
    securitySettings: '安全设置',
    currentPassword: '当前密码',
    newPassword: '新密码',
    confirmNewPassword: '确认新密码',
    changePassword: '修改密码',
    passwordRequirements: '密码至少需要6位',
    saved: '设置已保存'
  },

  // Work related translations
  work: {
    details: '详情',
    tracks: '音轨',
    reviews: '评价',
    releasedAt: '发售日期',
    circle: '社团',
    tags: '标签',
    vas: '声优',
    addToPlaylist: '添加到播放列表',
    createPlaylist: '创建播放列表',
    writeReview: '写评价',
    noReviews: '暂无评价',
    yourRating: '你的评分',
    rate: '评分',
    playAll: '全部播放',
    averageRating: '平均',
    starRating: '{count} 星',
    currency: '日元',
    soldCount: '销量',
    markProgress: '标记进度',
    uploadSubtitle: '上传字幕'
  },

  // Player related translations
  player: {
    previous: '上一首',
    play: '播放',
    pause: '暂停',
    rewind: '快退',
    forward: '快进',
    next: '下一首',
    showQueue: '显示队列',
    playMode: '播放模式',
    clearQueue: '清空队列',
    removeFromQueue: '移除',
    reorder: '重新排序',
    openWorkDetails: '打开作品详情',
    hideCoverButtons: '隐藏封面按钮',
    swapSeekButtons: '交换快进/快退按钮',
    subtitles: '字幕',
    noSubtitlesAvailable: '没有可用字幕',
    uploadSubtitleFile: '上传字幕文件',
    upload: '上传',
    close: '关闭',
    uploadedBy: '由 {name} 上传',
    localSubtitle: '本地字幕',
    subtitleLoaded: '已加载字幕: {name}',
    subtitleLoadFailed: '字幕加载失败',
    subtitleUploaded: '字幕上传成功',
    subtitleUploadFailed: '字幕上传失败',
    desktopLyrics: '桌面歌词',
    lyrics: '歌词',
    noLyrics: '暂无歌词',
    currentlyPlaying: '正在播放',
    queue: '播放队列'
  },

  // Lyric dialog related translations
  lyric: {
    title: '歌词',
    selectFile: '选择歌词文件',
    noLyricsFound: '未找到歌词文件',
    similarity: '相似度',
    selectLocalFile: '选择本地文件',
    noLyrics: '暂无歌词',
    noLyricsDesc: '请选择歌词文件以显示歌词',
    adjustTiming: '调整时间偏移以同步歌词与音频',
    localSelectedFile: '本地选择的文件'
  },

  // Review related translations
  review: {
    myReview: '我的评论',
    deleteTag: '删除标记',
    confirmDeleteTag: '确定要删除标记吗',
    progress: {
      wantToListen: '想听',
      listening: '在听',
      listened: '听过',
      relistening: '重听',
      postponed: '搁置'
    },
    updatedAt: '更新时间',
    editReview: '编辑评论',
    deleteReview: '删除评论',
    confirmDelete: {
      title: '确认删除',
      message: '确定要删除这条评论吗？'
    }
  },

  // Playlist related translations
  playlist: {
    create: '创建播放列表',
    edit: '编辑播放列表',
    delete: '删除播放列表',
    name: '播放列表名称',
    nameLabel: '播放列表名称',
    description: '描述',
    descriptionLabel: '描述 (可选)',
    visibility: '可见性',
    visibilityLabel: '可见性',
    public: '公开',
    private: '私人',
    unlisted: '不公开',
    itemCount: '{count} 个项目',
    addTrack: '添加曲目',
    removeTrack: '移除曲目',
    moveTracks: '移动曲目',
    moveUp: '上移',
    moveDown: '下移',
    track: '曲目',
    saveChanges: '保存更改',
    myPlaylists: '我的播放列表',
    createNew: '创建新播放列表',
    emptyPlaylist: '此播放列表为空',
    confirmDelete: '确定要删除此播放列表吗？',
    saveInProgress: '保存播放列表功能开发中',
    confirmRemove: {
      title: '确认移除',
      message: '确定要从播放列表中移除这个项目吗？'
    },
    confirmDeleteDialog: {
      title: '确认删除',
      message: '确定要删除播放列表 "{name}" 吗？此操作不可撤销。'
    },
    visibilityOptions: {
      private: '私有',
      unlisted: '不公开',
      public: '公开'
    },
    updatedAt: '更新于 {date}',
    playAll: '播放全部',
    shufflePlay: '随机播放',
    editInfo: '编辑信息',
    content: '播放列表内容',
    addItem: '添加项目',
    addItemsToPlaylist: '添加项目到播放列表',
    searchWorks: '搜索作品',
    searchPlaceholder: '输入作品标题或原始ID',
    noResultsFound: '未找到结果'
  },

  // Sleep mode related translations
  sleepMode: {
    title: '睡眠模式',
    setTime: '设置睡眠时间',
    cancel: '取消定时',
    disabled: '已关闭睡眠模式',
    willStopAt: '将于{time}停止播放'
  },

  // Work Tree specific translations
  worktree: {
    root: '根目录',
    emptyFolder: '文件夹为空',
    addToQueue: '添加到队列',
    playNext: '下一首播放',
    downloadFile: '下载文件',
    passwordRequired: '需要归档密码',
    enterPassword: '输入密码',
    selectEncoding: '选择字符编码',
    selectEncodingTitle: '归档字符编码',
    encoding: '编码',
    zipEncodingOnly: '注意：编码选择仅适用于ZIP格式的归档文件。RAR和7z格式不支持此功能。',
    encodingHelp: '如果ZIP归档文件名显示乱码，请尝试选择不同的编码',
    encodingIssueDetected: '此归档中的字符可能显示不正确。请选择适当的编码。',
    encodingUTF8: 'UTF-8（默认）',
    encodingShiftJIS: 'Shift-JIS（日语）',
    encodingEUCJP: 'EUC-JP（日语）',
    encodingGBK: 'GBK（简体中文）',
    encodingBig5: 'Big5（繁体中文）',
    encodingEUCKR: 'EUC-KR（韩语）'
  },

  // Error page translations
  error: {
    notFound: '哎呀，这里什么都没有...',
    goHome: '返回首页'
  },

  // List page translations
  list: {
    workCount: '{count} 个作品',
    noData: '暂无数据',
    searchPlaceholder: '搜索{type}...',
    circle: '社团',
    tag: '标签',
    va: '声优'
  },

  // History related translations
  history: {
    title: '播放历史',
    track: '曲目',
    finished: '已完成',
    playedAt: '播放于',
    deleteRecord: '删除记录',
    endOfList: '已到底部',
    confirmDelete: {
      title: '确认删除',
      message: '确定要删除此播放历史记录吗？'
    },
    sortOptions: {
      updatedDesc: '播放日期 (最新优先)',
      updatedAsc: '播放日期 (最早优先)',
      progressDesc: '进度 (从高到低)',
      progressAsc: '进度 (从低到高)'
    }
  },

  // Reviews Page specific translations
  reviewsPage: {
    sortBy: '排序方式',
    gridView: '网格视图',
    listView: '列表视图',
    filterOptions: {
      all: '全部'
      // 其他过滤选项如"想听"等已在 review.progress 中
    },
    sortOptions: {
      updatedDesc: '按更新时间倒序',
      updatedAsc: '按更新时间正序',
      ratingDesc: '按评分从高到低',
      ratingAsc: '按评分从低到高',
      releaseDesc: '按发售日期从新到老',
      releaseAsc: '按发售日期从老到新'
    }
  },

  // Works Page specific translations
  worksPage: {
    searchResultTitle: '"{keyword}"的搜索结果',
    circleWorksTitle: '社团"{circle}"的作品',
    tagWorksTitle: '标签为"{tag}"的作品',
    vaWorksTitle: '声优"{va}"的作品',
    sortBy: '排序方式',
    gridView: '网格视图',
    listView: '列表视图',
    showTags: '显示标签',
    hideTags: '隐藏标签',
    detailView: '详细视图',
    compactView: '紧凑视图',
    sortOptions: {
      releaseDesc: '按发售日期从新到老',
      ratingDesc: '按评价从高到低',
      releaseAsc: '按发售日期从老到新',
      salesDesc: '按销量从多到少',
      priceAsc: '按价格从低到高',
      priceDesc: '按价格从高到低',
      reviewsDesc: '按评论数从多到少',
      createdDesc: '按添加日期从新到老'
    }
  },

  // Work Page specific translations
  workPage: {
    loading: '加载中...'
  },

  // Admin Storage Management Page
  adminStorage: {
    title: '存储管理',
    addStorage: '添加存储源',
    reloadDrivers: '重载驱动',
    driverHelp: '驱动说明',
    statusEnabled: '启用',
    statusDisabled: '已禁用',
    testConnection: '测试连接',
    driverHelpTitle: '存储驱动说明',
    driverInfo: {
      supportedStrategies: '支持的下载策略',
      defaultStrategy: '默认',
      accessMethod: '访问方式',
      proxyOnly: '仅支持代理访问',
      localOnly: '仅支持本地访问',
      configParams: '配置参数',
      required: '必需',
      sensitive: '敏感'
    },
    confirmDeleteDialog: {
      title: '确认删除',
      message: '确定要删除存储源 "{remark}" 吗？此操作不可撤销。'
    },
    table: {
      order: '顺序',
      remark: '备注',
      driver: '驱动',
      downloadStrategy: '下载策略',
      status: '状态',
      createdAt: '创建时间',
      actions: '操作'
    }
  },

  // Admin Task Management Page
  adminTasks: {
    title: '任务管理',
    scanLibrary: '扫描图库',
    scrapeAllWorks: '刮削所有作品',
    refreshTaskList: '刷新任务列表',
    viewDetails: '查看详情',
    cancelTask: '取消任务',
    storageIdLabel: 'ID',
    scanLibraryDialog: {
      title: '扫描图库',
      selectStorage: '选择存储源',
      startScan: '开始扫描'
    },
    scrapeAllDialog: {
      title: '刮削所有作品',
      forceUpdate: '强制更新已有元数据',
      preferredLang: '首选语言',
      startScraping: '开始刮削'
    },
    taskDetailDialog: {
      title: '任务详情',
      taskId: '任务ID',
      taskType: '任务类型',
      status: '状态',
      progress: '进度',
      message: '消息',
      errorMessage: '错误信息',
      createdAt: '创建时间',
      startedAt: '开始时间',
      completedAt: '完成时间',
      payload: '任务参数',
      result: '执行结果'
    },
    table: {
      taskType: '任务类型',
      status: '状态',
      progress: '进度',
      message: '消息',
      createdAt: '创建时间',
      actions: '操作'
    },
    statusLabels: {
      pending: '等待中',
      running: '运行中',
      completed: '已完成',
      failed: '失败',
      cancelled: '已取消'
    },
    languageOptions: {
      zh: '中文',
      ja: '日文',
      en: '英文'
    },
    validation: {
      selectStorage: '请选择存储源'
    }
  },

  // Dashboard - Advanced Settings Page
  dashboardAdvanced: {
    title: '高级设置',
    systemConfig: {
      title: '系统配置',
      siteName: '站点名称',
      siteNameHint: '显示在页面标题的名称',
      allowRegistration: '允许用户注册',
      enableEmailFeatures: '启用邮件功能',
      forceEmailVerification: '注册时强制邮箱验证',
      saveButton: '保存系统配置'
    },
    cacheManagement: {
      title: '缓存管理',
      coverCache: '封面缓存',
      fileCache: '文件缓存',
      allCache: '全部缓存',
      calculating: '计算中...',
      clearCoverCache: '清理封面缓存',
      clearFileCache: '清理文件缓存',
      clearAllCache: '清理全部缓存',
      clearAllDescription: '一键清理所有缓存',
      refreshCacheInfo: '刷新缓存信息'
    },
    dataManagement: {
      title: '数据管理',
      exportData: '数据导出',
      exportDescription: '导出作品数据、用户数据等',
      exportWorks: '导出作品数据',
      exportUsers: '导出用户数据',
      importData: '数据导入',
      importDescription: '从备份文件导入数据',
      selectImportFile: '选择导入文件',
      importButton: '导入数据',
      noFileSelectedError: '请选择要导入的文件'
    },
    systemInfo: {
      title: '系统信息',
      version: '系统版本',
      fetching: '获取中...',
      dbStatus: '数据库状态',
      dbStatusHealthy: '正常',
      dbStatusError: '异常',
      totalWorks: '作品总数',
      totalWorksCount: '{count} 个作品',
      totalUsers: '用户总数',
      totalUsersCount: '{count} 个用户',
      totalStorages: '存储源总数',
      totalStoragesCount: '{count} 个存储源',
      refreshButton: '刷新系统信息'
    }
  },

  // Dashboard - Scanner Page
  dashboardScanner: {
    title: '扫描器管理',
    unnamedStorage: '未命名存储',
    neverScanned: '从未扫描',
    scannerStatus: {
      title: '扫描器状态',
      currentStatus: '当前状态',
      currentScanningStorage: '当前扫描存储源',
      statusInfo: '状态信息',
      lastError: '最后错误',
      scanProgressLabel: '扫描进度',
      refreshStatus: '刷新状态'
    },
    manualScan: {
      title: '手动扫描',
      selectStorage: '选择存储源',
      selectStorageHint: '选择要扫描的存储源',
      scanSelectedStorage: '扫描选定存储源',
      scanAllStorages: '扫描所有存储源'
    },
    storageList: {
      title: '存储源列表',
      statusEnabled: '启用',
      statusDisabled: '已禁用',
      scanThisStorage: '扫描此存储源',
      manageStorages: '管理存储源',
      table: {
        remark: '备注',
        driver: '驱动',
        status: '状态',
        lastScannedAt: '上次扫描时间',
        actions: '操作'
      }
    },
    scanConfirmDialog: {
      title: '确认扫描',
      message: '确定要开始扫描吗？扫描过程可能需要较长时间。',
      startScan: '开始扫描'
    },
    statusLabels: {
      idle: '空闲',
      scanning: '扫描中',
      scraping: '刮削中',
      completed: '已完成',
      failed: '失败',
      error: '错误'
    }
  },

  // Dashboard - Home Page (Folders.vue is effectively the Dashboard Home)
  dashboardHome: {
    title: '仪表盘',
    stats: {
      totalWorks: '作品总数',
      totalUsers: '用户总数',
      totalStorages: '存储源',
      runningTasks: '运行中任务'
    },
    quickActions: {
      title: '快捷操作',
      scanLibrary: '扫描媒体库',
      manageStorages: '存储管理',
      userManagement: '用户管理',
      feedbackManagement: '反馈管理'
    },
    recentTasks: {
      title: '最近任务',
      table: {
        taskType: '任务类型',
        status: '状态',
        createdAt: '创建时间'
      }
    }
  },

  // Dashboard - User Management Page
  dashboardUserManage: {
    title: '用户管理',
    addUser: '添加用户',
    searchPlaceholder: '搜索用户...',
    emailVerified: '已验证',
    emailUnverified: '未验证',
    noEmail: '无邮箱',
    editUserTooltip: '编辑用户',
    deleteUserTooltip: '删除用户',
    createUserDialog: {
      title: '添加用户',
      usernameLabel: '用户名',
      usernameRequired: '请输入用户名',
      emailLabel: '邮箱 (可选)',
      passwordLabel: '密码',
      passwordMinLength: '密码至少6位',
      groupLabel: '用户组'
    },
    editUserDialog: {
      title: '编辑用户',
      usernameLabel: '用户名',
      emailLabel: '邮箱',
      groupLabel: '用户组',
      newPasswordLabel: '新密码 (留空则不修改)'
    },
    deleteConfirmDialog: {
      title: '确认删除',
      message: '确定要删除用户 "{username}" 吗？此操作不可撤销。'
    },
    table: {
      username: '用户名',
      email: '邮箱',
      group: '用户组',
      emailVerified: '邮箱已验证',
      createdAt: '创建时间',
      updatedAt: '更新时间',
      actions: '操作'
    },
    userGroups: {
      admin: '管理员',
      user: '用户'
    }
  },

  // Dashboard - Feedback Management Page
  dashboardFeedbackManage: {
    title: '反馈管理',
    filterStatusLabel: '按状态过滤',
    filterTypeLabel: '按类型过滤',
    anonymousUser: '匿名用户',
    viewDetailTooltip: '查看详情',
    updateStatusTooltip: '更新状态',
    detailDialog: {
      title: '反馈详情',
      feedbackContentTitle: '反馈内容',
      resolutionNotesTitle: '处理备注',
      feedbackId: '反馈ID',
      type: '类型',
      status: '状态',
      submittedBy: '提交用户',
      submittedAt: '提交时间',
      updatedAt: '更新时间',
      resolvedAt: '解决时间',
      resolvedBy: '处理人'
    },
    editDialog: {
      title: '更新反馈状态',
      statusLabel: '状态',
      resolutionNotesLabel: '处理备注 (可选)'
    },
    table: {
      type: '类型',
      status: '状态',
      content: '内容',
      user: '用户',
      createdAt: '提交时间',
      actions: '操作'
    },
    feedbackStatus: {
      new: '新的',
      open: '开放',
      in_progress: '处理中',
      resolved: '已解决',
      closed: '已关闭',
      rejected: '已拒绝'
    },
    feedbackTypes: {
      bug: '错误报告',
      feature: '功能请求',
      improvement: '改进建议',
      comment: '评论',
      other: '其他'
    },
    statusFilters: {
      all: '所有状态'
    },
    typeFilters: {
      all: '所有类型'
    }
  },

  // Search related translations
  search: {
    placeholder: '搜索...',
    advancedSearch: '高级搜索',
    close: '关闭',
    ageCategory: '年龄分级',
    advancedFilters: '高级筛选',
    options: {
      addToFilter: '筛选',
      exclude: '排除',
      alwaysExclude: '永久排除',
      copyToClipboard: '复制文本',
      reverse: '反转搜索 (筛选/排除)'
    },
    ageOptions: {
      onlyAllAges: '⚪ 仅 全年龄',
      onlyR15: '🟠 仅 R-15',
      onlyAdult: '🔞 仅 R-18',
      allAgesAndR15: '🟡 全年龄 & R-15'
    },
    namespace: {
      tag: '搜索标签',
      circle: '搜索社团',
      va: '搜索声优',
      rate: '筛选评分（大于）',
      price: '筛选价格（大于）',
      sell: '筛选销量（大于）',
      age: '筛选年龄分级',
      duration: '筛选作品时长（大于）',
      lang: '筛选语言',
      '-tag': '排除标签',
      '-circle': '排除社团',
      '-va': '排除声优',
      '-age': '排除年龄分级',
      '-duration': '筛选作品时长（小于）',
      '-lang': '排除语言'
    },
    plainText: {
      filter: '纯文字搜索',
      exclude: '纯文字搜索（排除）'
    },
    selectOrEnter: '选择或输入{type}',
    apply: '应用',
    exclude: '排除',
    cancel: '取消'
  },

  // Subtitle upload related translations
  subtitle: {
    upload: {
      step1Title: '上传字幕文件',
      dragDropText: '将字幕文件拖放到此处',
      orText: '或',
      selectFilesBtn: '选择文件',
      selectedFiles: '已选文件',
      step2Title: '检查并编辑匹配',
      overallThreshold: '整体匹配阈值',
      matchThreshold: '匹配阈值',
      selectAll: '全选',
      clearAll: '清除全部',
      applyToAllFiles: '应用到所有文件',
      loadingAudioFiles: '正在加载音频文件...',
      noAudioFiles: '未找到音频文件',
      noAudioFilesDesc: '无法找到与此字幕匹配的音频文件。',
      fetchAudioFiles: '获取音频文件',
      searchTracksPlaceholder: '搜索音轨...',
      step3Title: '确认并上传',
      uploadSummary: '上传摘要',
      subtitleFiles: '字幕文件',
      description: '描述',
      descriptionPlaceholder: '可选描述',
      visibility: '可见性',
      public: '公开',
      selectedTracks: '已选音轨',
      showSelectedTracks: '显示已选音轨',
      match: '匹配',
      matches: '匹配',
      noMatch: '无匹配',
      preview: '预览匹配',
      removeMatch: '移除匹配',
      forceMatch: '强制匹配',
      matchPreviewTitle: '匹配预览',
      settingsAppliedToAll: '设置已应用到所有文件',
      thresholdApplied: '已将 {threshold}% 阈值应用到所有文件',
      uploadBtn: '上传',
      trackName: '音轨名称',
      matchPercentage: '匹配率',
      actions: '操作'
    },
    notification: {
      subtitleUploaded: '字幕上传成功',
      multipleTitlesUploaded: '{count} 个字幕上传成功',
      uploadFailed: '字幕上传失败',
      missingStorageInfo: '缺少存储信息',
      loadAudioFilesFailed: '加载音频文件失败，请重试'
    }
  },

  // 管理员归档密码管理
  adminArchivePasswords: {
    title: '归档密码管理',
    addPassword: '添加密码',
    editPassword: '编辑密码',
    batchImport: '批量导入',
    password: '密码',
    description: '描述',
    descriptionHint: '可选的密码描述',
    cachedPasswords: '缓存密码',
    storageId: '存储ID',
    archivePath: '归档路径',
    lastUsed: '最后使用',
    confirmDelete: '确定要删除这个密码吗？',
    confirmDeleteCached: '确定要删除 "{path}" 的缓存密码吗？',
    passwordList: '密码列表',
    passwordListHint: '使用选定的分隔符分隔密码',
    separator: '分隔符',
    separators: {
      newline: '换行符',
      comma: '逗号 (,)',
      semicolon: '分号 (;)',
      space: '空格'
    },
    import: '导入',
    importResult: '导入完成：{added} 个已添加，{duplicates} 个重复，{invalid} 个无效，共 {total} 个',
    importErrors: '导入错误'
  }
}
