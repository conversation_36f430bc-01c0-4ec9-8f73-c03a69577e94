package handler

import (
	"encoding/base64"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
	"github.com/Sakura-Byte/kikoeru-go/pkg/http/rest/common"
	"github.com/Sakura-Byte/kikoeru-go/pkg/log"
	"github.com/Sakura-Byte/kikoeru-go/pkg/ports"
	"github.com/gin-gonic/gin"
)

// ArchiveHandler handles archive-related API requests
type ArchiveHandler struct {
	service ports.FileSystemService
}

// NewArchiveHandler creates a new ArchiveHandler
func NewArchiveHandler(fsService ports.FileSystemService) *ArchiveHandler {
	return &ArchiveHandler{
		service: fsService,
	}
}

// ListArchiveContents handles POST /api/v1/archive/list
func (h *ArchiveHandler) ListArchiveContents(c *gin.Context) {
	var req ports.ArchiveRequest
	if err := c.ShouldBind<PERSON>(&req); err != nil {
		log.Warn(c.Request.Context(), "Failed to bind JSON for ListArchiveContents", "error", err)
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid request payload")
		return
	}

	entries, err := h.service.ListArchive(c.Request.Context(), req)
	if err != nil {
		log.Error(c.Request.Context(), "Failed to list archive contents", "request", req, "error", err)
		if errors.Is(err, apperrors.ErrStorageNotFound) || errors.Is(err, apperrors.ErrFileNotFound) {
			common.SendErrorResponse(c, http.StatusNotFound, err.Error())
		} else if errors.Is(err, apperrors.ErrPathIsDir) {
			common.SendErrorResponse(c, http.StatusBadRequest, "The specified path is a directory, not an archive")
		} else if errors.Is(err, apperrors.ErrUnsupportedArchiveFormat) {
			common.SendErrorResponse(c, http.StatusBadRequest, "Unsupported archive format")
		} else if errors.Is(err, apperrors.ErrPasswordRequired) {
			common.SendErrorResponse(c, http.StatusBadRequest, "Password is required for this archive")
		} else if errors.Is(err, apperrors.ErrInvalidPassword) {
			common.SendErrorResponse(c, http.StatusBadRequest, "Invalid password for this archive")
		} else {
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to list archive contents")
		}
		return
	}

	if entries == nil {
		entries = []ports.ArchiveEntry{}
	}
	common.SendSuccessResponse(c, http.StatusOK, entries)
}

// ExtractArchiveFile handles GET /api/v1/archive/extract
func (h *ArchiveHandler) ExtractArchiveFile(c *gin.Context) {
	storageIDStr := c.Query("storage_id")
	archivePath := c.Query("archive_path")
	filePathInArchive := c.Query("file_path")
	password := c.Query("password")
	encoding := c.Query("encoding")
	rangeHeader := c.GetHeader("Range")

	if storageIDStr == "" || archivePath == "" || filePathInArchive == "" {
		common.SendErrorResponse(c, http.StatusBadRequest, "storage_id, archive_path, and file_path query parameters are required")
		return
	}

	storageID, err := strconv.ParseUint(storageIDStr, 10, 32)
	if err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid storage_id format")
		return
	}

	stream, contentType, originalContentLength, actualContentLength, filename, httpStatus, err := h.service.GetArchiveFileStream(
		c.Request.Context(),
		uint(storageID),
		archivePath,
		filePathInArchive,
		password,
		encoding,
		rangeHeader,
	)
	if err != nil {
		log.Error(c.Request.Context(), "Failed to extract file from archive",
			"storage_id", storageID,
			"archive_path", archivePath,
			"file_path", filePathInArchive,
			"error", err)

		statusCode := http.StatusInternalServerError
		if errors.Is(err, apperrors.ErrStorageNotFound) || errors.Is(err, apperrors.ErrFileNotFound) {
			statusCode = http.StatusNotFound
		} else if errors.Is(err, apperrors.ErrPathIsDir) {
			statusCode = http.StatusBadRequest
		} else if errors.Is(err, apperrors.ErrUnsupportedArchiveFormat) {
			statusCode = http.StatusBadRequest
		} else if errors.Is(err, apperrors.ErrPasswordRequired) {
			statusCode = http.StatusBadRequest
		} else if errors.Is(err, apperrors.ErrInvalidPassword) {
			statusCode = http.StatusBadRequest
		} else if errors.Is(err, apperrors.ErrRangeNotSatisfiable) {
			statusCode = http.StatusRequestedRangeNotSatisfiable
			c.Header("Content-Range", fmt.Sprintf("bytes */%d", originalContentLength))
		}

		common.SendErrorResponse(c, statusCode, err.Error())
		return
	}
	defer stream.Close()

	c.Header("Content-Type", contentType)

	// Set Content-Disposition header with filename
	disposition := fmt.Sprintf("inline; filename=\"%s\"", filename)
	c.Header("Content-Disposition", disposition)

	if httpStatus == http.StatusPartialContent && rangeHeader != "" {
		// For partial content, set Content-Range header
		rangeStart := originalContentLength - actualContentLength
		if rangeHeader != "" && strings.HasPrefix(rangeHeader, "bytes=") {
			parts := strings.Split(strings.TrimPrefix(rangeHeader, "bytes="), "-")
			if len(parts) > 0 && parts[0] != "" {
				if start, err := strconv.ParseInt(parts[0], 10, 64); err == nil {
					rangeStart = start
				}
			}
		}

		c.Header("Content-Range", fmt.Sprintf("bytes %d-%d/%d",
			rangeStart,
			rangeStart+actualContentLength-1,
			originalContentLength))
		c.Header("Accept-Ranges", "bytes")
	} else if originalContentLength > 0 {
		// For full content, still indicate we accept ranges
		c.Header("Accept-Ranges", "bytes")
	}

	// Set Content-Length to the actual bytes being sent
	if actualContentLength > 0 {
		c.Header("Content-Length", strconv.FormatInt(actualContentLength, 10))
	}

	c.Writer.WriteHeader(httpStatus)

	_, copyErr := io.Copy(c.Writer, stream)
	if copyErr != nil {
		log.Error(c.Request.Context(), "Error streaming file to client",
			"storage_id", storageID,
			"archive_path", archivePath,
			"file_path", filePathInArchive,
			"error", copyErr)
	}
}

// ProxyArchiveFileStream handles GET /api/v1/archive/proxy/{storage_id}/{encoded_archive_path}/{encoded_file_path}
func (h *ArchiveHandler) ProxyArchiveFileStream(c *gin.Context) {
	storageIDStr := c.Param("storage_id")
	encodedArchivePath := c.Param("encoded_archive_path")
	encodedFilePath := c.Param("encoded_file_path")
	password := c.Query("password") // Optional
	encoding := c.Query("encoding") // Optional
	rangeHeader := c.GetHeader("Range")

	storageID, err := strconv.ParseUint(storageIDStr, 10, 32)
	if err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid storage_id in path")
		return
	}

	decodedArchivePathBytes, err := base64.URLEncoding.DecodeString(encodedArchivePath)
	if err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid encoded_archive_path: not valid base64 URL encoding")
		return
	}
	archivePath := string(decodedArchivePathBytes)

	decodedFilePathBytes, err := base64.URLEncoding.DecodeString(encodedFilePath)
	if err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid encoded_file_path: not valid base64 URL encoding")
		return
	}
	filePathInArchive := string(decodedFilePathBytes)

	stream, contentType, originalContentLength, actualContentLength, filename, httpStatus, err := h.service.GetArchiveFileStream(
		c.Request.Context(),
		uint(storageID),
		archivePath,
		filePathInArchive,
		password,
		encoding,
		rangeHeader,
	)
	if err != nil {
		log.Error(c.Request.Context(), "Failed to extract file from archive for proxy",
			"storage_id", storageID,
			"archive_path", archivePath,
			"file_path", filePathInArchive,
			"error", err)

		statusCode := http.StatusInternalServerError
		if errors.Is(err, apperrors.ErrStorageNotFound) || errors.Is(err, apperrors.ErrFileNotFound) {
			statusCode = http.StatusNotFound
		} else if errors.Is(err, apperrors.ErrPathIsDir) {
			statusCode = http.StatusBadRequest
		} else if errors.Is(err, apperrors.ErrUnsupportedArchiveFormat) {
			statusCode = http.StatusBadRequest
		} else if errors.Is(err, apperrors.ErrPasswordRequired) {
			statusCode = http.StatusBadRequest
		} else if errors.Is(err, apperrors.ErrInvalidPassword) {
			statusCode = http.StatusBadRequest
		} else if errors.Is(err, apperrors.ErrRangeNotSatisfiable) {
			statusCode = http.StatusRequestedRangeNotSatisfiable
			c.Header("Content-Range", fmt.Sprintf("bytes */%d", originalContentLength))
		}

		common.SendErrorResponse(c, statusCode, err.Error())
		return
	}
	defer stream.Close()

	c.Header("Content-Type", contentType)

	// Set Content-Disposition header with filename
	disposition := fmt.Sprintf("inline; filename=\"%s\"", filename)
	c.Header("Content-Disposition", disposition)

	if httpStatus == http.StatusPartialContent && rangeHeader != "" {
		// For partial content, set Content-Range header
		rangeStart := originalContentLength - actualContentLength
		if rangeHeader != "" && strings.HasPrefix(rangeHeader, "bytes=") {
			parts := strings.Split(strings.TrimPrefix(rangeHeader, "bytes="), "-")
			if len(parts) > 0 && parts[0] != "" {
				if start, err := strconv.ParseInt(parts[0], 10, 64); err == nil {
					rangeStart = start
				}
			}
		}

		c.Header("Content-Range", fmt.Sprintf("bytes %d-%d/%d",
			rangeStart,
			rangeStart+actualContentLength-1,
			originalContentLength))
		c.Header("Accept-Ranges", "bytes")
	} else if originalContentLength > 0 {
		// For full content, still indicate we accept ranges
		c.Header("Accept-Ranges", "bytes")
	}

	// Set Content-Length to the actual bytes being sent
	if actualContentLength > 0 {
		c.Header("Content-Length", strconv.FormatInt(actualContentLength, 10))
	}

	c.Writer.WriteHeader(httpStatus)

	_, copyErr := io.Copy(c.Writer, stream)
	if copyErr != nil {
		log.Error(c.Request.Context(), "Error streaming file to client",
			"storage_id", storageID,
			"archive_path", archivePath,
			"file_path", filePathInArchive,
			"error", copyErr)
	}
}

// TreeArchiveContents handles POST /api/v1/archive/tree
func (h *ArchiveHandler) TreeArchiveContents(c *gin.Context) {
	var req ports.ArchiveTreeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Warn(c.Request.Context(), "Failed to bind JSON for TreeArchiveContents", "error", err)
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid request payload")
		return
	}

	node, err := h.service.TreeArchive(c.Request.Context(), req)
	if err != nil {
		log.Error(c.Request.Context(), "Failed to get archive tree contents", "request", req, "error", err)
		// Consolidate error handling similar to ListArchiveContents
		if errors.Is(err, apperrors.ErrStorageNotFound) || errors.Is(err, apperrors.ErrFileNotFound) || errors.Is(err, apperrors.ErrPathNotFound) {
			common.SendErrorResponse(c, http.StatusNotFound, err.Error())
		} else if errors.Is(err, apperrors.ErrPathIsDir) { // This might not apply if root is a dir for a tree
			common.SendErrorResponse(c, http.StatusBadRequest, err.Error())
		} else if errors.Is(err, apperrors.ErrUnsupportedArchiveFormat) {
			common.SendErrorResponse(c, http.StatusBadRequest, "Unsupported archive format")
		} else if errors.Is(err, apperrors.ErrPasswordRequired) {
			common.SendErrorResponse(c, http.StatusBadRequest, "Password is required for this archive")
		} else if errors.Is(err, apperrors.ErrInvalidPassword) {
			common.SendErrorResponse(c, http.StatusBadRequest, "Invalid password for this archive")
		} else {
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to get archive tree contents")
		}
		return
	}

	if node == nil { // Should not happen if error is nil, but as a safeguard
		common.SendSuccessResponse(c, http.StatusOK, gin.H{}) // Send empty object or appropriate response
		return
	}
	common.SendSuccessResponse(c, http.StatusOK, node)
}
