-- migrations_temp/000005_create_system_tables.up.sql
-- Defines system-related tables, primarily for background tasks.

-- Background Task Table (t_background_task)
-- Based on 000013_create_background_task_table.up.sql
CREATE TABLE IF NOT EXISTS t_background_task (
    id VARCHAR(36) PRIMARY KEY, -- UUID for the task
    task_type VARCHAR(50) NOT NULL, -- Type of task (e.g., 'scan_library', 'scrape_metadata')
    status VARCHAR(20) NOT NULL, -- e.g., 'pending', 'running', 'completed', 'failed', 'cancelled'
    progress REAL DEFAULT 0.0, -- Task progress (0.0 to 1.0)
    message TEXT, -- User-friendly message about the current status or progress
    payload TEXT, -- JSON string representing the input/parameters for the task
    result TEXT,  -- JSON string representing the output/result of the task upon completion
    error_message TEXT, -- Error message if the task failed
    submitted_by_user_id VARCHAR(36), -- Optional: ID of the user who initiated the task
    
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP, -- When the task was created/submitted
    started_at DATETIME NULL, -- When the task processing actually started
    completed_at DATETIME NULL, -- When the task finished (successfully or not)
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Last update timestamp for the task record

    FOREIGN KEY (submitted_by_user_id) REFERENCES t_user(id) ON DELETE SET NULL ON UPDATE CASCADE
);

-- Indexes for t_background_task
CREATE INDEX IF NOT EXISTS idx_background_task_type ON t_background_task (task_type);
CREATE INDEX IF NOT EXISTS idx_background_task_status ON t_background_task (status);
CREATE INDEX IF NOT EXISTS idx_background_task_submitted_by_user_id ON t_background_task (submitted_by_user_id);
CREATE INDEX IF NOT EXISTS idx_background_task_created_at ON t_background_task (created_at DESC);
CREATE INDEX IF NOT EXISTS idx_background_task_updated_at ON t_background_task (updated_at DESC);

-- Trigger to update 'updated_at' timestamp
CREATE TRIGGER IF NOT EXISTS trigger_t_background_task_updated_at
AFTER UPDATE ON t_background_task
FOR EACH ROW
BEGIN
    UPDATE t_background_task SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
END;