import { api } from 'src/boot/axios'

// File System Service API
export default {
  /**
   * List files and directories at a specific path
   * @param {number} storageId - The storage ID
   * @param {string} path - The path to list
   * @param {boolean} refresh - Force refresh from source
   * @returns {Promise} - API response with file/directory entries
   */
  async list(storageId, path, refresh = false) {
    const response = await api.post('/api/v1/fs/list', {
      storage_id: storageId,
      path,
      refresh
    })
    return response
  },

  /**
   * Get the entire directory tree starting from a path
   * @param {number} storageId - The storage ID
   * @param {string} path - The root path to start from
   * @param {boolean} refresh - Force refresh from source
   * @returns {Promise} - API response with the directory tree
   */
  async tree(storageId, path, refresh = false) {
    // Simply fetch from API (one request per work visit)
    const response = await api.post('/api/v1/fs/tree', {
      storage_id: storageId,
      path,
      refresh
    })

    return response
  }
}
