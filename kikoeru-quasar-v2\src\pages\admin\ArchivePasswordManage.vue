<template>
  <div class="q-pa-md">
    <div class="text-h5 text-weight-regular q-mb-lg">{{ t('adminArchivePasswords.title') }}</div>

    <div class="row justify-between q-mb-md">
      <div class="row q-gutter-sm">
        <q-btn
          color="primary"
          icon="add"
          :label="t('adminArchivePasswords.addPassword')"
          @click="showCreateDialog = true"
        />
        <q-btn
          color="secondary"
          icon="upload"
          :label="t('adminArchivePasswords.batchImport')"
          @click="showBatchImportDialog = true"
        />
      </div>
      
      <q-btn
        color="info"
        icon="refresh"
        :label="t('common.refresh')"
        @click="loadPasswords"
        :loading="loading"
      />
    </div>

    <!-- Password List Table -->
    <q-table
      :rows="passwords"
      :columns="passwordColumns"
      row-key="id"
      :loading="loading"
      :pagination="{ rowsPerPage: 20 }"
      class="q-mb-lg"
    >
      <template v-slot:body-cell-password="props">
        <q-td :props="props">
          <span v-if="!showPasswords[props.row.id]">••••••••</span>
          <span v-else>{{ props.row.password }}</span>
          <q-btn
            flat
            dense
            round
            :icon="showPasswords[props.row.id] ? 'visibility_off' : 'visibility'"
            size="sm"
            @click="togglePasswordVisibility(props.row.id)"
            class="q-ml-sm"
          />
        </q-td>
      </template>

      <template v-slot:body-cell-actions="props">
        <q-td :props="props">
          <div class="row q-gutter-xs">
            <q-btn
              dense
              round
              color="primary"
              icon="edit"
              size="sm"
              @click="editPassword(props.row)"
            >
              <q-tooltip>{{ t('common.edit') }}</q-tooltip>
            </q-btn>

            <q-btn
              dense
              round
              color="negative"
              icon="delete"
              size="sm"
              @click="confirmDelete(props.row)"
            >
              <q-tooltip>{{ t('common.delete') }}</q-tooltip>
            </q-btn>
          </div>
        </q-td>
      </template>
    </q-table>

    <!-- Cached Passwords Section -->
    <div class="text-h6 q-mb-md">{{ t('adminArchivePasswords.cachedPasswords') }}</div>
    <q-table
      :rows="cachedPasswords"
      :columns="cachedPasswordColumns"
      row-key="id"
      :loading="loadingCached"
      :pagination="{ rowsPerPage: 10 }"
    >
      <template v-slot:body-cell-password="props">
        <q-td :props="props">
          <span v-if="!showCachedPasswords[props.row.id]">••••••••</span>
          <span v-else>{{ props.row.password }}</span>
          <q-btn
            flat
            dense
            round
            :icon="showCachedPasswords[props.row.id] ? 'visibility_off' : 'visibility'"
            size="sm"
            @click="toggleCachedPasswordVisibility(props.row.id)"
            class="q-ml-sm"
          />
        </q-td>
      </template>

      <template v-slot:body-cell-actions="props">
        <q-td :props="props">
          <q-btn
            dense
            round
            color="negative"
            icon="delete"
            size="sm"
            @click="confirmDeleteCached(props.row)"
          >
            <q-tooltip>{{ t('common.delete') }}</q-tooltip>
          </q-btn>
        </q-td>
      </template>
    </q-table>

    <!-- Create/Edit Password Dialog -->
    <q-dialog v-model="showCreateDialog" persistent>
      <q-card style="min-width: 500px">
        <q-card-section>
          <div class="text-h6">
            {{ editingPassword ? t('adminArchivePasswords.editPassword') : t('adminArchivePasswords.addPassword') }}
          </div>
        </q-card-section>

        <q-card-section class="q-pt-none">
          <q-form @submit="savePassword" class="q-gutter-md">
            <q-input
              v-model="passwordForm.password"
              :label="t('adminArchivePasswords.password')"
              :type="showPasswordInput ? 'text' : 'password'"
              outlined
              required
              :rules="[val => !!val || t('validation.required')]"
            >
              <template v-slot:append>
                <q-icon
                  :name="showPasswordInput ? 'visibility_off' : 'visibility'"
                  class="cursor-pointer"
                  @click="showPasswordInput = !showPasswordInput"
                />
              </template>
            </q-input>

            <q-input
              v-model="passwordForm.description"
              :label="t('adminArchivePasswords.description')"
              outlined
              type="textarea"
              rows="3"
              :hint="t('adminArchivePasswords.descriptionHint')"
            />
          </q-form>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="t('common.cancel')" @click="cancelPasswordEdit" />
          <q-btn
            color="primary"
            :label="editingPassword ? t('common.update') : t('common.create')"
            @click="savePassword"
            :loading="saving"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- Batch Import Dialog -->
    <q-dialog v-model="showBatchImportDialog" persistent>
      <q-card style="min-width: 600px">
        <q-card-section>
          <div class="text-h6">{{ t('adminArchivePasswords.batchImport') }}</div>
        </q-card-section>

        <q-card-section class="q-pt-none">
          <q-form @submit="batchImportPasswords" class="q-gutter-md">
            <q-input
              v-model="batchImportForm.passwordText"
              :label="t('adminArchivePasswords.passwordList')"
              outlined
              type="textarea"
              rows="10"
              :hint="t('adminArchivePasswords.passwordListHint')"
              required
              :rules="[val => !!val || t('validation.required')]"
            />

            <q-select
              v-model="batchImportForm.separator"
              :options="separatorOptions"
              :label="t('adminArchivePasswords.separator')"
              outlined
              emit-value
              map-options
            />
          </q-form>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="t('common.cancel')" @click="cancelBatchImport" />
          <q-btn
            color="primary"
            :label="t('adminArchivePasswords.import')"
            @click="batchImportPasswords"
            :loading="importing"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, getCurrentInstance } from 'vue'
import { useI18n } from 'vue-i18n'
import { useQuasar } from 'quasar'
import { useNotification } from '../../composables/useNotification'

defineOptions({
  name: 'ArchivePasswordManage'
})

const { t } = useI18n()
const $q = useQuasar()
const { proxy } = getCurrentInstance()
const { showSuccessNotification, showErrorNotification } = useNotification()

// Reactive data
const passwords = ref([])
const cachedPasswords = ref([])
const loading = ref(false)
const loadingCached = ref(false)
const saving = ref(false)
const importing = ref(false)
const showCreateDialog = ref(false)
const showBatchImportDialog = ref(false)
const editingPassword = ref(null)
const showPasswords = ref({})
const showCachedPasswords = ref({})
const showPasswordInput = ref(false)

const passwordForm = ref({
  password: '',
  description: ''
})

const batchImportForm = ref({
  passwordText: '',
  separator: '\n'
})

// Computed
const passwordColumns = computed(() => [
  {
    name: 'id',
    label: 'ID',
    field: 'id',
    sortable: true,
    align: 'left'
  },
  {
    name: 'password',
    label: t('adminArchivePasswords.password'),
    field: 'password',
    sortable: true,
    align: 'left'
  },
  {
    name: 'description',
    label: t('adminArchivePasswords.description'),
    field: 'description',
    sortable: true,
    align: 'left'
  },
  {
    name: 'created_at',
    label: t('common.createdAt'),
    field: 'created_at',
    sortable: true,
    align: 'left',
    format: val => new Date(val).toLocaleString()
  },
  {
    name: 'actions',
    label: t('common.actions'),
    field: 'actions',
    align: 'center'
  }
])

const cachedPasswordColumns = computed(() => [
  {
    name: 'storage_id',
    label: t('adminArchivePasswords.storageId'),
    field: 'storage_id',
    sortable: true,
    align: 'left'
  },
  {
    name: 'archive_path',
    label: t('adminArchivePasswords.archivePath'),
    field: 'archive_path',
    sortable: true,
    align: 'left'
  },
  {
    name: 'password',
    label: t('adminArchivePasswords.password'),
    field: 'password',
    sortable: true,
    align: 'left'
  },
  {
    name: 'last_used_at',
    label: t('adminArchivePasswords.lastUsed'),
    field: 'last_used_at',
    sortable: true,
    align: 'left',
    format: val => new Date(val).toLocaleString()
  },
  {
    name: 'actions',
    label: t('common.actions'),
    field: 'actions',
    align: 'center'
  }
])

const separatorOptions = computed(() => [
  { label: t('adminArchivePasswords.separators.newline'), value: '\n' },
  { label: t('adminArchivePasswords.separators.comma'), value: ',' },
  { label: t('adminArchivePasswords.separators.semicolon'), value: ';' },
  { label: t('adminArchivePasswords.separators.space'), value: ' ' }
])

// Methods
const loadPasswords = async () => {
  loading.value = true
  try {
    const response = await proxy.$api.get('/api/v1/admin/archive-passwords')
    passwords.value = response.data || []
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('notification.loadFailed'))
  } finally {
    loading.value = false
  }
}

const loadCachedPasswords = async () => {
  loadingCached.value = true
  try {
    const response = await proxy.$api.get('/api/v1/admin/archive-passwords/cache')
    cachedPasswords.value = response.data || []
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('notification.loadFailed'))
  } finally {
    loadingCached.value = false
  }
}

const togglePasswordVisibility = (id) => {
  showPasswords.value[id] = !showPasswords.value[id]
}

const toggleCachedPasswordVisibility = (id) => {
  showCachedPasswords.value[id] = !showCachedPasswords.value[id]
}

const editPassword = (password) => {
  editingPassword.value = password
  passwordForm.value = {
    password: password.password,
    description: password.description || ''
  }
  showCreateDialog.value = true
}

const cancelPasswordEdit = () => {
  showCreateDialog.value = false
  editingPassword.value = null
  passwordForm.value = { password: '', description: '' }
  showPasswordInput.value = false
}

const savePassword = async () => {
  saving.value = true
  try {
    if (editingPassword.value) {
      await proxy.$api.put(`/api/v1/admin/archive-passwords/${editingPassword.value.id}`, passwordForm.value)
      showSuccessNotification(t('notification.updateSuccess'))
    } else {
      await proxy.$api.post('/api/v1/admin/archive-passwords', passwordForm.value)
      showSuccessNotification(t('notification.createSuccess'))
    }
    cancelPasswordEdit()
    loadPasswords()
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('notification.saveFailed'))
  } finally {
    saving.value = false
  }
}

const confirmDelete = (password) => {
  $q.dialog({
    title: t('common.confirm'),
    message: t('adminArchivePasswords.confirmDelete', { password: '••••••••' }),
    cancel: true,
    persistent: true
  }).onOk(() => {
    deletePassword(password.id)
  })
}

const deletePassword = async (id) => {
  try {
    await proxy.$api.delete(`/api/v1/admin/archive-passwords/${id}`)
    showSuccessNotification(t('notification.deleteSuccess'))
    loadPasswords()
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('notification.deleteFailed'))
  }
}

const confirmDeleteCached = (cachedPassword) => {
  $q.dialog({
    title: t('common.confirm'),
    message: t('adminArchivePasswords.confirmDeleteCached', { path: cachedPassword.archive_path }),
    cancel: true,
    persistent: true
  }).onOk(() => {
    deleteCachedPassword(cachedPassword.storage_id, cachedPassword.archive_path)
  })
}

const deleteCachedPassword = async (storageId, archivePath) => {
  try {
    await proxy.$api.delete('/api/v1/admin/archive-passwords/cache', {
      params: { storage_id: storageId, archive_path: archivePath }
    })
    showSuccessNotification(t('notification.deleteSuccess'))
    loadCachedPasswords()
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('notification.deleteFailed'))
  }
}

const cancelBatchImport = () => {
  showBatchImportDialog.value = false
  batchImportForm.value = { passwordText: '', separator: '\n' }
}

const batchImportPasswords = async () => {
  importing.value = true
  try {
    const response = await proxy.$api.post('/api/v1/admin/archive-passwords/batch-import', batchImportForm.value)
    const result = response.data
    
    let message = t('adminArchivePasswords.importResult', {
      added: result.added,
      duplicates: result.duplicates,
      invalid: result.invalid,
      total: result.total
    })
    
    if (result.errors && result.errors.length > 0) {
      message += '\n' + t('adminArchivePasswords.importErrors') + ':\n' + result.errors.join('\n')
    }
    
    showSuccessNotification(message)
    cancelBatchImport()
    loadPasswords()
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('notification.importFailed'))
  } finally {
    importing.value = false
  }
}

// Lifecycle
onMounted(() => {
  loadPasswords()
  loadCachedPasswords()
})
</script>

<style scoped>
.q-table {
  box-shadow: 0 1px 5px rgba(0,0,0,0.2), 0 2px 2px rgba(0,0,0,0.14), 0 3px 1px -2px rgba(0,0,0,0.12);
}
</style>
