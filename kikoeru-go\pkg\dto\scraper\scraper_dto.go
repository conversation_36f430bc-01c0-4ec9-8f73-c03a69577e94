package scraper_dto

import (
	"encoding/json"

	circle_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/circle"
	tag_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/tag"
	va_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/va" // Added va_dto import
)

// ScrapedTag is now represented by tag_dto.TagNameDTO
// ScrapedCircle is now represented by circle_dto.CircleNameDTO
// ScrapedVA is now represented by va_dto.VANameDTO

// ScrapedRankItem represents a single rank entry.
type ScrapedRankItem struct {
	Term     string `json:"term"`
	Category string `json:"category"`
	Rank     int    `json:"rank"`
	RankDate string `json:"rank_date"`
}

// ScrapedRateCountDetailItem represents a single item in the rate count detail.
type ScrapedRateCountDetailItem struct {
	ReviewPoint int `json:"review_point"`
	Count       int `json:"count"`
	Ratio       int `json:"ratio"`
}

// ScrapedWorkData represents the data scraped for a single work.
type ScrapedWorkData struct {
	OriginalID      string                    `json:"original_id"`       // Original ID from the source (e.g., DLsite ID)
	Title           string                    `json:"title"`             // Work title
	WorkType        string                    `json:"work_type"`         // Type of work (e.g., "ASMR", "DOUJIN")
	Circle          *circle_dto.CircleNameDTO `json:"circle"`            // Changed to circle_dto.CircleNameDTO
	VAS             []va_dto.VANameDTO        `json:"vas"`               // List of voice actors - Changed to va_dto.VANameDTO
	Tags            []tag_dto.TagNameDTO      `json:"tags"`              // List of tags - Changed to tag.TagNameDTO
	ReleaseDate     string                    `json:"release_date"`      // Release date (string format from source)
	ThumbnailURL    string                    `json:"thumbnail_url"`     // URL of the thumbnail image
	CoverURL        string                    `json:"cover_url"`         // URL of the cover image
	PreviewAudioURL string                    `json:"preview_audio_url"` // URL of the preview audio
	Synopsis        string                    `json:"synopsis"`          // Synopsis or description
	TrackList       []string                  `json:"track_list"`        // List of track titles
	Extra           json.RawMessage           `json:"extra"`             // Any extra data specific to the source
	I18n            map[string]string         `json:"i18n"`              // Internationalization data

	// Fields to be restored
	Price           int64                        `json:"price"`
	DLCount         int64                        `json:"dl_count"`
	RatingAverage   float64                      `json:"rating_average"`
	RatingCount     int64                        `json:"rating_count"`
	Rank            []ScrapedRankItem            `json:"rank"`              // Updated type
	RateCountDetail []ScrapedRateCountDetailItem `json:"rate_count_detail"` // Updated type
	Duration        int64                        `json:"duration"`          // Duration in seconds
	AgeRating       string                       `json:"age_rating"`
	Language        string                       `json:"language"`
	ReviewCount     int64                        `json:"review_count"`
}
