package zip

import (
	"io"
	"io/fs"
	"time"

	"github.com/klauspost/compress/zip"
)

// ZipFileWrapper wraps a zip.File with additional functionality
type ZipFileWrapper struct {
	*zip.File
	password string
}

// NewZipFileWrapper creates a new wrapper for a zip.File
func NewZipFileWrapper(file *zip.File) *ZipFileWrapper {
	return &ZipFileWrapper{
		File: file,
	}
}

// IsEncrypted checks if the file is encrypted
// This is based on the zip file header flags
func (z *ZipFileWrapper) IsEncrypted() bool {
	// Check bit 0 of the general purpose bit flag
	// According to the ZIP specification:
	// Bit 0: If set, indicates that the file is encrypted
	return z.File.Flags&0x1 == 1
}

// SetPassword sets the password for the file
func (z *ZipFileWrapper) SetPassword(password string) {
	z.password = password
}

// Open opens the file with the password if set
func (z *ZipFileWrapper) Open() (io.ReadCloser, error) {
	// Note: klauspost/compress/zip doesn't directly support passwords
	// We'll need to check if the file is encrypted and return an appropriate error
	if z.IsEncrypted() && z.password != "" {
		// Try to open the file anyway - some ZIP implementations might work
		// but most likely this will fail for encrypted files
		return z.File.Open()
	}
	return z.File.Open()
}

// FileInfo returns the file info
func (z *ZipFileWrapper) FileInfo() fs.FileInfo {
	return z.File.FileInfo()
}

// Name returns the name of the file
func (z *ZipFileWrapper) Name() string {
	return z.File.Name
}

// Size returns the size of the file
func (z *ZipFileWrapper) Size() int64 {
	return int64(z.File.UncompressedSize64)
}

// Mode returns the mode of the file
func (z *ZipFileWrapper) Mode() fs.FileMode {
	return z.File.Mode()
}

// ModTime returns the modification time of the file
func (z *ZipFileWrapper) ModTime() time.Time {
	return z.File.Modified
}

// IsDir returns whether the file is a directory
func (z *ZipFileWrapper) IsDir() bool {
	return z.File.FileInfo().IsDir()
}

// Sys returns the underlying data source
func (z *ZipFileWrapper) Sys() interface{} {
	return z.File
}
