package ports

import (
	"context"
	"io"
)

// StorageService defines the interface for storage operations required by ArchiveService.
// This helps decouple ArchiveService from the full FileSystemService implementation.
type StorageService interface {
	GetStorageSource(ctx context.Context, id uint) (*StorageSource, error)
	GetFileStream(ctx context.Context, storageID uint, path string) (io.ReadCloser, string, error)
}

// StorageSource represents a storage source.
type StorageSource struct {
	ID      uint
	Enabled bool
}
