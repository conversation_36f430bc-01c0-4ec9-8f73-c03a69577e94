<template>
  <q-dialog v-model="showDialog" persistent>
    <q-card style="width: 400px; max-width: 90vw;">
      <q-card-section class="text-center">
        <div class="text-h5 q-mb-md">{{ $t('auth.registerNewAccount') }}</div>

        <q-form @submit="onSubmit" class="q-gutter-md">
          <q-input
            filled
            v-model="username"
            :label="$t('auth.username')"
            :rules="[val => val && val.length >= 3 || $t('validation.usernameMinLength', { length: 3 })]"
          />

          <q-input
            filled
            v-model="email"
            :label="$t('auth.email')"
            type="email"
            :rules="[val => val && val.includes('@') || $t('validation.invalidEmail')]"
          />

          <q-input
            filled
            type="password"
            v-model="password"
            :label="$t('auth.password')"
            :rules="[val => val && val.length >= 6 || $t('validation.passwordMinLength', { length: 6 })]"
          />

          <q-input
            filled
            type="password"
            v-model="confirmPassword"
            :label="$t('auth.confirmPassword')"
            :rules="[val => val === password || $t('validation.passwordMismatch')]"
          />

          <div class="text-center">
            <q-btn
              :label="$t('auth.register')"
              type="submit"
              color="primary"
              class="full-width"
              :loading="loading"
            />
          </div>
        </q-form>
      </q-card-section>

      <q-card-section class="text-center">
        <div class="q-mb-sm">
          <q-btn
            flat
            color="primary"
            :label="$t('auth.alreadyHaveAccount')"
            @click="goToLogin"
          />
        </div>
      </q-card-section>

      <q-card-actions align="right">
        <q-btn flat :label="$t('common.cancel')" color="primary" v-close-popup />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, computed, getCurrentInstance } from 'vue'
import { useI18n } from 'vue-i18n'
import { useNotification } from '../composables/useNotification'

defineOptions({
  name: 'RegisterDialog'
})

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'switch-to-login'])

const { t } = useI18n()
const { proxy } = getCurrentInstance()
const { showSuccessNotification, showWarningNotification, showErrorNotification } = useNotification()

// Reactive data
const username = ref('')
const email = ref('')
const password = ref('')
const confirmPassword = ref('')
const loading = ref(false)

// Computed properties
const showDialog = computed({
  get() {
    return props.modelValue
  },
  set(val) {
    emit('update:modelValue', val)
  }
})

// Methods
const onSubmit = async () => {
  if (password.value !== confirmPassword.value) {
    showWarningNotification(t('validation.passwordMismatch'))
    return
  }

  loading.value = true
  try {
    await proxy.$api.post('/api/v1/auth/register', {
      username: username.value,
      email: email.value,
      password: password.value
    })

    showSuccessNotification(t('notification.registerSuccessLogin'))
    showDialog.value = false

    // Clear form
    username.value = ''
    email.value = ''
    password.value = ''
    confirmPassword.value = ''

    // Switch to login dialog
    emit('switch-to-login')
  } catch (error) {
    if (error.response) {
      showErrorNotification(error.response.data.error || t('notification.registerFailed', { status: error.response.status }))
    } else {
      showErrorNotification(error.message || t('notification.networkError'))
    }
  } finally {
    loading.value = false
  }
}

const goToLogin = () => {
  showDialog.value = false
  emit('switch-to-login')
}


</script>
