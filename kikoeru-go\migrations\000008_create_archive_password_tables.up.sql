-- migrations/000008_create_archive_password_tables.up.sql
-- Creates tables for archive password management

-- Archive password list table
CREATE TABLE IF NOT EXISTS t_archive_passwords (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    password VARCHAR(255) NOT NULL,
    description TEXT, -- Optional description for the password
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create unique index on password to prevent duplicates
CREATE UNIQUE INDEX IF NOT EXISTS idx_archive_passwords_password ON t_archive_passwords(password);

-- Archive password cache table for remembering correct passwords for specific archives
CREATE TABLE IF NOT EXISTS t_archive_password_cache (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    storage_id INTEGER NOT NULL,
    archive_path VARCHAR(1024) NOT NULL, -- Path to the archive file within storage
    password VARCHAR(255) NOT NULL,
    last_used_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraint
    FOREIGN KEY (storage_id) REFERENCES t_storage_sources(id) ON DELETE CASCADE
);

-- Create unique index on storage_id + archive_path to ensure one password per archive
CREATE UNIQUE INDEX IF NOT EXISTS idx_archive_password_cache_unique ON t_archive_password_cache(storage_id, archive_path);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_archive_password_cache_lookup ON t_archive_password_cache(storage_id, archive_path);
