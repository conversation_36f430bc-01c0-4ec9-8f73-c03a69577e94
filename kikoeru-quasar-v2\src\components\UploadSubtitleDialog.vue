<template>
  <q-dialog v-model="isDialogOpen" persistent maximized :full-height="true">
    <q-card>
      <q-card-section class="row items-center q-pb-none">
        <div class="text-h6">{{ t('work.uploadSubtitle') }}</div>
        <q-space />
        <q-btn icon="close" flat round dense v-close-popup @click="close" />
      </q-card-section>

      <!-- Stepper UI -->
      <q-stepper v-model="step" vertical color="primary" animated>
        <!-- Step 1: Upload file -->
        <q-step :name="1" :title="t('subtitle.upload.step1Title')" icon="upload_file" :done="step > 1">
          <q-card-section class="q-pa-md">
            <!-- Custom file upload interface -->
            <div
              class="custom-uploader q-mb-md"
              @dragover.prevent="dragOver = true"
              @dragleave.prevent="dragOver = false"
              @drop.prevent="onFileDrop"
            >
              <div
                class="flex column items-center justify-center"
                :class="{'uploader-dragover': dragOver}"
              >
                <q-icon name="upload_file" size="50px" color="primary" class="q-mb-md" />
                <div class="text-subtitle1 text-center q-mb-md">
                  {{ t('subtitle.upload.dragDropText') }}<br>{{ t('subtitle.upload.orText') }}
                </div>
                <q-btn
                  color="primary"
                  :label="t('subtitle.upload.selectFilesBtn')"
                  @click="openFileDialog"
                />
                <input
                  type="file"
                  ref="fileInput"
                  accept=".srt,.vtt,.lrc"
                  @change="onFileChange"
                  multiple
                  style="display: none"
                />
              </div>
            </div>

            <!-- Display selected files -->
            <div v-if="selectedFiles.length > 0" class="selected-files-container">
              <div class="text-h6 q-pb-md">
                {{ t('subtitle.upload.selectedFiles') }}
                <q-badge color="primary" rounded>{{ selectedFiles.length }}</q-badge>
              </div>
              <div class="file-list q-mb-md">
                <q-list bordered separator class="rounded-borders">
                  <q-item v-for="(file, index) in selectedFiles" :key="index">
                    <q-item-section avatar>
                      <q-icon name="description" color="primary" />
                    </q-item-section>
                    <q-item-section>
                      <q-item-label>{{ getFileName(file) }}</q-item-label>
                      <q-item-label caption>{{ getFileSize(file) }}</q-item-label>
                    </q-item-section>
                    <q-item-section side>
                      <q-btn round flat icon="close" color="negative" @click="removeFile(index)">
                        <q-tooltip>{{ t('common.remove') }}</q-tooltip>
                      </q-btn>
                    </q-item-section>
                  </q-item>
                </q-list>
              </div>
            </div>
          </q-card-section>

          <q-stepper-navigation>
            <q-btn
              color="primary"
              :disable="selectedFiles.length === 0"
              @click="step = 2; analyzeMatches()"
              :label="t('common.next')"
            />
          </q-stepper-navigation>
        </q-step>

        <!-- Step 2: Match files -->
        <q-step :name="2" :title="t('subtitle.upload.step2Title')" icon="edit" :done="step > 2">
          <div class="review-container q-pa-md">
            <div class="text-h6 q-mb-md">{{ t('subtitle.upload.selectedFiles') }}</div>

            <!-- Global threshold slider -->
            <div class="overall-threshold q-mb-lg">
              <div class="row items-center q-mb-xs">
                <div class="col-auto text-subtitle1 text-weight-medium">{{ t('subtitle.upload.overallThreshold') }}:</div>
                <div class="col"></div>
                <div class="col-auto text-subtitle1 text-weight-medium">{{ globalThreshold }}%</div>
              </div>
              <q-slider
                v-model="globalThreshold"
                :min="0"
                :max="100"
                :step="1"
                label
                color="primary"
                @change="applyGlobalThreshold"
              />

              <!-- Global match summary -->
              <div class="row items-center q-mt-sm">
                <div class="col-auto">
                  <q-badge color="info" rounded>
                    {{ matchedTracks.length }} {{ t('subtitle.upload.totalMatched') }}
                  </q-badge>
                </div>
                <div class="col-auto q-ml-sm">
                  <q-badge color="positive" rounded>
                    {{ selectedTracks.length }} {{ t('subtitle.upload.totalSelected') }}
                  </q-badge>
                </div>
              </div>
            </div>

            <!-- Two-panel layout -->
            <div class="row q-col-gutter-md">
              <!-- Left panel - File list -->
              <div class="col-12 col-md-5">
                <q-card flat bordered>
                  <q-card-section>
                    <q-list separator>
                      <q-item
                        v-for="(file, index) in selectedFiles"
                        :key="index"
                        clickable
                        :active="activeFileTab === index"
                        @click="activeFileTab = index"
                        :class="{
                          'bg-green-1': getFileMatches(index).length > 0,
                          'bg-grey-2': getFileMatches(index).length === 0
                        }"
                      >
                        <q-item-section>
                          <q-item-label>{{ getFileName(file) }}</q-item-label>
                          <q-item-label caption>
                            <q-badge v-if="getFileMatches(index).length > 0" color="green">
                              {{ getFileMatches(index).length }}
                              {{ getFileMatches(index).length > 1 ? t('subtitle.upload.matches') : t('subtitle.upload.match') }}
                            </q-badge>
                            <q-badge v-else color="grey">{{ t('subtitle.upload.noMatch') }}</q-badge>
                          </q-item-label>
                        </q-item-section>
                      </q-item>
                    </q-list>
                  </q-card-section>
                </q-card>
              </div>

              <!-- Right panel - Match details -->
              <div class="col-12 col-md-7">
                <q-card flat bordered class="q-pa-md">
                  <div class="text-subtitle1 q-mb-md" v-if="selectedFiles[activeFileTab]">
                    {{ getFileName(selectedFiles[activeFileTab]) }}
                  </div>

                  <!-- Match threshold for current file -->
                  <div class="match-threshold q-mb-md">
                    <div class="row items-center q-mb-xs">
                      <div class="col text-subtitle1">{{ t('subtitle.upload.matchThreshold') }}:</div>
                      <div class="col-auto text-h6 text-primary">{{ matchThreshold }}%</div>
                    </div>
                    <q-slider
                      v-model="matchThreshold"
                      :min="0"
                      :max="100"
                      :step="1"
                      label
                      color="primary"
                      @change="updateMatchesByThreshold"
                    />
                  </div>

                  <!-- Match summary -->
                  <div class="match-summary q-mb-md">
                    <div class="row items-center">
                      <div class="col-auto">
                        <q-badge color="info" rounded>
                          {{ getFileMatches(activeFileTab).length }} {{ t('subtitle.upload.matched') }}
                        </q-badge>
                      </div>
                      <div class="col-auto q-ml-sm">
                        <q-badge color="positive" rounded>
                          {{ getSelectedTracks(activeFileTab).length }} {{ t('subtitle.upload.selected') }}
                        </q-badge>
                      </div>
                      <div class="col-auto q-ml-sm">
                        <q-badge color="grey" rounded>
                          {{ audioTracks.length }} {{ t('subtitle.upload.total') }}
                        </q-badge>
                      </div>
                    </div>
                  </div>

                  <div class="row q-mb-md">
                    <q-btn color="primary" icon="select_all" :label="t('subtitle.upload.selectAll')" class="q-mr-md"
                         @click="selectAllMatches" :disable="getFileMatches(activeFileTab).length === 0" />
                    <q-btn color="secondary" icon="clear_all" :label="t('subtitle.upload.clearAll')"
                         @click="clearAllSelections" :disable="getSelectedTracks(activeFileTab).length === 0" />
                    <q-space />
                    <q-btn color="purple" icon="swap_vert" :label="t('subtitle.upload.applyToAllFiles')"
                         @click="applySettingsToAllFiles" :disable="selectedFiles.length <= 1" />
                  </div>

                  <!-- Loading state -->
                  <div v-if="treeLoading || isLoading" class="q-pa-xl text-center">
                    <q-spinner color="primary" size="3em" />
                    <div class="q-mt-md">{{ t('subtitle.upload.loadingAudioFiles') }}</div>
                  </div>

                  <!-- No files found state -->
                  <div v-else-if="!audioTracks.length" class="q-pa-xl text-center text-grey-8">
                    <q-icon name="sentiment_dissatisfied" size="3em" />
                    <div class="q-mt-md text-h6">{{ t('subtitle.upload.noAudioFiles') }}</div>
                    <div class="q-mt-sm">
                      <p>{{ t('subtitle.upload.noAudioFilesDesc') }}</p>
                      <q-btn color="primary" :label="t('subtitle.upload.fetchAudioFiles')" @click="fetchTreeData" />
                    </div>
                  </div>

                  <div v-else>
                    <!-- Search input -->
                    <div class="q-mb-md">
                      <q-input
                        v-model="searchQuery"
                        outlined
                        :placeholder="t('subtitle.upload.searchTracksPlaceholder')"
                        class="q-mb-md"
                      >
                        <template v-slot:append>
                          <q-icon name="search" />
                        </template>
                      </q-input>
                    </div>

                    <!-- Audio tracks table -->
                    <q-table
                      flat
                      bordered
                      :rows="getFilteredTracks(activeFileTab)"
                      :columns="columns"
                      row-key="path"
                      :pagination="{ rowsPerPage: 10 }"
                      :filter="searchQuery"
                      :loading="isLoading"
                    >
                      <template v-slot:body="props">
                        <q-tr :props="props" :class="{
                          'bg-green-1': props.row.matched && props.row.selected,
                          'bg-blue-1': props.row.matched && !props.row.selected
                        }">
                          <q-td key="select" :props="props">
                            <q-checkbox v-model="props.row.selected" />
                          </q-td>
                          <q-td key="name" :props="props">
                            {{ props.row.name }}
                          </q-td>
                          <q-td key="similarity" :props="props">
                            <q-badge
                              v-if="props.row.similarity > 0"
                              :color="props.row.similarity > 80 ? 'positive' : props.row.similarity > 50 ? 'warning' : 'negative'"
                              :label="props.row.similarity + '%'"
                            />
                            <span v-else>-</span>
                          </q-td>
                          <q-td key="actions" :props="props">
                            <q-btn
                              v-if="props.row.matched"
                              size="sm"
                              flat
                              round
                              icon="visibility"
                              @click="previewMatch(props.row)"
                            >
                              <q-tooltip>{{ t('subtitle.upload.preview') }}</q-tooltip>
                            </q-btn>
                            <q-btn
                              size="sm"
                              flat
                              round
                              :icon="props.row.matched ? 'link_off' : 'link'"
                              @click="toggleMatch(props.row)"
                            >
                              <q-tooltip>{{ props.row.matched ? t('subtitle.upload.removeMatch') : t('subtitle.upload.forceMatch') }}</q-tooltip>
                            </q-btn>
                          </q-td>
                        </q-tr>
                      </template>
                    </q-table>
                  </div>
                </q-card>
              </div>
            </div>
          </div>

          <q-stepper-navigation>
            <q-btn color="primary" @click="step = 3" :label="t('common.next')" :disable="selectedTracks.length === 0" />
            <q-btn flat color="primary" @click="step = 1" :label="t('common.back')" class="q-ml-sm" />
          </q-stepper-navigation>
        </q-step>

        <!-- Step 3: Confirmation -->
        <q-step :name="3" :title="t('subtitle.upload.step3Title')" icon="cloud_upload">
          <div class="q-pa-md">
            <q-card class="q-pa-md">
              <div class="text-h6">{{ t('subtitle.upload.uploadSummary') }}</div>
              <q-separator class="q-my-md" />

              <div class="row q-mb-md">
                <div class="col-3 text-weight-bold">{{ t('subtitle.upload.subtitleFiles') }}:</div>
                <div class="col-9">{{ selectedFiles.length }}</div>
              </div>

              <div class="row q-mb-md">
                <div class="col-3 text-weight-bold">{{ t('subtitle.upload.description') }}:</div>
                <div class="col-9">
                  <q-input outlined v-model="description" :label="t('subtitle.upload.descriptionPlaceholder')" />
                </div>
              </div>

              <div class="row q-mb-md">
                <div class="col-3 text-weight-bold">{{ t('subtitle.upload.visibility') }}:</div>
                <div class="col-9">
                  <q-toggle v-model="isPublic" :label="t('subtitle.upload.public')" />
                </div>
              </div>

              <q-separator class="q-my-md" />

              <div v-for="(file, fileIndex) in selectedFiles" :key="fileIndex" class="q-mb-lg">
                <div class="row q-mb-md">
                  <div class="col-12 text-subtitle1 text-weight-bold">
                    {{ t('common.file') }} {{ fileIndex + 1 }}: {{ getFileName(file) }}
                  </div>
                </div>
                <div class="row q-mb-md q-pl-md">
                  <div class="col-3 text-weight-medium">{{ t('subtitle.upload.selectedTracks') }}:</div>
                  <div class="col-9">{{ getSelectedTracks(fileIndex).length }}</div>
                </div>

                <q-expansion-item
                  :label="`${t('subtitle.upload.showSelectedTracks')} (${getSelectedTracks(fileIndex).length})`"
                  icon="playlist_play"
                  header-class="text-primary"
                  class="q-pl-md"
                >
                  <q-card>
                    <q-card-section>
                      <q-list dense>
                        <q-item v-for="track in getSelectedTracks(fileIndex)" :key="track.path">
                          <q-item-section>{{ track.name }}</q-item-section>
                          <q-item-section side v-if="track.matched">
                            <q-badge :color="track.similarity > 80 ? 'positive' : 'warning'">
                              {{ track.similarity }}%
                            </q-badge>
                          </q-item-section>
                        </q-item>
                      </q-list>
                    </q-card-section>
                  </q-card>
                </q-expansion-item>
              </div>
            </q-card>
          </div>

          <q-stepper-navigation>
            <q-btn color="positive" @click="submit" :label="t('subtitle.upload.uploadBtn')" :loading="uploading" />
            <q-btn flat color="primary" @click="step = 2" :label="t('common.back')" class="q-ml-sm" />
          </q-stepper-navigation>
        </q-step>
      </q-stepper>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, computed, onMounted, getCurrentInstance } from 'vue'
import { useQuasar } from 'quasar'
import { useI18n } from 'vue-i18n'
import { useNotification } from '../composables/useNotification'
import {
  audioLyricNameMatch,
  basenameWithoutExt,
  calculateSimilarityPercentage
} from 'src/utils/lyricUtils'

defineOptions({
  name: 'UploadSubtitleDialog'
})

const props = defineProps({
  originalId: {
    type: String,
    required: true
  },
  treeData: {
    type: Object,
    required: false,
    default: null
  },
  storageId: {
    type: Number,
    required: false,
    default: null
  },
  pathInStorage: {
    type: String,
    required: false,
    default: ''
  },
  workTitle: {
    type: String,
    default: ''
  },
  treeLoading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['closed', 'uploaded'])

const $q = useQuasar()
const { t } = useI18n()
const { proxy } = getCurrentInstance()
const { showSuccessNotification, showErrorNotification } = useNotification()

// Basic state
const isDialogOpen = ref(true)
const step = ref(1)
const fileInput = ref(null)
const dragOver = ref(false)
const selectedFiles = ref([])
const activeFileTab = ref(0)
const fileMatchesMap = ref({}) // Maps file index to its matched tracks
const description = ref('')
const isPublic = ref(true)
const audioTracks = ref([])
const searchQuery = ref('')
const uploading = ref(false)
const isLoading = ref(false)
const matchThreshold = ref(50)
const globalThreshold = ref(80)

// Table columns
const columns = ref([
  { name: 'select', label: '', field: 'selected', align: 'left', sortable: false },
  { name: 'name', label: t('subtitle.upload.trackName'), field: 'name', align: 'left', sortable: true },
  { name: 'similarity', label: t('subtitle.upload.matchPercentage'), field: 'similarity', align: 'center', sortable: true },
  { name: 'actions', label: t('subtitle.upload.actions'), field: 'actions', align: 'center', sortable: false }
])

// Computed properties
const matchedTracks = computed(() => {
  return audioTracks.value.filter(track => track.matched)
})

const selectedTracks = computed(() => {
  return audioTracks.value.filter(track => track.selected)
})

const filteredAudioTracks = computed(() => {
  // Sort: matched and selected first, then matched, then alphabetically
  return [...audioTracks.value].sort((a, b) => {
    if (a.matched && a.selected && (!b.matched || !b.selected)) return -1
    if (b.matched && b.selected && (!a.matched || !a.selected)) return 1

    if (a.matched && !b.matched) return -1
    if (!a.matched && b.matched) return 1

    if (a.matched && b.matched) {
      return b.similarity - a.similarity
    }

    return a.name.localeCompare(b.name)
  })
})

// Methods
const close = () => {
  isDialogOpen.value = false
  emit('closed')
}

const openFileDialog = () => {
  fileInput.value.click()
}

const onFileChange = (event) => {
  const files = event.target.files
  if (!files || files.length === 0) return

  // Process files from file input
  Array.from(files).forEach(file => {
    // Check if file is a subtitle file
    if (/\.(srt|vtt|lrc)$/i.test(file.name)) {
      selectedFiles.value.push(file)
      // Initialize file match map for this file
      fileMatchesMap.value[selectedFiles.value.length - 1] = {}
    }
  })

  // Set active tab to the first new file
  if (selectedFiles.value.length > 0) {
    activeFileTab.value = selectedFiles.value.length - 1
  }

  // Clear the input
  event.target.value = ''
}

const onFileDrop = (event) => {
  dragOver.value = false
  const files = event.dataTransfer.files
  if (!files || files.length === 0) return

  // Process dropped files
  Array.from(files).forEach(file => {
    // Check if file is a subtitle file
    if (/\.(srt|vtt|lrc)$/i.test(file.name)) {
      selectedFiles.value.push(file)
      // Initialize file match map for this file
      fileMatchesMap.value[selectedFiles.value.length - 1] = {}
    }
  })

  // Set active tab to the first new file
  if (selectedFiles.value.length > 0) {
    activeFileTab.value = selectedFiles.value.length - 1
  }
}

const removeFile = (index) => {
  selectedFiles.value.splice(index, 1)
  delete fileMatchesMap.value[index]

  // Adjust active tab if necessary
  if (activeFileTab.value >= selectedFiles.value.length) {
    activeFileTab.value = Math.max(0, selectedFiles.value.length - 1)
  }
}

const getFileName = (file) => {
  return file.name || 'Unknown file'
}

const getFileSize = (file) => {
  if (!file.size) return ''
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(file.size) / Math.log(1024))
  return Math.round(file.size / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}

// Load audio tracks from tree data
const loadAudioTracksFromTree = (treeData) => {
  const tracks = []

  const extractAudioFiles = (node, currentPath = '') => {
    if (!node) return

    if (node.entry) {
      const fullPath = currentPath ? `${currentPath}/${node.entry.name}` : node.entry.name

      if (node.entry.type === 'audio') {
        tracks.push({
          name: node.entry.name,
          path: fullPath,
          size: node.entry.size,
          matched: false,
          selected: false,
          similarity: 0
        })
      }
    }

    if (node.children) {
      node.children.forEach(child => {
        const childPath = node.entry ?
          (currentPath ? `${currentPath}/${node.entry.name}` : node.entry.name) :
          currentPath
        extractAudioFiles(child, childPath)
      })
    }
  }

  extractAudioFiles(treeData)
  audioTracks.value = tracks
}

const fetchTreeData = async () => {
  if (!props.storageId || !props.pathInStorage) {
    showErrorNotification(t('subtitle.upload.missingStorageInfo'))
    return
  }

  isLoading.value = true
  try {
    const response = await proxy.$api.get('/api/v1/fs/tree', {
      params: {
        storage_id: props.storageId,
        path: props.pathInStorage
      }
    })

    if (response.data) {
      loadAudioTracksFromTree(response.data)
    }
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('subtitle.upload.fetchTreeFailed'))
  } finally {
    isLoading.value = false
  }
}

// Find matching lyric files for an audio file
const findMatchingLyrics = (subtitleFileName, audioFiles) => {
  const matches = []
  const subtitleBaseName = basenameWithoutExt(subtitleFileName)

  for (const audioFile of audioFiles) {
    const audioBaseName = basenameWithoutExt(audioFile.name)

    if (audioLyricNameMatch(audioBaseName, subtitleBaseName)) {
      const similarity = calculateSimilarityPercentage(audioBaseName, subtitleBaseName)

      matches.push({
        ...audioFile,
        similarity,
        matched: true
      })
    }
  }

  // Sort by similarity (highest first)
  return matches.sort((a, b) => b.similarity - a.similarity)
}

const analyzeMatches = () => {
  // Reset all matches
  audioTracks.value.forEach(track => {
    track.matched = false
    track.selected = false
    track.similarity = 0
  })

  // Clear file matches map
  fileMatchesMap.value = {}

  // For each selected file, find matching audio tracks
  selectedFiles.value.forEach((file, fileIndex) => {
    const matches = findMatchingLyrics(file.name, audioTracks.value)

    // Store matches for this file
    fileMatchesMap.value[fileIndex] = matches.map(match => match.path)

    // Update audio tracks with match info
    matches.forEach(match => {
      const trackIndex = audioTracks.value.findIndex(t => t.path === match.path)
      if (trackIndex !== -1) {
        audioTracks.value[trackIndex].matched = true
        audioTracks.value[trackIndex].similarity = match.similarity

        // Auto-select if similarity is above threshold
        if (match.similarity >= globalThreshold.value) {
          audioTracks.value[trackIndex].selected = true
        }
      }
    })
  })
}

const getFileMatches = (fileIndex) => {
  const matchPaths = fileMatchesMap.value[fileIndex] || []
  return audioTracks.value.filter(track => matchPaths.includes(track.path))
}

const getSelectedTracks = (fileIndex) => {
  const matchPaths = fileMatchesMap.value[fileIndex] || []
  return audioTracks.value.filter(track => matchPaths.includes(track.path) && track.selected)
}

const getFilteredTracks = (fileIndex) => {
  const matchPaths = fileMatchesMap.value[fileIndex] || []

  // Use the filteredAudioTracks computed property for better sorting
  return filteredAudioTracks.value.filter(track => matchPaths.includes(track.path))
}

const updateMatchesByThreshold = () => {
  // Update selections based on current threshold for active file
  const matchPaths = fileMatchesMap.value[activeFileTab.value] || []
  audioTracks.value.forEach(track => {
    if (matchPaths.includes(track.path)) {
      track.selected = track.similarity >= matchThreshold.value
    }
  })
}

const applyGlobalThreshold = () => {
  // Apply global threshold to all files
  selectedFiles.value.forEach((_, fileIndex) => {
    const matchPaths = fileMatchesMap.value[fileIndex] || []
    audioTracks.value.forEach(track => {
      if (matchPaths.includes(track.path)) {
        track.selected = track.similarity >= globalThreshold.value
      }
    })
  })
}

const selectAllMatches = () => {
  const matchPaths = fileMatchesMap.value[activeFileTab.value] || []
  audioTracks.value.forEach(track => {
    if (matchPaths.includes(track.path)) {
      track.selected = true
    }
  })
}

const clearAllSelections = () => {
  const matchPaths = fileMatchesMap.value[activeFileTab.value] || []
  audioTracks.value.forEach(track => {
    if (matchPaths.includes(track.path)) {
      track.selected = false
    }
  })
}

const applySettingsToAllFiles = () => {
  // Apply current file's threshold and selections to all files
  const currentThreshold = matchThreshold.value
  selectedFiles.value.forEach((_, fileIndex) => {
    const matchPaths = fileMatchesMap.value[fileIndex] || []
    audioTracks.value.forEach(track => {
      if (matchPaths.includes(track.path)) {
        track.selected = track.similarity >= currentThreshold
      }
    })
  })
}

const toggleMatch = (track) => {
  track.matched = !track.matched
  if (!track.matched) {
    track.selected = false
    track.similarity = 0
  }
}

const previewMatch = (track) => {
  // Show preview dialog for the matched track
  $q.dialog({
    title: t('subtitle.upload.preview'),
    message: `${t('subtitle.upload.trackName')}: ${track.name}\n${t('subtitle.upload.matchPercentage')}: ${track.similarity}%`,
    ok: t('common.close')
  })
}

const submit = async () => {
  uploading.value = true

  try {
    const formData = new FormData()

    // Add basic info
    formData.append('original_id', props.originalId)
    formData.append('description', description.value)
    formData.append('is_public', isPublic.value)

    // Add files and their track mappings
    selectedFiles.value.forEach((file, fileIndex) => {
      formData.append(`files`, file)

      const selectedTracks = getSelectedTracks(fileIndex)
      const trackPaths = selectedTracks.map(track => track.path)
      formData.append(`track_mappings[${fileIndex}]`, JSON.stringify(trackPaths))
    })

    await proxy.$api.post('/api/v1/subtitles/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })

    showSuccessNotification(t('notification.subtitleUploaded'))
    emit('uploaded')
    close()

  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('notification.subtitleUploadFailed'))
  } finally {
    uploading.value = false
  }
}

// Lifecycle
onMounted(() => {
  if (props.treeData) {
    loadAudioTracksFromTree(props.treeData)
  } else if (props.storageId && props.pathInStorage) {
    // If no tree data but we have storageId and path, fetch it
    fetchTreeData()
  }
})
</script>

<style lang="scss" scoped>
.custom-uploader {
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover, &.uploader-dragover {
    border-color: #1976d2;
    background-color: #f5f5f5;
  }
}

.selected-files-container {
  max-height: 300px;
  overflow-y: auto;
}

.file-list {
  max-height: 200px;
  overflow-y: auto;
}

.review-container {
  min-height: 500px;
}

.overall-threshold {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.match-threshold {
  background: #fff3cd;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #ffeaa7;
}
</style>
