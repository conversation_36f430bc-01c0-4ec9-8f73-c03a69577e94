package circle_dto

import (
	"time"

	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
)

// CircleDTO represents a basic circle in API responses.
type CircleDTO struct {
	ID        string    `json:"id"`
	Name      string    `json:"name"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// CircleNameDTO represents a circle by its name, typically for requests.
type CircleNameDTO struct {
	Name string `json:"name" binding:"required"`
}

// MapCircleModelToDTO converts a models.Circle to a CircleDTO.
func MapCircleModelToDTO(circleModel *models.Circle) *CircleDTO {
	if circleModel == nil {
		return nil
	}
	return &CircleDTO{
		ID:        circleModel.ID,
		Name:      circleModel.Name,
		CreatedAt: circleModel.CreatedAt,
		UpdatedAt: circleModel.UpdatedAt,
	}
}

// MapCircleModelsToDTOs converts a slice of models.Circle to a slice of CircleDTO.
func MapCircleModelsToDTOs(circleModels []*models.Circle) []*CircleDTO {
	if circleModels == nil {
		return nil
	}
	circleDTOs := make([]*CircleDTO, len(circleModels))
	for i, model := range circleModels {
		circleDTOs[i] = MapCircleModelToDTO(model)
	}
	return circleDTOs
}
