package iso9660

import (
	"io"
	"os"
	stdpath "path"
	"strings"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/stream"
	"github.com/kdomanski/iso9660"
)

func getImage(ss *models.StreamWithSeek) (*iso9660.Image, error) {
	adapter, err := stream.AdaptStreamWithSeek(ss)
	if err != nil {
		return nil, err
	}

	reader, err := stream.NewReadAtSeeker(adapter, 0)
	if err != nil {
		return nil, err
	}
	return iso9660.OpenImage(reader)
}

func getObj(img *iso9660.Image, path string) (*iso9660.File, error) {
	obj, err := img.RootDir()
	if err != nil {
		return nil, err
	}
	if path == "/" {
		return obj, nil
	}
	paths := strings.Split(strings.TrimPrefix(path, "/"), "/")
	for _, p := range paths {
		if !obj.IsDir() {
			return nil, apperrors.ErrFileNotFound
		}
		children, err := obj.GetChildren()
		if err != nil {
			return nil, err
		}
		exist := false
		for _, child := range children {
			if child.Name() == p {
				obj = child
				exist = true
				break
			}
		}
		if !exist {
			return nil, apperrors.ErrFileNotFound
		}
	}
	return obj, nil
}

func toModelObj(file *iso9660.File) *models.Object {
	return &models.Object{
		Name:     file.Name(),
		Size:     file.Size(),
		Modified: file.ModTime(),
		IsFolder: file.IsDir(),
	}
}

func decompress(f *iso9660.File, path string, up func(float64)) error {
	file, err := os.OpenFile(stdpath.Join(path, f.Name()), os.O_WRONLY|os.O_CREATE|os.O_EXCL, 0600)
	if err != nil {
		return err
	}
	defer file.Close()

	// Create a reader with size information
	reader := &sizedReader{
		Reader: f.Reader(),
		Size:   f.Size(),
	}

	// Copy with progress updates
	_, err = io.Copy(file, &progressReader{
		Reader:   reader,
		Size:     f.Size(),
		UpdateFn: up,
	})
	return err
}

func decompressAll(children []*iso9660.File, path string) error {
	for _, child := range children {
		if child.IsDir() {
			nextChildren, err := child.GetChildren()
			if err != nil {
				return err
			}
			nextPath := stdpath.Join(path, child.Name())
			if err = os.MkdirAll(nextPath, 0700); err != nil {
				return err
			}
			if err = decompressAll(nextChildren, nextPath); err != nil {
				return err
			}
		} else {
			if err := decompress(child, path, func(_ float64) {}); err != nil {
				return err
			}
		}
	}
	return nil
}

// sizedReader is a reader with size information
type sizedReader struct {
	io.Reader
	Size int64
}

// progressReader reports progress during reading
type progressReader struct {
	Reader   io.Reader
	Size     int64
	UpdateFn func(float64)
	read     int64
}

func (r *progressReader) Read(p []byte) (n int, err error) {
	n, err = r.Reader.Read(p)
	if n > 0 {
		r.read += int64(n)
		if r.Size > 0 && r.UpdateFn != nil {
			r.UpdateFn(float64(r.read) / float64(r.Size) * 100)
		}
	}
	return
}
