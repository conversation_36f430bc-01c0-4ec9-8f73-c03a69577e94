package stream

import (
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
)

// StreamWithSeekAdapter adapts StreamWithSeek to work with NewReadAtSeeker
type StreamWithSeekAdapter struct {
	*models.StreamWithSeek
}

// GetSize returns the size of the stream
func (s *StreamWithSeekAdapter) GetSize() int64 {
	return s.Size
}

// GetName returns the name of the stream
func (s *StreamWithSeekAdapter) GetName() string {
	return s.Name
}

// AdaptStreamWithSeek converts a StreamWithSeek to a ReadAtSeeker compatible type
func AdaptStreamWithSeek(ss *models.StreamWithSeek) (*StreamWithSeekAdapter, error) {
	return &StreamWithSeekAdapter{
		StreamWithSeek: ss,
	}, nil
}
