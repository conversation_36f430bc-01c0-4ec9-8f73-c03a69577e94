package tag_dto

import (
	"time"

	"github.com/Sakura-Byte/kikoeru-go/pkg/models" // Added models import
)

// TagDTO represents a basic tag in API responses.
type TagDTO struct {
	ID        string    `json:"id"`
	Name      string    `json:"name"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// TagNameDTO represents a tag by its name, typically for requests or simple lists.
type TagNameDTO struct {
	Name string `json:"name"`
}

// MapTagModelToDTO converts a models.Tag to a TagDTO.
func MapTagModelToDTO(tagModel *models.Tag) *TagDTO {
	if tagModel == nil {
		return nil
	}
	return &TagDTO{
		ID:        tagModel.ID,
		Name:      tagModel.Name,
		CreatedAt: tagModel.CreatedAt,
		UpdatedAt: tagModel.UpdatedAt,
		// Note: WorkCount from models.Tag is not included in TagDTO by default.
		// If needed, it should be part of a specific DTO like TagWithWorkCount.
	}
}

// MapTagModelsToDTOs converts a slice of models.Tag to a slice of TagDTO.
func MapTagModelsToDTOs(tagModels []*models.Tag) []*TagDTO {
	if tagModels == nil {
		return nil
	}
	tagDTOs := make([]*TagDTO, len(tagModels))
	for i, model := range tagModels {
		tagDTOs[i] = MapTagModelToDTO(model)
	}
	return tagDTOs
}
