<template>
  <div class="q-ma-md">
    <!-- 面包屑导航 -->
    <q-breadcrumbs gutter="xs" v-if="currentPath.length > 0 || inArchive">
      <q-breadcrumbs-el>
        <q-btn
          no-caps
          flat
          dense
          size="md"
          icon="folder"
          style="height: 30px;"
          @click="navigateToRoot"
        >
          {{ t('worktree.root') }}
        </q-btn>
      </q-breadcrumbs-el>

      <q-breadcrumbs-el
        v-for="(segment, index) in breadcrumbs"
        :key="index"
        class="cursor-pointer"
      >
        <q-btn
          no-caps
          flat
          dense
          size="md"
          :icon="segment.isArchive ? 'archive' : 'folder'"
          style="height: 30px;"
          @click="navigateToBreadcrumb(index)"
        >
          {{ segment.label }}
        </q-btn>
      </q-breadcrumbs-el>

      <!-- 压缩包编码选择按钮 -->
      <q-breadcrumbs-el v-if="inArchive && archivePath.toLowerCase().endsWith('.zip')">
        <q-btn
          flat dense round
          size="md"
          icon="text_format"
          color="purple"
          @click="showEncodingDialog = true"
        >
          <q-tooltip>{{ t('worktree.encoding') }}</q-tooltip>
        </q-btn>
      </q-breadcrumbs-el>
    </q-breadcrumbs>

    <!-- 加载状态 -->
    <div v-if="loading" class="row justify-center q-my-md">
      <q-spinner-dots color="primary" size="40px" />
    </div>

    <!-- 文件列表 -->
    <q-card v-else>
      <q-list separator>
        <q-item
          clickable
          v-ripple
          v-for="(item, index) in currentItems"
          :key="index"
          :active="isActiveTrack(item)"
          active-class="text-white bg-teal"
          @click="onClickItem(item)"
          class="non-selectable"
        >
          <q-item-section avatar>
            <template v-if="item.type === 'audio'">
              <q-btn
                round
                dense
                color="primary"
                :icon="playing && isActiveTrack(item) ? 'pause' : 'play_arrow'"
                class="q-btn--standard"
                @click.stop="onClickPlayButton(item)"
              />
            </template>
            <q-icon
              v-else
              size="34px"
              :color="getItemColor(item)"
              :name="getItemIcon(item)"
            />
          </q-item-section>

          <q-item-section>
            <q-item-label lines="2">{{ item.name }}</q-item-label>
            <q-item-label v-if="item.size && !item.is_dir" caption lines="1">
              {{ formatFileSize(item.size) }}
            </q-item-label>
            <q-item-label v-if="item.modified_time" caption lines="1">
              {{ formatDate(item.modified_time) }}
            </q-item-label>
          </q-item-section>

          <!-- 上下文菜单 -->
          <q-menu
            v-if="!item.is_dir"
            touch-position
            context-menu
            auto-close
            transition-show="jump-down"
            transition-hide="jump-up"
          >
            <q-list separator>
              <q-item clickable @click="addToQueue(item)" v-if="item.type === 'audio'">
                <q-item-section>{{ t('worktree.addToQueue') }}</q-item-section>
              </q-item>

              <q-item clickable @click="playNext(item)" v-if="item.type === 'audio'">
                <q-item-section>{{ t('worktree.playNext') }}</q-item-section>
              </q-item>

              <q-item clickable @click="download(item)">
                <q-item-section>{{ t('worktree.downloadFile') }}</q-item-section>
              </q-item>
            </q-list>
          </q-menu>
        </q-item>
      </q-list>

      <!-- 空文件夹提示 -->
      <div v-if="currentItems.length === 0" class="text-center q-my-xl text-grey-6">
        {{ t('worktree.emptyFolder') }}
      </div>
    </q-card>

    <!-- Archive Password Dialog -->
    <q-dialog v-model="showPasswordDialog">
      <q-card style="min-width: 350px">
        <q-card-section>
          <div class="text-h6">{{ t('worktree.passwordRequired') }}</div>
        </q-card-section>

        <q-card-section class="q-pt-none">
          <q-input
            v-model="archivePassword"
            :type="showPassword ? 'text' : 'password'"
            :label="t('worktree.enterPassword')"
          >
            <template v-slot:append>
              <q-icon
                :name="showPassword ? 'visibility' : 'visibility_off'"
                class="cursor-pointer"
                @click="showPassword = !showPassword"
              />
            </template>
          </q-input>

          <!-- Only show encoding selection for ZIP files -->
          <template v-if="isZipArchive">
            <q-select
              v-model="archiveEncoding"
              :options="encodingOptions"
              :label="t('worktree.selectEncoding')"
              class="q-mt-md"
              outlined
              emit-value
              map-options
            />
            <div class="text-caption text-grey-7 q-mt-sm">
              {{ t('worktree.encodingHelp') }}
            </div>
          </template>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="t('common.cancel')" color="negative" v-close-popup />
          <q-btn flat :label="t('common.ok')" color="primary" @click="submitPassword" />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- Archive Encoding Dialog -->
    <q-dialog v-model="showEncodingDialog">
      <q-card style="min-width: 350px">
        <q-card-section>
          <div class="text-h6">{{ t('worktree.selectEncodingTitle') }}</div>
        </q-card-section>

        <q-card-section class="q-pt-none">
          <div class="text-caption text-purple q-mb-md">
            {{ t('worktree.zipEncodingOnly') }}
          </div>
          <div class="text-caption text-grey-7 q-mb-md">
            {{ t('worktree.encodingIssueDetected') }}
          </div>
          <q-select
            v-model="archiveEncoding"
            :options="encodingOptions"
            :label="t('worktree.selectEncoding')"
            class="q-mt-md"
            outlined
            emit-value
            map-options
          />
          <div class="text-caption text-grey-7 q-mt-sm">
            {{ t('worktree.encodingHelp') }}
          </div>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="t('common.cancel')" color="negative" v-close-popup />
          <q-btn flat :label="t('common.ok')" color="primary" @click="applyEncoding" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, getCurrentInstance } from 'vue'
import { useI18n } from 'vue-i18n'
import { useQuasar } from 'quasar'
import { useAudioPlayer } from '../composables/useAudioPlayer'
import { useWorkInfo } from '../composables/useWorkInfo'

defineOptions({
  name: 'WorkTree'
})

const props = defineProps({
  storageId: {
    type: Number,
    required: true
  },
  workId: {
    type: Number,
    required: true
  },
  originalId: {
    type: String,
    required: true
  },
  basePath: {
    type: String,
    default: '/'
  },
  workTitle: {
    type: String,
    default: ''
  }
})

const { t, locale } = useI18n()
const $q = useQuasar()
const { proxy } = getCurrentInstance()
const { currentFile, isPlaying, setQueue, addToQueue: addTrackToQueue, playNext: playNextTrack, togglePlaying } = useAudioPlayer()
const { getTreeData } = useWorkInfo()

// Reactive data
const loading = ref(false)
const currentItems = ref([])
const currentPath = ref([]) // Current path segments starting from base path
const inArchive = ref(false)
const archivePath = ref('') // Path to the archive file
const pathInArchive = ref('/') // Path inside the archive
const archivePassword = ref('') // Password for encrypted archives
const archiveEncoding = ref('') // Encoding for non-UTF8 archives
const fullTreeData = ref(null) // Store the complete tree data to avoid repeated requests
const showPasswordDialog = ref(false) // Control visibility of password dialog
const showEncodingDialog = ref(false) // Control visibility of encoding dialog
const showPassword = ref(false) // Toggle password visibility
const passwordRequiredForPath = ref('') // Store the path that needs password
const currentArchiveTreeData = ref(null) // Store the complete tree data for the current archive

// Encoding options for archives
const encodingOptions = computed(() => [
  { label: t('worktree.encodingUTF8'), value: '' },  // Default, empty means use UTF-8
  { label: t('worktree.encodingShiftJIS'), value: 'shift-jis' },
  { label: t('worktree.encodingEUCJP'), value: 'euc-jp' },
  { label: t('worktree.encodingGBK'), value: 'gbk' }, // Chinese
  { label: t('worktree.encodingBig5'), value: 'big5' }, // Traditional Chinese
  { label: t('worktree.encodingEUCKR'), value: 'euc-kr' } // Korean
])

// Archive encoding storage key - use path as unique identifier
const getArchiveEncodingStorageKey = (path) => {
  return `archive_encoding:${path}`
}

// Load saved encoding for an archive
const loadSavedEncoding = (path) => {
  try {
    if ($q && $q.localStorage) {
      const key = getArchiveEncodingStorageKey(path)
      if ($q.localStorage.has(key)) {
        return $q.localStorage.getItem(key)
      }
    }
  } catch (error) {
    console.warn('Failed to load saved encoding:', error)
  }
  return '' // Default to UTF-8 (empty string)
}

// Save encoding preference for an archive
const saveEncodingPreference = (path, encoding) => {
  try {
    if ($q && $q.localStorage && path) {
      const key = getArchiveEncodingStorageKey(path)
      $q.localStorage.set(key, encoding)
    }
  } catch (error) {
    console.warn('Failed to save encoding preference:', error)
  }
}

// Computed properties
const breadcrumbs = computed(() => {
  // Skip the root element and only include actual path segments
  const crumbs = []
  let currentPathSegments = []

  if (inArchive.value) {
    // Handle archive breadcrumbs
    // First add the archive filename as a crumb
    const archiveFileName = archivePath.value.split('/').pop()
    crumbs.push({
      label: archiveFileName,
      path: [], // Empty path will return to root
      isArchive: true
    })

    // Then add path segments within the archive
    const archivePathSegments = pathInArchive.value.split('/').filter(segment => segment.length > 0)
    let currentArchivePath = ''
    for (const segment of archivePathSegments) {
      currentArchivePath = currentArchivePath ? `${currentArchivePath}/${segment}` : segment
      crumbs.push({
        label: segment,
        archivePath: currentArchivePath,
        isArchive: true
      })
    }
  } else {
    // Regular filesystem breadcrumbs
    for (const segment of currentPath.value) {
      currentPathSegments = [...currentPathSegments, segment]
      crumbs.push({
        label: segment,
        path: [...currentPathSegments]
      })
    }
  }
  return crumbs
})

const currentPlayingFile = computed(() => {
  return currentFile?.value || {}
})

const playing = computed(() => {
  return isPlaying?.value || false
})

// Check if current archive is a ZIP file (encoding only works for ZIP)
const isZipArchive = computed(() => {
  if (!inArchive.value || !archivePath.value) return false
  return archivePath.value.toLowerCase().endsWith('.zip')
})

// Methods
const loadCurrentDirectory = async () => {
  loading.value = true
  try {
    if (inArchive.value) {
      await loadArchiveContents()
    } else {
      await loadFileSystemContents()
    }
  } catch (error) {
    console.error('Failed to load directory:', error)
    $q.notify({
      color: 'negative',
      message: t('notification.loadFolderFailed')
    })
  } finally {
    loading.value = false
  }
}

const loadFileSystemContents = async () => {
  // First, try to get cached tree data from Pinia
  if (!fullTreeData.value) {
    const cachedData = getTreeData(props.originalId)
    if (cachedData && cachedData.treeData) {
      console.log('Using cached tree data from Pinia')
      fullTreeData.value = cachedData.treeData
    }
  }

  // If we have tree data (either cached or already loaded), use it
  if (fullTreeData.value) {
    updateDisplayFromTreeData()
    return
  }

  // If no tree data available, wait a bit and try again (WorkDetails might still be fetching)
  console.log('Tree data not yet available, waiting for WorkDetails to finish fetching...')

  // Wait up to 5 seconds for tree data to become available
  let attempts = 0
  const maxAttempts = 50 // 50 * 100ms = 5 seconds

  while (attempts < maxAttempts) {
    await new Promise(resolve => setTimeout(resolve, 100)) // Wait 100ms

    const cachedData = getTreeData(props.originalId)
    if (cachedData && cachedData.treeData) {
      console.log('Tree data now available from Pinia after waiting')
      fullTreeData.value = cachedData.treeData
      updateDisplayFromTreeData()
      return
    }

    attempts++
  }

  // If still no tree data after waiting, show error
  console.error('No tree data available after waiting - WorkDetails failed to load data')
  $q.notify({
    color: 'negative',
    message: t('notification.loadFolderFailed')
  })
}

const updateDisplayFromTreeData = () => {
  // If at root, show top level items
  if (currentPath.value.length === 0) {
    if (fullTreeData.value && fullTreeData.value.children) {
      currentItems.value = fullTreeData.value.children.map(node => ({
        ...node.entry
      }))
    }
    return
  }

  // For subdirectories, find the correct node in the tree
  let currentNode = fullTreeData.value
  let found = true

  // Navigate the tree based on currentPath
  for (const pathSegment of currentPath.value) {
    if (!currentNode || !currentNode.children) {
      found = false
      break
    }

    const childNode = currentNode.children.find(node =>
      node.entry.name === pathSegment && node.entry.is_dir
    )

    if (!childNode) {
      found = false
      break
    }

    currentNode = childNode
  }

  // If found the target directory in the tree
  if (found && currentNode && currentNode.children) {
    currentItems.value = currentNode.children.map(node => ({
      ...node.entry
    }))
  } else {
    // Could not find in tree data - this shouldn't happen if tree data is complete
    console.error('Could not find path in tree data:', currentPath.value)
    currentItems.value = []
  }
}

const loadArchiveContents = async () => {
  try {
    await loadArchiveTreeContents()
  } catch (error) {
    console.error('Failed to load archive tree. Initial error:', error);

    if (error.response && error.response.data) {
      const errorMessage = String(error.response.data.message || error.response.data.error || '').toLowerCase();
      const statusCode = error.response.status;

      if (statusCode === 400) {
        // Password issues - check for various password-related error messages
        const passwordErrorKeywords = [
          'password required', 'invalid password', 'incorrect password',
          'wrong password', 'encrypted archive', 'password needed',
          'password required for encrypted archive', 'password required to extract this archive'
        ];
        if (passwordErrorKeywords.some(keyword => errorMessage.includes(keyword))) {
          console.log('Password error detected, showing password dialog. Error message:', errorMessage);
          passwordRequiredForPath.value = pathInArchive.value;
          showPasswordDialog.value = true;
          return;
        }

        // Known encoding related errors - only for ZIP files
        const encodingErrorKeywords = [
          'file names', 'character', 'encoding', 'cannot be processed',
          'invalid argument', 'charset', 'utf', 'readdir', 'archive encoding error'
        ];
        if (encodingErrorKeywords.some(keyword => errorMessage.includes(keyword)) && isZipArchive.value) {
          console.log('Specific encoding error message detected for ZIP archive, showing encoding dialog. Server message:', errorMessage);
          showEncodingDialog.value = true;
          return;
        }
      }
    }

    // For archives, we need to be more careful about the fallback
    // Only show encoding dialog if we're confident it's not a password issue AND it's a ZIP file
    if (inArchive.value) {
      const errorMessage = String(error.response?.data?.message || error.response?.data?.error || '').toLowerCase();

      // If the error message suggests it might be password-related, show password dialog
      const possiblePasswordKeywords = [
        'password', 'encrypted', 'decrypt', 'authentication', 'access denied'
      ];
      if (possiblePasswordKeywords.some(keyword => errorMessage.includes(keyword))) {
        console.log('Possible password error detected in archive, showing password dialog. Error message:', errorMessage);
        passwordRequiredForPath.value = pathInArchive.value;
        showPasswordDialog.value = true;
        return;
      }

      // Only show encoding dialog for ZIP files with other types of errors
      if (isZipArchive.value) {
        console.warn('ZIP archive tree loading failed with an unspecific error. Offering encoding selection as a potential fix. Original error:', error);
        showEncodingDialog.value = true;
        return;
      }

      // For non-ZIP archives, just show a generic error
      console.error('Non-ZIP archive loading failed. Error:', error);
      $q.notify({
        color: 'negative',
        message: t('notification.archiveLoadFailed')
      });
      return;
    }

    // If not in an archive, or if error is a network error (no error.response), or truly unhandled:
    console.error('Generic archive load failure. Details:', {
      errorObject: error,
      errorMessageFromServer: error?.response?.data?.message,
      errorStatusFromServer: error?.response?.status,
      inArchive: inArchive.value
    });
    $q.notify({
      color: 'negative',
      message: t('notification.archiveLoadFailed')
    });
  }
};

const loadArchiveTreeContents = async () => {
  // Only fetch if we don't have the tree data for the current archivePath and encoding,
  // or if a refresh is implicitly needed (e.g., password changed, though explicit refresh is better)
  // For now, if archiveEncoding or archivePassword changes, we assume a reload is needed.
  // A more robust solution might involve a dedicated refresh flag or more granular cache invalidation.

  // Clear previous tree data if archivePath or encoding has changed, forcing a reload.
  // This simple check helps, but a more direct comparison with previous state might be better.
  if (!currentArchiveTreeData.value ||
      currentArchiveTreeData.value.archivePath !== archivePath.value ||
      currentArchiveTreeData.value.encoding !== archiveEncoding.value) {
    currentArchiveTreeData.value = null // Invalidate old tree
  }

  if (!currentArchiveTreeData.value) {
    console.log('Fetching new archive tree for:', archivePath.value, 'Encoding:', archiveEncoding.value)
    const requestData = {
      storage_id: props.storageId,
      path_in_storage: archivePath.value,
      path_in_archive: '/', // Always fetch the full tree from the root of the archive
      password: archivePassword.value || undefined,
      refresh: false // Consider adding a refresh mechanism if needed
    }

    // Only include encoding for ZIP archives
    if (isZipArchive.value && archiveEncoding.value) {
      requestData.encoding = archiveEncoding.value
    }

    const response = await proxy.$api.post('/api/v1/archive/tree', requestData)
    // Store the fetched tree along with the path and encoding it was fetched for
    currentArchiveTreeData.value = {
      tree: response.data || { entry: { path: '/', is_dir: true }, children: [] },
      archivePath: archivePath.value,
      encoding: archiveEncoding.value
    }
  } else {
    console.log('Using cached archive tree for:', archivePath.value, 'Encoding:', archiveEncoding.value)
  }

  // Navigate the stored tree to display the current pathInArchive
  updateDisplayFromArchiveTreeData()
}

const updateDisplayFromArchiveTreeData = () => {
  if (!currentArchiveTreeData.value || !currentArchiveTreeData.value.tree) {
    currentItems.value = []
    return
  }

  let currentNode = currentArchiveTreeData.value.tree
  const segments = pathInArchive.value.split('/').filter(s => s.length > 0)

  for (const segment of segments) {
    if (!currentNode || !currentNode.children) {
      console.error('Could not find path in archive tree:', pathInArchive.value, segment)
      currentItems.value = []
      return
    }
    const foundNode = currentNode.children.find(node => node.entry.name === segment && node.entry.is_dir)
    if (!foundNode) {
      console.error('Could not find segment in archive tree:', pathInArchive.value, segment)
      currentItems.value = []
      return
    }
    currentNode = foundNode
  }

  currentItems.value = (currentNode.children || []).map(node => ({
    ...node.entry,
    isInArchive: true,
    archivePath: archivePath.value // Keep original archive path for actions
  }))
}

const getItemIcon = (item) => {
  const iconMap = {
    folder: 'folder',
    audio: 'play_arrow',
    image: 'photo',
    archive: 'archive',
    text: 'description',
    subtitle: 'description',
    video: 'video_file',
    unknown: 'insert_drive_file'
  }
  return iconMap[item.type] || 'insert_drive_file'
}

const getItemColor = (item) => {
  const colorMap = {
    folder: 'amber',
    audio: 'primary',
    image: 'orange',
    archive: 'purple',
    text: 'info',
    subtitle: 'info',
    video: 'green',
    unknown: 'grey'
  }
  return colorMap[item.type] || 'grey'
}

const isActiveTrack = (item) => {
  if (item.type !== 'audio') return false
  if (!currentFile?.value) return false
  const track = createAudioTrack(item)
  const currentTrack = currentPlayingFile.value
  return currentTrack.trackPath === track.trackPath &&
         currentTrack.workId === track.workId
}

const createAudioTrack = (item) => {
  // Remove the basePath prefix from item.path to get relative path within work
  let relativePath = item.path
  if (!item.isInArchive && props.basePath && relativePath.startsWith(props.basePath)) {
    // Remove basePath prefix and any leading slash ONLY for non-archive paths
    relativePath = relativePath.substring(props.basePath.length)
    relativePath = relativePath.replace(/^\/+/, '') // Remove leading slashes
  }

  // For archive audio files, we need a different approach
  if (item.isInArchive) {
    return {
      title: item.name,
      trackPath: item.path, // Path of the file *inside* the archive (e.g., "folder/track.mp3")
      workId: props.workId,
      originalId: props.originalId,
      workTitle: props.workTitle || '',
      duration: null,
      isInArchive: true,
      // Provide all necessary components for AudioElement.vue to build the URL:
      storageId: props.storageId,             // Storage ID where the archive resides
      actualArchivePath: item.archivePath,    // Full path to the archive file itself in storage
      archivePassword: archivePassword.value, // Current password for the archive
      archiveEncoding: archiveEncoding.value  // Current encoding for the archive
    }
  }

  // For regular file system files
  return {
    title: item.name,
    trackPath: relativePath, // Path relative to the work's root
    workId: props.workId,
    originalId: props.originalId,
    workTitle: props.workTitle || '',
    duration: null,
    isInArchive: false
    // No archive-specific fields needed here
  }
}

const onClickItem = async (item) => {
  if (item.is_dir) {
    if (inArchive.value) {
      // Navigate within the currently loaded archive tree
      let newPath = pathInArchive.value
      if (!newPath.endsWith('/')) {
        newPath += '/'
      }
      newPath += item.name
      pathInArchive.value = newPath
      // No API call here, just update display from existing tree data
      updateDisplayFromArchiveTreeData()
    } else {
      // 在普通文件系统中进入文件夹
      currentPath.value.push(item.name)
      // Use existing tree data if we have it
      if (fullTreeData.value) {
        updateDisplayFromTreeData()
      } else {
        await loadCurrentDirectory()
      }
    }
  } else if (item.type === 'archive') {
    // 进入归档文件
    inArchive.value = true
    archivePath.value = item.path
    pathInArchive.value = '/'

    // Load saved encoding preference only for ZIP archives
    if (item.path.toLowerCase().endsWith('.zip')) {
      archiveEncoding.value = loadSavedEncoding(item.path)
      console.log(`Using saved encoding '${archiveEncoding.value}' for ZIP archive ${item.path}`)
    } else {
      archiveEncoding.value = '' // Reset encoding for non-ZIP archives
      console.log(`Entering non-ZIP archive ${item.path}, encoding not applicable`)
    }

    // Reset password and archive tree data when entering a new archive
    archivePassword.value = ''
    currentArchiveTreeData.value = null // Invalidate archive tree data
    await loadCurrentDirectory()
  } else if (item.type === 'audio') {
    // 播放音频
    playAudio(item)
  } else if (item.type === 'image' || item.type === 'text') {
    // 打开文件
    openFile(item)
  } else {
    // 下载文件
    download(item)
  }
}

const onClickPlayButton = (item) => {
  if (isActiveTrack(item)) {
    togglePlaying?.()
  } else {
    playAudio(item)
  }
}

const playAudio = (item) => {
  if (!setQueue) return

  const track = createAudioTrack(item)

  // 获取当前文件夹中的所有音频文件
  const audioTracks = currentItems.value
    .filter(file => file.type === 'audio')
    .sort((a, b) => a.name.localeCompare(b.name))
    .map(file => createAudioTrack(file))

  // 找到当前播放文件在列表中的索引
  const currentIndex = audioTracks.findIndex(t =>
    t.title === track.title && t.trackPath === track.trackPath
  )

  // 设置队列，包含当前文件夹的所有音频文件
  console.log(`Adding ${audioTracks.length} audio files from folder to queue`)

  setQueue({
    queue: audioTracks,
    index: currentIndex >= 0 ? currentIndex : 0,
    resetPlaying: true
  })
}

const addToQueue = (item) => {
  if (!addTrackToQueue) return
  const track = createAudioTrack(item)
  addTrackToQueue(track)
}

const playNext = (item) => {
  if (!playNextTrack) return
  const track = createAudioTrack(item)
  playNextTrack(track)
}

const download = async (item) => {
  try {
    if (item.isInArchive) {
      // 从归档中下载
      let url = `/api/v1/archive/extract?storage_id=${props.storageId}&archive_path=${encodeURIComponent(item.archivePath)}&file_path=${encodeURIComponent(item.path)}`;

      // Add password parameter if available
      if (archivePassword.value) {
        url += `&password=${encodeURIComponent(archivePassword.value)}`;
      }

      // Add encoding parameter only for ZIP archives
      if (archiveEncoding.value && isZipArchive.value) {
        url += `&encoding=${encodeURIComponent(archiveEncoding.value)}`;
      }

      // Add filename for Content-Disposition header
      url += `&filename=${encodeURIComponent(item.name)}`;

      const link = document.createElement('a');
      link.href = url;
      link.target = '_blank';
      link.download = item.name;
      link.click();
    } else {
      // 从文件系统下载
      const response = await proxy.$api.get('/api/v1/fs/link', {
        params: {
          storage_id: props.storageId,
          path: item.path,
          filename: item.name // Add filename for Content-Disposition header
        }
      });
      // Extract link data from response wrapper
      const linkData = response.data || response.data;
      const link = document.createElement('a');
      link.href = linkData.url;
      link.target = '_blank';
      link.download = item.name;
      link.click();
    }
  } catch (error) {
    console.error('Download failed:', error);
    $q.notify({
      color: 'negative',
      message: t('notification.downloadFailed')
    });
  }
}

// Navigation methods for hash-based navigation
const navigateToFolder = async (folderPath) => {
  console.log('Navigating to folder:', folderPath)

  // Reset to root first
  currentPath.value = []
  inArchive.value = false
  archivePath.value = ''
  pathInArchive.value = '/'

  if (!folderPath) {
    await loadCurrentDirectory()
    return
  }

  // Split the folder path and navigate step by step
  const pathSegments = folderPath.split('/').filter(segment => segment.length > 0)

  for (const segment of pathSegments) {
    // Load current directory to get available items
    await loadCurrentDirectory()

    // Find the folder item
    const folderItem = currentItems.value.find(item =>
      item.is_dir && item.name === segment
    )

    if (folderItem) {
      if (folderItem.type === 'archive') {
        // Enter archive
        inArchive.value = true
        archivePath.value = folderItem.path
        pathInArchive.value = '/'
      } else {
        // Enter regular folder
        currentPath.value.push(segment)
      }
    } else {
      console.warn(`Folder segment not found: ${segment}`)
      break
    }
  }

  // Load the final directory
  await loadCurrentDirectory()
}

const playFileByName = async (fileName) => {
  console.log('Looking for file to play:', fileName)

  // Find the audio file in current items
  const audioFile = currentItems.value.find(item =>
    item.type === 'audio' && item.name === fileName
  )

  if (audioFile) {
    console.log('Found audio file, playing:', audioFile.name)
    playAudio(audioFile)
    return true
  } else {
    console.warn('Audio file not found:', fileName)
    return false
  }
}

// Expose methods for parent component
defineExpose({
  navigateToFolder,
  playFileByName
})

const openFile = async (item) => {
  try {
    if (item.isInArchive) {
      // 从归档中打开
      let url = `/api/v1/archive/extract?storage_id=${props.storageId}&archive_path=${encodeURIComponent(item.archivePath)}&file_path=${encodeURIComponent(item.path)}`;

      // Add password parameter if available
      if (archivePassword.value) {
        url += `&password=${encodeURIComponent(archivePassword.value)}`;
      }

      // Add encoding parameter only for ZIP archives
      if (archiveEncoding.value && isZipArchive.value) {
        url += `&encoding=${encodeURIComponent(archiveEncoding.value)}`;
      }

      // Add filename for Content-Disposition header
      url += `&filename=${encodeURIComponent(item.name)}`;

      window.open(url, '_blank');
    } else {
      // 从文件系统打开
      const response = await proxy.$api.get('/api/v1/fs/link', {
        params: {
          storage_id: props.storageId,
          path: item.path,
          filename: item.name // Add filename for Content-Disposition header
        }
      });
      // Extract link data from response wrapper
      const linkData = response.data || response.data;
      window.open(linkData.url, '_blank');
    }
  } catch (error) {
    console.error('Open file failed:', error);
    $q.notify({
      color: 'negative',
      message: t('notification.openFileFailed')
    });
  }
}

const navigateToRoot = () => {
  // Reset to root directory
  currentPath.value = []
  inArchive.value = false
  archivePath.value = ''
  pathInArchive.value = '/'

  // Use existing tree data instead of making another request
  if (fullTreeData.value) {
    updateDisplayFromTreeData()
  } else {
    loadCurrentDirectory()
  }
}

const navigateToBreadcrumb = (index) => {
  const breadcrumb = breadcrumbs.value[index]

  if (breadcrumb.isArchive) {
    if (breadcrumb.archivePath) {
      // Navigate to specific directory inside archive
      pathInArchive.value = `/${breadcrumb.archivePath}`
    } else {
      // Navigate back to archive root
      pathInArchive.value = '/'
    }
    loadCurrentDirectory()
  } else {
    // Regular filesystem navigation
    currentPath.value = breadcrumb.path
    inArchive.value = false
    archivePath.value = ''
    pathInArchive.value = '/'

    // Use existing tree data instead of making another request
    if (fullTreeData.value) {
      updateDisplayFromTreeData()
    } else {
      loadCurrentDirectory()
    }
  }
}

const formatFileSize = (bytes) => {
  if (!bytes) return ''
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString(locale.value)
}

// Function to submit password and retry loading archive
const submitPassword = async () => {
  showPasswordDialog.value = false
  currentArchiveTreeData.value = null // Invalidate archive tree data due to potential password change success
  // Retry loading the archive with the provided password
  await loadCurrentDirectory()
}

// Function to apply encoding change and reload archive
const applyEncoding = async () => {
  showEncodingDialog.value = false

  // Save the encoding preference only for ZIP archives
  if (archivePath.value && isZipArchive.value) {
    saveEncodingPreference(archivePath.value, archiveEncoding.value)
    console.log(`Saved encoding preference '${archiveEncoding.value}' for ZIP archive ${archivePath.value}`)
  }

  currentArchiveTreeData.value = null // Invalidate archive tree data due to encoding change

  // Reload the archive with the new encoding
  await loadCurrentDirectory()

  // Show notification
  $q.notify({
    color: 'positive',
    message: t('notification.encodingChanged')
  })
}

// Lifecycle
onMounted(() => {
  loadCurrentDirectory()
})
</script>
