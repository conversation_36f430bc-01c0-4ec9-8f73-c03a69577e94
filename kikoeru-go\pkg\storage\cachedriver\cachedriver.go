package cachedriver

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"path"
	"strconv"
	"time"

	"github.com/Sakura-Byte/kikoeru-go/pkg/log"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/driver"
	"github.com/Xhofe/go-cache"
)

var (
	listCache = cache.NewMemCache(cache.WithShards[[]driver.AlistObj](64))
	linkCache = cache.NewMemCache(cache.WithShards[*driver.AlistLink](16))
	getCache  = cache.NewMemCache(cache.WithShards[driver.AlistObj](32))
	listG     *Group[[]driver.AlistObj]
	linkG     *Group[*driver.AlistLink]
	getG      *Group[driver.AlistObj]
)

func init() {
	listG = NewGroup[[]driver.AlistObj]()
	linkG = NewGroup[*driver.AlistLink]()
	getG = NewGroup[driver.AlistObj]()
}

type CachedStorageDriver struct {
	rawDriver       driver.StorageDriver
	storageID       uint
	cacheExpiration time.Duration
}

func NewCachedStorageDriver(
	rawDriver driver.StorageDriver,
	storageID uint,
	expirationMinutes int,
) driver.StorageDriver { // Return the interface type
	exp := time.Duration(expirationMinutes) * time.Minute
	if expirationMinutes <= 0 {
		exp = 5 * time.Minute
		if expirationMinutes < 0 {
			exp = 10 * time.Second
		}
	}
	// No logger needed with unified logging
	return &CachedStorageDriver{
		rawDriver:       rawDriver,
		storageID:       storageID,
		cacheExpiration: exp,
	}
}

func (csd *CachedStorageDriver) generateCacheKey(itemPath string) string {
	return fmt.Sprintf("sid%d:%s", csd.storageID, path.Clean(itemPath))
}

// --- Implementing driver.StorageDriver interface ---

func (csd *CachedStorageDriver) GetStorage() *models.StorageSource {
	return csd.rawDriver.GetStorage()
}

func (csd *CachedStorageDriver) SetStorage(storage models.StorageSource) {
	csd.rawDriver.SetStorage(storage)
}

func (csd *CachedStorageDriver) GetAddition() interface{} {
	return csd.rawDriver.GetAddition()
}

func (csd *CachedStorageDriver) Init(ctx context.Context) error {
	return csd.rawDriver.Init(ctx)
}

func (csd *CachedStorageDriver) Drop(ctx context.Context) error {
	return csd.rawDriver.Drop(ctx)
}

func (csd *CachedStorageDriver) Config() driver.Config {
	return csd.rawDriver.Config()
}

func (csd *CachedStorageDriver) List(ctx context.Context, dir driver.AlistObj, args driver.AlistListArgs) ([]driver.AlistObj, error) {
	// Do not cache if cacheExpiration is not set positively, or if driver is local (unless explicitly configured otherwise)
	// For simplicity, we rely on csd.cacheExpiration being set appropriately in NewCachedStorageDriver.
	// A driver-specific config like `driver.Config().NoCache` could also be checked.
	if csd.cacheExpiration <= 0 {
		return csd.rawDriver.List(ctx, dir, args)
	}

	cacheKey := csd.generateCacheKey(dir.GetPath())
	log.Debug(ctx, "List operation", "path", dir.GetPath(), "cache_key", cacheKey, "refresh_arg", args.Refresh)

	if !args.Refresh {
		if items, ok := listCache.Get(cacheKey); ok {
			log.Debug(ctx, "List cache hit", "path", dir.GetPath(), "key", cacheKey)
			return items, nil
		}
	}

	items, err, shared := listG.Do(cacheKey, func() ([]driver.AlistObj, error) {
		log.Debug(ctx, "List cache miss or refresh, calling raw driver", "path", dir.GetPath(), "key", cacheKey)
		freshItems, fetchErr := csd.rawDriver.List(ctx, dir, args)
		if fetchErr != nil {
			return nil, fetchErr
		}
		listCache.Set(cacheKey, freshItems, cache.WithEx[[]driver.AlistObj](csd.cacheExpiration))
		log.Debug(ctx, "List result cached", "path", dir.GetPath(), "key", cacheKey, "count", len(freshItems), "expiration", csd.cacheExpiration)
		return freshItems, nil
	})

	if err != nil {
		log.Error(ctx, "Error during List operation (raw or singleflight)", "path", dir.GetPath(), "error", err, "shared_call", shared)
		return nil, err
	}
	log.Debug(ctx, "List operation completed", "path", dir.GetPath(), "count", len(items), "shared_call", shared)
	return items, nil
}

func (csd *CachedStorageDriver) Link(ctx context.Context, file driver.AlistObj, args driver.AlistLinkArgs) (*driver.AlistLink, error) {
	// Skip caching for drivers that only support proxy strategy
	driverConfig := csd.rawDriver.Config()
	if len(driverConfig.DownloadOptions) == 1 && driverConfig.DownloadOptions[0] == driver.DownloadStrategyProxy {
		return csd.rawDriver.Link(ctx, file, args)
	}

	if csd.cacheExpiration <= 0 {
		return csd.rawDriver.Link(ctx, file, args)
	}

	cacheKey := csd.generateCacheKey(file.GetPath())
	log.Debug(ctx, "Link operation", "path", file.GetPath(), "cache_key", cacheKey)

	if link, ok := linkCache.Get(cacheKey); ok {
		// Check if link has an internal expiration that has passed
		if link.Expiration != nil && *link.Expiration <= 0 { // Assuming Expiration is duration *remaining*
			log.Debug(ctx, "Link cache hit but link itself expired, re-fetching", "path", file.GetPath())
			linkCache.Del(cacheKey) // Remove stale entry
		} else {
			log.Debug(ctx, "Link cache hit", "path", file.GetPath(), "key", cacheKey)
			return link, nil
		}
	}

	link, err, shared := linkG.Do(cacheKey, func() (*driver.AlistLink, error) {
		log.Debug(ctx, "Link cache miss, calling raw driver", "path", file.GetPath(), "key", cacheKey)
		freshLink, fetchErr := csd.rawDriver.Link(ctx, file, args)
		if fetchErr != nil {
			return nil, fetchErr
		}

		effectiveExpiration := csd.cacheExpiration
		if freshLink.Expiration != nil && *freshLink.Expiration > 0 {
			// If link has its own expiration, use the shorter of the two
			linkSpecificExp := *freshLink.Expiration
			if linkSpecificExp < effectiveExpiration {
				effectiveExpiration = linkSpecificExp
			}
		}

		if effectiveExpiration > 0 {
			linkCache.Set(cacheKey, freshLink, cache.WithEx[*driver.AlistLink](effectiveExpiration))
			log.Debug(ctx, "Link result cached", "path", file.GetPath(), "key", cacheKey, "expiration", effectiveExpiration)
		}
		return freshLink, nil
	})

	if err != nil {
		log.Error(ctx, "Error during Link operation (raw or singleflight)", "path", file.GetPath(), "error", err, "shared_call", shared)
		return nil, err
	}
	log.Debug(ctx, "Link operation completed", "path", file.GetPath(), "shared_call", shared)
	return link, nil
}

// Get attempts to retrieve an object by its path or ID.
// It checks if the rawDriver implements driver.Getter.
func (csd *CachedStorageDriver) Get(ctx context.Context, pathOrID string) (driver.AlistObj, error) {
	if getter, ok := csd.rawDriver.(driver.Getter); ok {
		// Skip caching if cache is disabled
		if csd.cacheExpiration <= 0 {
			return getter.Get(ctx, pathOrID)
		}

		cacheKey := csd.generateCacheKey("get:" + pathOrID)
		log.Debug(ctx, "Get operation", "path_or_id", pathOrID, "cache_key", cacheKey)

		// Check cache first
		if cachedObj, ok := getCache.Get(cacheKey); ok {
			log.Debug(ctx, "Get cache hit", "path_or_id", pathOrID, "key", cacheKey)
			return cachedObj, nil
		}

		// Use singleflight to prevent cache stampede
		obj, err, shared := getG.Do(cacheKey, func() (driver.AlistObj, error) {
			log.Debug(ctx, "Get cache miss, calling raw driver", "path_or_id", pathOrID, "key", cacheKey)
			result, fetchErr := getter.Get(ctx, pathOrID)
			if fetchErr != nil {
				return nil, fetchErr
			}

			// Cache the result
			getCache.Set(cacheKey, result, cache.WithEx[driver.AlistObj](csd.cacheExpiration))
			log.Debug(ctx, "Get result cached", "path_or_id", pathOrID, "key", cacheKey, "expiration", csd.cacheExpiration)
			return result, nil
		})

		if err != nil {
			log.Error(ctx, "Error during Get operation (raw or singleflight)", "path_or_id", pathOrID, "error", err, "shared_call", shared)
			return nil, err
		}

		log.Debug(ctx, "Get operation completed", "path_or_id", pathOrID, "shared_call", shared)
		return obj, nil
	}

	// If rawDriver does not implement Getter, this CachedStorageDriver instance also cannot fulfill Get.
	// The List-based fallback is typically handled by a higher-level utility like GetItemRecursive.
	return nil, driver.ErrOperationNotSupported
}

// GetRootID forwards to the underlying driver if it supports IRootIDGetter
func (csd *CachedStorageDriver) GetRootID() string {
	if rootIDGetter, ok := csd.rawDriver.(driver.IRootIDGetter); ok {
		return rootIDGetter.GetRootID()
	}
	return ""
}

// GetRootPath forwards to the underlying driver if it supports IRootPathGetter
func (csd *CachedStorageDriver) GetRootPath() string {
	if rootPathGetter, ok := csd.rawDriver.(driver.IRootPathGetter); ok {
		return rootPathGetter.GetRootPath()
	}
	return ""
}

// Unwrap returns the raw driver.
func (csd *CachedStorageDriver) Unwrap() driver.StorageDriver {
	return csd.rawDriver
}

// Ensure CachedStorageDriver implements all the interfaces of the base driver
var _ driver.StorageDriver = (*CachedStorageDriver)(nil)
var _ driver.IRootIDGetter = (*CachedStorageDriver)(nil)
var _ driver.IRootPathGetter = (*CachedStorageDriver)(nil)
var _ driver.Wrapper = (*CachedStorageDriver)(nil)

// Proxy method for streaming files
// This implementation:
// 1. Uses the underlying driver's Proxy method if available
// 2. Otherwise implements a generic proxy using Link()
func (csd *CachedStorageDriver) Proxy(ctx context.Context, file driver.AlistObj, httpRange string) (io.ReadCloser, string, int64, int64, int, error) {
	// First check if the underlying driver implements Proxy
	streamer, ok := csd.rawDriver.(interface {
		Proxy(ctx context.Context, file driver.AlistObj, httpRange string) (io.ReadCloser, string, int64, int64, int, error)
	})

	if ok {
		// If driver has its own implementation, use it
		log.Debug(ctx, "Using driver's native Proxy implementation",
			"driver_type", csd.rawDriver.Config().Name,
			"file_path", file.GetPath())
		return streamer.Proxy(ctx, file, httpRange)
	}

	// If driver doesn't implement Proxy, implement a general proxy using Link()
	log.Debug(ctx, "Driver does not implement Proxy, using general implementation with Link()",
		"driver_type", csd.rawDriver.Config().Name,
		"file_path", file.GetPath())

	// Cannot proxy directories
	if file.IsDir() {
		return nil, "", 0, 0, http.StatusBadRequest, driver.ErrNotAFile
	}

	// Get link from the driver
	link, err := csd.Link(ctx, file, driver.AlistLinkArgs{})
	if err != nil {
		log.Error(ctx, "Failed to get link for proxy", "error", err)
		return nil, "", 0, 0, http.StatusInternalServerError, err
	}

	// Create HTTP request with the link
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, link.URL, nil)
	if err != nil {
		log.Error(ctx, "Failed to create HTTP request", "error", err)
		return nil, "", 0, 0, http.StatusInternalServerError, err
	}

	// Add headers from the link
	for key, values := range link.Header {
		for _, value := range values {
			req.Header.Add(key, value)
		}
	}

	// Add range header if provided
	if httpRange != "" {
		req.Header.Add("Range", httpRange)
	}

	// Execute the request
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		log.Error(ctx, "Failed HTTP request", "error", err)
		return nil, "", 0, 0, http.StatusInternalServerError, err
	}

	// Check for errors
	if resp.StatusCode >= 400 {
		resp.Body.Close()
		return nil, "", 0, 0, resp.StatusCode, fmt.Errorf("HTTP request failed with status %d", resp.StatusCode)
	}

	// Get content type and length
	contentType := resp.Header.Get("Content-Type")
	if contentType == "" {
		contentType = "application/octet-stream"
	}

	// Parse content-length and range response
	originalSize := file.GetSize()
	contentLength := originalSize

	// If partial content, update the content length
	if resp.StatusCode == http.StatusPartialContent {
		if cl := resp.Header.Get("Content-Length"); cl != "" {
			if parsedLength, err := strconv.ParseInt(cl, 10, 64); err == nil {
				contentLength = parsedLength
			}
		}
	}

	return resp.Body, contentType, originalSize, contentLength, resp.StatusCode, nil
}

// Explicitly check if CachedStorageDriver implements Getter if rawDriver does.
// This is more of a conceptual check; the Get method above handles it dynamically.
// var _ driver.Getter = (*CachedStorageDriver)(nil)
