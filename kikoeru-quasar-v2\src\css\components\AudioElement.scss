/* AudioElement.scss - Custom audio player styles */

.custom-audio-player {
  .custom-progress-container {
    width: 100%;
    height: 8px;
    cursor: pointer;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    position: relative;
    margin: 12px 0;
    border: 1px solid rgba(0, 0, 0, 0.3);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  .progress-track {
    width: 100%;
    height: 100%;
    position: relative;
    border-radius: 4px;
    overflow: hidden;
  }

  .progress-buffered {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 4px;
    transition: width 0.3s ease;
    border-right: 1px solid rgba(255, 255, 255, 0.9);
  }

  .progress-played {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: linear-gradient(90deg, #1976d2 0%, #2196f3 100%);
    border-radius: 4px;
    transition: width 0.1s ease;
    z-index: 2;
    box-shadow: 0 0 4px rgba(25, 118, 210, 0.4);
    border-right: 2px solid rgba(25, 118, 210, 0.8);
  }

  .custom-progress-container:hover {
    height: 10px;
    background: rgba(0, 0, 0, 0.25);
    border-color: rgba(0, 0, 0, 0.4);
  }

  .custom-progress-container:hover .progress-played {
    background: linear-gradient(90deg, #1565c0 0%, #1e88e5 100%);
    box-shadow: 0 0 6px rgba(21, 101, 192, 0.6);
  }

  .custom-progress-container:hover .progress-buffered {
    background: rgba(255, 255, 255, 0.8);
  }
} 