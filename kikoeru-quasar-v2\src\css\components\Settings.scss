/* Settings.scss - Styles for Settings component */

.section-title {
  padding-bottom: 8px;
  margin-bottom: 16px;
  font-weight: 500;
}

.settings-card {
  border-radius: 8px;
  margin-bottom: 16px;
  overflow: hidden;
}

.settings-section {
  margin-bottom: 16px;
}

.settings-list {
  // No specific background, items will sit on the page background.
  padding: 0;
}

.setting-item {
  min-height: 48px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12); // Default separator line for light mode
}

// Light mode specific styles
body:not(.body--dark) {
  .setting-item {
    border-bottom-color: rgba(0, 0, 0, 0.12);
    
    &:last-child {
      border-bottom: none;
    }
  }
  
  .settings-card {
    background-color: white;
    border: 1px solid rgba(0, 0, 0, 0.12);
  }
  
  .section-title {
    color: #333;
  }
  
  .guest-banner {
    background-color: $blue-1;
    color: $blue-8;
  }
  
  // Transparent list styling for light mode
  .rounded-borders.q-list.q-list--bordered:not(.q-list--dark) {
    border: 1px solid rgba(0, 0, 0, 0.12);
    background: white;
    
    .q-separator {
      background-color: rgba(0, 0, 0, 0.12);
    }
  }
}

.time-select {
  // Targets the q-select component for seek time
  .q-field__control {
    background-color: transparent !important; // Ensure transparent background
    padding: 0; // Remove extra padding around the native element
  }
  .q-field__native, .q-field__input {
    padding: 0; // Align text properly
    text-align: right; // Ensure text is right aligned within its container
  }
  .text-right.full-width {
    min-width: 80px; // Ensure consistent width
  }
}

// Settings section title styling
.text-weight-medium.text-center.flex {
  margin: 16px 16px 8px 16px;
  font-weight: 500;
  display: flex;
  flex-wrap: wrap;
  text-align: center;
}

// Common styling for both light and dark mode lists
.rounded-borders.q-list.q-list--bordered {
  border-radius: 4px;
  margin: 8px 16px 16px 16px;
  
  .q-item {
    background-color: transparent;
  }
}

// Dark mode list specific styling
.rounded-borders.q-list.q-list--bordered.q-list--dark:not(.bg-black) {
  border: 1px solid hsla(0,0%,100%,0.28);
  background: transparent !important;
  
  .q-item--dark {
    background-color: transparent;
  }
  
  .q-separator--dark {
    background-color: hsla(0,0%,100%,0.28);
  }
}

// Dark mode specific styles
.body--dark {
  .settings-card {
    background-color: #1d1d1d;
    border: 1px solid rgba(255, 255, 255, 0.12);
  }
  
  .section-title {
    color: #f0f0f0;
  }

  .setting-item {
    color: #e0e0e0;
    border-bottom-color: rgba(255, 255, 255, 0.12); // Darker separator for dark mode
    
    &:last-child {
      border-bottom: none;
    }
  }
  
  .guest-banner {
    background-color: #2d2d2d;
  }

  .settings-section {
    background-color: transparent;
  }

  .settings-list {
    background-color: transparent;
  }
  
  // Add styling for transparent list items in dark mode
  .q-list--dark:not(.bg-black) {
    background-color: transparent !important;
    
    .q-item--dark {
      background-color: transparent;
    }
    
    .q-separator--dark {
      background-color: rgba(255, 255, 255, 0.12);
    }
  }
}