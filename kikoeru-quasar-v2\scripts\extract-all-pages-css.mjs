#!/usr/bin/env node

/**
 * CSS Extraction Script for all Pages
 *
 * This script extracts CSS from all Vue components in the pages directory
 * and moves them to dedicated SCSS files.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { execSync } from 'child_process';

// Get current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const PAGES_DIR = path.join(__dirname, '..', 'src', 'pages');

// Function to process files in a directory recursively
function processDirectory(dir) {
  // Read directory contents
  const entries = fs.readdirSync(dir, { withFileTypes: true });

  // Process each entry
  for (const entry of entries) {
    const fullPath = path.join(dir, entry.name);

    if (entry.isDirectory()) {
      // Recursively process subdirectories
      processDirectory(fullPath);
    } else if (entry.isFile() && entry.name.endsWith('.vue')) {
      // Process Vue files
      const componentName = entry.name.replace('.vue', '');
      console.log(`\nProcessing ${componentName}...`);

      // Check if the component has a style tag
      const content = fs.readFileSync(fullPath, 'utf8');
      if (!content.includes('<style')) {
        console.log(`No style tag found in ${componentName}, skipping.`);
        continue;
      }

      try {
        // Execute the extract-css script for this component
        execSync(`node ${path.join(__dirname, 'extract-css.mjs')} ${componentName}`, {
          stdio: 'inherit',
          cwd: path.join(__dirname, '..')
        });
        console.log(`Successfully processed ${componentName}`);
      } catch (error) {
        console.error(`Error processing ${componentName}: ${error.message}`);
      }
    }
  }
}

console.log('Starting CSS extraction for all page components...');
processDirectory(PAGES_DIR);
console.log('\nCSS extraction completed for all pages!');
