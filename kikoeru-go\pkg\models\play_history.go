package models

import (
	"time"
)

// PlayHistory records a user's playback progress for a work or track.
type PlayHistory struct {
	ID                      uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	CreatedAt               time.Time `json:"created_at"`
	UpdatedAt               time.Time `json:"updated_at"` // Effectively LastPlayedAt
	UserID                  string    `gorm:"type:varchar(36);index;not null" json:"user_id"`
	WorkID                  uint      `gorm:"index;not null" json:"work_id"`
	TrackPath               string    `gorm:"type:varchar(1024);index" json:"track_path,omitempty"` // Relative path to the track within the work, if applicable
	PlaybackPositionSeconds int       `gorm:"default:0" json:"playback_position_seconds"`
	ProgressPercentage      float64   `gorm:"type:float;default:0" json:"progress_percentage"` // 0.0 to 1.0
	IsFinished              bool      `gorm:"default:false" json:"is_finished"`

	User *User `gorm:"foreignKey:UserID" json:"-"`              // User relation
	Work *Work `gorm:"foreignKey:WorkID" json:"work,omitempty"` // Work relation, can be preloaded
}

func (PlayHistory) TableName() string {
	return "t_play_history"
}

// Unique constraint on (UserID, WorkID, TrackPath) to ensure one record per user per track/work.
// GORM can handle this with composite primary keys or unique indexes in migrations.
// For simplicity, the Upsert logic in the repository will handle this.
