import { computed } from 'vue'
import { useUserStore } from '../stores/user'
import { api } from '../boot/axios'

export function useAuth() {
  const userStore = useUserStore()

  const isLoggedIn = computed(() => userStore.isLoggedIn)
  const isAdmin = computed(() => userStore.isAdmin)
  const isEmailVerified = computed(() => userStore.isEmailVerified)
  const hasEmail = computed(() => userStore.hasEmail)
  const user = computed(() => userStore.user)
  const username = computed(() => userStore.username)
  const showLoginDialog = computed(() => userStore.showLoginDialog)

  const login = async (credentials, axiosInstance = api) => {
    return userStore.login({ credentials, axios: axiosInstance })
  }

  const logout = (options = {}) => {
    userStore.logout(options)
  }

  const register = async (userData, axiosInstance = api) => {
    return userStore.register({ userData, axios: axiosInstance })
  }

  const fetchCurrentUser = async (axiosInstance = api) => {
    return userStore.fetchCurrentUser(axiosInstance)
  }

  const changePassword = async (passwordData, axiosInstance = api) => {
    return userStore.changePassword({ passwordData, axios: axiosInstance })
  }

  const updateUsername = (username) => {
    userStore.updateUsername(username)
  }

  const setShowLoginDialog = (show) => {
    userStore.setShowLoginDialog(show)
  }

  const hideLoginDialog = () => {
    userStore.setShowLoginDialog(false)
  }

  return {
    // Computed properties
    isLoggedIn,
    isAdmin,
    isEmailVerified,
    hasEmail,
    user,
    username,
    showLoginDialog,

    // Actions
    login,
    logout,
    register,
    fetchCurrentUser,
    changePassword,
    updateUsername,
    setShowLoginDialog,
    hideLoginDialog
  }
}
