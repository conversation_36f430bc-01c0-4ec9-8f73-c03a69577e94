package database

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// UserRating 结构体，对应t_user_ratings表
type UserRating struct {
	UserID    uuid.UUID `gorm:"primaryKey;column:user_id;type:uuid"`
	WorkID    uuid.UUID `gorm:"primaryKey;column:work_id;type:uuid"`
	Rating    int       `gorm:"column:rating"`
	CreatedAt time.Time `gorm:"column:created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at"`
}

func (UserRating) TableName() string {
	return "t_user_ratings"
}

// UserRatingRepository 定义用户评分仓库接口
type UserRatingRepository interface {
	GetUserRating(ctx context.Context, userID uuid.UUID, workID uuid.UUID) (*int, error)
	SetUserRating(ctx context.Context, userID uuid.UUID, workID uuid.UUID, rating int) error
	DeleteUserRating(ctx context.Context, userID uuid.UUID, workID uuid.UUID) error
	GetUserRatingsForWorks(ctx context.Context, userID uuid.UUID, workIDs []uuid.UUID) (map[uuid.UUID]int, error)
}

type userRatingRepository struct {
	db *gorm.DB
}

// NewUserRatingRepository 创建新的用户评分仓库实现
func NewUserRatingRepository(db *gorm.DB) UserRatingRepository {
	return &userRatingRepository{db: db}
}

// GetUserRating 获取用户对作品的评分
func (r *userRatingRepository) GetUserRating(ctx context.Context, userID uuid.UUID, workID uuid.UUID) (*int, error) {
	var rating UserRating
	result := r.db.WithContext(ctx).Where("user_id = ? AND work_id = ?", userID, workID).First(&rating)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil // 返回nil表示未评分
		}
		return nil, fmt.Errorf("failed to get user rating: %w", result.Error)
	}
	return &rating.Rating, nil
}

// SetUserRating 设置用户对作品的评分
func (r *userRatingRepository) SetUserRating(ctx context.Context, userID uuid.UUID, workID uuid.UUID, rating int) error {
	if rating < 1 || rating > 5 {
		return errors.New("rating must be between 1 and 5")
	}

	var count int64
	r.db.WithContext(ctx).Model(&UserRating{}).Where("user_id = ? AND work_id = ?", userID, workID).Count(&count)

	if count > 0 {
		// 已存在评分，更新
		result := r.db.WithContext(ctx).Model(&UserRating{}).
			Where("user_id = ? AND work_id = ?", userID, workID).
			Update("rating", rating)
		if result.Error != nil {
			return fmt.Errorf("failed to update user rating: %w", result.Error)
		}
	} else {
		// 不存在评分，创建
		userRating := UserRating{
			UserID: userID,
			WorkID: workID,
			Rating: rating,
		}
		result := r.db.WithContext(ctx).Create(&userRating)
		if result.Error != nil {
			return fmt.Errorf("failed to create user rating: %w", result.Error)
		}
	}

	return nil
}

// DeleteUserRating 删除用户对作品的评分
func (r *userRatingRepository) DeleteUserRating(ctx context.Context, userID uuid.UUID, workID uuid.UUID) error {
	result := r.db.WithContext(ctx).Where("user_id = ? AND work_id = ?", userID, workID).Delete(&UserRating{})
	if result.Error != nil {
		return fmt.Errorf("failed to delete user rating: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return apperrors.ErrReviewNotFound // 使用最相关的已定义错误类型
	}
	return nil
}

// GetUserRatingsForWorks 批量获取用户对多个作品的评分
func (r *userRatingRepository) GetUserRatingsForWorks(ctx context.Context, userID uuid.UUID, workIDs []uuid.UUID) (map[uuid.UUID]int, error) {
	if len(workIDs) == 0 {
		return make(map[uuid.UUID]int), nil
	}

	var ratings []UserRating
	result := r.db.WithContext(ctx).
		Where("user_id = ? AND work_id IN ?", userID, workIDs).
		Find(&ratings)

	if result.Error != nil {
		return nil, fmt.Errorf("failed to get user ratings for works: %w", result.Error)
	}

	ratingMap := make(map[uuid.UUID]int, len(ratings))
	for _, rating := range ratings {
		ratingMap[rating.WorkID] = rating.Rating
	}

	return ratingMap, nil
}
