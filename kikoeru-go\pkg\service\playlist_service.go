package service

import (
	"context"
	"database/sql"
	"errors"
	"fmt"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
	"github.com/Sakura-Byte/kikoeru-go/pkg/log"
	"github.com/Sakura-Byte/kikoeru-go/pkg/config"
	circle_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/circle" // Added circle_dto import
	dto_playlist "github.com/Sakura-Byte/kikoeru-go/pkg/dto/playlist"
	tag_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/tag" // Added tag_dto import
	va_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/va"   // Added va_dto import
	dto_work "github.com/Sakura-Byte/kikoeru-go/pkg/dto/work"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"github.com/Sakura-Byte/kikoeru-go/pkg/ports"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/database"
)

// PlaylistService interface has been moved to github.com/Sakura-Byte/kikoeru-go/pkg/ports

type playlistService struct {
	playlistRepo database.PlaylistRepository
	workRepo     database.WorkRepository
	mediaService ports.MediaService
	appConfig    *config.AppConfig
}

// Ensure playlistService implements ports.PlaylistService
var _ ports.PlaylistService = (*playlistService)(nil)

func NewPlaylistService(
	playlistRepo database.PlaylistRepository,
	workRepo database.WorkRepository,
	mediaService ports.MediaService,
	appConfig *config.AppConfig,
) ports.PlaylistService { // Changed return type to ports.PlaylistService
	return &playlistService{
		playlistRepo: playlistRepo,
		workRepo:     workRepo,
		mediaService: mediaService,
		appConfig:    appConfig,
	}
}

func (s *playlistService) convertModelWorkToWorkDTO(ctx context.Context, workModel *models.Work) *dto_work.WorkDTO {
	if workModel == nil {
		return nil
	}
	dto := &dto_work.WorkDTO{
		ID:              workModel.ID,
		CreatedAt:       workModel.CreatedAt,
		UpdatedAt:       workModel.UpdatedAt,
		OriginalID:      workModel.OriginalID,
		WorkType:        workModel.WorkType,
		Title:           workModel.Title,
		AgeRating:       workModel.AgeRating,
		ReleaseDate:     workModel.ReleaseDate,
		RateCount:       workModel.RateCount,
		RateAverage2DP:  workModel.RateAverage2DP,
		Rank:            workModel.Rank,
		LyricStatus:     workModel.LyricStatus,
		Language:        workModel.Language,
		Duration:        workModel.Duration,
		DlCount:         workModel.DlCount,
		Price:           workModel.Price,
		ReviewCount:     workModel.ReviewCount,
		RateCountDetail: workModel.RateCountDetail,
		StorageID:       workModel.StorageID,
		PathInStorage:   workModel.PathInStorage,
		Circle:          circle_dto.MapCircleModelToDTO(workModel.Circle),
		Tags:            tag_dto.MapTagModelsToDTOs(workModel.Tags),
		VAs:             va_dto.MapVAModelsToDTOs(workModel.VAs),
	}

	if workModel.OriginalID != nil && *workModel.OriginalID != "" {
		originalIDStr := *workModel.OriginalID
		baseAPIPath := fmt.Sprintf("/api/v1/cover/%s", originalIDStr)
		mainURL := fmt.Sprintf("%s?type=main", baseAPIPath)
		samURL := fmt.Sprintf("%s?type=sam", baseAPIPath)
		dto.ImageMainURL = &mainURL
		dto.ImageSamURL = &samURL
	}
	return dto
}

func (s *playlistService) convertModelPlaylistToPlaylistDTO(playlistModel *models.Playlist) *dto_playlist.PlaylistDTO {
	if playlistModel == nil {
		return nil
	}
	return &dto_playlist.PlaylistDTO{
		ID:          playlistModel.ID,
		CreatedAt:   playlistModel.CreatedAt,
		UpdatedAt:   playlistModel.UpdatedAt,
		UserID:      playlistModel.UserID,
		Name:        playlistModel.Name,
		Description: playlistModel.Description,
		Visibility:  playlistModel.Visibility,
		ItemCount:   playlistModel.ItemCount,
	}
}

func (s *playlistService) CreatePlaylist(ctx context.Context, userID string, req dto_playlist.CreatePlaylistRequest) (*dto_playlist.PlaylistDTO, error) {
	if userID == "" {
		return nil, errors.New("userID cannot be empty")
	}
	visibility := req.Visibility
	if visibility == "" {
		visibility = models.PlaylistVisibilityPrivate
	}
	if visibility != models.PlaylistVisibilityPrivate && visibility != models.PlaylistVisibilityUnlisted && visibility != models.PlaylistVisibilityPublic {
		return nil, fmt.Errorf("invalid visibility value: %s", visibility)
	}

	playlistModel := &models.Playlist{
		UserID:      userID,
		Name:        req.Name,
		Description: req.Description,
		Visibility:  visibility,
	}
	err := s.playlistRepo.Create(ctx, playlistModel)
	if err != nil {
		log.Error(ctx, "Failed to create playlist in repository", "userID", userID, "name", req.Name, "error", err)
		return nil, fmt.Errorf("could not create playlist: %w", err)
	}
	log.Info(ctx, "Playlist created successfully", "playlist_id", playlistModel.ID, "userID", userID, "name", playlistModel.Name)

	// Calculate ItemCount before converting
	// For a new playlist, item count is 0
	playlistModel.ItemCount = 0
	return s.convertModelPlaylistToPlaylistDTO(playlistModel), nil
}

func (s *playlistService) GetPlaylistDetails(ctx context.Context, userID string, playlistID uint) (*dto_playlist.PlaylistWithItemsDTO, error) {
	playlistModel, err := s.playlistRepo.GetByID(ctx, playlistID, userID)
	if err != nil {
		if errors.Is(err, apperrors.ErrPlaylistNotFound) {
			log.Warn(ctx, "Playlist not found or not accessible by user", "playlist_id", playlistID, "userID", userID, "error", err)
			return nil, apperrors.ErrPlaylistNotFound
		}
		log.Error(ctx, "Failed to get playlist details from repository", "playlist_id", playlistID, "userID", userID, "error", err)
		return nil, fmt.Errorf("could not retrieve playlist details: %w", err)
	}

	if playlistModel.Visibility == models.PlaylistVisibilityPrivate && playlistModel.UserID != userID {
		log.Warn(ctx, "User attempted to access private playlist they do not own", "playlist_id", playlistID, "ownerUserID", playlistModel.UserID, "requesterUserID", userID)
		return nil, apperrors.ErrPlaylistActionForbidden
	}

	modelItems, err := s.playlistRepo.ListItemsByPlaylistID(ctx, playlistID, userID)
	if err != nil {
		log.Error(ctx, "Failed to list items for playlist", "playlist_id", playlistID, "error", err)
		return nil, fmt.Errorf("could not list playlist items: %w", err)
	}

	playlistModel.ItemCount = len(modelItems)

	dtoItems := make([]*dto_playlist.PlaylistItemDTO, len(modelItems))
	for i, modelItem := range modelItems {
		dtoItems[i] = &dto_playlist.PlaylistItemDTO{
			ID:         modelItem.ID,
			PlaylistID: modelItem.PlaylistID,
			WorkID:     modelItem.WorkID,
			TrackPath:  modelItem.TrackPath,
			Order:      modelItem.Order,
			Work:       s.convertModelWorkToWorkDTO(ctx, modelItem.Work),
		}
	}

	playlistDTO := s.convertModelPlaylistToPlaylistDTO(playlistModel)

	return &dto_playlist.PlaylistWithItemsDTO{PlaylistDTO: *playlistDTO, Items: dtoItems}, nil
}

func (s *playlistService) ListUserPlaylists(ctx context.Context, userID string, page int, pageSize int) ([]*dto_playlist.PlaylistDTO, int64, error) {
	playlistModels, total, err := s.playlistRepo.ListByUserID(ctx, userID, page, pageSize)
	if err != nil {
		log.Error(ctx, "Failed to list user playlists from repository", "userID", userID, "error", err)
		return nil, 0, fmt.Errorf("could not retrieve user playlists: %w", err)
	}

	playlistDTOs := make([]*dto_playlist.PlaylistDTO, len(playlistModels))
	for i, p := range playlistModels {
		items, errList := s.playlistRepo.ListItemsByPlaylistID(ctx, p.ID, userID)
		if errList == nil {
			p.ItemCount = len(items)
		} else {
			log.Warn(ctx, "Failed to get item count for playlist in list", "playlist_id", p.ID, "error", errList)
			p.ItemCount = 0
		}
		playlistDTOs[i] = s.convertModelPlaylistToPlaylistDTO(p)
	}
	return playlistDTOs, total, nil
}

func (s *playlistService) UpdatePlaylistInfo(ctx context.Context, userID string, playlistID uint, req dto_playlist.UpdatePlaylistInfoRequest) (*dto_playlist.PlaylistDTO, error) {
	playlistModel, err := s.playlistRepo.GetByID(ctx, playlistID, userID)
	if err != nil {
		if errors.Is(err, apperrors.ErrPlaylistNotFound) {
			return nil, apperrors.ErrPlaylistNotFound
		}
		log.Error(ctx, "Playlist not found for update", "playlist_id", playlistID, "userID", userID, "error", err)
		return nil, fmt.Errorf("playlist not found for update: %w", err)
	}

	updated := false
	if req.Name != nil && *req.Name != "" && playlistModel.Name != *req.Name {
		playlistModel.Name = *req.Name
		updated = true
	}
	if req.Description != nil && playlistModel.Description != *req.Description {
		playlistModel.Description = *req.Description
		updated = true
	}
	if req.Visibility != nil && *req.Visibility != "" {
		vis := *req.Visibility
		if vis != models.PlaylistVisibilityPrivate && vis != models.PlaylistVisibilityUnlisted && vis != models.PlaylistVisibilityPublic {
			return nil, fmt.Errorf("invalid visibility value: %s", vis)
		}
		if playlistModel.Visibility != vis {
			playlistModel.Visibility = vis
			updated = true
		}
	}

	if !updated {
		log.Info(ctx, "No changes detected for playlist update", "playlist_id", playlistID)
		// Recalculate ItemCount before returning
		items, _ := s.playlistRepo.ListItemsByPlaylistID(ctx, playlistID, userID)
		playlistModel.ItemCount = len(items)
		return s.convertModelPlaylistToPlaylistDTO(playlistModel), nil
	}

	err = s.playlistRepo.Update(ctx, playlistModel, userID)
	if err != nil {
		log.Error(ctx, "Failed to update playlist in repository", "playlist_id", playlistID, "error", err)
		return nil, fmt.Errorf("could not update playlist: %w", err)
	}
	log.Info(ctx, "Playlist updated successfully", "playlist_id", playlistModel.ID, "userID", userID)

	// Recalculate ItemCount after update
	items, _ := s.playlistRepo.ListItemsByPlaylistID(ctx, playlistID, userID)
	playlistModel.ItemCount = len(items)
	return s.convertModelPlaylistToPlaylistDTO(playlistModel), nil
}

func (s *playlistService) DeletePlaylist(ctx context.Context, userID string, playlistID uint) error {
	_, err := s.playlistRepo.GetByID(ctx, playlistID, userID)
	if err != nil {
		if errors.Is(err, apperrors.ErrPlaylistNotFound) {
			return apperrors.ErrPlaylistNotFound
		}
		log.Error(ctx, "Playlist not found for deletion", "playlist_id", playlistID, "userID", userID, "error", err)
		return fmt.Errorf("playlist not found or not owned by user: %w", err)
	}

	err = s.playlistRepo.Delete(ctx, playlistID, userID)
	if err != nil {
		log.Error(ctx, "Failed to delete playlist from repository", "playlist_id", playlistID, "error", err)
		return fmt.Errorf("could not delete playlist: %w", err)
	}
	log.Info(ctx, "Playlist deleted successfully", "playlist_id", playlistID, "userID", userID)
	return nil
}

func (s *playlistService) AddItemToPlaylist(ctx context.Context, userID string, playlistID uint, req dto_playlist.AddItemToPlaylistRequest) (*dto_playlist.PlaylistItemDTO, error) {
	_, err := s.playlistRepo.GetByID(ctx, playlistID, userID)
	if err != nil {
		if errors.Is(err, apperrors.ErrPlaylistNotFound) {
			return nil, apperrors.ErrPlaylistNotFound
		}
		log.Error(ctx, "Playlist not found for adding item", "playlist_id", playlistID, "userID", userID, "error", err)
		return nil, apperrors.ErrPlaylistActionForbidden
	}

	workModel, err := s.workRepo.GetByID(ctx, req.WorkID)
	if err != nil {
		if errors.Is(err, apperrors.ErrWorkNotFound) {
			return nil, apperrors.ErrWorkNotFound
		}
		log.Error(ctx, "Work not found for adding to playlist", "work_id", req.WorkID, "error", err)
		return nil, fmt.Errorf("work to add not found: %w", err)
	}

	if req.TrackPath != "" {
		log.Info(ctx, "TrackPath provided for playlist item, will be stored.", "work_id", workModel.ID, "track_path", req.TrackPath, "playlist_id", playlistID)
	}

	maxOrder, err := s.playlistRepo.GetMaxOrderItem(ctx, playlistID)
	if err != nil {
		log.Error(ctx, "Failed to get max order for playlist item", "playlist_id", playlistID, "error", err)
		return nil, fmt.Errorf("could not determine order for new item: %w", err)
	}

	modelItem := &models.PlaylistItem{
		PlaylistID: playlistID,
		WorkID:     req.WorkID,
		TrackPath:  sql.NullString{String: req.TrackPath, Valid: req.TrackPath != ""},
		Order:      maxOrder + 1,
	}

	err = s.playlistRepo.AddItem(ctx, modelItem)
	if err != nil {
		log.Error(ctx, "Failed to add item to playlist in repository", "playlist_id", playlistID, "work_id", req.WorkID, "error", err)
		return nil, fmt.Errorf("could not add item to playlist: %w", err)
	}
	log.Info(ctx, "Item added to playlist", "item_id", modelItem.ID, "playlist_id", playlistID, "work_id", req.WorkID)

	dtoItem := &dto_playlist.PlaylistItemDTO{
		ID:         modelItem.ID,
		PlaylistID: modelItem.PlaylistID,
		WorkID:     modelItem.WorkID,
		TrackPath:  modelItem.TrackPath,
		Order:      modelItem.Order,
		Work:       s.convertModelWorkToWorkDTO(ctx, workModel),
	}
	return dtoItem, nil
}

func (s *playlistService) RemoveItemFromPlaylist(ctx context.Context, userID string, playlistID uint, itemID uint) error {
	err := s.playlistRepo.RemoveItem(ctx, itemID, userID)
	if err != nil {
		if errors.Is(err, apperrors.ErrPlaylistItemNotFound) {
			return apperrors.ErrPlaylistItemNotFound
		}
		if errors.Is(err, apperrors.ErrNotPlaylistOwner) {
			return apperrors.ErrPlaylistActionForbidden
		}
		log.Error(ctx, "Failed to remove item from playlist", "item_id", itemID, "userID", userID, "error", err)
		return fmt.Errorf("could not remove item from playlist: %w", err)
	}
	log.Info(ctx, "Item removed from playlist", "item_id", itemID, "playlist_id", playlistID, "userID", userID)
	return nil
}

func (s *playlistService) UpdatePlaylistItemOrders(ctx context.Context, userID string, playlistID uint, itemOrders map[uint]int) error {
	_, err := s.playlistRepo.GetByID(ctx, playlistID, userID)
	if err != nil {
		if errors.Is(err, apperrors.ErrPlaylistNotFound) {
			return apperrors.ErrPlaylistNotFound
		}
		log.Error(ctx, "Playlist not found for reordering items", "playlist_id", playlistID, "userID", userID, "error", err)
		return apperrors.ErrPlaylistActionForbidden
	}

	err = s.playlistRepo.UpdateItemOrders(ctx, playlistID, itemOrders)
	if err != nil {
		log.Error(ctx, "Failed to update item orders in repository", "playlist_id", playlistID, "error", err)
		return fmt.Errorf("could not update item orders: %w", err)
	}
	log.Info(ctx, "Playlist item orders updated", "playlist_id", playlistID, "userID", userID)
	return nil
}
