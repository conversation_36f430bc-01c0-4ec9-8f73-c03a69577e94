package model

import (
	"time"
)

// Subtitle represents a subtitle in the system
type Subtitle struct {
	ID           uint   `gorm:"primaryKey"`
	UUID         string `gorm:"uniqueIndex;not null"`
	OriginalID   string `gorm:"index;not null"`
	WorkID       uint   `gorm:"index;not null"`
	Format       string `gorm:"not null"` // srt, lrc, ass, vtt
	UploaderID   string `gorm:"index;not null"`
	SubtitleType string `gorm:"index;not null"` // user_submitted, ai_generated, admin_uploaded
	Description  string
	IsPublic     bool `gorm:"index;default:true"`
	UpVotes      int  `gorm:"default:0"`
	DownVotes    int  `gorm:"default:0"`
	IsInArchive  bool `gorm:"default:false"`
	ArchivePath  string
	CreatedAt    time.Time `gorm:"autoCreateTime"`
	UpdatedAt    time.Time `gorm:"autoUpdateTime"`

	// Associations
	// Uploader   User    `gorm:"foreignKey:UploaderID"`
	// Work       Work    `gorm:"foreignKey:WorkID"`
	// Votes     []SubtitleVote `gorm:"foreignKey:SubtitleID"`
	// Tracks    []SubtitleTrack `gorm:"foreignKey:SubtitleID"`
}

// TableName returns the table name for the subtitle model
func (Subtitle) TableName() string {
	return "t_subtitle"
}

// SubtitleVote represents a user's vote on a subtitle
type SubtitleVote struct {
	ID         uint      `gorm:"primaryKey"`
	SubtitleID uint      `gorm:"index;not null"`
	UserID     string    `gorm:"index;not null"`
	Vote       int       `gorm:"not null"` // +1 for upvote, -1 for downvote
	CreatedAt  time.Time `gorm:"autoCreateTime"`
	UpdatedAt  time.Time `gorm:"autoUpdateTime"`

	// Associations
	// Subtitle   Subtitle `gorm:"foreignKey:SubtitleID"`
	// User       User    `gorm:"foreignKey:UserID"`
}

// TableName returns the table name for the subtitle vote model
func (SubtitleVote) TableName() string {
	return "t_subtitle_vote"
}
