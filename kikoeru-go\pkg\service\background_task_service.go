package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sync"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
	"github.com/Sakura-Byte/kikoeru-go/pkg/log"
	"github.com/Sakura-Byte/kikoeru-go/pkg/auth"
	task_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/task" // Added for task payloads
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"github.com/Sakura-Byte/kikoeru-go/pkg/ports"
	"github.com/Sakura-Byte/kikoeru-go/pkg/scanner"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/database"
)

// ScanStatus and ScannerStatusResponse types have been moved to github.com/Sakura-Byte/kikoeru-go/pkg/ports

// BackgroundTaskService interface has been moved to github.com/Sakura-Byte/kikoeru-go/pkg/ports

type ProgressUpdater func(taskID string, progress float64, message string) error

// BackgroundTaskService is the concrete implementation of ports.BackgroundTaskService.
// Exported to allow setting dependencies in main.
type BackgroundTaskService struct { // Changed to exported type
	repo              database.BackgroundTaskRepository
	scannerService    *scanner.ScannerService
	workService       ports.WorkService
	activeTaskCancels map[string]context.CancelFunc
	cancelMapMutex    sync.Mutex
}

// SetScannerService is now a method on the concrete type, not the interface.
func (s *BackgroundTaskService) SetScannerService(scannerService *scanner.ScannerService) { // Method on exported type
	s.scannerService = scannerService
}

// SetWorkService is now a method on the concrete type, not the interface.
func (s *BackgroundTaskService) SetWorkService(workService ports.WorkService) { // Method on exported type
	s.workService = workService
}

func NewBackgroundTaskService(
	repo database.BackgroundTaskRepository,
) ports.BackgroundTaskService {
	s := &BackgroundTaskService{ // Use exported type
		repo:              repo,
		activeTaskCancels: make(map[string]context.CancelFunc),
	}
	return s
}

func (s *BackgroundTaskService) SubmitTask(ctx context.Context, taskType models.TaskType, payloadData interface{}, claims *auth.Claims) (*models.BackgroundTask, error) { // Method on exported type
	var userID string
	if claims != nil {
		userID = claims.Username
	}

	payloadBytes, err := json.Marshal(payloadData)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal task payload: %w", err)
	}

	task := &models.BackgroundTask{
		TaskType:          taskType,
		Status:            models.TaskStatusPending,
		Payload:           payloadBytes,
		SubmittedByUserID: userID,
	}

	if err := s.repo.Create(ctx, task); err != nil {
		log.Error(ctx, "Failed to create background task record", "type", taskType, "error", err)
		return nil, fmt.Errorf("could not submit task: %w", err)
	}
	log.Info(ctx, "Background task submitted", "task_id", task.ID, "type", taskType, "user_id", userID)

	go s.executeTaskWrapper(task)

	return task, nil
}

func (s *BackgroundTaskService) executeTaskWrapper(task *models.BackgroundTask) { // Method on exported type
	taskCtx, cancel := context.WithCancel(context.Background())

	s.cancelMapMutex.Lock()
	s.activeTaskCancels[task.ID] = cancel
	s.cancelMapMutex.Unlock()

	defer func() {
		s.cancelMapMutex.Lock()
		delete(s.activeTaskCancels, task.ID)
		s.cancelMapMutex.Unlock()
		cancel()

		if errors.Is(taskCtx.Err(), context.Canceled) {
			dbOpCtx := context.Background()
			currentTask, err := s.repo.GetByID(dbOpCtx, task.ID)
			if err == nil {
				if currentTask.Status != models.TaskStatusCompleted &&
					currentTask.Status != models.TaskStatusFailed &&
					currentTask.Status != models.TaskStatusCancelled {
					log.Info(dbOpCtx, "Task context was cancelled, ensuring task status is Cancelled in DB", "task_id", task.ID)
					s.SetTaskResult(dbOpCtx, task.ID, nil, models.TaskStatusCancelled, "Task was cancelled.")
				}
			} else {
				log.Error(dbOpCtx, "Failed to get task for status check after context cancellation", "task_id", task.ID, "error", err)
			}
		}
	}()

	s.executeTask(taskCtx, task)
}

func (s *BackgroundTaskService) executeTask(taskCtx context.Context, task *models.BackgroundTask) { // Method on exported type
	log.Info(taskCtx, "Starting execution of background task", "task_id", task.ID, "type", task.TaskType)

	select {
	case <-taskCtx.Done():
		log.Info(taskCtx, "Task cancelled before marking as Running", "task_id", task.ID)
		return
	default:
		if err := s.UpdateTaskStatus(taskCtx, task.ID, models.TaskStatusRunning, "Task execution started."); err != nil {
			log.Error(taskCtx, "Failed to update task status to Running", "task_id", task.ID, "error", err)
			s.SetTaskResult(taskCtx, task.ID, nil, models.TaskStatusFailed, "Failed to set task status to running.")
			return
		}
	}

	var err error
	var resultPayload interface{}

	switch task.TaskType {
	case models.TaskTypeScanLibrary:
		if s.scannerService == nil {
			err = errors.New("scanner service not available for TaskTypeScanLibrary")
			break
		}
		var payload task_dto.ScanLibraryTaskPayload
		if errUnmarshal := json.Unmarshal(task.Payload, &payload); errUnmarshal != nil {
			err = fmt.Errorf("failed to unmarshal ScanLibraryTaskPayload: %w", errUnmarshal)
			break
		}
		progressUpdater := func(taskIDToUpdate string, progress float64, message string) error {
			if task.ID != taskIDToUpdate {
				log.Warn(taskCtx, "Progress update callback for unexpected task ID", "expected", task.ID, "got", taskIDToUpdate)
			}
			select {
			case <-taskCtx.Done():
				return taskCtx.Err()
			default:
				return s.UpdateTaskProgress(taskCtx, task.ID, progress, message)
			}
		}
		err = s.scannerService.ScanLibrary(taskCtx, payload.StorageID, task.ID, progressUpdater)
		if err == nil {
			resultPayload = map[string]string{"message": fmt.Sprintf("Storage source scan (ID: '%s') completed.", payload.StorageID)}
		}

	case models.TaskTypeScrapeAllWorks:
		if s.workService == nil {
			err = errors.New("work service not available for TaskTypeScrapeAllWorks")
			break
		}
		var taskPayload task_dto.ScrapeAllWorksTaskPayload
		if errUnmarshal := json.Unmarshal(task.Payload, &taskPayload); errUnmarshal != nil {
			err = fmt.Errorf("failed to unmarshal ScrapeAllWorksTaskPayload: %w", errUnmarshal)
			break
		}
		scrapeOptions := ports.ScrapeOptions{
			ForceUpdate:    taskPayload.ForceUpdate,
			PreferredLang:  taskPayload.PreferredLang,
			ScrapeMetadata: true, ScrapeCover: true, DownloadCover: true,
		}
		err = s.workService.TriggerScrapeForAllWorksByAdmin(taskCtx, nil, scrapeOptions)
		if err == nil {
			resultPayload = map[string]string{"message": "Scraping for all works completed."}
		}
	case models.TaskTypeScrapeSingleWork:
		if s.workService == nil {
			err = errors.New("work service not available for TaskTypeScrapeSingleWork")
			break
		}
		var payload task_dto.ScrapeSingleWorkTaskPayload
		if errUnmarshal := json.Unmarshal(task.Payload, &payload); errUnmarshal != nil {
			err = fmt.Errorf("failed to unmarshal ScrapeSingleWorkTaskPayload: %w", errUnmarshal)
			break
		}
		scrapeOptions := ports.ScrapeOptions{
			ForceUpdate:    payload.ForceUpdate,
			PreferredLang:  payload.PreferredLang,
			ScrapeMetadata: true, ScrapeCover: true, DownloadCover: true,
		}
		err = s.workService.TriggerScrapeForWorkByAdmin(taskCtx, nil, payload.WorkID, scrapeOptions)
		if err == nil {
			resultPayload = map[string]string{"message": fmt.Sprintf("Scraping for work ID %d completed.", payload.WorkID)}
		}

	default:
		err = fmt.Errorf("%w: %s", apperrors.ErrTaskTypeNotSupported, task.TaskType)
	}

	if errors.Is(taskCtx.Err(), context.Canceled) {
		log.Info(taskCtx, "Task execution was interrupted by cancellation", "task_id", task.ID, "type", task.TaskType)
		return
	}

	finalStatus := models.TaskStatusCompleted
	errMsgStr := ""
	if err != nil {
		log.Error(taskCtx, "Background task execution failed", "task_id", task.ID, "type", task.TaskType, "error", err)
		finalStatus = models.TaskStatusFailed
		errMsgStr = err.Error()
		resultPayload = map[string]string{"error": errMsgStr}
	} else {
		log.Info(taskCtx, "Background task execution completed successfully", "task_id", task.ID, "type", task.TaskType)
	}
	s.SetTaskResult(taskCtx, task.ID, resultPayload, finalStatus, errMsgStr)
}

func (s *BackgroundTaskService) GetTaskByID(ctx context.Context, taskID string) (*models.BackgroundTask, error) { // Method on exported type
	task, err := s.repo.GetByID(ctx, taskID)
	if err != nil {
		if errors.Is(err, apperrors.ErrTaskNotFound) {
			return nil, apperrors.ErrTaskNotFound
		}
		return nil, err
	}
	return task, nil
}

func (s *BackgroundTaskService) ListTasks(ctx context.Context, params database.ListBackgroundTaskParams) ([]*models.BackgroundTask, int64, error) { // Method on exported type
	return s.repo.List(ctx, params)
}

func (s *BackgroundTaskService) UpdateTaskStatus(ctx context.Context, taskID string, status models.TaskStatus, message string) error { // Method on exported type
	log.Debug(ctx, "Updating task status", "task_id", taskID, "status", status, "message", message)
	err := s.repo.UpdateStatus(ctx, taskID, status, message)
	return err
}

func (s *BackgroundTaskService) UpdateTaskProgress(ctx context.Context, taskID string, progress float64, message string) error { // Method on exported type
	log.Debug(ctx, "Updating task progress", "task_id", taskID, "progress", progress, "message", message)
	err := s.repo.UpdateProgress(ctx, taskID, progress, message)
	return err
}

func (s *BackgroundTaskService) SetTaskResult(ctx context.Context, taskID string, resultPayload interface{}, status models.TaskStatus, errorMessage string) error { // Method on exported type
	log.Info(ctx, "Setting task result", "task_id", taskID, "status", status, "has_error", errorMessage != "")

	var resultBytes json.RawMessage
	var err error
	if resultPayload != nil {
		resultBytes, err = json.Marshal(resultPayload)
		if err != nil {
			log.Error(ctx, "Failed to marshal task result payload", "task_id", taskID, "error", err)
			errMsgWithMarshalFail := fmt.Sprintf("Result marshaling failed: %v. Original error: %s", err, errorMessage)
			if errorMessage == "" {
				errorMessage = errMsgWithMarshalFail
			} else {
				errorMessage += "; " + errMsgWithMarshalFail
			}
			resultBytes = json.RawMessage(fmt.Sprintf(`{"error": "failed to marshal result: %v"}`, err))
			if status == models.TaskStatusCompleted {
				status = models.TaskStatusFailed
			}
		}
	}

	err = s.repo.SetResult(ctx, taskID, resultBytes, status, errorMessage)
	return err
}

func (s *BackgroundTaskService) CancelTask(ctx context.Context, taskID string, claims *auth.Claims) error { // Method on exported type
	log.Info(ctx, "Attempting to cancel task", "task_id", taskID, "user", claims.Username)

	task, err := s.repo.GetByID(ctx, taskID)
	if err != nil {
		if errors.Is(err, apperrors.ErrTaskNotFound) {
			return apperrors.ErrTaskNotFound
		}
		log.Error(ctx, "Failed to get task for cancellation", "task_id", taskID, "error", err)
		return fmt.Errorf("failed to retrieve task: %w", err)
	}

	isAdmin := (claims.UserGroup == string(models.UserGroupAdmin))

	if !isAdmin && task.SubmittedByUserID != claims.UserID {
		log.Warn(ctx, "User not authorized to cancel task", "task_id", taskID, "requesting_user_id", claims.UserID, "task_submitter_id", task.SubmittedByUserID)
		return apperrors.ErrTaskActionForbidden
	}

	if task.Status != models.TaskStatusPending && task.Status != models.TaskStatusRunning {
		log.Info(ctx, "Task cannot be cancelled, already in terminal or non-cancellable state", "task_id", taskID, "status", task.Status)
		return fmt.Errorf("%w: current status is '%s'", apperrors.ErrTaskCannotBeCancelled, task.Status)
	}

	if task.Status == models.TaskStatusPending {
		log.Info(ctx, "Task is pending, cancelling it directly in DB", "task_id", taskID)
		return s.SetTaskResult(ctx, taskID, nil, models.TaskStatusCancelled, "Task was cancelled while pending.")
	}

	s.cancelMapMutex.Lock()
	cancelFunc, ok := s.activeTaskCancels[taskID]
	s.cancelMapMutex.Unlock()

	if ok {
		log.Info(ctx, "Found active cancel function for running task, invoking it", "task_id", taskID)
		cancelFunc()
	} else {
		log.Warn(ctx, "No active cancel function found for a running task. This might be a race or the task just finished.", "task_id", taskID, "status", task.Status)
		refetchedTask, errFetch := s.repo.GetByID(ctx, taskID)
		if errFetch == nil && (refetchedTask.Status == models.TaskStatusCompleted || refetchedTask.Status == models.TaskStatusFailed || refetchedTask.Status == models.TaskStatusCancelled) {
			return fmt.Errorf("%w: task already completed, failed, or cancelled (status: %s)", apperrors.ErrTaskCannotBeCancelled, refetchedTask.Status)
		}
		return fmt.Errorf("cannot cancel running task %s: no active cancel mechanism found, or task state inconsistent", taskID)
	}

	log.Info(ctx, "Task cancellation request processed for running task", "task_id", taskID)
	return nil
}
