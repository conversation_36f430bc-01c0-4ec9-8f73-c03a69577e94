<template>
    <q-card
      id="draggable"
      @mousedown="onCursorDown"
      @mouseup="onCursorUp"
      @touchstart="onCursorDown"
      @touchend="onCursorUp"
    >
        <div id="lyricsBar" class="text-center text-h6 text-bold ellipsis-2-lines text-purple q-mb-md absolute-bottom">
            <span id="lyric">
              {{ currentLyric }}
            </span>
        </div>
    </q-card>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useAudioPlayer } from '../composables/useAudioPlayer'

defineOptions({
  name: 'LyricsBar'
})

const onCursorMove = (that) => (ev) => {
  if (!that.beTouched.value) { return }

  // ev.preventDefault()
  const touch = that.getTouch(ev)

  // 计算 element 新位置坐标
  const eleX = touch.clientX - that.startX.value
  const eleY = touch.clientY - that.startY.value

  that.draggable.value.style.left = eleX + 'px'
  that.draggable.value.style.top = eleY + 'px'
}

const { currentLyric } = useAudioPlayer()

// Reactive data
const beTouched = ref(false)
const startX = ref(0)
const startY = ref(0)

// Computed properties
const draggable = computed(() => {
  return document.getElementById('draggable')
})

// Methods
const getTouch = (ev) => {
  return ev.touches ? ev.touches[0] : ev
}

const onCursorDown = (ev) => {
  ev.preventDefault()
  beTouched.value = true

  // 移动端使用 ev.touches[0]
  const touch = getTouch(ev)
  startX.value = touch.clientX - draggable.value.offsetLeft
  startY.value = touch.clientY - draggable.value.offsetTop
}

const onCursorUp = (ev) => {
  ev.preventDefault()
  beTouched.value = false
}

// Lifecycle
onMounted(() => {
  const moveHandler = onCursorMove({
    beTouched,
    startX,
    startY,
    draggable,
    getTouch
  })

  addEventListener('mousemove', moveHandler, false)
  addEventListener('touchmove', moveHandler, false)
})


</script>

<!-- Styles moved to /src/css/components/LyricsBar.scss -->
