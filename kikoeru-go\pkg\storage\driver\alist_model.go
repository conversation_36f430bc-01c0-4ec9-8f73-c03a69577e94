package driver

// Note: Core driver interfaces (StorageDriver, StorageMeta, StorageReader, Getter, Storage, Config struct,
// AlistDriverCommonConfig struct, DriverConstructor type, IRootPathGetter, IRootIDGetter)
// are defined in 'interface.go' to avoid circular dependencies and redeclarations.
// ParamInfo is also in 'interface.go'.

import (
	"context"
	"io"
	"net/http"
	"path/filepath"
	"strings"
	"time"
)

// --- Alist Object Model & Related Types ---

const (
	ConfTypeImage = "image"
	ConfTypeVideo = "video"
	ConfTypeAudio = "audio"
	ConfTypeText  = "text"
)

type AlistHashInfo struct {
	MD5    string `json:"md5,omitempty"`
	SHA1   string `json:"sha1,omitempty"`
	SHA256 string `json:"sha256,omitempty"`
}

type AlistHTTPRange struct {
	Start  int64
	Length int64
}

// AlistObj interface remains here as it's fundamental to the object model drivers interact with.
type AlistObj interface {
	GetSize() int64
	GetName() string
	ModTime() time.Time
	CreateTime() time.Time
	IsDir() bool
	GetHash() AlistHashInfo
	GetID() string
	GetPath() string
}

// AlistObject struct remains here.
type AlistObject struct {
	ID       string        `json:"id"`
	Path     string        `json:"path"`
	Name     string        `json:"name"`
	Size     int64         `json:"size"`
	Modified time.Time     `json:"modified"`
	Ctime    time.Time     `json:"created"`
	IsFolder bool          `json:"is_dir"`
	HashInfo AlistHashInfo `json:"hash_info,omitempty"`
}

func (o *AlistObject) GetName() string    { return o.Name }
func (o *AlistObject) GetSize() int64     { return o.Size }
func (o *AlistObject) ModTime() time.Time { return o.Modified }
func (o *AlistObject) CreateTime() time.Time {
	if o.Ctime.IsZero() {
		return o.Modified
	}
	return o.Ctime
}
func (o *AlistObject) IsDir() bool            { return o.IsFolder }
func (o *AlistObject) GetID() string          { return o.ID }
func (o *AlistObject) GetPath() string        { return o.Path }
func (o *AlistObject) GetHash() AlistHashInfo { return o.HashInfo }

var _ AlistObj = (*AlistObject)(nil)

func NewPathAlistObj(path string) AlistObj {
	cleanPath := filepath.Clean(path)
	originalPath := path
	var name string
	var isDir bool
	if originalPath == "/" {
		path = "/"
		name = "/"
		isDir = true
	} else if cleanPath == "." || cleanPath == "" {
		path = "/"
		name = "/"
		isDir = true
	} else {
		path = cleanPath
		name = filepath.Base(path)
		isDir = strings.HasSuffix(originalPath, "/")
	}
	return &AlistObject{
		Path:     path,
		Name:     name,
		IsFolder: isDir,
		ID:       path,
	}
}

// RootPathConfig and RootIDConfig remain here as they are specific configurations
// that drivers' Addition structs might embed.
type RootPathConfig struct {
	RootFolderPath string `json:"root_folder_path" mapstructure:"root_folder_path" label:"Root Folder Path" description:"The absolute path to the root directory on the server. This path must exist and be accessible by Kikoeru." required:"true" type:"string"`
}

func (r RootPathConfig) GetRootPath() string { return r.RootFolderPath }

type RootIDConfig struct {
	// RootFolderID for ID-based drivers. Description should guide user on typical defaults if any (e.g. "root" for GDrive).
	RootFolderID string `json:"root_folder_id" mapstructure:"root_folder_id" label:"Root Folder ID" description:"The ID of the folder to use as the root. For some services, this might be optional if a service-wide default (like the entire drive for Google Drive, often identified by 'root') is used when left empty." required:"false" type:"string"`
}

func (r RootIDConfig) GetRootID() string { return r.RootFolderID }

// IRootPathGetter and IRootIDGetter interfaces are defined in interface.go

// AlistLink, AlistListArgs, AlistLinkArgs, AlistOtherArgs remain here.
type AlistFileStreamer interface {
	io.Reader
	io.Closer
	AlistObj
	GetMimetype() string
	NeedStore() bool
	IsForceStreamUpload() bool
	GetExist() AlistObj
	SetExist(AlistObj)
	RangeRead(http_range AlistHTTPRange) (io.Reader, error)
}

type AlistUpdateProgress func(percentage float64)

type AlistLink struct {
	URL         string            `json:"url"`
	Header      http.Header       `json:"header,omitempty"`
	Expiration  *time.Duration    `json:"-"`
	MFile       AlistFileStreamer `json:"-"`
	IPCacheKey  bool              `json:"-"`
	Concurrency int               `json:"concurrency,omitempty"`
	PartSize    int               `json:"part_size,omitempty"`
}

type AlistListArgs struct {
	ReqPath  string
	Refresh  bool
	Password string `json:"password,omitempty"`
	PageNum  int    `json:"page_num,omitempty"`
	PageSize int    `json:"page_size,omitempty"`
}

type AlistLinkArgs struct {
	IP      string
	Header  http.Header
	Type    string
	HttpReq *http.Request
	Path    string
}

type AlistOtherArgs struct {
	Obj    AlistObj
	Method string
	Data   interface{}
}

type AlistFile interface {
	io.Reader
	io.ReaderAt
	io.Seeker
	io.Closer
}

type AlistRangeReadCloserIF interface {
	RangeRead(ctx context.Context, httpRange AlistHTTPRange) (io.ReadCloser, error)
	io.Closer
}
