package config

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/spf13/viper" // Corrected import path
)

// AppConfig 是所有配置的根结构体
type AppConfig struct {
	Server   ServerConfig   `mapstructure:"server"`
	Auth     AuthConfig     `mapstructure:"auth"`
	Database DatabaseConfig `mapstructure:"database"`
	Log      LogConfig      `mapstructure:"log"`
	Scanner  ScannerConfig  `mapstructure:"scanner"`
	Scraper  ScraperConfig  `mapstructure:"scraper"`
	Paths    PathConfig     `mapstructure:"paths"`
	SMTP     SMTPConfig     `mapstructure:"smtp"`
	Cache    CacheConfig    `mapstructure:"cache"`

	jwtSecretBytes []byte
}

// GetJWTSecretBytes returns the JWT secret as a byte slice.
func (ac *AppConfig) GetJWTSecretBytes() []byte {
	return ac.jwtSecretBytes
}

// ServerConfig 服务器相关配置
type ServerConfig struct {
	ListenPort            string        `mapstructure:"listen_port"`
	ReadTimeout           time.Duration `mapstructure:"read_timeout"`
	WriteTimeout          time.Duration `mapstructure:"write_timeout"`
	IdleTimeout           time.Duration `mapstructure:"idle_timeout"`
	EnableGzip            bool          `mapstructure:"enable_gzip"`
	BehindProxy           bool          `mapstructure:"behind_proxy"`
	HttpsEnabled          bool          `mapstructure:"https_enabled"`
	HttpsPort             string        `mapstructure:"https_port"`
	HttpsCertFile         string        `mapstructure:"https_cert_file"`
	HttpsKeyFile          string        `mapstructure:"https_key_file"`
	UseSelfSignedCert     bool          `mapstructure:"use_self_signed_cert"` // 是否使用自签名证书
	BlockRemoteConnection bool          `mapstructure:"block_remote_connection"`
	PageSize              int           `mapstructure:"page_size"`
	PublicURL             string        `mapstructure:"public_url"` // Added for constructing public-facing URLs
}

// AuthConfig 认证相关配置
type AuthConfig struct {
	EnableAuth            bool          `mapstructure:"enable_auth"`
	JWTSecret             string        `mapstructure:"jwt_secret"`
	JWTExpiresIn          time.Duration `mapstructure:"jwt_expires_in"`
	BcryptCost            int           `mapstructure:"bcrypt_cost"`
	AllowGuest            bool          `mapstructure:"allow_guest"`
	AllowRegistration     bool          `mapstructure:"allow_registration"`
	MinPasswordLength     int           `mapstructure:"min_password_length"`
	EnableEmailFeatures   bool          `mapstructure:"enable_email_features"`
	EnsureEmail           bool          `mapstructure:"ensure_email"` // If true, email is mandatory during registration and requires verification.
	VerificationTokenTTL  time.Duration `mapstructure:"verification_token_ttl"`
	PasswordResetTokenTTL time.Duration `mapstructure:"password_reset_token_ttl"`
	EmailCooldownSeconds  int           `mapstructure:"email_cooldown_seconds"` // Email cooldown per email address
	IPCooldownSeconds     int           `mapstructure:"ip_cooldown_seconds"`    // Email cooldown per IP address
	EmailDailyLimit       int           `mapstructure:"email_daily_limit"`      // Daily email limit per email address
	IPDailyLimit          int           `mapstructure:"ip_daily_limit"`         // Daily email limit per IP address
}

// SMTPConfig 电子邮件发送 (SMTP) 相关配置
type SMTPConfig struct {
	Host          string `mapstructure:"host"`
	Port          int    `mapstructure:"port"`
	Username      string `mapstructure:"username"`
	Password      string `mapstructure:"password"`
	Sender        string `mapstructure:"sender"` // Sender email address
	EnableTLS     bool   `mapstructure:"enable_tls"`
	SkipTLSVerify bool   `mapstructure:"skip_tls_verify"` // Added for self-signed certs or testing
}

// DatabaseConfig 数据库相关配置
type DatabaseConfig struct {
	Driver      string       `mapstructure:"driver"`
	SQLite      SQLiteConfig `mapstructure:"sqlite"`
	MySQL       MySQLConfig  `mapstructure:"mysql"`
	BusyTimeout int          `mapstructure:"busy_timeout_ms"`
}

// SQLiteConfig SQLite 特定配置
type SQLiteConfig struct {
	Path string `mapstructure:"path"`
}

// MySQLConfig MySQL 特定配置
type MySQLConfig struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	User     string `mapstructure:"user"`
	Password string `mapstructure:"password"`
	DBName   string `mapstructure:"dbname"`
	Params   string `mapstructure:"params"`
}

// LogConfig 日志相关配置
type LogConfig struct {
	Level  string `mapstructure:"level"`
	Format string `mapstructure:"format"`
}

// ScannerConfig 扫描器相关配置
type ScannerConfig struct {
	MaxParallelism     int    `mapstructure:"max_parallelism"`
	MaxRecursionDepth  int    `mapstructure:"max_recursion_depth"`
	SkipCleanup        bool   `mapstructure:"skip_cleanup"`
	ForceScrapeOnScan  bool   `mapstructure:"force_scrape_on_scan"`
	DefaultScraperLang string `mapstructure:"default_scraper_lang"`
	CoverJPEGQuality   int    `mapstructure:"cover_jpeg_quality"`
}

// ScraperConfig 爬虫相关配置
type ScraperConfig struct {
	TagLanguage   string        `mapstructure:"tag_language"`
	RetryAttempts int           `mapstructure:"retry_attempts"`
	RetryDelay    time.Duration `mapstructure:"retry_delay"`
	Timeout       time.Duration `mapstructure:"timeout"`
	HttpProxy     string        `mapstructure:"http_proxy"`
}

// PathConfig 全局路径配置
type PathConfig struct {
	DataDir   string `mapstructure:"data_dir"`
	CoversDir string `mapstructure:"covers_dir"`
	LyricsDir string `mapstructure:"lyrics_dir"`
}

// CacheConfig 缓存相关配置
type CacheConfig struct {
	Enabled  bool   `mapstructure:"enabled"`
	CacheDir string `mapstructure:"cache_dir"` // Relative to data_dir
}

var cfg *AppConfig

func LoadConfig(configPath string) (*AppConfig, error) {
	v := viper.New()
	if configPath != "" {
		v.SetConfigFile(configPath)
	} else {
		v.SetConfigName("config")
		v.SetConfigType("yaml")
		v.AddConfigPath("./configs")
		v.AddConfigPath("$HOME/.kikoeru-go")
		v.AddConfigPath("/etc/kikoeru-go")
	}
	setDefaults(v)
	v.SetEnvPrefix("KIKOERU")
	v.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	v.AutomaticEnv()
	if err := v.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			fmt.Println("Configuration file not found, creating default config...")
			if err := writeDefaultConfig(v, configPath); err != nil {
				return nil, fmt.Errorf("failed to write default config: %w", err)
			}
			if err := v.ReadInConfig(); err != nil {
				return nil, fmt.Errorf("failed to read config after writing default: %w", err)
			}
		} else {
			return nil, fmt.Errorf("failed to read config: %w", err)
		}
	}
	var localCfg AppConfig
	if err := v.Unmarshal(&localCfg); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// Pre-check: EnsureEmail cannot be true if EnableEmailFeatures is false
	if !localCfg.Auth.EnableEmailFeatures && localCfg.Auth.EnsureEmail {
		return nil, fmt.Errorf("configuration error: auth.ensure_email cannot be true when auth.enable_email_features is false")
	}

	if localCfg.Auth.JWTSecret == "" {
		if localCfg.Auth.EnableAuth {
			return nil, fmt.Errorf("auth.jwt_secret is empty but authentication is enabled")
		}
		localCfg.jwtSecretBytes = []byte{}
	} else {
		localCfg.jwtSecretBytes = []byte(localCfg.Auth.JWTSecret)
	}
	if err := ensureDataDirs(&localCfg.Paths, &localCfg.Cache); err != nil {
		return nil, fmt.Errorf("failed to ensure data directories: %w", err)
	}
	cfg = &localCfg
	return cfg, nil
}

func GetConfig() *AppConfig {
	if cfg == nil {
		fmt.Println("Warning: config accessed before LoadConfig was called. Loading with defaults.")
		tempCfg, err := LoadConfig("")
		if err != nil {
			panic(fmt.Sprintf("Failed to load default config: %v", err))
		}
		return tempCfg
	}
	return cfg
}

func generateRandomHex(length int) string {
	bytes := make([]byte, length)
	if _, err := rand.Read(bytes); err != nil {
		return "default_fallback_secret_please_change"
	}
	return hex.EncodeToString(bytes)
}

func setDefaults(v *viper.Viper) {
	v.SetDefault("server.listen_port", ":8888")
	v.SetDefault("server.read_timeout", "15s")
	v.SetDefault("server.write_timeout", "15s")
	v.SetDefault("server.idle_timeout", "60s")
	v.SetDefault("server.enable_gzip", true)
	v.SetDefault("server.behind_proxy", false)
	v.SetDefault("server.https_enabled", false)
	v.SetDefault("server.https_port", ":8443")
	v.SetDefault("server.https_cert_file", "cert.pem")
	v.SetDefault("server.https_key_file", "key.pem")
	v.SetDefault("server.use_self_signed_cert", false) // 默认不使用自签名证书
	v.SetDefault("server.block_remote_connection", false)
	v.SetDefault("server.page_size", 24)
	v.SetDefault("server.public_url", "http://localhost"+v.GetString("server.listen_port"))

	v.SetDefault("auth.enable_auth", true)
	v.SetDefault("auth.jwt_secret", generateRandomHex(32))
	v.SetDefault("auth.jwt_expires_in", "720h")
	v.SetDefault("auth.bcrypt_cost", 10)
	v.SetDefault("auth.allow_guest", false)
	v.SetDefault("auth.allow_registration", true)
	v.SetDefault("auth.min_password_length", 8)
	v.SetDefault("auth.enable_email_features", false)
	v.SetDefault("auth.ensure_email", true) // Changed from verify_email, true means email is mandatory if email features are on.
	v.SetDefault("auth.verification_token_ttl", "24h")
	v.SetDefault("auth.password_reset_token_ttl", "1h")
	v.SetDefault("auth.email_cooldown_seconds", 60) // Default email cooldown per email: 60 seconds
	v.SetDefault("auth.ip_cooldown_seconds", 60)    // Default email cooldown per IP: 60 seconds
	v.SetDefault("auth.email_daily_limit", 5)       // Default daily email limit per email: 5
	v.SetDefault("auth.ip_daily_limit", 10)         // Default daily email limit per IP: 10

	v.SetDefault("smtp.host", "")
	v.SetDefault("smtp.port", 587)
	v.SetDefault("smtp.username", "")
	v.SetDefault("smtp.password", "")
	v.SetDefault("smtp.sender", "")
	v.SetDefault("smtp.enable_tls", true)
	v.SetDefault("smtp.skip_tls_verify", false)

	v.SetDefault("database.driver", "sqlite")
	v.SetDefault("database.sqlite.path", "kikoeru.db")
	v.SetDefault("database.mysql.host", "localhost")
	v.SetDefault("database.mysql.port", 3306)
	v.SetDefault("database.mysql.user", "kikoeru")
	v.SetDefault("database.mysql.password", "password")
	v.SetDefault("database.mysql.dbname", "kikoeru")
	v.SetDefault("database.mysql.params", "charset=utf8mb4&parseTime=True&loc=Local")
	v.SetDefault("database.busy_timeout_ms", 5000)

	v.SetDefault("log.level", "info")
	v.SetDefault("log.format", "text")

	v.SetDefault("scanner.max_parallelism", 4)
	v.SetDefault("scanner.max_recursion_depth", 5)
	v.SetDefault("scanner.skip_cleanup", false)
	v.SetDefault("scanner.force_scrape_on_scan", false)
	v.SetDefault("scanner.default_scraper_lang", "")
	v.SetDefault("scanner.cover_jpeg_quality", 85)

	v.SetDefault("scraper.tag_language", "zh-cn")
	v.SetDefault("scraper.retry_attempts", 3)
	v.SetDefault("scraper.retry_delay", "2s")
	v.SetDefault("scraper.timeout", "20s")
	v.SetDefault("scraper.http_proxy", "")

	// Cache defaults
	v.SetDefault("cache.enabled", true)
	v.SetDefault("cache.cache_dir", "cache")

	defaultDataDir := ".kikoeru-go-data"
	exePath, err := os.Executable()
	if err == nil {
		defaultDataDir = filepath.Join(filepath.Dir(exePath), ".kikoeru-go-data")
	}

	v.SetDefault("paths.data_dir", defaultDataDir)
	v.SetDefault("paths.covers_dir", "covers")
	v.SetDefault("paths.lyrics_dir", "lyrics")
}

func writeDefaultConfig(v *viper.Viper, configPath string) error {
	v.Set("auth.jwt_secret", generateRandomHex(32)) // Ensure JWT secret is always set when writing default
	writePath := configPath
	if writePath == "" {
		homeDir, err := os.UserHomeDir()
		if err == nil {
			userConfigDir := filepath.Join(homeDir, ".kikoeru-go")
			if err := os.MkdirAll(userConfigDir, 0750); err == nil {
				writePath = filepath.Join(userConfigDir, "config.yaml")
			}
		}
		if writePath == "" {
			cwd, err := os.Getwd()
			if err == nil {
				configsDir := filepath.Join(cwd, "configs")
				if err := os.MkdirAll(configsDir, 0750); err == nil {
					writePath = filepath.Join(configsDir, "config.yaml")
				}
			}
		}
		if writePath == "" {
			cwd, err := os.Getwd()
			if err == nil {
				writePath = filepath.Join(cwd, "config.yaml")
			} else {
				return fmt.Errorf("cannot determine current working directory to write default config")
			}
		}
	}
	fmt.Printf("Writing default configuration to: %s\n", writePath)
	if err := os.MkdirAll(filepath.Dir(writePath), 0750); err != nil {
		return fmt.Errorf("failed to create directory for config file %s: %w", filepath.Dir(writePath), err)
	}
	return v.WriteConfigAs(writePath)
}

func ensureDataDirs(paths *PathConfig, cache *CacheConfig) error {
	dataDir := paths.DataDir
	if dataDir == "" {
		exePath, err := os.Executable()
		if err != nil {
			dataDir = ".kikoeru-go-data"
		} else {
			dataDir = filepath.Join(filepath.Dir(exePath), ".kikoeru-go-data")
		}
		paths.DataDir = dataDir
	}
	dirsToCreate := []string{dataDir}
	if paths.CoversDir != "" {
		dirsToCreate = append(dirsToCreate, filepath.Join(dataDir, paths.CoversDir))
	}
	if paths.LyricsDir != "" {
		dirsToCreate = append(dirsToCreate, filepath.Join(dataDir, paths.LyricsDir))
	}
	if cache.Enabled && cache.CacheDir != "" {
		dirsToCreate = append(dirsToCreate, filepath.Join(dataDir, cache.CacheDir))
	}
	for _, dir := range dirsToCreate {
		if err := os.MkdirAll(dir, 0750); err != nil {
			return fmt.Errorf("failed to create directory %s: %w", dir, err)
		}
	}
	return nil
}

// Add this method to the AppConfig struct
// Save persists the current configuration to the file it was loaded from
func (ac *AppConfig) Save() error {
	v := viper.New()

	// Map the AppConfig struct to viper settings
	v.Set("auth.allow_registration", ac.Auth.AllowRegistration)
	v.Set("auth.enable_email_features", ac.Auth.EnableEmailFeatures)
	v.Set("auth.ensure_email", ac.Auth.EnsureEmail)
	// Preserve the JWT secret
	v.Set("auth.jwt_secret", ac.Auth.JWTSecret)
	v.Set("auth.jwt_expires_in", ac.Auth.JWTExpiresIn)
	v.Set("auth.bcrypt_cost", ac.Auth.BcryptCost)
	v.Set("auth.allow_guest", ac.Auth.AllowGuest)
	v.Set("auth.min_password_length", ac.Auth.MinPasswordLength)
	v.Set("auth.verification_token_ttl", ac.Auth.VerificationTokenTTL)
	v.Set("auth.password_reset_token_ttl", ac.Auth.PasswordResetTokenTTL)
	v.Set("auth.email_cooldown_seconds", ac.Auth.EmailCooldownSeconds)
	v.Set("auth.ip_cooldown_seconds", ac.Auth.IPCooldownSeconds)
	v.Set("auth.email_daily_limit", ac.Auth.EmailDailyLimit)
	v.Set("auth.ip_daily_limit", ac.Auth.IPDailyLimit)

	// Map server settings
	v.Set("server.listen_port", ac.Server.ListenPort)
	v.Set("server.read_timeout", ac.Server.ReadTimeout)
	v.Set("server.write_timeout", ac.Server.WriteTimeout)
	v.Set("server.idle_timeout", ac.Server.IdleTimeout)
	v.Set("server.enable_gzip", ac.Server.EnableGzip)
	v.Set("server.behind_proxy", ac.Server.BehindProxy)
	v.Set("server.https_enabled", ac.Server.HttpsEnabled)
	v.Set("server.https_port", ac.Server.HttpsPort)
	v.Set("server.https_cert_file", ac.Server.HttpsCertFile)
	v.Set("server.https_key_file", ac.Server.HttpsKeyFile)
	v.Set("server.use_self_signed_cert", ac.Server.UseSelfSignedCert)
	v.Set("server.block_remote_connection", ac.Server.BlockRemoteConnection)
	v.Set("server.page_size", ac.Server.PageSize)
	v.Set("server.public_url", ac.Server.PublicURL)

	// Map cache settings
	v.Set("cache.enabled", ac.Cache.Enabled)
	v.Set("cache.cache_dir", ac.Cache.CacheDir)

	// Map path settings
	v.Set("paths.data_dir", ac.Paths.DataDir)
	v.Set("paths.covers_dir", ac.Paths.CoversDir)
	v.Set("paths.lyrics_dir", ac.Paths.LyricsDir)

	// Map other settings as needed (database, etc.)
	// ...

	// If there's an active config file path in memory, use it
	// Otherwise, try to locate it in standard locations
	configFile := viper.ConfigFileUsed()
	if configFile == "" {
		// Look in standard locations
		homeDir, err := os.UserHomeDir()
		if err == nil {
			userConfigPath := filepath.Join(homeDir, ".kikoeru-go", "config.yaml")
			if _, err := os.Stat(userConfigPath); err == nil {
				configFile = userConfigPath
			}
		}

		// If still not found, check current working directory
		if configFile == "" {
			cwd, err := os.Getwd()
			if err == nil {
				cwdConfigPath := filepath.Join(cwd, "configs", "config.yaml")
				if _, err := os.Stat(cwdConfigPath); err == nil {
					configFile = cwdConfigPath
				} else {
					cwdConfigPath = filepath.Join(cwd, "config.yaml")
					if _, err := os.Stat(cwdConfigPath); err == nil {
						configFile = cwdConfigPath
					}
				}
			}
		}

		// If we still can't find it, return an error
		if configFile == "" {
			return fmt.Errorf("cannot determine configuration file location to save to")
		}
	}

	// Write the configuration to file
	v.SetConfigFile(configFile)
	return v.WriteConfig()
}
