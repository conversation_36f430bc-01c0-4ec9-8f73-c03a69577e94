<template>
  <div class="q-pa-md">
    <div class="text-h5 text-weight-regular q-mb-lg">{{ t('dashboardFeedbackManage.title') }}</div>

    <div class="row justify-between q-mb-md">
      <div class="row q-gutter-sm">
        <!-- 状态过滤 -->
        <q-select
          v-model="filterStatus"
          :options="statusOptionsComputed"
          option-label="label"
          option-value="value"
          emit-value
          map-options
          :label="t('dashboardFeedbackManage.filterStatusLabel')"
          dense
          outlined
          style="min-width: 120px"
          @update:model-value="loadFeedback"
        />

        <!-- 类型过滤 -->
        <q-select
          v-model="filterType"
          :options="typeOptionsComputed"
          option-label="label"
          option-value="value"
          emit-value
          map-options
          :label="t('dashboardFeedbackManage.filterTypeLabel')"
          dense
          outlined
          style="min-width: 120px"
          @update:model-value="loadFeedback"
        />
      </div>

      <div class="row q-gutter-sm">
        <q-btn
          color="secondary"
          icon="refresh"
          @click="loadFeedback"
          :loading="loading"
        />
      </div>
    </div>

    <!-- 反馈列表 -->
    <q-table
      :rows="feedbacks"
      :columns="columnsComputed"
      row-key="id"
      :loading="loading"
      flat
      bordered
      class="shadow-2"
      :pagination="pagination"
      @request="onRequest"
    >
      <template v-slot:body-cell-category="props">
        <q-td :props="props">
          <q-chip
            :color="getTypeColor(props.row.category)"
            text-color="white"
            dense
          >
            {{ getTypeLabel(props.row.category) }}
          </q-chip>
        </q-td>
      </template>

      <template v-slot:body-cell-status="props">
        <q-td :props="props">
          <q-chip
            :color="getStatusColor(props.row.status)"
            text-color="white"
            dense
          >
            {{ getStatusLabel(props.row.status) }}
          </q-chip>
        </q-td>
      </template>

      <template v-slot:body-cell-content="props">
        <q-td :props="props">
          <div class="text-body2 ellipsis" style="max-width: 300px;">
            {{ props.row.content }}
          </div>
        </q-td>
      </template>

      <template v-slot:body-cell-user="props">
        <q-td :props="props">
          <div v-if="props.row.user">
            <div class="text-body2">{{ props.row.user.username }}</div>
            <div class="text-caption text-grey-6">{{ props.row.user.email }}</div>
          </div>
          <span v-else class="text-grey-6">{{ t('dashboardFeedbackManage.anonymousUser') }}</span>
        </q-td>
      </template>

      <template v-slot:body-cell-actions="props">
        <q-td :props="props">
          <div class="row q-gutter-xs">
            <q-btn
              dense
              round
              color="primary"
              icon="visibility"
              size="sm"
              @click="viewFeedback(props.row)"
            >
              <q-tooltip>{{ t('dashboardFeedbackManage.viewDetailTooltip') }}</q-tooltip>
            </q-btn>

            <q-btn
              dense
              round
              color="orange"
              icon="edit"
              size="sm"
              @click="editFeedback(props.row)"
            >
              <q-tooltip>{{ t('dashboardFeedbackManage.updateStatusTooltip') }}</q-tooltip>
            </q-btn>
          </div>
        </q-td>
      </template>
    </q-table>

    <!-- 查看反馈详情对话框 -->
    <q-dialog v-model="showDetailDialog" maximized>
      <q-card v-if="selectedFeedback">
        <q-card-section class="row items-center q-pb-none">
          <div class="text-h6">{{ t('dashboardFeedbackManage.detailDialog.title') }}</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12 col-md-8">
              <q-card flat bordered>
                <q-card-section>
                  <div class="text-h6 q-mb-md">{{ t('dashboardFeedbackManage.detailDialog.feedbackContentTitle') }}</div>
                  <div class="text-body1">{{ selectedFeedback.content }}</div>
                </q-card-section>
              </q-card>

              <q-card flat bordered class="q-mt-md" v-if="selectedFeedback.resolution_notes">
                <q-card-section>
                  <div class="text-h6 q-mb-md">{{ t('dashboardFeedbackManage.detailDialog.resolutionNotesTitle') }}</div>
                  <div class="text-body1">{{ selectedFeedback.resolution_notes }}</div>
                </q-card-section>
              </q-card>
            </div>

            <div class="col-12 col-md-4">
              <q-list bordered>
                <q-item>
                  <q-item-section>
                    <q-item-label>{{ t('dashboardFeedbackManage.detailDialog.feedbackId') }}</q-item-label>
                    <q-item-label caption>{{ selectedFeedback.id }}</q-item-label>
                  </q-item-section>
                </q-item>

                <q-item>
                  <q-item-section>
                    <q-item-label>{{ t('dashboardFeedbackManage.detailDialog.type') }}</q-item-label>
                    <q-item-label caption>
                      <q-chip
                        :color="getTypeColor(selectedFeedback.category)"
                        text-color="white"
                        dense
                      >
                        {{ getTypeLabel(selectedFeedback.category) }}
                      </q-chip>
                    </q-item-label>
                  </q-item-section>
                </q-item>

                <q-item>
                  <q-item-section>
                    <q-item-label>{{ t('dashboardFeedbackManage.detailDialog.status') }}</q-item-label>
                    <q-item-label caption>
                      <q-chip
                        :color="getStatusColor(selectedFeedback.status)"
                        text-color="white"
                        dense
                      >
                        {{ getStatusLabel(selectedFeedback.status) }}
                      </q-chip>
                    </q-item-label>
                  </q-item-section>
                </q-item>

                <q-item v-if="selectedFeedback.user">
                  <q-item-section>
                    <q-item-label>{{ t('dashboardFeedbackManage.detailDialog.submittedBy') }}</q-item-label>
                    <q-item-label caption>{{ selectedFeedback.user.username }}</q-item-label>
                    <q-item-label caption v-if="selectedFeedback.user.email">{{ selectedFeedback.user.email }}</q-item-label>
                  </q-item-section>
                </q-item>

                <q-item>
                  <q-item-section>
                    <q-item-label>{{ t('dashboardFeedbackManage.detailDialog.submittedAt') }}</q-item-label>
                    <q-item-label caption>{{ formatDate(selectedFeedback.created_at) }}</q-item-label>
                  </q-item-section>
                </q-item>

                <q-item v-if="selectedFeedback.updated_at !== selectedFeedback.created_at">
                  <q-item-section>
                    <q-item-label>{{ t('dashboardFeedbackManage.detailDialog.updatedAt') }}</q-item-label>
                    <q-item-label caption>{{ formatDate(selectedFeedback.updated_at) }}</q-item-label>
                  </q-item-section>
                </q-item>

                <q-item v-if="selectedFeedback.resolved_at">
                  <q-item-section>
                    <q-item-label>{{ t('dashboardFeedbackManage.detailDialog.resolvedAt') }}</q-item-label>
                    <q-item-label caption>{{ formatDate(selectedFeedback.resolved_at) }}</q-item-label>
                  </q-item-section>
                </q-item>

                <q-item v-if="selectedFeedback.resolved_by">
                  <q-item-section>
                    <q-item-label>{{ t('dashboardFeedbackManage.detailDialog.resolvedBy') }}</q-item-label>
                    <q-item-label caption>{{ selectedFeedback.resolved_by }}</q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
            </div>
          </div>
        </q-card-section>
      </q-card>
    </q-dialog>

    <!-- 更新反馈状态对话框 -->
    <q-dialog v-model="showEditDialog" persistent>
      <q-card style="min-width: 500px">
        <q-card-section>
          <div class="text-h6">{{ t('dashboardFeedbackManage.editDialog.title') }}</div>
        </q-card-section>

        <q-card-section class="q-pt-none">
          <q-form @submit="updateFeedback" class="q-gutter-md">
            <q-select
              v-model="editForm.status"
              :options="statusEditOptionsComputed"
              option-label="label"
              option-value="value"
              emit-value
              map-options
              :label="t('dashboardFeedbackManage.editDialog.statusLabel')"
              dense
              outlined
            />

            <q-input
              v-model="editForm.resolution_notes"
              :label="t('dashboardFeedbackManage.editDialog.resolutionNotesLabel')"
              type="textarea"
              dense
              outlined
              autogrow
            />
          </q-form>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="t('common.cancel')" @click="cancelEdit" />
          <q-btn flat :label="t('common.save')" color="primary" @click="updateFeedback" :loading="updating" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, getCurrentInstance } from 'vue'
import { useI18n } from 'vue-i18n'
import { useNotification } from '../../composables/useNotification'

defineOptions({
  name: 'FeedbackManagePage'
})

// Composables
    const { t, locale } = useI18n()
    const { proxy } = getCurrentInstance()
    const { showSuccessNotification, showErrorNotification } = useNotification()

    // Reactive data
    const feedbacks = ref([])
    const loading = ref(false)
    const updating = ref(false)
    const filterStatus = ref('all')
    const filterType = ref('all')
    const showDetailDialog = ref(false)
    const showEditDialog = ref(false)
    const selectedFeedback = ref(null)
    const editForm = ref({
      id: null,
      status: 'new',
      resolution_notes: ''
    })
    const pagination = ref({
      page: 1,
      rowsPerPage: 10,
      rowsNumber: 0
    })

    // Computed properties
    const columnsComputed = computed(() => [
      { name: 'id', label: 'ID', field: 'id', sortable: true, align: 'left', style: 'width: 80px' },
      { name: 'category', label: t('dashboardFeedbackManage.table.type'), field: 'category', sortable: true, align: 'center' },
      { name: 'status', label: t('dashboardFeedbackManage.table.status'), field: 'status', sortable: true, align: 'center' },
      { name: 'content', label: t('dashboardFeedbackManage.table.content'), field: 'content', align: 'left', classes: 'ellipsis', style: 'max-width: 300px;' },
      { name: 'user', label: t('dashboardFeedbackManage.table.user'), field: 'user', align: 'left' },
      {
        name: 'created_at',
        label: t('dashboardFeedbackManage.table.createdAt'),
        field: 'created_at',
        sortable: true,
        align: 'center',
        format: val => formatDate(val)
      },
      { name: 'actions', label: t('dashboardFeedbackManage.table.actions'), field: 'actions', align: 'center' }
    ])

    const statusOptionsComputed = computed(() => [
      { label: t('dashboardFeedbackManage.statusFilters.all'), value: 'all' },
      { label: t('dashboardFeedbackManage.feedbackStatus.new'), value: 'new' },
      { label: t('dashboardFeedbackManage.feedbackStatus.open'), value: 'open' },
      { label: t('dashboardFeedbackManage.feedbackStatus.in_progress'), value: 'in_progress' },
      { label: t('dashboardFeedbackManage.feedbackStatus.resolved'), value: 'resolved' },
      { label: t('dashboardFeedbackManage.feedbackStatus.closed'), value: 'closed' },
      { label: t('dashboardFeedbackManage.feedbackStatus.rejected'), value: 'rejected' }
    ])

    const typeOptionsComputed = computed(() => [
      { label: t('dashboardFeedbackManage.typeFilters.all'), value: 'all' },
      { label: t('dashboardFeedbackManage.feedbackTypes.bug'), value: 'bug' },
      { label: t('dashboardFeedbackManage.feedbackTypes.feature'), value: 'feature' },
      { label: t('dashboardFeedbackManage.feedbackTypes.improvement'), value: 'improvement' },
      { label: t('dashboardFeedbackManage.feedbackTypes.comment'), value: 'comment' },
      { label: t('dashboardFeedbackManage.feedbackTypes.other'), value: 'other' }
    ])

    const statusEditOptionsComputed = computed(() => {
      // Exclude 'all' for editing status
      return statusOptionsComputed.value.filter(opt => opt.value !== 'all')
    })

    // Methods
    const formatDate = (dateString) => {
      if (!dateString) return ''
      return new Date(dateString).toLocaleString(locale.value)
    }

const loadFeedback = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.value.page,
      page_size: pagination.value.rowsPerPage
    }
    if (filterStatus.value !== 'all') {
      params.status = filterStatus.value
    }
    if (filterType.value !== 'all') {
      params.category = filterType.value
    }

    const response = await proxy.$api.get('/api/v1/admin/feedback', { params })
    feedbacks.value = response.data.items || []
    pagination.value.rowsNumber = response.data.pagination.total_items
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('notification.adminLoadFeedbackFailed'))
  } finally {
    loading.value = false
  }
}

const onRequest = (props) => {
  pagination.value = props.pagination
  loadFeedback()
}

const getTypeColor = (type) => {
  const colors = {
    bug: 'negative',
    feature: 'positive',
    improvement: 'info',
    comment: 'grey-7',
    other: 'black'
  }
  return colors[type.toLowerCase()] || 'primary'
}

const getTypeLabel = (type) => {
  const option = typeOptionsComputed.value.find(opt => opt.value === type.toLowerCase())
  return option ? option.label : type
}

const getStatusColor = (status) => {
  const colors = {
    new: 'blue',
    open: 'light-blue',
    in_progress: 'orange',
    resolved: 'green',
    closed: 'grey',
    rejected: 'red'
  }
  return colors[status.toLowerCase()] || 'primary'
}

const getStatusLabel = (status) => {
  const option = statusOptionsComputed.value.find(opt => opt.value === status.toLowerCase())
  return option ? option.label : status
}

const viewFeedback = (feedback) => {
  selectedFeedback.value = feedback
  showDetailDialog.value = true
}

const editFeedback = (feedback) => {
  editForm.value = {
    id: feedback.id,
    status: feedback.status,
    resolution_notes: feedback.resolution_notes || ''
  }
  showEditDialog.value = true
}

const cancelEdit = () => {
  showEditDialog.value = false
  editForm.value = { id: null, status: 'new', resolution_notes: '' }
}

const updateFeedback = async () => {
  updating.value = true
  try {
    await proxy.$api.put(`/api/v1/admin/feedback/${editForm.value.id}`, {
      status: editForm.value.status,
      resolution_notes: editForm.value.resolution_notes
    })
    showSuccessNotification(t('notification.adminFeedbackUpdated'))
    cancelEdit()
    loadFeedback()
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('notification.adminFeedbackUpdateFailed'))
  } finally {
    updating.value = false
  }
}

// Lifecycle
onMounted(() => {
  loadFeedback()
})
</script>
