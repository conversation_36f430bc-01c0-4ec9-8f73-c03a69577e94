package handler

import (
	"errors"
	"fmt"

	// "fmt" // Removed: not used
	"io"
	"net/http"

	// "path/filepath" // Removed: not used
	"strconv"
	"strings"
	"syscall"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors" // Added for apperrors
	"github.com/Sakura-Byte/kikoeru-go/pkg/http/rest/common"
	"github.com/Sakura-Byte/kikoeru-go/pkg/log"
	"github.com/Sakura-Byte/kikoeru-go/pkg/ports" // Changed from service to ports
	"github.com/gin-gonic/gin"
)

type MediaHandler struct {
	mediaService ports.MediaService
}

// NewMediaHandler creates a new MediaHandler
func NewMediaHandler(mediaService ports.MediaService) *MediaHandler {
	return &MediaHandler{
		mediaService: mediaService,
	}
}

// isClientDisconnection checks if an error is due to client disconnection
func isClientDisconnection(err error) bool {
	if err == nil {
		return false
	}

	errStr := err.Error()

	// Common client disconnection patterns
	return strings.Contains(errStr, "connection was forcibly closed by the remote host") ||
		strings.Contains(errStr, "broken pipe") ||
		strings.Contains(errStr, "connection reset by peer") ||
		strings.Contains(errStr, "wsasend:") ||
		errors.Is(err, syscall.EPIPE) ||
		errors.Is(err, syscall.ECONNRESET)
}

// GetMediaStream handles GET /api/v1/media/stream/work/:workID/track/*trackPath
func (h *MediaHandler) GetMediaStream(c *gin.Context) {
	workIDStr := c.Param("workID")
	trackPath := c.Param("trackPath")
	if strings.HasPrefix(trackPath, "/") {
		trackPath = trackPath[1:]
	}

	workID, err := strconv.ParseUint(workIDStr, 10, 32)
	if err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid work ID format")
		return
	}
	if trackPath == "" {
		common.SendErrorResponse(c, http.StatusBadRequest, "Track path is required")
		return
	}

	stream, contentType, originalSize, actualLength, filename, httpStatus, err := h.mediaService.GetMediaStream(
		c.Request.Context(),
		uint(workID),
		trackPath,
		c.ClientIP(),
		c.GetHeader("Range"),
	)

	if err != nil {
		if errors.Is(err, apperrors.ErrWorkNotFound) || errors.Is(err, apperrors.ErrTrackNotFound) { // Use apperrors
			common.SendErrorResponse(c, http.StatusNotFound, err.Error())
		} else if errors.Is(err, apperrors.ErrMediaAccessDenied) { // Use apperrors
			common.SendErrorResponse(c, http.StatusForbidden, err.Error())
		} else if errors.Is(err, apperrors.ErrRangeNotSatisfiable) { // Use apperrors
			common.SendErrorResponse(c, http.StatusRequestedRangeNotSatisfiable, err.Error())
		} else {
			log.Error(c.Request.Context(), "Failed to get media stream", "work_id", workID, "track_path", trackPath, "error", err)
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to stream media")
		}
		return
	}
	defer stream.Close()

	if contentType != "" {
		c.Header("Content-Type", contentType)
	}
	if actualLength > 0 {
		c.Header("Content-Length", strconv.FormatInt(actualLength, 10))
	}

	// Add Accept-Ranges header to indicate range support for audio streaming
	c.Header("Accept-Ranges", "bytes")

	// Set Content-Disposition header with filename
	if filename != "" {
		disposition := fmt.Sprintf("inline; filename=\"%s\"", filename)
		c.Header("Content-Disposition", disposition)
	}

	// For partial content, set Content-Range header with correct total size
	if httpStatus == http.StatusPartialContent {
		rangeHeader := c.GetHeader("Range")
		if rangeHeader != "" {
			// Parse the range to get the start position
			// Range header format: "bytes=start-end"
			rangePart := strings.TrimPrefix(rangeHeader, "bytes=")
			if rangePart != rangeHeader {
				parts := strings.Split(rangePart, "-")
				if len(parts) >= 1 && parts[0] != "" {
					if start, err := strconv.ParseInt(parts[0], 10, 64); err == nil {
						end := start + actualLength - 1
						// Use originalSize instead of * for total file size
						contentRangeValue := fmt.Sprintf("bytes %d-%d/%d", start, end, originalSize)
						c.Header("Content-Range", contentRangeValue)
						log.Debug(c.Request.Context(), "Set Content-Range header", "range", contentRangeValue)
					}
				}
			}
		} else {
			// No range header but returning 206 - this should not happen with our driver fix
			if actualLength > 0 {
				contentRangeValue := fmt.Sprintf("bytes 0-%d/%d", actualLength-1, originalSize)
				c.Header("Content-Range", contentRangeValue)
				log.Debug(c.Request.Context(), "Set Content-Range header for full file", "range", contentRangeValue)
			}
		}
	}

	// For audio files, add cache control headers
	if strings.HasPrefix(contentType, "audio/") {
		c.Header("Cache-Control", "public, max-age=3600")
	}

	c.Status(httpStatus)

	if httpStatus != http.StatusNoContent && httpStatus != http.StatusNotModified {
		written, copyErr := io.Copy(c.Writer, stream)
		if copyErr != nil {
			// Check if this is a client disconnection (normal behavior)
			if isClientDisconnection(copyErr) {
				log.Info(c.Request.Context(), "Client disconnected during media stream", "work_id", workID, "track_path", trackPath, "bytes_written", written)
			} else {
				log.Error(c.Request.Context(), "Error copying media stream to response", "work_id", workID, "track_path", trackPath, "error", copyErr, "bytes_written", written)
			}
		} else {
			log.Debug(c.Request.Context(), "Media stream copied to response", "work_id", workID, "track_path", trackPath, "bytes_written", written)
		}
	}
}

// ServeCoverImageByOriginalID handles GET /api/v1/cover/{originalID}/{filename}
// ServeCoverImageByOriginalID handles GET /api/v1/cover/{originalID}?type={type}
func (h *MediaHandler) ServeCoverImageByOriginalID(c *gin.Context) {
	originalID := c.Param("originalID")
	coverType := c.Query("type")

	if originalID == "" {
		common.SendErrorResponse(c, http.StatusBadRequest, "OriginalID cannot be empty")
		return
	}

	var filename string
	switch coverType {
	case "main":
		filename = fmt.Sprintf("%s_img_main.jpg", originalID)
	case "sam":
		filename = fmt.Sprintf("%s_sam.jpg", originalID)
	default:
		// Default to main if type is not specified
		filename = fmt.Sprintf("%s_img_main.jpg", originalID)
	}

	// Assuming MediaService handles validation and security checks for filename
	coverPath, err := h.mediaService.GetCoverPathByOriginalIDAndFilename(c.Request.Context(), originalID, filename)
	if err != nil {
		if errors.Is(err, apperrors.ErrCoverNotFound) { // Use apperrors
			common.SendErrorResponse(c, http.StatusNotFound, "Cover image not found")
		} else if errors.Is(err, apperrors.ErrCoverDirNotConfigured) { // Use apperrors
			common.SendErrorResponse(c, http.StatusInternalServerError, "Cover directory not configured")
		} else {
			log.Error(c.Request.Context(), "Failed to get cover by originalID and filename", "original_id", originalID, "filename", filename, "error", err)
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to retrieve cover image")
		}
		return
	}

	if coverPath == "" {
		common.SendErrorResponse(c, http.StatusNotFound, "Cover image not available")
		return
	}
	c.File(coverPath)
}
