package apperrors

import "errors"

// Common application-level errors
var (
	ErrWorkNotFound                  = errors.New("work not found")
	ErrUserNotFound                  = errors.New("user not found")
	ErrInvalidCredentials            = errors.New("invalid username or password")
	ErrUserAlreadyExists             = errors.New("user with this username already exists")
	ErrWorkAlreadyExists             = errors.New("work with this original_id or path already exists")
	ErrAdminAccessDenied             = errors.New("admin access denied")
	ErrGuestActionNotAllowed         = errors.New("guest users are not allowed to perform this action")
	ErrEmailAlreadyLinkedAndVerified = errors.New("email is already linked to this account and verified")
	ErrEmailInUseByAnotherAccount    = errors.New("this email address is already in use by another account")
	ErrInvalidOrExpiredToken         = errors.New("token is invalid or has expired")
	ErrForbidden                     = errors.New("forbidden: you do not have permission to perform this action")

	// Playlist specific
	ErrPlaylistNotFound     = errors.New("playlist not found")
	ErrPlaylistItemNotFound = errors.New("playlist item not found")
	ErrNotPlaylistOwner     = errors.New("user is not the owner of the playlist")

	// Feedback specific
	ErrFeedbackNotFound = errors.New("feedback not found")

	// Review specific
	ErrReviewNotFound     = errors.New("review not found")
	ErrInvalidRatingValue = errors.New("invalid rating value")

	// Media specific errors
	ErrTrackNotFound         = errors.New("track not found")
	ErrMediaAccessDenied     = errors.New("media access denied")
	ErrRangeNotSatisfiable   = errors.New("range not satisfiable")
	ErrCoverNotFound         = errors.New("cover not found")
	ErrCoverDirNotConfigured = errors.New("covers directory not configured")
	ErrInvalidMediaRequest   = errors.New("invalid media request parameters")

	// FileSystem specific errors (also used by Storage)
	ErrStorageNotFound             = errors.New("storage source not found")
	ErrStorageSourceRemarkConflict = errors.New("storage source with this remark already exists")
	ErrPathNotFound                = errors.New("path not found in storage")
	ErrPathIsNotDir                = errors.New("path is not a directory")
	ErrPathIsDir                   = errors.New("path is a directory, expected a file")
	ErrFileNotFound                = errors.New("file not found in storage")
	ErrPathIsNotFile               = errors.New("path is not a file") // Added ErrPathIsNotFile

	// Archive specific errors
	ErrUnsupportedArchiveFormat = errors.New("unsupported archive format")
	ErrPasswordRequired         = errors.New("password required to extract this archive")
	ErrInvalidPassword          = errors.New("invalid password for this archive")
	ErrArchiveFileNotFound      = errors.New("file not found in archive")
	ErrArchiveEncodingError     = errors.New("archive encoding error")
	ErrArchivePasswordNotFound  = errors.New("archive password not found")
	ErrArchivePasswordExists    = errors.New("archive password already exists")

	// Play History specific
	ErrPlayHistoryNotFound = errors.New("play history record not found")

	// Metadata Admin specific (Tags, VAs, Circles)
	ErrTagNotFound         = errors.New("tag not found")
	ErrTagAlreadyExists    = errors.New("tag with this name already exists")
	ErrVANotFound          = errors.New("va not found")
	ErrVAAlreadyExists     = errors.New("va with this name or id already exists")
	ErrCircleNotFound      = errors.New("circle not found")
	ErrCircleAlreadyExists = errors.New("circle with this name already exists")

	// Generic validation error
	ErrValidation = errors.New("validation failed")
	// Other specific validation errors from user_service
	ErrRegistrationEmailInUse = errors.New("email is already in use by a verified account during registration")
	ErrEmailNotVerified       = errors.New("email not verified")
	ErrEmailNotLinked         = errors.New("email is not linked to this account")
	ErrRegistrationDisabled   = errors.New("user registration is disabled")
	ErrEmailRequired          = errors.New("email is required for registration when ensure_email is enabled")

	// Scraper specific errors
	ErrScrapingFailed        = errors.New("failed to scrape work data")
	ErrScrapingSourceOffline = errors.New("scraping source appears to be offline or unreachable")
	ErrScrapingRateLimited   = errors.New("rate limited by scraping source")
	ErrScrapingDataNotFound  = errors.New("data not found at scraping source for the given identifier")
	ErrScrapingLoginRequired = errors.New("login required to access data from scraping source")
	ErrScrapingInvalidData   = errors.New("invalid or unexpected data format from scraping source")
	ErrHTMLParsingFailed     = errors.New("failed to parse HTML content")
	ErrCoverDownloadFailed   = errors.New("failed to download cover image")

	// Background Task specific errors
	ErrTaskTypeNotSupported  = errors.New("background task type not supported")
	ErrTaskNotFound          = errors.New("background task not found")
	ErrTaskCannotBeCancelled = errors.New("task is not in a state that can be cancelled")
	ErrTaskActionForbidden   = errors.New("user is not authorized to perform this action on the task")

	// Storage Admin specific errors
	ErrStorageSourceOperationFailed = errors.New("storage source operation failed")
	ErrStorageSourceInvalidInput    = errors.New("invalid input for storage source operation")
	ErrStorageSourceDisabled        = errors.New("storage source is disabled")        // Added ErrStorageSourceDisabled
	ErrStorageDriverNotFound        = errors.New("storage driver not found")          // Added ErrStorageDriverNotFound
	ErrOperationNotSupported        = errors.New("operation not supported by driver") // Added ErrOperationNotSupported

	// Playlist service specific
	ErrPlaylistActionForbidden = errors.New("user does not have permission to perform this action on the playlist")
	ErrTrackPathNotFoundInWork = errors.New("specified track path not found in work's memo")

	// Play History service specific
	ErrInvalidPlayHistoryInput = errors.New("invalid input for play history operation")

	// Feedback service specific
	ErrFeedbackSubmissionFailed = errors.New("failed to submit feedback")
	ErrFeedbackUpdateFailed     = errors.New("failed to update feedback")
	ErrFeedbackAccessDenied     = errors.New("access to feedback resource denied")
	ErrFeedbackInvalidStatus    = errors.New("invalid feedback status provided")

	// Subtitle errors
	ErrSubtitleNotFound      = errors.New("subtitle not found")
	ErrInvalidSubtitleFormat = errors.New("invalid subtitle format")
	ErrSubtitleTooLarge      = errors.New("subtitle file is too large")
	ErrVoteNotFound          = errors.New("vote not found")
	ErrInvalidVoteValue      = errors.New("invalid vote value")

	ErrNotADirectory = errors.New("path is not a directory")
)

// ValidationError is a custom error type for validation failures.
type ValidationError struct {
	Message string
}

func (e *ValidationError) Error() string {
	return e.Message
}

// NewValidationError creates a new ValidationError.
func NewValidationError(message string) error {
	return &ValidationError{Message: message}
}
