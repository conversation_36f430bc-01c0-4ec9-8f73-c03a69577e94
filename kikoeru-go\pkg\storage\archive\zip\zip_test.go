package zip

import (
	"archive/zip"
	"bytes"
	"io"
	"testing"

	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
)

func TestZipBasicFunctionality(t *testing.T) {
	// Create a simple ZIP file in memory for testing
	var buf bytes.Buffer
	w := zip.NewWriter(&buf)

	// Add a test file
	f, err := w.Create("test.txt")
	if err != nil {
		t.Fatal(err)
	}
	_, err = f.Write([]byte("Hello, World!"))
	if err != nil {
		t.Fatal(err)
	}

	// Close the ZIP writer
	err = w.Close()
	if err != nil {
		t.Fatal(err)
	}

	// Create a StreamWithSeek from the buffer
	zipData := buf.Bytes()
	stream := &models.StreamWithSeek{
		ReadCloser: io.NopCloser(bytes.NewReader(zipData)),
		Seeker:     bytes.NewReader(zipData),
		Size:       int64(len(zipData)),
		Name:       "test.zip",
	}

	// Test the Zip handler
	z := &Zip{}

	// Test GetMeta
	args := models.ArchiveArgs{
		Filename: "test.zip",
		Encoding: "",
		Password: "",
	}

	meta, err := z.GetMeta(stream, args)
	if err != nil {
		t.Fatalf("GetMeta failed: %v", err)
	}

	if meta == nil {
		t.Fatal("GetMeta returned nil")
	}

	// Check that we found the test file
	tree := meta.GetTree()
	if len(tree) != 1 {
		t.Fatalf("Expected 1 file in archive, got %d", len(tree))
	}

	obj := tree[0].GetObject()
	if obj.GetName() != "test.txt" {
		t.Fatalf("Expected file name 'test.txt', got '%s'", obj.GetName())
	}

	t.Log("ZIP migration test passed successfully!")
}

func TestDecodeName(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		encoding string
		expected string
	}{
		{
			name:     "no encoding",
			input:    "test.txt",
			encoding: "",
			expected: "test.txt",
		},
		{
			name:     "utf-8 encoding",
			input:    "test.txt",
			encoding: "utf-8",
			expected: "test.txt",
		},
		{
			name:     "invalid encoding",
			input:    "test.txt",
			encoding: "invalid",
			expected: "test.txt",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := decodeName(tt.input, tt.encoding)
			if result != tt.expected {
				t.Errorf("decodeName(%q, %q) = %q, want %q", tt.input, tt.encoding, result, tt.expected)
			}
		})
	}
}
