package zip

import (
	"bytes"
	"io"
	"testing"

	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"github.com/yeka/zip"
)

// Helper function to create test data
func createTestData(size int) []byte {
	data := make([]byte, size)
	for i := range data {
		data[i] = byte(i % 256)
	}
	return data
}

// Helper function to create a StreamWithSeek from bytes
func createStreamFromBytes(data []byte, name string) *models.StreamWithSeek {
	reader := bytes.NewReader(data)
	return &models.StreamWithSeek{
		ReadCloser: io.NopCloser(reader),
		Seeker:     reader,
		Size:       int64(len(data)),
		Name:       name,
	}
}

func TestZipBasicFunctionality(t *testing.T) {
	// Create a simple ZIP file in memory for testing
	var buf bytes.Buffer
	w := zip.NewWriter(&buf)

	// Add a test file
	f, err := w.Create("test.txt")
	if err != nil {
		t.Fatal(err)
	}
	_, err = f.Write([]byte("Hello, World!"))
	if err != nil {
		t.Fatal(err)
	}

	// Close the ZIP writer
	err = w.Close()
	if err != nil {
		t.Fatal(err)
	}

	// Create a StreamWithSeek from the buffer
	zipData := buf.Bytes()
	reader := bytes.NewReader(zipData)
	stream := &models.StreamWithSeek{
		ReadCloser: io.NopCloser(reader),
		Seeker:     reader,
		Size:       int64(len(zipData)),
		Name:       "test.zip",
	}

	// Test the Zip handler
	z := &Zip{}

	// Test GetMeta
	args := models.ArchiveArgs{
		Filename: "test.zip",
		Encoding: "",
		Password: "",
	}

	meta, err := z.GetMeta(stream, args)
	if err != nil {
		t.Fatalf("GetMeta failed: %v", err)
	}

	if meta == nil {
		t.Fatal("GetMeta returned nil")
	}

	// Check that we found the test file
	tree := meta.GetTree()
	if len(tree) != 1 {
		t.Fatalf("Expected 1 file in archive, got %d", len(tree))
	}

	obj := tree[0].GetObject()
	if obj.GetName() != "test.txt" {
		t.Fatalf("Expected file name 'test.txt', got '%s'", obj.GetName())
	}

	t.Log("ZIP migration test passed successfully!")
}

func TestDecodeName(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		encoding string
		expected string
	}{
		{
			name:     "no encoding",
			input:    "test.txt",
			encoding: "",
			expected: "test.txt",
		},
		{
			name:     "utf-8 encoding",
			input:    "test.txt",
			encoding: "utf-8",
			expected: "test.txt",
		},
		{
			name:     "invalid encoding",
			input:    "test.txt",
			encoding: "invalid",
			expected: "test.txt",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := decodeName(tt.input, tt.encoding)
			if result != tt.expected {
				t.Errorf("decodeName(%q, %q) = %q, want %q", tt.input, tt.encoding, result, tt.expected)
			}
		})
	}
}

// TestZipWithPassword tests ZIP files with password protection
func TestZipWithPassword(t *testing.T) {
	// Create a password-protected ZIP file
	var buf bytes.Buffer
	w := zip.NewWriter(&buf)

	// Create a file with password protection
	f, err := w.Encrypt("secret.txt", "testpass", zip.AES256Encryption)
	if err != nil {
		t.Fatal(err)
	}

	testData := createTestData(1024)
	_, err = f.Write(testData)
	if err != nil {
		t.Fatal(err)
	}

	// Close the ZIP writer
	err = w.Close()
	if err != nil {
		t.Fatal(err)
	}

	// Create stream from the encrypted ZIP
	zipData := buf.Bytes()
	stream := createStreamFromBytes(zipData, "encrypted.zip")

	// Test the Zip handler
	z := &Zip{}

	// Test GetMeta with password
	args := models.ArchiveArgs{
		Filename: "encrypted.zip",
		Encoding: "",
		Password: "testpass",
	}

	meta, err := z.GetMeta(stream, args)
	if err != nil {
		t.Fatalf("GetMeta with password failed: %v", err)
	}

	if !meta.IsEncrypted() {
		t.Fatal("Expected encrypted archive")
	}

	// Test extraction with correct password
	stream2 := createStreamFromBytes(zipData, "encrypted.zip")
	extractArgs := models.ArchiveInnerArgs{
		ArchiveArgs: args,
		InnerPath:   "secret.txt",
	}

	rc, size, err := z.Extract(stream2, extractArgs)
	if err != nil {
		t.Fatalf("Extract with correct password failed: %v", err)
	}
	defer rc.Close()

	if size != int64(len(testData)) {
		t.Fatalf("Expected size %d, got %d", len(testData), size)
	}

	// Read and verify the extracted data
	extractedData, err := io.ReadAll(rc)
	if err != nil {
		t.Fatalf("Failed to read extracted data: %v", err)
	}

	if !bytes.Equal(extractedData, testData) {
		t.Fatal("Extracted data doesn't match original")
	}

	// Test extraction with wrong password
	stream3 := createStreamFromBytes(zipData, "encrypted.zip")
	wrongArgs := models.ArchiveInnerArgs{
		ArchiveArgs: models.ArchiveArgs{
			Filename: "encrypted.zip",
			Password: "wrongpass",
		},
		InnerPath: "secret.txt",
	}

	_, _, err = z.Extract(stream3, wrongArgs)
	if err == nil {
		t.Fatal("Expected error with wrong password")
	}

	t.Log("Password-protected ZIP test passed!")
}

// TestMultipartZip tests multipart ZIP files (.zip.001, .zip.002, etc.)
func TestMultipartZip(t *testing.T) {
	// Create test data
	testData1 := createTestData(2048)
	testData2 := createTestData(1536)

	// Create a ZIP file with multiple files
	var buf bytes.Buffer
	w := zip.NewWriter(&buf)

	// Add first file
	f1, err := w.Create("file1.txt")
	if err != nil {
		t.Fatal(err)
	}
	_, err = f1.Write(testData1)
	if err != nil {
		t.Fatal(err)
	}

	// Add second file
	f2, err := w.Create("file2.txt")
	if err != nil {
		t.Fatal(err)
	}
	_, err = f2.Write(testData2)
	if err != nil {
		t.Fatal(err)
	}

	err = w.Close()
	if err != nil {
		t.Fatal(err)
	}

	// Simulate splitting the ZIP into parts
	zipData := buf.Bytes()
	partSize := len(zipData) / 2

	// Create part 1 (.zip.001)
	part1Data := zipData[:partSize]
	stream1 := createStreamFromBytes(part1Data, "test.zip.001")

	// Create part 2 (.zip.002)
	part2Data := zipData[partSize:]
	stream2 := createStreamFromBytes(part2Data, "test.zip.002")

	// Test multipart functionality
	z := &Zip{}
	streams := []*models.StreamWithSeek{stream1, stream2}

	args := models.ArchiveArgs{
		Filename: "test.zip.001",
		Encoding: "",
		Password: "",
	}

	// Test GetMetaMultipart
	meta, err := z.GetMetaMultipart(streams, args)
	if err != nil {
		t.Fatalf("GetMetaMultipart failed: %v", err)
	}

	tree := meta.GetTree()
	if len(tree) != 2 {
		t.Fatalf("Expected 2 files in multipart archive, got %d", len(tree))
	}

	t.Log("Multipart ZIP test passed!")
}

// TestMultipartZipWithZ01Format tests multipart ZIP files (.zip, .z01, .z02, etc.)
func TestMultipartZipWithZ01Format(t *testing.T) {
	// Create test data
	testData := createTestData(4096)

	// Create a ZIP file
	var buf bytes.Buffer
	w := zip.NewWriter(&buf)

	f, err := w.Create("largefile.txt")
	if err != nil {
		t.Fatal(err)
	}
	_, err = f.Write(testData)
	if err != nil {
		t.Fatal(err)
	}

	err = w.Close()
	if err != nil {
		t.Fatal(err)
	}

	// Simulate splitting into .zip, .z01, .z02 format
	zipData := buf.Bytes()
	partSize := len(zipData) / 3

	// Create main part (.zip)
	mainPart := zipData[:partSize]
	streamMain := createStreamFromBytes(mainPart, "test.zip")

	// Create part 1 (.z01)
	part1 := zipData[partSize : 2*partSize]
	stream1 := createStreamFromBytes(part1, "test.z01")

	// Create part 2 (.z02)
	part2 := zipData[2*partSize:]
	stream2 := createStreamFromBytes(part2, "test.z02")

	// Test multipart functionality with z01 format
	z := &Zip{}
	streams := []*models.StreamWithSeek{streamMain, stream1, stream2}

	args := models.ArchiveArgs{
		Filename: "test.zip",
		Encoding: "",
		Password: "",
	}

	// Test GetMetaMultipart
	meta, err := z.GetMetaMultipart(streams, args)
	if err != nil {
		t.Fatalf("GetMetaMultipart with z01 format failed: %v", err)
	}

	tree := meta.GetTree()
	if len(tree) == 0 {
		t.Fatal("Expected files in multipart archive")
	}

	t.Log("Multipart ZIP with z01 format test passed!")
}

// TestMultipartZipWithPassword tests multipart ZIP files with password protection
func TestMultipartZipWithPassword(t *testing.T) {
	// Create test data
	testData := createTestData(3072)

	// Create a password-protected ZIP file
	var buf bytes.Buffer
	w := zip.NewWriter(&buf)

	// Create encrypted file
	f, err := w.Encrypt("protected.txt", "multipass", zip.AES256Encryption)
	if err != nil {
		t.Fatal(err)
	}
	_, err = f.Write(testData)
	if err != nil {
		t.Fatal(err)
	}

	err = w.Close()
	if err != nil {
		t.Fatal(err)
	}

	// Split into multiple parts
	zipData := buf.Bytes()
	partSize := len(zipData) / 2

	// Create parts
	part1Data := zipData[:partSize]
	stream1 := createStreamFromBytes(part1Data, "encrypted.zip.001")

	part2Data := zipData[partSize:]
	stream2 := createStreamFromBytes(part2Data, "encrypted.zip.002")

	// Test multipart encrypted ZIP
	z := &Zip{}
	streams := []*models.StreamWithSeek{stream1, stream2}

	args := models.ArchiveArgs{
		Filename: "encrypted.zip.001",
		Encoding: "",
		Password: "multipass",
	}

	// Test GetMetaMultipart with password
	meta, err := z.GetMetaMultipart(streams, args)
	if err != nil {
		t.Fatalf("GetMetaMultipart with password failed: %v", err)
	}

	if !meta.IsEncrypted() {
		t.Fatal("Expected encrypted multipart archive")
	}

	// Test extraction from multipart encrypted ZIP
	extractArgs := models.ArchiveInnerArgs{
		ArchiveArgs: args,
		InnerPath:   "protected.txt",
	}

	rc, size, err := z.ExtractMultipart(streams, extractArgs)
	if err != nil {
		t.Fatalf("ExtractMultipart with password failed: %v", err)
	}
	defer rc.Close()

	if size != int64(len(testData)) {
		t.Fatalf("Expected size %d, got %d", len(testData), size)
	}

	// Verify extracted data
	extractedData, err := io.ReadAll(rc)
	if err != nil {
		t.Fatalf("Failed to read extracted data: %v", err)
	}

	if !bytes.Equal(extractedData, testData) {
		t.Fatal("Extracted data doesn't match original")
	}

	t.Log("Multipart encrypted ZIP test passed!")
}

// TestZipAcceptedExtensions tests the accepted extensions
func TestZipAcceptedExtensions(t *testing.T) {
	z := &Zip{}

	extensions := z.AcceptedExtensions()
	if len(extensions) == 0 {
		t.Fatal("Expected at least one accepted extension")
	}

	found := false
	for _, ext := range extensions {
		if ext == ".zip" {
			found = true
			break
		}
	}
	if !found {
		t.Fatal("Expected .zip to be in accepted extensions")
	}

	// Test multipart extensions
	multipartExts := z.AcceptedMultipartExtensions()
	if len(multipartExts) == 0 {
		t.Fatal("Expected multipart extensions")
	}

	if _, ok := multipartExts[".zip"]; !ok {
		t.Fatal("Expected .zip multipart extension")
	}

	if _, ok := multipartExts[".zip.001"]; !ok {
		t.Fatal("Expected .zip.001 multipart extension")
	}

	t.Log("ZIP extensions test passed!")
}
