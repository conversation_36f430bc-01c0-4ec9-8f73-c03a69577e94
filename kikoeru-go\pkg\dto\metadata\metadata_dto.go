package metadata_dto

import (
	circle_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/circle"
	tag_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/tag"
	va_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/va" // Added va_dto import
)

// CircleWithWorkCount is a DTO for returning circles with their associated work count.
type CircleWithWorkCount struct {
	circle_dto.CircleDTO                   // Embed CircleDTO
	WorkCount            int64             `json:"count"`
	I18n                 map[string]string `json:"i18n"` // Placeholder for i18n
}

// TagWithWorkCount is a DTO for returning tags with their associated work count.
type TagWithWorkCount struct {
	tag_dto.TagDTO                   // Embed TagDTO
	WorkCount      int64             `json:"count"`
	I18n           map[string]string `json:"i18n"` // Placeholder for i18n
}

// VAWithWorkCount is a DTO for returning VAs with their associated work count.
type VAWithWorkCount struct {
	va_dto.VADTO                   // Embed VADTO
	WorkCount    int64             `json:"count"`
	I18n         map[string]string `json:"i18n"` // Placeholder for i18n
}
