package service

import (
	"fmt"
	"strings"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
)

// HTTPRange specifies the byte range to be sent to the client
type HTTPRange struct {
	Start, Length int64
}

// ParseRangeHeader parses a Range header string as per RFC 7233
func ParseRangeHeader(s string, size int64) ([]HTTPRange, error) {
	if s == "" {
		return nil, nil // No range header means no ranges to parse
	}
	const b = "bytes="
	if !strings.HasPrefix(s, b) {
		return nil, fmt.Errorf("invalid range header format")
	}

	var ranges []HTTPRange
	// Only process the first range specifier if multiple are present
	rangeSpec := strings.Split(s[len(b):], ",")[0]
	rangeSpec = strings.TrimSpace(rangeSpec)

	if rangeSpec == "" {
		return nil, fmt.Errorf("empty range specifier")
	}

	dashIndex := strings.Index(rangeSpec, "-")
	if dashIndex < 0 {
		return nil, fmt.Errorf("malformed range specifier (missing '-')")
	}

	startStr := strings.TrimSpace(rangeSpec[:dashIndex])
	endStr := strings.TrimSpace(rangeSpec[dashIndex+1:])

	var r HTTPRange
	var err error

	if startStr == "" {
		// RFC 7233, Section 4.2: "bytes=-<suffix-length>"
		// This means the last N bytes.
		if endStr == "" {
			return nil, fmt.Errorf("invalid suffix range (missing length)")
		}
		suffixLength, err := ParseInt64(endStr)
		if err != nil || suffixLength < 0 { // suffixLength must be non-negative
			return nil, fmt.Errorf("invalid suffix-length in range")
		}
		if suffixLength == 0 { // "bytes=-0" is valid but means no bytes
			r.Start = size
			r.Length = 0
			ranges = append(ranges, r)
			return ranges, nil
		}
		if suffixLength > size {
			r.Start = 0
			r.Length = size
		} else {
			r.Start = size - suffixLength
			r.Length = suffixLength
		}
	} else {
		r.Start, err = ParseInt64(startStr)
		if err != nil || r.Start < 0 {
			return nil, fmt.Errorf("invalid start in range")
		}
		if r.Start >= size && size > 0 { // If size is 0, start=0 is okay for a 0-length range
			return nil, apperrors.ErrRangeNotSatisfiable // Start is past the end of the content
		}

		if endStr == "" {
			// "bytes=<start>-" means from <start> to the end of the file.
			if r.Start >= size { // e.g. bytes=100- for a 50 byte file
				return nil, apperrors.ErrRangeNotSatisfiable
			}
			r.Length = size - r.Start
		} else {
			end, err := ParseInt64(endStr)
			if err != nil || end < r.Start {
				return nil, fmt.Errorf("invalid end in range")
			}
			if end >= size {
				end = size - 1 // Adjust end to be within bounds
			}
			r.Length = end - r.Start + 1
		}
	}
	if r.Length < 0 { // Should not happen with above logic but as a safeguard
		return nil, fmt.Errorf("calculated negative range length")
	}

	ranges = append(ranges, r)
	return ranges, nil
}

// ParseInt64 converts a string to int64
func ParseInt64(s string) (int64, error) {
	n := int64(0)
	for i := 0; i < len(s); i++ {
		if s[i] < '0' || s[i] > '9' {
			return 0, fmt.Errorf("invalid digit: %c", s[i])
		}
		n = n*10 + int64(s[i]-'0')
	}
	return n, nil
}
