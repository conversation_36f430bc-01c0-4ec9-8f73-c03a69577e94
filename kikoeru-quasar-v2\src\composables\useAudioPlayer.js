import { computed } from 'vue'
import { useAudioPlayerStore } from '../stores/audioPlayer'

export function useAudioPlayer() {
  const audioPlayerStore = useAudioPlayerStore()

  // Computed properties from state
  const hide = computed(() => audioPlayerStore.hide)
  const playing = computed(() => audioPlayerStore.playing)
  const currentTime = computed(() => audioPlayerStore.currentTime)
  const duration = computed(() => audioPlayerStore.duration)
  const source = computed(() => audioPlayerStore.source)
  const queue = computed(() => audioPlayerStore.queue)
  const queueIndex = computed(() => audioPlayerStore.queueIndex)
  const playMode = computed(() => audioPlayerStore.playMode)
  const muted = computed(() => audioPlayerStore.muted)
  const volume = computed(() => audioPlayerStore.volume)
  const currentLyric = computed(() => audioPlayerStore.currentLyric)
  const sleepTime = computed(() => audioPlayerStore.sleepTime)
  const sleepMode = computed(() => audioPlayerStore.sleepMode)
  const rewindSeekTime = computed(() => audioPlayerStore.rewindSeekTime)
  const forwardSeekTime = computed(() => audioPlayerStore.forwardSeekTime)
  const rewindSeekMode = computed(() => audioPlayerStore.rewindSeekMode)
  const forwardSeekMode = computed(() => audioPlayerStore.forwardSeekMode)
  const seekToTime = computed(() => audioPlayerStore.seekToTime)
  const pendingSeekTime = computed(() => audioPlayerStore.pendingSeekTime)
  const availableLyrics = computed(() => audioPlayerStore.availableLyrics)
  const currentLyricFile = computed(() => audioPlayerStore.currentLyricFile)
  const lyricContent = computed(() => audioPlayerStore.lyricContent)
  const enablePIPLyrics = computed(() => audioPlayerStore.enablePIPLyrics)

  // Computed properties from getters
  const currentPlayingFile = computed(() => audioPlayerStore.currentPlayingFile)
  const isQueueEmpty = computed(() => audioPlayerStore.isQueueEmpty)

  // Actions
  const toggleHide = () => audioPlayerStore.toggleHide()
  const play = () => audioPlayerStore.play()
  const pause = () => audioPlayerStore.pause()
  const togglePlaying = () => audioPlayerStore.togglePlaying()
  const setTrack = (index) => audioPlayerStore.setTrack(index)
  const setQueue = (options) => audioPlayerStore.setQueue(options)
  const addToQueue = (track) => audioPlayerStore.addToQueue(track)
  const removeFromQueue = (index) => audioPlayerStore.removeFromQueue(index)
  const emptyQueue = () => audioPlayerStore.emptyQueue()
  const nextTrack = () => audioPlayerStore.nextTrack()
  const previousTrack = () => audioPlayerStore.previousTrack()
  const changePlayMode = () => audioPlayerStore.changePlayMode()
  const setVolume = (vol) => audioPlayerStore.setVolume(vol)
  const setMuted = (muted) => audioPlayerStore.setMuted(muted)
  const setCurrentTime = (time) => audioPlayerStore.setCurrentTime(time)
  const setDuration = (dur) => audioPlayerStore.setDuration(dur)
  const setRewindSeekTime = (time) => audioPlayerStore.setRewindSeekTime(time)
  const setForwardSeekTime = (time) => audioPlayerStore.setForwardSeekTime(time)
  const setRewindSeekMode = (mode) => audioPlayerStore.setRewindSeekMode(mode)
  const setForwardSeekMode = (mode) => audioPlayerStore.setForwardSeekMode(mode)
  const setSleepTime = (time) => audioPlayerStore.setSleepTime(time)
  const setSleepMode = (mode) => audioPlayerStore.setSleepMode(mode)
  const setSeekToTime = (time) => audioPlayerStore.setSeekToTime(time)
  const setPendingSeekTime = (time) => audioPlayerStore.setPendingSeekTime(time)
  const setAvailableLyrics = (lyrics) => audioPlayerStore.setAvailableLyrics(lyrics)
  const setCurrentLyricFile = (file) => audioPlayerStore.setCurrentLyricFile(file)
  const setLyricContent = (content) => audioPlayerStore.setLyricContent(content)
  const setCurrentLyric = (lyric) => audioPlayerStore.setCurrentLyric(lyric)
  const setEnablePIPLyrics = (enable) => audioPlayerStore.setEnablePIPLyrics(enable)

  return {
    // State
    hide,
    playing,
    currentTime,
    duration,
    source,
    queue,
    queueIndex,
    playMode,
    muted,
    volume,
    currentLyric,
    sleepTime,
    sleepMode,
    rewindSeekTime,
    forwardSeekTime,
    rewindSeekMode,
    forwardSeekMode,
    seekToTime,
    pendingSeekTime,
    availableLyrics,
    currentLyricFile,
    lyricContent,
    enablePIPLyrics,

    // Getters
    currentPlayingFile,
    isQueueEmpty,

    // Actions
    toggleHide,
    play,
    pause,
    togglePlaying,
    setTrack,
    setQueue,
    addToQueue,
    removeFromQueue,
    emptyQueue,
    nextTrack,
    previousTrack,
    changePlayMode,
    setVolume,
    setMuted,
    setCurrentTime,
    setDuration,
    setRewindSeekTime,
    setForwardSeekTime,
    setRewindSeekMode,
    setForwardSeekMode,
    setSleepTime,
    setSleepMode,
    setSeekToTime,
    setPendingSeekTime,
    setAvailableLyrics,
    setCurrentLyricFile,
    setLyricContent,
    setCurrentLyric,
    setEnablePIPLyrics
  }
}
