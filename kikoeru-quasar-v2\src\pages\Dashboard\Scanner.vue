<template>
  <div class="q-pa-md">
    <div class="text-h5 text-weight-regular q-mb-lg">{{ t('dashboardScanner.title') }}</div>

    <!-- 扫描器状态 -->
    <q-card class="q-mb-lg">
      <q-card-section>
        <div class="text-h6 q-mb-md">{{ t('dashboardScanner.scannerStatus.title') }}</div>

        <div class="row q-col-gutter-md items-center">
          <div class="col-12 col-md-6">
            <q-list>
              <q-item>
                <q-item-section avatar>
                  <q-icon
                    :name="getStatusIcon(scannerStatus.status)"
                    :color="getStatusColor(scannerStatus.status)"
                    size="md"
                  />
                </q-item-section>
                <q-item-section>
                  <q-item-label>{{ t('dashboardScanner.scannerStatus.currentStatus') }}</q-item-label>
                  <q-item-label caption>
                    <q-chip
                      :color="getStatusColor(scannerStatus.status)"
                      text-color="white"
                      dense
                    >
                      {{ getStatusLabel(scannerStatus.status) }}
                    </q-chip>
                  </q-item-label>
                </q-item-section>
              </q-item>

              <q-item v-if="scannerStatus.current_storage_identifier">
                <q-item-section avatar>
                  <q-icon name="storage" color="primary" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>{{ t('dashboardScanner.scannerStatus.currentScanningStorage') }}</q-item-label>
                  <q-item-label caption>{{ scannerStatus.current_storage_identifier }}</q-item-label>
                </q-item-section>
              </q-item>

              <q-item v-if="scannerStatus.message">
                <q-item-section avatar>
                  <q-icon name="info" color="info" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>{{ t('dashboardScanner.scannerStatus.statusInfo') }}</q-item-label>
                  <q-item-label caption>{{ scannerStatus.message }}</q-item-label>
                </q-item-section>
              </q-item>

              <q-item v-if="scannerStatus.last_error">
                <q-item-section avatar>
                  <q-icon name="error" color="negative" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>{{ t('dashboardScanner.scannerStatus.lastError') }}</q-item-label>
                  <q-item-label caption class="text-negative">{{ scannerStatus.last_error }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </div>

          <div class="col-12 col-md-6" v-if="scannerStatus.total_works_in_lib > 0">
            <div class="text-subtitle2 q-mb-sm">
              {{ t('dashboardScanner.scannerStatus.scanProgressLabel') }}: {{ scannerStatus.processed_works }} / {{ scannerStatus.total_works_in_lib }}
            </div>
            <q-linear-progress
              :value="scanProgress"
              color="primary"
              size="20px"
              class="q-mb-sm"
            >
              <div class="absolute-full flex flex-center">
                <q-badge
                  color="white"
                  text-color="accent"
                  :label="`${Math.round(scanProgress * 100)}%`"
                />
              </div>
            </q-linear-progress>
          </div>
        </div>
      </q-card-section>

      <q-card-actions>
        <q-btn
          color="secondary"
          icon="refresh"
          :label="t('dashboardScanner.scannerStatus.refreshStatus')"
          @click="loadScannerStatus"
          :loading="loadingStatus"
        />
      </q-card-actions>
    </q-card>

    <!-- 手动扫描控制 -->
    <q-card class="q-mb-lg">
      <q-card-section>
        <div class="text-h6 q-mb-md">{{ t('dashboardScanner.manualScan.title') }}</div>

        <div class="row q-col-gutter-md">
          <div class="col-12 col-md-6">
            <q-select
              v-model="selectedStorageForScan"
              :options="storageOptionsComputed"
              option-label="label"
              option-value="value"
              emit-value
              map-options
              :label="t('dashboardScanner.manualScan.selectStorage')"
              dense
              outlined
              :hint="t('dashboardScanner.manualScan.selectStorageHint')"
            />
          </div>

          <div class="col-12 col-md-6">
            <div class="row q-gutter-sm">
              <q-btn
                color="primary"
                icon="search"
                :label="t('dashboardScanner.manualScan.scanSelectedStorage')"
                @click="startScan"
                :loading="startingScan"
                :disable="!selectedStorageForScan || isScanning"
              />

              <q-btn
                color="orange"
                icon="library_books"
                :label="t('dashboardScanner.manualScan.scanAllStorages')"
                @click="startFullScan"
                :loading="startingFullScan"
                :disable="isScanning"
              />
            </div>
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- 存储源列表 -->
    <q-card>
      <q-card-section>
        <div class="text-h6 q-mb-md">{{ t('dashboardScanner.storageList.title') }}</div>

        <div class="row justify-end q-mb-md">
          <q-btn
            color="secondary"
            icon="refresh"
            @click="loadStorages"
            :loading="loadingStorages"
          />
        </div>

        <q-table
          :rows="storages"
          :columns="storageTableColumns"
          row-key="id"
          :loading="loadingStorages"
          flat
          bordered
          class="shadow-2"
          :pagination="{ rowsPerPage: 0 }"
        >
          <template v-slot:body-cell-status="props">
            <q-td :props="props">
              <q-chip
                :color="props.row.disabled ? 'negative' : 'positive'"
                text-color="white"
                dense
              >
                {{ props.row.disabled ? t('dashboardScanner.storageList.statusDisabled') : t('dashboardScanner.storageList.statusEnabled') }}
              </q-chip>
            </q-td>
          </template>

          <template v-slot:body-cell-actions="props">
            <q-td :props="props">
              <div class="row q-gutter-xs">
                <q-btn
                  dense
                  round
                  color="primary"
                  icon="search"
                  size="sm"
                  @click="scanStorage(props.row)"
                  :loading="scanningStorages[props.row.id]"
                  :disable="props.row.disabled || isScanning"
                >
                  <q-tooltip>{{ t('dashboardScanner.storageList.scanThisStorage') }}</q-tooltip>
                </q-btn>

                <q-btn
                  dense
                  round
                  color="info"
                  icon="settings"
                  size="sm"
                  @click="router.push('/admin/storages')"
                >
                  <q-tooltip>{{ t('dashboardScanner.storageList.manageStorages') }}</q-tooltip>
                </q-btn>
              </div>
            </q-td>
          </template>
        </q-table>
      </q-card-section>
    </q-card>

    <!-- 扫描确认对话框 -->
    <q-dialog v-model="showScanConfirm" persistent>
      <q-card>
        <q-card-section>
          <div class="text-h6">{{ t('dashboardScanner.scanConfirmDialog.title') }}</div>
          <div class="text-body2">{{ t('dashboardScanner.scanConfirmDialog.message') }}</div>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="t('common.cancel')" v-close-popup />
          <q-btn flat :label="t('dashboardScanner.scanConfirmDialog.startScan')" color="primary" @click="confirmScan" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, getCurrentInstance } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { useNotification } from '../../composables/useNotification'

defineOptions({
  name: 'ScannerPage'
})

const { t, locale } = useI18n()
const router = useRouter()
const { proxy } = getCurrentInstance()
const { showSuccessNotification, showErrorNotification } = useNotification()

// Reactive data
const scannerStatus = ref({
  status: 'Idle',
  current_storage_identifier: '',
  processed_works: 0,
  total_works_in_lib: 0,
  last_error: '',
  message: ''
})
const storages = ref([])
const storageOptionsData = ref([])
const loadingStatus = ref(false)
const loadingStorages = ref(false)
const startingScan = ref(false)
const startingFullScan = ref(false)
const scanningStorages = ref({})
const selectedStorageForScan = ref(null)
const showScanConfirm = ref(false)
const scanConfirmCallback = ref(null)
const statusRefreshTimer = ref(null)

// Computed properties
const scanProgress = computed(() => {
  if (scannerStatus.value.total_works_in_lib > 0) {
    return scannerStatus.value.processed_works / scannerStatus.value.total_works_in_lib
  }
  return 0
})

const isScanning = computed(() => {
  return ['Scanning', 'Scraping'].includes(scannerStatus.value.status)
})

const storageOptionsComputed = computed(() => {
  return storageOptionsData.value.map(storage => ({
    label: `${storage.remark || t('dashboardScanner.unnamedStorage')} (${storage.driver}) [ID: ${storage.id}]`,
    value: storage.id.toString()
  }))
})

const storageTableColumns = computed(() => [
  {
    name: 'remark',
    label: t('dashboardScanner.storageList.table.remark'),
    field: 'remark',
    sortable: true,
    align: 'left'
  },
  {
    name: 'driver',
    label: t('dashboardScanner.storageList.table.driver'),
    field: 'driver',
    sortable: true,
    align: 'center'
  },
  {
    name: 'status',
    label: t('dashboardScanner.storageList.table.status'),
    field: 'disabled',
    sortable: true,
    align: 'center'
  },
  {
    name: 'last_scanned_at',
    label: t('dashboardScanner.storageList.table.lastScannedAt'),
    field: 'last_scanned_at',
    sortable: true,
    align: 'center',
    format: val => (val ? new Date(val).toLocaleString(locale.value) : t('dashboardScanner.neverScanned'))
  },
  {
    name: 'actions',
    label: t('dashboardScanner.storageList.table.actions'),
    field: 'actions',
    align: 'center'
  }
])

const loadScannerStatus = async () => {
  try {
    const response = await proxy.$api.get('/api/v1/admin/scanner/status')
    scannerStatus.value = response.data
  } catch (error) {
    console.error('Failed to load scanner status:', error.response?.data?.error || error.message)
  }
}

const loadStorages = async () => {
  loadingStorages.value = true
  try {
    const response = await proxy.$api.get('/api/v1/admin/storages')
    storageOptionsData.value = response.data || []
    storages.value = response.data || []
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('notification.adminLoadStoragesFailed'))
  } finally {
    loadingStorages.value = false
  }
}

const getStatusLabel = (status) => {
  const key = `dashboardScanner.statusLabels.${status.toLowerCase()}`
  const label = t(key)
  return label === key ? status : label
}

const getStatusColor = (status) => {
  const colors = {
    Idle: 'grey',
    Scanning: 'blue',
    Scraping: 'purple',
    Completed: 'green',
    Failed: 'red',
    Error: 'red'
  }
  return colors[status] || 'grey'
}

const getStatusIcon = (status) => {
  const icons = {
    Idle: 'pause_circle_outline',
    Scanning: 'search',
    Scraping: 'document_scanner',
    Completed: 'check_circle_outline',
    Failed: 'error_outline',
    Error: 'report_problem'
  }
  return icons[status] || 'help_outline'
}

const startScanAction = (actionAsync) => {
  scanConfirmCallback.value = actionAsync
  showScanConfirm.value = true
}

const confirmScan = () => {
  if (scanConfirmCallback.value) {
    scanConfirmCallback.value()
  }
  showScanConfirm.value = false
  scanConfirmCallback.value = null
}

const startActualScan = async (storageId = null) => {
  const endpoint = storageId
    ? '/api/v1/admin/tasks/scan-library'
    : '/api/v1/admin/tasks/scrape-all-works'
  const successMsg = storageId ? t('notification.adminScanSpecificStarted') : t('notification.adminScanAllStarted')
  const errorMsg = storageId ? t('notification.adminScanSpecificFailed') : t('notification.adminScanAllFailed')

  if (storageId) {
    startingScan.value = true
    scanningStorages.value[storageId] = true
  } else {
    startingFullScan.value = true
  }

  try {
    await proxy.$api.post(endpoint, storageId ? { storage_id: storageId.toString() } : undefined)
    showSuccessNotification(successMsg)
    loadScannerStatus()
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || errorMsg)
  } finally {
    if (storageId) {
      startingScan.value = false
      scanningStorages.value = {
        ...scanningStorages.value,
        [storageId]: false
      }
    } else {
      startingFullScan.value = false
    }
  }
}

const startScan = () => {
  if (!selectedStorageForScan.value) return
  startScanAction(() => startActualScan(selectedStorageForScan.value))
}

const startFullScan = () => {
  startScanAction(() => startActualScan())
}

const scanStorage = (storage) => {
  startScanAction(() => startActualScan(storage.id))
}

// Lifecycle
onMounted(() => {
  loadScannerStatus()
  loadStorages()
  statusRefreshTimer.value = setInterval(loadScannerStatus, 5000)
})

onBeforeUnmount(() => {
  if (statusRefreshTimer.value) {
    clearInterval(statusRefreshTimer.value)
  }
})
</script>
