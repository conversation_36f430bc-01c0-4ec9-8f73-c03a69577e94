<template>
  <div class="q-pa-md">
    <div class="text-h5 text-weight-regular q-mb-lg">{{ t('dashboardHome.title') }}</div>

    <div class="row q-col-gutter-md">
      <!-- 系统统计 -->
      <div class="col-12 col-md-6 col-lg-3">
        <q-card class="text-center">
          <q-card-section>
            <q-icon name="library_music" size="3rem" color="primary" />
            <div class="text-h6 q-mt-sm">{{ t('dashboardHome.stats.totalWorks') }}</div>
            <div class="text-h4 text-primary">{{ stats.totalWorks || 0 }}</div>
          </q-card-section>
        </q-card>
      </div>

      <div class="col-12 col-md-6 col-lg-3">
        <q-card class="text-center">
          <q-card-section>
            <q-icon name="people" size="3rem" color="secondary" />
            <div class="text-h6 q-mt-sm">{{ t('dashboardHome.stats.totalUsers') }}</div>
            <div class="text-h4 text-secondary">{{ stats.totalUsers || 0 }}</div>
          </q-card-section>
        </q-card>
      </div>

      <div class="col-12 col-md-6 col-lg-3">
        <q-card class="text-center">
          <q-card-section>
            <q-icon name="storage" size="3rem" color="info" />
            <div class="text-h6 q-mt-sm">{{ t('dashboardHome.stats.totalStorages') }}</div>
            <div class="text-h4 text-info">{{ stats.totalStorages || 0 }}</div>
          </q-card-section>
        </q-card>
      </div>

      <div class="col-12 col-md-6 col-lg-3">
        <q-card class="text-center">
          <q-card-section>
            <q-icon name="task" size="3rem" color="warning" />
            <div class="text-h6 q-mt-sm">{{ t('dashboardHome.stats.runningTasks') }}</div>
            <div class="text-h4 text-warning">{{ stats.runningTasks || 0 }}</div>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="q-mt-lg">
      <div class="text-h6 q-mb-md">{{ t('dashboardHome.quickActions.title') }}</div>
      <div class="row q-gutter-md">
        <q-btn
          color="primary"
          icon="library_books"
          :label="t('dashboardHome.quickActions.scanLibrary')"
          @click="router.push('/admin/tasks')"
        />
        <q-btn
          color="secondary"
          icon="storage"
          :label="t('dashboardHome.quickActions.manageStorages')"
          @click="router.push('/admin/storages')"
        />
        <q-btn
          color="info"
          icon="people"
          :label="t('dashboardHome.quickActions.userManagement')"
          @click="router.push('/admin/usermanage')"
        />
        <q-btn
          color="warning"
          icon="feedback"
          :label="t('dashboardHome.quickActions.feedbackManagement')"
          @click="router.push('/admin/feedback')"
        />
      </div>
    </div>

    <!-- 最近任务 -->
    <div class="q-mt-lg">
      <div class="text-h6 q-mb-md">{{ t('dashboardHome.recentTasks.title') }}</div>
      <q-table
        :rows="recentTasks"
        :columns="taskTableColumns"
        row-key="id"
        :loading="loadingTasks"
        flat
        bordered
        class="shadow-2"
        :pagination="{ rowsPerPage: 5 }"
      >
        <template v-slot:body-cell-status="props">
          <q-td :props="props">
            <q-chip
              :color="getStatusColor(props.row.status)"
              text-color="white"
              dense
            >
              {{ getStatusLabel(props.row.status) }}
            </q-chip>
          </q-td>
        </template>
      </q-table>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, getCurrentInstance } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { useNotification } from '../../composables/useNotification'

defineOptions({
  name: 'DashboardHomePage'
})

const { t, locale } = useI18n()
const router = useRouter()
const { proxy } = getCurrentInstance()
const { showErrorNotification } = useNotification()

// Reactive data
const stats = ref({
  totalWorks: 0,
  totalUsers: 0,
  totalStorages: 0,
  runningTasks: 0
})
const recentTasks = ref([])
const loadingTasks = ref(false)

// Computed properties
const taskTableColumns = computed(() => [
  {
    name: 'task_type',
    label: t('dashboardHome.recentTasks.table.taskType'),
    field: 'task_type',
    align: 'left'
  },
  {
    name: 'status',
    label: t('dashboardHome.recentTasks.table.status'),
    field: 'status',
    align: 'center'
  },
  {
    name: 'created_at',
    label: t('dashboardHome.recentTasks.table.createdAt'),
    field: 'created_at',
    align: 'center',
    format: val => new Date(val).toLocaleString(locale.value)
  }
])

// Methods
const loadStats = async () => {
  try {
    // Get total works count
    const worksResponse = await proxy.$api.get('/api/v1/works', { params: { page_size: 1 } })
    stats.value.totalWorks = worksResponse.data.pagination?.total_items || 0

    // Get users count
    const usersResponse = await proxy.$api.get('/api/v1/admin/users', { params: { page_size: 1 } })
    stats.value.totalUsers = usersResponse.data.pagination?.total_items || 0

    // Get storages count
    const storagesResponse = await proxy.$api.get('/api/v1/admin/storages')
    if (storagesResponse.data?.items) {
      stats.value.totalStorages = storagesResponse.data.items.length || 0
    } else if (Array.isArray(storagesResponse.data)) {
      stats.value.totalStorages = storagesResponse.data.length || 0
    } else {
      console.warn('Unexpected storage response format:', storagesResponse.data)
      stats.value.totalStorages = 0
    }

    // Get running tasks count
    const tasksResponse = await proxy.$api.get('/api/v1/admin/tasks', {
      params: { page_size: 1, status: 'running' }
    })
    stats.value.runningTasks = tasksResponse.data.pagination?.total_items || 0

    console.log('Dashboard stats loaded:', stats.value)
  } catch (error) {
    console.warn('Failed to load stats:', error)
    showErrorNotification(t('notification.adminLoadDashboardStatsFailed'))
  }
}

const loadRecentTasks = async () => {
  loadingTasks.value = true
  try {
    const response = await proxy.$api.get('/api/v1/admin/tasks', {
      params: { page_size: 5, page: 1 }
    })
    recentTasks.value = response.data.items || []
  } catch (error) {
    console.warn('Failed to load recent tasks:', error)
    showErrorNotification(t('notification.adminLoadRecentTasksFailed'))
  } finally {
    loadingTasks.value = false
  }
}

const getStatusColor = (status) => {
  const colors = {
    'pending': 'orange',
    'running': 'blue',
    'completed': 'green',
    'failed': 'red',
    'cancelled': 'grey'
  }
  return colors[status.toLowerCase()] || 'grey'
}

const getStatusLabel = (status) => {
  const key = `adminTasks.statusLabels.${status.toLowerCase()}`
  const label = t(key)
  return label === key ? status : label // Fallback to status if translation is missing
}

// Lifecycle
onMounted(() => {
  loadStats()
  loadRecentTasks()
})
</script>
