package service

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"time"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
	"github.com/Sakura-Byte/kikoeru-go/pkg/auth"
	"github.com/Sakura-Byte/kikoeru-go/pkg/config"
	feedback_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/feedback"
	"github.com/Sakura-Byte/kikoeru-go/pkg/log"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"github.com/Sakura-Byte/kikoeru-go/pkg/ports"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/database"
	"github.com/go-playground/validator/v10"
)

type feedbackService struct {
	repo      database.FeedbackRepository
	userRepo  database.UserRepository // Added for checking user existence
	appConfig *config.AppConfig
	validate  *validator.Validate
}

// Ensure feedbackService implements ports.FeedbackService
var _ ports.FeedbackService = (*feedbackService)(nil)

func NewFeedbackService(
	repo database.FeedbackRepository,
	userRepo database.UserRepository,
	appConfig *config.AppConfig,
) ports.FeedbackService {
	return &feedbackService{
		repo:      repo,
		userRepo:  userRepo,
		appConfig: appConfig,
		validate:  validator.New(),
	}
}

func (s *feedbackService) SubmitFeedback(ctx context.Context, req feedback_dto.FeedbackSubmissionRequest, userID *string, ipAddress string) (*models.Feedback, error) {
	if err := s.validate.Struct(req); err != nil {
		return nil, fmt.Errorf("%w: %v", apperrors.ErrValidation, err)
	}

	// If userID is provided, verify the user exists
	if userID != nil && *userID != "" {
		_, err := s.userRepo.GetByID(ctx, *userID)
		if err != nil {
			if errors.Is(err, apperrors.ErrUserNotFound) {
				return nil, fmt.Errorf("submitting user with ID %s not found: %w", *userID, err)
			}
			return nil, fmt.Errorf("failed to verify submitting user: %w", err)
		}
	}

	feedback := &models.Feedback{
		Category: req.Type, // Changed from Type to Category
		// Subject is merged into Content
		Content: fmt.Sprintf("Subject: %s\n\n%s", req.Subject, req.Message), // Merged Subject and Message
		Status:  models.FeedbackStatusNew,                                   // Changed from FeedbackStatusOpen
		// SubmitterIP: sql.NullString{String: ipAddress, Valid: ipAddress != ""}, // Field does not exist in models.Feedback
	}

	if userID != nil {
		feedback.UserID = sql.NullString{String: *userID, Valid: *userID != ""} // Changed from SubmittedByUserID
	}
	if req.Email != "" {
		feedback.Email = sql.NullString{String: req.Email, Valid: true} // Changed from SubmitterEmail
	}

	err := s.repo.Create(ctx, feedback)
	if err != nil {
		log.Error(ctx, "Failed to create feedback in repository", "error", err)
		return nil, fmt.Errorf("failed to submit feedback: %w", err)
	}
	log.Info(ctx, "Feedback submitted successfully", "feedback_id", feedback.ID)
	return feedback, nil
}

func (s *feedbackService) ListFeedback(ctx context.Context, params database.ListFeedbackParams, claims *auth.Claims) ([]*models.Feedback, int64, error) {
	// Authorization check (already done by middleware, but good for service layer too)
	if claims == nil || claims.UserGroup != models.UserGroupAdmin {
		return nil, 0, apperrors.ErrAdminAccessDenied
	}

	// Apply default pagination if not provided
	if params.Page <= 0 {
		params.Page = 1
	}
	if params.PageSize <= 0 {
		params.PageSize = s.appConfig.Server.PageSize // Use default from config
	}
	if params.PageSize > 100 { // Max page size limit
		params.PageSize = 100
	}

	feedbacks, total, err := s.repo.List(ctx, params)
	if err != nil {
		log.Error(ctx, "Failed to list feedback from repository", "params", params, "error", err)
		return nil, 0, fmt.Errorf("failed to retrieve feedback list: %w", err)
	}
	return feedbacks, total, nil
}

func (s *feedbackService) GetFeedbackByID(ctx context.Context, feedbackID uint, claims *auth.Claims) (*models.Feedback, error) {
	// Authorization check
	if claims == nil || claims.UserGroup != models.UserGroupAdmin {
		return nil, apperrors.ErrAdminAccessDenied
	}

	feedback, err := s.repo.GetByID(ctx, feedbackID)
	if err != nil {
		if errors.Is(err, apperrors.ErrFeedbackNotFound) {
			return nil, apperrors.ErrFeedbackNotFound
		}
		log.Error(ctx, "Failed to get feedback by ID from repository", "feedback_id", feedbackID, "error", err)
		return nil, fmt.Errorf("failed to retrieve feedback: %w", err)
	}
	return feedback, nil
}

func (s *feedbackService) UpdateFeedback(ctx context.Context, feedbackID uint, req feedback_dto.UpdateFeedbackRequest, adminUserID string) (*models.Feedback, error) {
	// Authorization check (adminUserID implies admin, but could be more robust)
	// For now, assume admin check is done by handler or middleware using claims.
	// Here, we just ensure adminUserID is provided.
	if adminUserID == "" {
		return nil, apperrors.ErrAdminAccessDenied // Or a more specific error like "admin user ID required"
	}

	feedback, err := s.repo.GetByID(ctx, feedbackID)
	if err != nil {
		if errors.Is(err, apperrors.ErrFeedbackNotFound) {
			return nil, apperrors.ErrFeedbackNotFound
		}
		log.Error(ctx, "Failed to get feedback for update", "feedback_id", feedbackID, "error", err)
		return nil, fmt.Errorf("failed to retrieve feedback for update: %w", err)
	}

	updated := false
	if req.Status != nil && *req.Status != "" && feedback.Status != *req.Status {
		// Validate status
		switch *req.Status {
		case models.FeedbackStatusNew, models.FeedbackStatusInProgress, models.FeedbackStatusClosed, models.FeedbackStatusWontFix, models.FeedbackStatusResolved: // Added Resolved, changed Open to New, Rejected to WontFix
			feedback.Status = *req.Status
			if *req.Status == models.FeedbackStatusClosed || *req.Status == models.FeedbackStatusWontFix || *req.Status == models.FeedbackStatusResolved {
				now := time.Now()
				feedback.ResolvedAt = sql.NullTime{Time: now, Valid: true}
			} else {
				feedback.ResolvedAt = sql.NullTime{Valid: false}
			}
			updated = true
		default:
			return nil, fmt.Errorf("%w: invalid feedback status '%s'", apperrors.ErrValidation, *req.Status)
		}
	}

	if req.AdminNotes != nil {
		if feedback.ResolutionNotes.String != *req.AdminNotes || !feedback.ResolutionNotes.Valid {
			feedback.ResolutionNotes = sql.NullString{String: *req.AdminNotes, Valid: *req.AdminNotes != ""}
			updated = true
		}
	}

	if !updated {
		log.Info(ctx, "No changes detected for feedback update", "feedback_id", feedbackID)
		return feedback, nil
	}

	feedback.UpdatedAt = time.Now() // Ensure UpdatedAt is set
	err = s.repo.Update(ctx, feedback)
	if err != nil {
		log.Error(ctx, "Failed to update feedback in repository", "feedback_id", feedbackID, "error", err)
		return nil, fmt.Errorf("failed to update feedback: %w", err)
	}
	log.Info(ctx, "Feedback updated successfully", "feedback_id", feedbackID, "admin_user_id", adminUserID)
	return feedback, nil
}
