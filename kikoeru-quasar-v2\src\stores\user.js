import { defineStore } from 'pinia'
import { LocalStorage, Notify } from 'quasar'

export const useUserStore = defineStore('user', {
  state: () => ({
    auth: false,        // 用户是否已认证
    id: null,          // 用户 ID (UUID string)
    username: '',      // 用户名
    email: '',         // 用户邮箱
    email_verified: false, // 邮箱是否已验证
    group: '',         // 用户组
    token: null,
    created_at: '',    // 用户创建时间
    updated_at: '',    // 用户信息更新时间
    showLoginDialog: false // Added for controlling login dialog globally
  }),

  getters: {
    isLoggedIn: (state) => state.auth && state.token,
    isAdmin: (state) => state.group === 'admin',
    isEmailVerified: (state) => state.email_verified,
    hasEmail: (state) => !!state.email,
    user: (state) => {
      // Only return user data if authenticated
      if (!state.auth) return null

      return {
        id: state.id,
        username: state.username,
        email: state.email,
        email_verified: state.email_verified,
        group: state.group,
        created_at: state.created_at,
        updated_at: state.updated_at
      }
    }
  },

  actions: {
    init(userPayload) {
      this.id = userPayload.id || null
      this.username = userPayload.username || ''
      this.email = userPayload.email || ''
      this.email_verified = userPayload.email_verified || false
      this.group = userPayload.group || ''
      this.created_at = userPayload.created_at || ''
      this.updated_at = userPayload.updated_at || ''
      // Don't reset auth and token here - they are set separately
      // The showLoginDialog state should also not be reset here
    },

    setAuth(flag) {
      this.auth = !!flag // Ensure it's a boolean
    },

    setToken(token) {
      this.token = token || ''
    },

    updateUsername(username) {
      this.username = username
    },

    setShowLoginDialog(show) {
      this.showLoginDialog = show
    },

    clearUserData() {
      this.auth = false
      this.id = null
      this.username = ''
      this.email = ''
      this.email_verified = false
      this.group = ''
      this.token = null
      this.created_at = ''
      this.updated_at = ''
      this.showLoginDialog = false
    },

    async fetchCurrentUser(axios) {
      return new Promise((resolve, reject) => {
        const token = LocalStorage.getItem('jwt-token')
        if (!token) {
          this.logout({ silent: true })
          return resolve(null)
        }

        // Use new API endpoint
        axios.get('/api/v1/auth/me', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })
          .then(response => {
            // Handle both wrapped and direct response formats
            const user = response.data || response.data
            if (user) {
              console.log('Fetched user data:', user) // Debug log
              this.init(user)
              this.setAuth(true)
              this.setToken(token)
              resolve(user)
            } else {
              this.logout({ silent: true })
              reject(new Error('Failed to fetch user data.'))
            }
          })
          .catch(error => {
            this.logout({ silent: true })
            reject(error)
          })
      })
    },

    async login({ credentials, axios }) {
      return new Promise((resolve, reject) => {
        axios.post('/api/v1/auth/login', credentials)
          .then(response => {
            // Handle both wrapped and direct response formats for login
            const responseData = response.data || response.data
            const token = responseData.token
            const user = responseData.user

            if (!token) {
              reject(new Error('No token received from login response'))
              return
            }

            LocalStorage.set('jwt-token', token)

            this.init(user)
            this.setAuth(true)
            this.setToken(token)

            console.log('Login successful, user info:', user) // Debug log

            resolve(response)
          })
          .catch(error => {
            LocalStorage.remove('jwt-token')
            this.clearUserData()
            reject(error)
          })
      })
    },

    async register({ userData, axios }) {
      return new Promise((resolve, reject) => {
        axios.post('/api/v1/auth/register', userData)
          .then(response => {
            resolve(response)
          })
          .catch(error => {
            reject(error)
          })
      })
    },

    logout({ silent = false } = {}) {
      this.clearUserData()
      LocalStorage.remove('jwt-token')
      if (!silent) {
        Notify.create({ type: 'info', message: '已登出。' })
      }
    },

    async refresh(axios) {
      return this.fetchCurrentUser(axios)
    },

    async changePassword({ passwordData, axios }) {
      return new Promise((resolve, reject) => {
        axios.post('/api/v1/me/password/change', passwordData, {
          headers: {
            'Authorization': `Bearer ${this.token}`
          }
        })
          .then(response => {
            resolve(response)
          })
          .catch(error => {
            reject(error)
          })
      })
    }
  }
})
