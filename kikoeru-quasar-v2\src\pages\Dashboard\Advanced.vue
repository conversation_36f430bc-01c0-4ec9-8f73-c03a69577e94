<template>
  <div class="q-pa-md">
    <div class="text-h5 text-weight-regular q-mb-lg">{{ t('dashboardAdvanced.title') }}</div>

    <!-- 系统配置 -->
    <q-card class="q-mb-lg">
      <q-card-section>
        <div class="text-h6 q-mb-md">{{ t('dashboardAdvanced.systemConfig.title') }}</div>

        <div class="row q-col-gutter-md">
          <div class="col-12 col-md-6">
            <q-input
              v-model="systemConfig.site_name"
              :label="t('dashboardAdvanced.systemConfig.siteName')"
              dense
              outlined
              :hint="t('dashboardAdvanced.systemConfig.siteNameHint')"
            />
          </div>

          <div class="col-12 col-md-6">
            <q-toggle
              v-model="systemConfig.allow_registration"
              :label="t('dashboardAdvanced.systemConfig.allowRegistration')"
              color="primary"
            />
          </div>

          <div class="col-12 col-md-6">
            <q-toggle
              v-model="systemConfig.enable_email_features"
              :label="t('dashboardAdvanced.systemConfig.enableEmailFeatures')"
              color="primary"
            />
          </div>

          <div class="col-12 col-md-6">
            <q-toggle
              v-model="systemConfig.ensure_email"
              :label="t('dashboardAdvanced.systemConfig.forceEmailVerification')"
              color="primary"
              :disable="!systemConfig.enable_email_features"
            />
          </div>
        </div>

        <div class="q-mt-md">
          <q-btn
            color="primary"
            icon="save"
            :label="t('dashboardAdvanced.systemConfig.saveButton')"
            @click="saveSystemConfig"
            :loading="savingConfig"
          />
        </div>
      </q-card-section>
    </q-card>

    <!-- 缓存管理 -->
    <q-card class="q-mb-lg">
      <q-card-section>
        <div class="text-h6 q-mb-md">{{ t('dashboardAdvanced.cacheManagement.title') }}</div>

        <div class="row q-col-gutter-md">
          <div class="col-12 col-md-4">
            <q-card flat bordered>
              <q-card-section class="text-center">
                <q-icon name="cached" size="3rem" color="primary" />
                <div class="text-h6 q-mt-sm">{{ t('dashboardAdvanced.cacheManagement.coverCache') }}</div>
                <div class="text-body2 text-grey-6">{{ cacheInfo.cover_cache_size || t('dashboardAdvanced.cacheManagement.calculating') }}</div>
                <q-btn
                  color="orange"
                  flat
                  :label="t('dashboardAdvanced.cacheManagement.clearCoverCache')"
                  @click="clearCoverCache"
                  :loading="clearingCoverCache"
                  class="q-mt-sm"
                />
              </q-card-section>
            </q-card>
          </div>

          <div class="col-12 col-md-4">
            <q-card flat bordered>
              <q-card-section class="text-center">
                <q-icon name="storage" size="3rem" color="blue" />
                <div class="text-h6 q-mt-sm">{{ t('dashboardAdvanced.cacheManagement.fileCache') }}</div>
                <div class="text-body2 text-grey-6">{{ cacheInfo.file_cache_size || t('dashboardAdvanced.cacheManagement.calculating') }}</div>
                <q-btn
                  color="orange"
                  flat
                  :label="t('dashboardAdvanced.cacheManagement.clearFileCache')"
                  @click="clearFileCache"
                  :loading="clearingFileCache"
                  class="q-mt-sm"
                />
              </q-card-section>
            </q-card>
          </div>

          <div class="col-12 col-md-4">
            <q-card flat bordered>
              <q-card-section class="text-center">
                <q-icon name="cleaning_services" size="3rem" color="red" />
                <div class="text-h6 q-mt-sm">{{ t('dashboardAdvanced.cacheManagement.allCache') }}</div>
                <div class="text-body2 text-grey-6">{{ t('dashboardAdvanced.cacheManagement.clearAllDescription') }}</div>
                <q-btn
                  color="negative"
                  flat
                  :label="t('dashboardAdvanced.cacheManagement.clearAllCache')"
                  @click="clearAllCache"
                  :loading="clearingAllCache"
                  class="q-mt-sm"
                />
              </q-card-section>
            </q-card>
          </div>
        </div>

        <div class="q-mt-md">
          <q-btn
            color="secondary"
            icon="refresh"
            :label="t('dashboardAdvanced.cacheManagement.refreshCacheInfo')"
            @click="loadCacheInfo"
            :loading="loadingCache"
          />
        </div>
      </q-card-section>
    </q-card>

    <!-- 数据管理 -->
    <q-card class="q-mb-lg">
      <q-card-section>
        <div class="text-h6 q-mb-md">{{ t('dashboardAdvanced.dataManagement.title') }}</div>

        <div class="row q-col-gutter-md">
          <div class="col-12 col-md-6">
            <q-card flat bordered>
              <q-card-section>
                <div class="text-subtitle1 q-mb-sm">{{ t('dashboardAdvanced.dataManagement.exportData') }}</div>
                <div class="text-body2 text-grey-6 q-mb-md">{{ t('dashboardAdvanced.dataManagement.exportDescription') }}</div>

                <q-btn-group>
                  <q-btn
                    color="primary"
                    icon="download"
                    :label="t('dashboardAdvanced.dataManagement.exportWorks')"
                    @click="exportWorks"
                    :loading="exportingWorks"
                  />
                  <q-btn
                    color="blue"
                    icon="download"
                    :label="t('dashboardAdvanced.dataManagement.exportUsers')"
                    @click="exportUsers"
                    :loading="exportingUsers"
                  />
                </q-btn-group>
              </q-card-section>
            </q-card>
          </div>

          <div class="col-12 col-md-6">
            <q-card flat bordered>
              <q-card-section>
                <div class="text-subtitle1 q-mb-sm">{{ t('dashboardAdvanced.dataManagement.importData') }}</div>
                <div class="text-body2 text-grey-6 q-mb-md">{{ t('dashboardAdvanced.dataManagement.importDescription') }}</div>

                <q-file
                  v-model="importFile"
                  :label="t('dashboardAdvanced.dataManagement.selectImportFile')"
                  accept=".json"
                  outlined
                  dense
                  class="q-mb-sm"
                />

                <q-btn
                  color="orange"
                  icon="upload"
                  :label="t('dashboardAdvanced.dataManagement.importButton')"
                  @click="importData"
                  :loading="importingData"
                  :disable="!importFile"
                />
              </q-card-section>
            </q-card>
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- 系统信息 -->
    <q-card>
      <q-card-section>
        <div class="text-h6 q-mb-md">{{ t('dashboardAdvanced.systemInfo.title') }}</div>

        <q-list bordered>
          <q-item>
            <q-item-section>
              <q-item-label>{{ t('dashboardAdvanced.systemInfo.version') }}</q-item-label>
              <q-item-label caption>{{ systemInfo.version || t('dashboardAdvanced.systemInfo.fetching') }}</q-item-label>
            </q-item-section>
          </q-item>

          <q-item>
            <q-item-section>
              <q-item-label>{{ t('dashboardAdvanced.systemInfo.dbStatus') }}</q-item-label>
              <q-item-label caption>
                <q-chip
                  :color="systemInfo.db_status === 'healthy' ? 'green' : 'red'"
                  text-color="white"
                  dense
                >
                  {{ systemInfo.db_status === 'healthy' ? t('dashboardAdvanced.systemInfo.dbStatusHealthy') : t('dashboardAdvanced.systemInfo.dbStatusError') }}
                </q-chip>
              </q-item-label>
            </q-item-section>
          </q-item>

          <q-item>
            <q-item-section>
              <q-item-label>{{ t('dashboardAdvanced.systemInfo.totalWorks') }}</q-item-label>
              <q-item-label caption>{{ t('dashboardAdvanced.systemInfo.totalWorksCount', { count: systemInfo.total_works || 0 }) }}</q-item-label>
            </q-item-section>
          </q-item>

          <q-item>
            <q-item-section>
              <q-item-label>{{ t('dashboardAdvanced.systemInfo.totalUsers') }}</q-item-label>
              <q-item-label caption>{{ t('dashboardAdvanced.systemInfo.totalUsersCount', { count: systemInfo.total_users || 0 }) }}</q-item-label>
            </q-item-section>
          </q-item>

          <q-item>
            <q-item-section>
              <q-item-label>{{ t('dashboardAdvanced.systemInfo.totalStorages') }}</q-item-label>
              <q-item-label caption>{{ t('dashboardAdvanced.systemInfo.totalStoragesCount', { count: systemInfo.total_storages || 0 }) }}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>

        <div class="q-mt-md">
          <q-btn
            color="secondary"
            icon="refresh"
            :label="t('dashboardAdvanced.systemInfo.refreshButton')"
            @click="loadSystemInfo"
            :loading="loadingSystemInfo"
          />
        </div>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from 'vue'
import { useI18n } from 'vue-i18n'
import { useNotification } from '../../composables/useNotification'

defineOptions({
  name: 'AdvancedSettingsPage'
})

// Composables
    const { t } = useI18n()
    const { proxy } = getCurrentInstance()
    const { showSuccessNotification, showErrorNotification } = useNotification()

    // Reactive data
    const systemConfig = ref({
      site_name: 'Kikoeru',
      allow_registration: true,
      enable_email_features: false,
      ensure_email: false
    })
    const cacheInfo = ref({
      cover_cache_size: '',
      file_cache_size: ''
    })
    const systemInfo = ref({
      version: '',
      db_status: 'healthy',
      total_works: 0,
      total_users: 0,
      total_storages: 0
    })
    const importFile = ref(null)
    const savingConfig = ref(false)
    const loadingConfig = ref(false)
    const loadingCache = ref(false)
    const clearingCoverCache = ref(false)
    const clearingFileCache = ref(false)
    const clearingAllCache = ref(false)
    const exportingWorks = ref(false)
    const exportingUsers = ref(false)
    const importingData = ref(false)
    const loadingSystemInfo = ref(false)

    // Lifecycle
    onMounted(() => {
      loadSystemConfig()
      loadCacheInfo()
      loadSystemInfo()
    })

    // Define the missing functions
    const loadSystemConfig = async () => {
      try {
        loadingConfig.value = true
        // Using the site info endpoint since there's no dedicated GET /api/v1/admin/config endpoint
        const response = await proxy.$api.get('/api/v1/site/info')
        systemConfig.value = {
          site_name: response.data.site_name || 'Kikoeru',
          allow_registration: response.data.allow_registration || true,
          enable_email_features: response.data.enable_email_features || false,
          ensure_email: response.data.ensure_email || false
        }
      } catch {
        showErrorNotification(t('notification.adminLoadSystemConfigFailed'))
      } finally {
        loadingConfig.value = false
      }
    }

    const loadCacheInfo = async () => {
      try {
        loadingCache.value = true
        const response = await proxy.$api.get('/api/v1/admin/cache/info')
        cacheInfo.value = response.data.data
      } catch {
        showErrorNotification(t('notification.adminLoadCacheInfoFailed'))
      } finally {
        loadingCache.value = false
      }
    }

    const loadSystemInfo = async () => {
      try {
        loadingSystemInfo.value = true
        const response = await proxy.$api.get('/api/v1/admin/system/info')
        systemInfo.value = response.data.data
      } catch {
        showErrorNotification(t('notification.adminLoadSystemInfoFailed'))
      } finally {
        loadingSystemInfo.value = false
      }
    }

const saveSystemConfig = async () => {
  savingConfig.value = true
  try {
    await proxy.$api.put('/api/v1/admin/config', systemConfig.value)
    showSuccessNotification(t('notification.adminSystemConfigSaved'))
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('notification.adminSystemConfigSaveFailed'))
  } finally {
    savingConfig.value = false
  }
}

const clearCoverCache = async () => {
  clearingCoverCache.value = true
  try {
    await proxy.$api.post('/api/v1/admin/cache/clear-cover')
    showSuccessNotification(t('notification.adminCoverCacheCleared'))
    loadCacheInfo() // Refresh info
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('notification.adminClearCoverCacheFailed'))
  } finally {
    clearingCoverCache.value = false
  }
}

const clearFileCache = async () => {
  clearingFileCache.value = true
  try {
    await proxy.$api.post('/api/v1/admin/cache/clear-file')
    showSuccessNotification(t('notification.adminFileCacheCleared'))
    loadCacheInfo() // Refresh info
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('notification.adminClearFileCacheFailed'))
  } finally {
    clearingFileCache.value = false
  }
}

const clearAllCache = async () => {
  clearingAllCache.value = true
  try {
    await proxy.$api.post('/api/v1/admin/cache/clear-all')
    showSuccessNotification(t('notification.adminAllCacheCleared'))
    loadCacheInfo() // Refresh info
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('notification.adminClearAllCacheFailed'))
  } finally {
    clearingAllCache.value = false
  }
}

const exportWorks = async () => {
  exportingWorks.value = true
  try {
    const response = await proxy.$api.get('/api/v1/admin/data/export/works', { responseType: 'blob' })
    downloadFile(response.data, 'works_export.json')
    showSuccessNotification(t('notification.adminWorksExported'))
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('notification.adminExportWorksFailed'))
  } finally {
    exportingWorks.value = false
  }
}

const exportUsers = async () => {
  exportingUsers.value = true
  try {
    const response = await proxy.$api.get('/api/v1/admin/data/export/users', { responseType: 'blob' })
    downloadFile(response.data, 'users_export.json')
    showSuccessNotification(t('notification.adminUsersExported'))
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('notification.adminExportUsersFailed'))
  } finally {
    exportingUsers.value = false
  }
}

const importData = async () => {
  if (!importFile.value) {
    showErrorNotification(t('dashboardAdvanced.dataManagement.noFileSelectedError'))
    return
  }
  importingData.value = true
  const formData = new FormData()
  formData.append('file', importFile.value)
  try {
    await proxy.$api.post('/api/v1/admin/data/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    showSuccessNotification(t('notification.adminDataImported'))
    importFile.value = null // Reset file input
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('notification.adminImportDataFailed'))
  } finally {
    importingData.value = false
  }
}

const downloadFile = (data, filename) => {
  const blob = new Blob([data], { type: 'application/json' })
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}

// Lifecycle
onMounted(() => {
  loadSystemConfig()
  loadCacheInfo()
  loadSystemInfo()
})
</script>
