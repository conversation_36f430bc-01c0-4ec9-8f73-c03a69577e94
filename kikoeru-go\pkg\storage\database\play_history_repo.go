package database

import (
	"context"
	"errors"
	"fmt"

	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var (
	ErrPlayHistoryNotFound = errors.New("play history record not found")
)

// PlayHistoryRepository defines the interface for play history data operations.
type PlayHistoryRepository interface {
	// Upsert creates a new play history record or updates an existing one
	// based on UserID, WorkID, and TrackPath.
	Upsert(ctx context.Context, history *models.PlayHistory) error
	// ListByUser retrieves a paginated list of play history records for a user,
	// ordered by the specified field and direction.
	ListByUser(ctx context.Context, userID string, page int, pageSize int, orderBy string, sortDirection string) (records []*models.PlayHistory, totalCount int64, err error)
	// GetSpecific retrieves a specific play history record.
	GetSpecific(ctx context.Context, userID string, workID uint, trackPath string) (*models.PlayHistory, error)
	// Delete removes a specific play history record.
	Delete(ctx context.Context, id uint) error
	// DeleteByUserAndWork removes all history for a specific work by a user (optional).
	// DeleteByUserAndWork(ctx context.Context, userID string, workID uint) error
}

type playHistoryRepository struct {
	db *gorm.DB
}

// NewPlayHistoryRepository creates a new PlayHistoryRepository.
func NewPlayHistoryRepository(db *gorm.DB) PlayHistoryRepository {
	return &playHistoryRepository{db: db}
}

// Upsert creates or updates a play history record.
// It uses GORM's OnConflict to handle the upsert based on the unique constraint (user_id, work_id, track_path).
func (r *playHistoryRepository) Upsert(ctx context.Context, history *models.PlayHistory) error {
	if history.UserID == "" || history.WorkID == 0 {
		return errors.New("user_id and work_id are required for play history")
	}
	// Ensure TrackPath is consistent (e.g., empty string if not applicable, not nil)
	if history.TrackPath == "" {
		// GORM might treat empty string and NULL differently in unique constraints depending on DB.
		// For SQLite, UNIQUE constraint treats NULLs as distinct.
		// To ensure upsert works correctly for "whole work" history, we might need a consistent value.
		// However, the UNIQUE (user_id, work_id, track_path) should handle this if track_path is nullable.
		// Let's assume the model and DB schema handle nullable track_path correctly for uniqueness.
	}

	// Columns to update on conflict. We want to update everything except CreatedAt.
	// GORM's clause.AssignmentColumns can be used, or specify all mutable fields.
	// UpdatedAt is automatically handled by GORM.
	err := r.db.WithContext(ctx).Clauses(clause.OnConflict{
		Columns: []clause.Column{{Name: "user_id"}, {Name: "work_id"}, {Name: "track_path"}},
		DoUpdates: clause.AssignmentColumns([]string{
			"playback_position_seconds",
			"progress_percentage",
			"is_finished",
			"updated_at", // Explicitly update updated_at to ensure it reflects last play
		}),
	}).Create(history).Error

	if err != nil {
		return fmt.Errorf("failed to upsert play history: %w", err)
	}
	return nil
}

// ListByUser retrieves paginated play history for a user.
func (r *playHistoryRepository) ListByUser(ctx context.Context, userID string, page int, pageSize int, orderBy string, sortDirection string) ([]*models.PlayHistory, int64, error) {
	var records []*models.PlayHistory
	var totalCount int64

	query := r.db.WithContext(ctx).Model(&models.PlayHistory{}).Where("user_id = ?", userID)

	if err := query.Count(&totalCount).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count play history for user %s: %w", userID, err)
	}

	if totalCount == 0 {
		return []*models.PlayHistory{}, 0, nil
	}

	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 { // Default page size if not specified or invalid
		pageSize = 20
	}
	if pageSize > 100 { // Max page size
		pageSize = 100
	}

	// Default ordering if not specified
	if orderBy == "" {
		orderBy = "updated_at"
	}
	if sortDirection == "" {
		sortDirection = "desc"
	}

	// Validate and sanitize the orderBy field to prevent SQL injection
	validOrderFields := map[string]bool{
		"updated_at":          true,
		"created_at":          true,
		"progress_percentage": true,
		"is_finished":         true,
	}

	if !validOrderFields[orderBy] {
		orderBy = "updated_at" // Fall back to default if invalid
	}

	// Validate sort direction
	if sortDirection != "asc" && sortDirection != "desc" {
		sortDirection = "desc" // Fall back to default if invalid
	}

	orderClause := fmt.Sprintf("%s %s", orderBy, sortDirection)

	offset := (page - 1) * pageSize
	err := query.Order(orderClause).
		Preload("Work"). // Preload Work details
		// Optionally preload Work.Circle, Work.Tags, Work.VAs if needed on history list
		// Preload("Work.Circle").
		Offset(offset).Limit(pageSize).
		Find(&records).Error

	if err != nil {
		return nil, 0, fmt.Errorf("failed to list play history for user %s: %w", userID, err)
	}
	return records, totalCount, nil
}

// GetSpecific retrieves a specific play history record.
func (r *playHistoryRepository) GetSpecific(ctx context.Context, userID string, workID uint, trackPath string) (*models.PlayHistory, error) {
	var record models.PlayHistory
	query := r.db.WithContext(ctx).Where("user_id = ? AND work_id = ?", userID, workID)
	// Handle nullable track_path correctly in query
	if trackPath == "" {
		query = query.Where("track_path IS NULL OR track_path = ''")
	} else {
		query = query.Where("track_path = ?", trackPath)
	}

	if err := query.First(&record).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrPlayHistoryNotFound
		}
		return nil, fmt.Errorf("failed to get specific play history: %w", err)
	}
	return &record, nil
}

// Delete removes a play history record by its ID.
func (r *playHistoryRepository) Delete(ctx context.Context, id uint) error {
	if id == 0 {
		return errors.New("cannot delete play history with zero ID")
	}
	result := r.db.WithContext(ctx).Delete(&models.PlayHistory{}, id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete play history ID %d: %w", id, result.Error)
	}
	if result.RowsAffected == 0 {
		return ErrPlayHistoryNotFound
	}
	return nil
}
