package local

import kikdrv "github.com/Sakura-Byte/kikoeru-go/pkg/storage/driver"

// Package local implements a storage driver for local filesystem access.

const (
	// DriverName is the programmatic name of this driver.
	DriverName = "local"
	// DriverDisplayName is the human-readable name for this driver.
	DriverDisplayName = "Local Storage"
)

// Addition defines the driver-specific configuration for the Local driver.
// These fields are typically populated from the 'Addition' or 'Config' section
// of the storage source configuration.
// Tags on these fields are used by the driver's Config() method (via reflection)
// to generate the []ParamInfo list for the API.
type Addition struct {
	// Embed RootPathConfig for RootFolderPath.
	// Tags for RootFolderPath (label, description, required, type) are defined in driver.RootPathConfig
	// within alist_model.go.
	kikdrv.RootPathConfig `mapstructure:",squash"`

	// ShowHidden controls whether files and folders starting with a dot (.) are visible.
	ShowHidden bool `json:"show_hidden" mapstructure:"show_hidden" label:"Show Hidden Files" description:"If true, hidden files and folders (those starting with a dot) will be shown." type:"bool" default:"false"`
}

// Note: The Alist-specific global 'config' variable and 'init()' based driver registration
// are not used in Kikoeru-Go. Driver registration and configuration injection are handled
// by the StorageDriverManager.
