package ports

import (
	"context"

	dto_playlist "github.com/Sakura-Byte/kikoeru-go/pkg/dto/playlist"
)

// PlaylistService defines the application service interface for playlist-related operations.
// This interface is a "driven" port, implemented by the service layer and used by handlers.
type PlaylistService interface {
	CreatePlaylist(ctx context.Context, userID string, req dto_playlist.CreatePlaylistRequest) (*dto_playlist.PlaylistDTO, error)
	GetPlaylistDetails(ctx context.Context, userID string, playlistID uint) (*dto_playlist.PlaylistWithItemsDTO, error)
	ListUserPlaylists(ctx context.Context, userID string, page int, pageSize int) ([]*dto_playlist.PlaylistDTO, int64, error)
	UpdatePlaylistInfo(ctx context.Context, userID string, playlistID uint, req dto_playlist.UpdatePlaylistInfoRequest) (*dto_playlist.PlaylistDTO, error)
	DeletePlaylist(ctx context.Context, userID string, playlistID uint) error
	AddItemToPlaylist(ctx context.Context, userID string, playlistID uint, req dto_playlist.AddItemToPlaylistRequest) (*dto_playlist.PlaylistItemDTO, error)
	RemoveItemFromPlaylist(ctx context.Context, userID string, playlistID uint, itemID uint) error
	UpdatePlaylistItemOrders(ctx context.Context, userID string, playlistID uint, itemOrders map[uint]int) error
}
