package auth

import (
	"fmt"

	"golang.org/x/crypto/bcrypt"
)

// DefaultBcryptCost 是在配置未指定时的默认 bcrypt cost
const DefaultBcryptCost = 10

// HashPassword 使用 bcrypt 生成密码的哈希值
// cost 参数控制哈希的计算强度，值越高越安全但越慢。
func HashPassword(password string, cost int) (string, error) {
	if cost <= 0 {
		cost = DefaultBcryptCost
	}
	bytes, err := bcrypt.GenerateFromPassword([]byte(password), cost)
	if err != nil {
		return "", fmt.Errorf("failed to hash password: %w", err)
	}
	return string(bytes), nil
}

// CheckPasswordHash 验证提供的密码是否与存储的哈希值匹配
func CheckPasswordHash(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}
