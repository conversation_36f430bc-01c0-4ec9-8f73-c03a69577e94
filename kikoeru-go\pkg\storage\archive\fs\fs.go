package fs

import (
	"io"
	"io/fs"
	"time"
)

// ArchiveFS is an interface for accessing files in an archive
type ArchiveFS interface {
	// ReadDir reads the contents of the directory
	ReadDir(name string) ([]fs.DirEntry, error)

	// Open opens the named file
	Open(name string) (fs.File, error)

	// Stat returns a FileInfo for the named file
	Stat(name string) (fs.FileInfo, error)
}

// ReadDirFile is a directory that can be read as a list of entries
type ReadDirFile interface {
	fs.File
	ReadDir(n int) ([]fs.DirEntry, error)
}

// ArchiveEntry represents a file or directory in an archive
type ArchiveEntry struct {
	name       string
	size       int64
	mode       fs.FileMode
	modTime    time.Time
	isDir      bool
	sys        interface{}
	readCloser io.ReadCloser
	fs         ArchiveFS
}

// Name returns the name of the file
func (f *ArchiveEntry) Name() string {
	return f.name
}

// Size returns the size of the file
func (f *ArchiveEntry) Size() int64 {
	return f.size
}

// Mode returns the file mode
func (f *ArchiveEntry) Mode() fs.FileMode {
	return f.mode
}

// ModTime returns the modification time
func (f *ArchiveEntry) ModTime() time.Time {
	return f.modTime
}

// IsDir returns whether the file is a directory
func (f *ArchiveEntry) IsDir() bool {
	return f.isDir
}

// Sys returns the underlying data source
func (f *ArchiveEntry) Sys() interface{} {
	return f.sys
}

// Read reads from the file
func (f *ArchiveEntry) Read(p []byte) (int, error) {
	if f.readCloser == nil {
		return 0, fs.ErrClosed
	}
	return f.readCloser.Read(p)
}

// Close closes the file
func (f *ArchiveEntry) Close() error {
	if f.readCloser == nil {
		return nil
	}
	err := f.readCloser.Close()
	f.readCloser = nil
	return err
}

// Info returns the file info
func (f *ArchiveEntry) Info() (fs.FileInfo, error) {
	return f, nil
}

// Type returns the type of the entry
func (f *ArchiveEntry) Type() fs.FileMode {
	return f.mode.Type()
}

// FS returns the filesystem of the entry
func (f *ArchiveEntry) FS() ArchiveFS {
	return f.fs
}

// ReadDir reads the directory
func (f *ArchiveEntry) ReadDir(n int) ([]fs.DirEntry, error) {
	if !f.isDir {
		return nil, fs.ErrInvalid
	}
	// This would typically read the directory entries from the archive
	// For now, we'll return an empty slice
	return []fs.DirEntry{}, nil
}

// NewArchiveEntry creates a new archive entry
func NewArchiveEntry(name string, size int64, mode fs.FileMode, modTime time.Time, isDir bool, readCloser io.ReadCloser, filesystem ArchiveFS) *ArchiveEntry {
	return &ArchiveEntry{
		name:       name,
		size:       size,
		mode:       mode,
		modTime:    modTime,
		isDir:      isDir,
		readCloser: readCloser,
		fs:         filesystem,
	}
}
