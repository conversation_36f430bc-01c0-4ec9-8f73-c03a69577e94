<template>
  <q-card>
    <router-link :to="`/work/${metadata.original_id}`">
      <CoverSFW :originalId="metadata.original_id" :nsfw="metadata.nsfw" :release="metadata.release_date" />
    </router-link>

    <q-separator />

    <div v-if="!thumbnailMode">
      <!-- 标题 -->
      <div class="q-mx-sm text-h6 text-weight-regular ellipsis-2-lines">
        <router-link :to="`/work/${metadata.original_id}`" class="text-black text-decoration-none">
          {{ metadata.title }}
        </router-link>
      </div>

      <!-- 社团 -->
      <div v-if="metadata.circle" class="q-ml-sm q-mt-sm q-mb-xs text-subtitle1 text-weight-regular ellipsis">
        <router-link :to="`/works?keyword=$circle:${metadata.circle.name}$`" class="text-grey text-decoration-none">
          {{ metadata.circle.name }}
        </router-link>
      </div>

      <!-- 评价&评论 -->
      <div v-show="metadata.title" class="row items-center">
        <!-- 评价 -->
        <div class="col-auto q-ml-sm">
          <q-rating
            v-model="rating"
            size="sm"
            :color="userMarked ? 'blue' : 'amber'"
            icon="star_border"
            icon-selected="star"
            icon-half="star_half"
          />

          <!-- 评价分布明细 -->
          <q-tooltip content-class="text-subtitle1" v-if="metadata.rate_count_detail && sortedRatings">
            <div>{{ $t('workCard.rating') }}: {{ metadata.rate_average_2dp }}</div>
            <div v-for="(rate, index) in sortedRatings" :key="index" class="row items-center">
              <div class="col">{{ rate.review_point }}{{ $t('work.starRating', { count: rate.review_point }) }}</div>

              <!-- 评价占比 -->
              <q-linear-progress
                :value="rate.ratio/100"
                color="amber"
                track-color="white"
                style="height: 15px; width: 100px"
                class="col-auto"
              />

              <div class="col q-mx-sm">({{ rate.count }})</div>
            </div>
          </q-tooltip>
        </div>

        <div class="col-auto">
          <span class="text-weight-medium text-body1 text-red">{{ metadata.rate_average_2dp || 0 }}</span>
          <span class="text-grey"> ({{ metadata.rate_count || 0 }})</span>
        </div>

        <!-- 评论数量 -->
        <div class="col-auto q-px-sm">
          <q-icon name="chat" size="xs" />
          <span class="text-grey"> ({{ metadata.review_count || 0 }})</span>
        </div>

        <!-- DLsite链接 -->
        <div class="col-auto">
          <q-icon name="launch" size="xs" />
          <a class="text-blue" :href="`https://www.dlsite.com/home/<USER>/=/product_id/${metadata.original_id}.html`" rel="noreferrer noopener" target="_blank">{{ $t('workCard.dlsiteLink') }}</a>
        </div>
      </div>

      <!-- 价格&售出数 -->
      <div v-show="metadata.title">
        <span class="q-mx-sm text-weight-medium text-h6 text-red">{{ metadata.price || 0 }} {{ $t('work.currency') }}</span>
        <span>{{ $t('workCard.soldCount') }}: {{ metadata.dl_count || 0 }}</span>
        <!-- Map age_rating strings to their display text -->
        <q-badge class="q-ml-sm" :color="metadata.age_rating === 'adult' ? 'red' : metadata.age_rating === 'r15' ? 'blue' : 'green'" :label="$t(`workCard.ageRating.${metadata.age_rating}`)" />
      </div>

      <!-- 标签 -->
      <div class="q-ma-xs" v-if="showTags && metadata.tags">
        <router-link
          v-for="(tag, index) in metadata.tags"
          :to="`/works?keyword=$tag:${tag.name}$`"
          :key="index"
        >
          <q-chip size="md" class="shadow-2">
            {{ tag.name }}
          </q-chip>
        </router-link>
      </div>

      <!-- 声优 -->
      <div class="q-mx-xs q-my-sm" v-if="metadata.vas">
        <router-link
          v-for="(va, index) in metadata.vas"
          :to="`/works?keyword=$va:${va.name}$`"
          :key="index"
        >
          <q-chip square size="md" class="shadow-2" color="teal" text-color="white">
            {{ va.name }}
          </q-chip>
        </router-link>
      </div>
    </div>
  </q-card>
</template>

<script setup>
import { ref, computed, watch, onMounted, getCurrentInstance } from 'vue'
import { useI18n } from 'vue-i18n'
import CoverSFW from './CoverSFW.vue'
import { useNotification } from '../composables/useNotification'

defineOptions({
  name: 'WorkCard'
})

const props = defineProps({
  metadata: {
    type: Object,
    required: true
  },
  thumbnailMode: {
    type: Boolean,
    default: false
  }
})

const { t } = useI18n()
const { showSuccessNotification, showErrorNotification } = useNotification()
const { proxy } = getCurrentInstance()

// Reactive data
const rating = ref(0)
const userMarked = ref(false)
const showTags = ref(true)

// Computed properties
const sortedRatings = computed(() => {
  if (!props.metadata.rate_count_detail) return null

  try {
    const rateDetail = typeof props.metadata.rate_count_detail === 'string'
      ? JSON.parse(props.metadata.rate_count_detail)
      : props.metadata.rate_count_detail

    // Check if it's already in the correct array format (from backend)
    if (Array.isArray(rateDetail)) {
      // Backend format: array of {review_point, count, ratio}
      return rateDetail.sort((a, b) => b.review_point - a.review_point)
    } else {
      // Legacy format: object with rating as keys
      const ratings = Object.keys(rateDetail).map(ratingKey => {
        const count = rateDetail[ratingKey]
        const total = props.metadata.rate_count || 1
        return {
          review_point: parseInt(ratingKey),
          count: count,
          ratio: (count / total) * 100
        }
      })
      return ratings.sort((a, b) => b.review_point - a.review_point)
    }
  } catch (error) {
    console.warn('Failed to parse rate_count_detail:', error)
    return null
  }
})

// Methods
const submitRating = (payload) => {
  proxy.$api.put('/api/v1/review', payload)
    .then(() => {
      showSuccessNotification(t('notification.reviewSubmitted'))
    })
    .catch((error) => {
      showErrorNotification(error.response?.data?.error || error.message || t('notification.reviewSubmitFailed'))
    })
}

// Lifecycle hooks
onMounted(() => {
  if (props.metadata.user_rating) {
    userMarked.value = true
    rating.value = props.metadata.user_rating
  } else {
    userMarked.value = false
    rating.value = props.metadata.rate_average_2dp || 0
  }

  // 检查是否有有效标签
  if (!props.metadata.tags || props.metadata.tags.length === 0) {
    showTags.value = false
  }
})

// Watchers
watch(rating, (newRating, oldRating) => {
  if (oldRating && userMarked.value) {
    const submitPayload = {
      'work_id': props.metadata.original_id,
      'rating': newRating
    }
    submitRating(submitPayload)
  }
})


</script>
