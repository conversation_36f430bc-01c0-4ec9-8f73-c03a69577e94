package database

import (
	"context"
	"time"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"gorm.io/gorm"
)

// ArchivePasswordRepository defines the interface for archive password operations
type ArchivePasswordRepository interface {
	// Password list management
	CreatePassword(ctx context.Context, password *models.ArchivePassword) error
	GetPasswordByID(ctx context.Context, id uint) (*models.ArchivePassword, error)
	GetAllPasswords(ctx context.Context) ([]*models.ArchivePassword, error)
	UpdatePassword(ctx context.Context, id uint, password *models.ArchivePassword) error
	DeletePassword(ctx context.Context, id uint) error
	PasswordExists(ctx context.Context, password string) (bool, error)
	CreatePasswordsInBatch(ctx context.Context, passwords []*models.ArchivePassword) (int, error)

	// Password cache management
	GetCachedPassword(ctx context.Context, storageID uint, archivePath string) (*models.ArchivePasswordCache, error)
	SetCachedPassword(ctx context.Context, cache *models.ArchivePasswordCache) error
	UpdateCachedPasswordUsage(ctx context.Context, storageID uint, archivePath string) error
	DeleteCachedPassword(ctx context.Context, storageID uint, archivePath string) error
	GetAllCachedPasswords(ctx context.Context) ([]*models.ArchivePasswordCache, error)
	CleanupOldCachedPasswords(ctx context.Context, olderThan time.Duration) error
}

// archivePasswordRepository implements ArchivePasswordRepository
type archivePasswordRepository struct {
	db *gorm.DB
}

// NewArchivePasswordRepository creates a new archive password repository
func NewArchivePasswordRepository(db *gorm.DB) ArchivePasswordRepository {
	return &archivePasswordRepository{db: db}
}

// Password list management methods

func (r *archivePasswordRepository) CreatePassword(ctx context.Context, password *models.ArchivePassword) error {
	if err := r.db.WithContext(ctx).Create(password).Error; err != nil {
		if isDuplicateError(err) {
			return apperrors.ErrArchivePasswordExists
		}
		return err
	}
	return nil
}

func (r *archivePasswordRepository) GetPasswordByID(ctx context.Context, id uint) (*models.ArchivePassword, error) {
	var password models.ArchivePassword
	if err := r.db.WithContext(ctx).First(&password, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, apperrors.ErrArchivePasswordNotFound
		}
		return nil, err
	}
	return &password, nil
}

func (r *archivePasswordRepository) GetAllPasswords(ctx context.Context) ([]*models.ArchivePassword, error) {
	var passwords []*models.ArchivePassword
	if err := r.db.WithContext(ctx).Order("created_at DESC").Find(&passwords).Error; err != nil {
		return nil, err
	}
	return passwords, nil
}

func (r *archivePasswordRepository) UpdatePassword(ctx context.Context, id uint, password *models.ArchivePassword) error {
	result := r.db.WithContext(ctx).Model(&models.ArchivePassword{}).Where("id = ?", id).Updates(password)
	if result.Error != nil {
		if isDuplicateError(result.Error) {
			return apperrors.ErrArchivePasswordExists
		}
		return result.Error
	}
	if result.RowsAffected == 0 {
		return apperrors.ErrArchivePasswordNotFound
	}
	return nil
}

func (r *archivePasswordRepository) DeletePassword(ctx context.Context, id uint) error {
	result := r.db.WithContext(ctx).Delete(&models.ArchivePassword{}, id)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return apperrors.ErrArchivePasswordNotFound
	}
	return nil
}

func (r *archivePasswordRepository) PasswordExists(ctx context.Context, password string) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.ArchivePassword{}).Where("password = ?", password).Count(&count).Error; err != nil {
		return false, err
	}
	return count > 0, nil
}

func (r *archivePasswordRepository) CreatePasswordsInBatch(ctx context.Context, passwords []*models.ArchivePassword) (int, error) {
	if len(passwords) == 0 {
		return 0, nil
	}

	// Use batch insert with ignore duplicates
	result := r.db.WithContext(ctx).Create(passwords)
	if result.Error != nil {
		// If it's a duplicate error, we still want to know how many were actually inserted
		// This is database-specific behavior, so we'll handle it gracefully
		return 0, result.Error
	}

	return int(result.RowsAffected), nil
}

// Password cache management methods

func (r *archivePasswordRepository) GetCachedPassword(ctx context.Context, storageID uint, archivePath string) (*models.ArchivePasswordCache, error) {
	var cache models.ArchivePasswordCache
	if err := r.db.WithContext(ctx).Where("storage_id = ? AND archive_path = ?", storageID, archivePath).First(&cache).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, apperrors.ErrArchivePasswordNotFound
		}
		return nil, err
	}
	return &cache, nil
}

func (r *archivePasswordRepository) SetCachedPassword(ctx context.Context, cache *models.ArchivePasswordCache) error {
	// Use upsert to either create or update
	return r.db.WithContext(ctx).Save(cache).Error
}

func (r *archivePasswordRepository) UpdateCachedPasswordUsage(ctx context.Context, storageID uint, archivePath string) error {
	result := r.db.WithContext(ctx).Model(&models.ArchivePasswordCache{}).
		Where("storage_id = ? AND archive_path = ?", storageID, archivePath).
		Update("last_used_at", time.Now())
	
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return apperrors.ErrArchivePasswordNotFound
	}
	return nil
}

func (r *archivePasswordRepository) DeleteCachedPassword(ctx context.Context, storageID uint, archivePath string) error {
	result := r.db.WithContext(ctx).Where("storage_id = ? AND archive_path = ?", storageID, archivePath).Delete(&models.ArchivePasswordCache{})
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return apperrors.ErrArchivePasswordNotFound
	}
	return nil
}

func (r *archivePasswordRepository) GetAllCachedPasswords(ctx context.Context) ([]*models.ArchivePasswordCache, error) {
	var caches []*models.ArchivePasswordCache
	if err := r.db.WithContext(ctx).Preload("StorageSource").Order("last_used_at DESC").Find(&caches).Error; err != nil {
		return nil, err
	}
	return caches, nil
}

func (r *archivePasswordRepository) CleanupOldCachedPasswords(ctx context.Context, olderThan time.Duration) error {
	cutoffTime := time.Now().Add(-olderThan)
	return r.db.WithContext(ctx).Where("last_used_at < ?", cutoffTime).Delete(&models.ArchivePasswordCache{}).Error
}

// Helper function to check if error is a duplicate constraint violation
func isDuplicateError(err error) bool {
	// This is database-specific. For SQLite, we check for UNIQUE constraint failed
	// For other databases, you might need to check different error patterns
	return err != nil && (
		// SQLite
		err.Error() == "UNIQUE constraint failed: t_archive_passwords.password" ||
		// MySQL
		err.Error() == "Error 1062: Duplicate entry" ||
		// PostgreSQL
		err.Error() == "duplicate key value violates unique constraint")
}
