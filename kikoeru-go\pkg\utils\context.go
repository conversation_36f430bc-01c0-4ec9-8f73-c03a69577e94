package utils

import (
	"context"
)

// IsCanceled checks if a context is canceled
func IsCanceled(ctx context.Context) bool {
	if ctx == nil {
		return false
	}
	select {
	case <-ctx.Done():
		return true
	default:
		return false
	}
}

// CanceledError returns the error if the context is canceled
func CanceledError(ctx context.Context) error {
	if IsCanceled(ctx) {
		return ctx.Err()
	}
	return nil
}

// CheckCtx checks if a context is canceled and returns the error if it is
func CheckCtx(ctx context.Context) error {
	if ctx == nil {
		return nil
	}
	return CanceledError(ctx)
}
