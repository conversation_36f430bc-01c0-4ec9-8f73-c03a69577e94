package models

import (
	"database/sql"
	"time"

	user_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/user"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

const (
	UserGroupAdmin = "admin"
	UserGroupUser  = "user"
	UserGroupGuest = "guest"
)

// User 对应数据库中的 t_user 表
type User struct {
	ID        string    `gorm:"type:varchar(36);primaryKey" json:"id"`
	Username  string    `gorm:"type:varchar(100);uniqueIndex;not null" json:"username"`
	Password  string    `gorm:"type:varchar(255);not null" json:"-"`
	Group     string    `gorm:"column:user_group;type:varchar(50);not null;index" json:"group"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	Email                       sql.NullString `gorm:"type:varchar(255);uniqueIndex" json:"email"`
	EmailVerified               bool           `gorm:"default:false" json:"email_verified"`
	VerificationToken           sql.NullString `gorm:"type:varchar(100);index" json:"-"`
	VerificationTokenExpiresAt  sql.NullTime   `gorm:"index" json:"-"`
	ResetPasswordToken          sql.NullString `gorm:"type:varchar(100);index" json:"-"`
	ResetPasswordTokenExpiresAt sql.NullTime   `gorm:"index" json:"-"`
}

// UserResponse DTO has been moved to pkg/dto/user/user_dto.go

// TableName 指定 GORM 使用的表名
func (User) TableName() string {
	return "t_user"
}

// BeforeCreate GORM hook to generate UUID for ID
func (u *User) BeforeCreate(tx *gorm.DB) (err error) {
	if u.ID == "" {
		u.ID = uuid.NewString()
	}
	return
}

// ToUserResponse converts a User model to a user_dto.UserResponse DTO
func (u *User) ToUserResponse() user_dto.UserResponse {
	resp := user_dto.UserResponse{
		ID:            u.ID,
		Username:      u.Username,
		Group:         u.Group,
		EmailVerified: u.EmailVerified,
		CreatedAt:     u.CreatedAt.Format(time.RFC3339), // Format time to string
		UpdatedAt:     u.UpdatedAt.Format(time.RFC3339), // Format time to string
	}
	if u.Email.Valid {
		resp.Email = u.Email.String
	}
	return resp
}

func (u *User) IsAdmin() bool {
	return u.Group == UserGroupAdmin
}
