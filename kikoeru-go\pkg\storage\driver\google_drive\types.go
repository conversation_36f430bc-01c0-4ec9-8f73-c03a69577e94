package google_drive

import (
	"path/filepath" // Added for filepath.Join
	"strconv"
	"strings" // Added for strings.HasPrefix
	"time"


	kikdrv "github.com/Sakura-Byte/kikoeru-go/pkg/storage/driver" // Alias to kikdrv
)

type TokenError struct {
	Error            string `json:"error"`
	ErrorDescription string `json:"error_description"`
}

type Files struct {
	NextPageToken string `json:"nextPageToken"`
	Files         []File `json:"files"`
}

// File represents a Google Drive file or folder.
type File struct {
	Id              string    `json:"id"`
	Name            string    `json:"name"`
	MimeType        string    `json:"mimeType"`
	ModifiedTime    time.Time `json:"modifiedTime"`
	CreatedTime     time.Time `json:"createdTime"`
	Size            string    `json:"size"` // String because Google API returns it as string
	ThumbnailLink   string    `json:"thumbnailLink"`
	ShortcutDetails struct {
		TargetId       string `json:"targetId"`
		TargetMimeType string `json:"targetMimeType"`
	} `json:"shortcutDetails"`
	Parents []string `json:"parents,omitempty"` // Added to potentially help with path reconstruction if needed
}

// fileToObj converts a Google Drive API File to kikoeru-go's AlistObj.
// parentLogicalPath is the logical path of the directory containing this item.
func fileToObj(f File, parentLogicalPath string) kikdrv.AlistObj {
	size, _ := strconv.ParseInt(f.Size, 10, 64)
	isFolder := f.MimeType == "application/vnd.google-apps.folder"
	id := f.Id
	name := f.Name

	if f.MimeType == "application/vnd.google-apps.shortcut" && f.ShortcutDetails.TargetId != "" {
		id = f.ShortcutDetails.TargetId
		isFolder = f.ShortcutDetails.TargetMimeType == "application/vnd.google-apps.folder"
		if isFolder {
			size = 0
		}
	}

	var logicalPath string
	if parentLogicalPath == "/" {
		// If parent is root, path is just /name
		logicalPath = "/" + name
	} else if parentLogicalPath == "" {
		// If parent path is empty (e.g. direct Get of an item where parent context is unknown),
		// make path /name to signify it's relative to some root.
		// Or, could use ID as path: logicalPath = id
		logicalPath = "/" + name
	} else {
		logicalPath = filepath.ToSlash(filepath.Join(parentLogicalPath, name))
	}
	// Ensure path always starts with / unless it's meant to be empty (which it shouldn't be if name is present)
	if name != "" && !strings.HasPrefix(logicalPath, "/") {
		logicalPath = "/" + logicalPath
	}

	obj := &kikdrv.AlistObject{
		ID:       id,
		Path:     logicalPath,
		Name:     name,
		Size:     size,
		Modified: f.ModifiedTime,
		Ctime:    f.CreatedTime,
		IsFolder: isFolder,
	}
	return obj
}

// Error represents a Google Drive API error response.
type Error struct {
	Error struct {
		Errors []struct {
			Domain       string `json:"domain"`
			Reason       string `json:"reason"`
			Message      string `json:"message"`
			LocationType string `json:"location_type"`
			Location     string `json:"location"`
		} `json:"errors"`
		Code    int    `json:"code"`
		Message string `json:"message"`
	} `json:"error"`
}
