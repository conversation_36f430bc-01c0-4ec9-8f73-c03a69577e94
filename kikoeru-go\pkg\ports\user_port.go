package ports

import (
	"context"
	// "database/sql" // Removed unused import
	// "github.com/Sakura-Byte/kikoeru-go/pkg/auth" // Removed unused import
	user_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/user"
	// "github.com/Sakura-Byte/kikoeru-go/pkg/storage/database" // Removed unused import
)

// ListUsersParams contains parameters for listing users
type ListUsersParams struct {
	Page     int
	PageSize int
	SortBy   string
	Order    string
	Filter   map[string]interface{}
}

// UserService defines the application service interface for user-related operations.
// This interface is a "driven" port, implemented by the service layer and used by handlers.
type UserService interface {
	RegisterUser(ctx context.Context, req user_dto.UserRegistrationRequest, ipAddress string) (*user_dto.UserResponse, error)
	LoginUser(ctx context.Context, req user_dto.UserLoginRequest) (tokenString string, user *user_dto.UserResponse, err error)
	ChangeUserPassword(ctx context.Context, username string, req user_dto.ChangePasswordRequest) error
	GetUserByName(ctx context.Context, username string) (*user_dto.UserResponse, error)
	GetUserByID(ctx context.Context, userID string) (*user_dto.UserResponse, error)
	RequestLinkEmail(ctx context.Context, username string, email string, ipAddress string) error
	VerifyUserEmail(ctx context.Context, verificationToken string) error
	UnlinkUserEmail(ctx context.Context, username string, password string) error
	RequestPasswordReset(ctx context.Context, email string, ipAddress string) error
	ResetPasswordWithToken(ctx context.Context, req user_dto.ResetPasswordWithTokenRequest) error
	ListUsers(ctx context.Context, params user_dto.ListUsersParams) (usersDto []user_dto.UserResponse, totalCount int64, err error)
	CountUsers() (int64, error)
	GetUserForAdmin(ctx context.Context, userID string) (*user_dto.UserResponse, error)
	UpdateUserByAdmin(ctx context.Context, userID string, req user_dto.UpdateUserByAdminRequest) (*user_dto.UserResponse, error)
	DeleteUserByAdmin(ctx context.Context, userID string) error

	// New methods for admin to update self and create users
	AdminUpdateOwnProfile(ctx context.Context, adminUsername string, newUsername, newPassword *string) (*user_dto.UserResponse, error)
	AdminSelfUpdateProfile(ctx context.Context, adminUsername string, req user_dto.AdminSelfUpdateRequest) (*user_dto.UserResponse, error)
	CreateUserByAdmin(ctx context.Context, req user_dto.AdminCreateUserRequest) (*user_dto.UserResponse, error)
}

// Note: Other service interfaces (WorkService, PlaylistService, etc.) will also be moved here.
