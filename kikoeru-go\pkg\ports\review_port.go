package ports

import (
	"context"

	dto_review "github.com/Sakura-Byte/kikoeru-go/pkg/dto/review"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/database"
)

// ReviewService defines the application service interface for managing user reviews.
// This interface is a "driven" port, implemented by the service layer and used by handlers.
type ReviewService interface {
	// PutReview creates or updates a review for a work by a user.
	PutReview(ctx context.Context, userID string, req dto_review.PutReviewRequest) (*dto_review.ReviewResponseItem, error)
	// GetUserReviews lists reviews for a given user with pagination, sorting, and filtering.
	GetUserReviews(ctx context.Context, userID string, params database.ListReviewParams) ([]dto_review.ReviewResponseItem, int64, error)
	// GetUserReviewForWork retrieves a user's review for a specific work.
	GetUserReviewForWork(ctx context.Context, userID string, workOriginalID string) (*models.Review, error)
	// DeleteReview deletes a review by its ID.
	DeleteReview(ctx context.Context, userID string, originalID string) error
}
