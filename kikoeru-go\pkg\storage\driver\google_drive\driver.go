package google_drive

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/Sakura-Byte/kikoeru-go/pkg/log"
	kikoerumodels "github.com/Sakura-Byte/kikoeru-go/pkg/models"
	kikdrv "github.com/Sakura-Byte/kikoeru-go/pkg/storage/driver"

	"github.com/go-resty/resty/v2"
	"github.com/mitchellh/mapstructure"
)

// File, ShortcutDetails, and fileToObj are now expected to be in types.go or another file in this package.

type GoogleDrive struct {
	StorageModel *kikoerumodels.StorageSource
	Addition
	AccessToken string
	httpClient  *resty.Client
}

func (d *GoogleDrive) GetStorage() *kikoerumodels.StorageSource {
	return d.StorageModel
}

func (d *GoogleDrive) SetStorage(storage kikoerumodels.StorageSource) {
	d.StorageModel = &storage
}

// NewGoogleDrive creates a new GoogleDrive driver instance
func NewGoogleDrive(commonConfig kikdrv.AlistDriverCommonConfig, additionInput interface{}) (kikdrv.StorageDriver, error) {
	d := &GoogleDrive{}

	err := mapstructure.Decode(additionInput, &d.Addition)
	if err != nil {
		log.Error(context.Background(), "Failed to decode addition for GoogleDrive", "error", err)
		return nil, fmt.Errorf("failed to decode addition for GoogleDrive: %w", err)
	}

	requestTimeout := 30 * time.Second
	if commonConfig.RequestTimeout > 0 {
		requestTimeout = commonConfig.RequestTimeout
	}
	d.httpClient = resty.New().SetTimeout(requestTimeout)

	if d.Addition.RootFolderID == "" {
		d.Addition.RootFolderID = DriverDefaultDriverRoot
		log.Info(context.Background(), "RootFolderID not found in addition configuration, defaulting to driver type's default.", "default_root_folder_id", d.Addition.RootFolderID)
	} else {
		log.Info(context.Background(), "Using RootFolderID from driver addition configuration.", "root_folder_id", d.Addition.RootFolderID)
	}

	return d, nil
}

func (d *GoogleDrive) Config() kikdrv.Config {
	return kikdrv.Config{
		Name:              DriverName,
		DisplayName:       DriverDisplayName,
		DefaultDriverRoot: DriverDefaultDriverRoot,
		DownloadOptions:   []string{kikdrv.DownloadStrategyProxy},
		Params:            kikdrv.ParamsFromStruct(Addition{}),
	}
}

func (d *GoogleDrive) GetAddition() interface{} {
	return &d.Addition
}

func (d *GoogleDrive) Init(ctx context.Context) error {
	log.Info(ctx, "Initializing GoogleDrive driver")
	return d.refreshToken(ctx)
}

func (d *GoogleDrive) Drop(ctx context.Context) error {
	log.Info(ctx, "Dropping GoogleDrive driver (no-op)")
	return nil
}

func (d *GoogleDrive) List(ctx context.Context, dir kikdrv.AlistObj, args kikdrv.AlistListArgs) ([]kikdrv.AlistObj, error) {
	folderID := dir.GetID()
	parentLogicalPath := dir.GetPath()

	if folderID == "" || folderID == "/" {
		folderID = d.Addition.GetRootID()
		if parentLogicalPath == "" || parentLogicalPath == "." || parentLogicalPath == "/" {
			parentLogicalPath = "/"
		}
	}
	if parentLogicalPath != "/" && strings.HasSuffix(parentLogicalPath, "/") {
		parentLogicalPath = strings.TrimRight(parentLogicalPath, "/")
	}
	if parentLogicalPath == "" {
		parentLogicalPath = "/"
	}

	log.Debug(ctx, "Listing folder in GoogleDrive", "folder_id", folderID, "logical_parent_path_input", dir.GetPath(), "effective_parent_logical_path", parentLogicalPath)
	files, err := d.getFiles(ctx, folderID)
	if err != nil {
		log.Error(ctx, "Failed to list files in GoogleDrive", "folder_id", folderID, "error", err)
		return nil, err
	}

	res := make([]kikdrv.AlistObj, 0, len(files))
	for _, file := range files {
		// fileToObj is now expected to be in this package (e.g. types.go)
		// and to accept parentLogicalPath.
		obj := fileToObj(file, parentLogicalPath)
		res = append(res, obj)
	}
	log.Debug(ctx, "Successfully listed files", "folder_id", folderID, "count", len(res))
	return res, nil
}

func (d *GoogleDrive) Link(ctx context.Context, file kikdrv.AlistObj, args kikdrv.AlistLinkArgs) (*kikdrv.AlistLink, error) {
	fileID := file.GetID()
	log.Debug(ctx, "Generating link for GoogleDrive file", "file_id", fileID, "file_name", file.GetName())

	downloadURL := fmt.Sprintf("https://www.googleapis.com/drive/v3/files/%s?alt=media&supportsAllDrives=true&includeItemsFromAllDrives=true&acknowledgeAbuse=true", fileID)

	link := kikdrv.AlistLink{
		URL: downloadURL,
		Header: http.Header{
			"Authorization": []string{"Bearer " + d.AccessToken},
		},
	}
	log.Info(ctx, "Generated download link", "file_id", fileID)
	return &link, nil
}

func (d *GoogleDrive) Get(ctx context.Context, pathOrID string) (kikdrv.AlistObj, error) {
	if strings.Contains(pathOrID, "/") || strings.Contains(pathOrID, "\\") {
		log.Warn(ctx, "GoogleDrive.Get received a path-like string, but expects an ID. This indicates that path resolution failed or was not properly performed.",
			"path_or_id_received", pathOrID)

		// For Google Drive, a proper ID never contains slashes
		// This requires path resolution to happen before Get is called
		return nil, fmt.Errorf("GoogleDrive.Get received path-like string '%s' but requires object IDs: %w", pathOrID, kikdrv.ErrOperationNotSupported)
	}

	fileID := pathOrID

	log.Debug(ctx, "Getting item by ID from GoogleDrive", "id", fileID)

	url := fmt.Sprintf("https://www.googleapis.com/drive/v3/files/%s", fileID)
	query := map[string]string{
		"fields": "id,name,mimeType,size,modifiedTime,createdTime,thumbnailLink,shortcutDetails,parents",
	}
	var fileData File // Expects File struct to be defined in this package (e.g. types.go)

	_, statusCode, err := d.request(ctx, url, http.MethodGet, func(req *resty.Request) {
		req.SetQueryParams(query)
	}, &fileData)

	if err != nil {
		log.Error(ctx, "Failed to get item by ID from GoogleDrive", "id", fileID, "status_code", statusCode, "error", err)
		if statusCode == http.StatusNotFound {
			return nil, kikdrv.ErrFileNotFound
		}
		return nil, fmt.Errorf("google drive: API error getting item '%s': %w", fileID, err)
	}

	parentLogicalPathForGet := "/"
	obj := fileToObj(fileData, parentLogicalPathForGet) // fileToObj is now expected to be in this package

	log.Debug(ctx, "Successfully got item by ID", "id", fileID, "name", obj.GetName(), "logical_path", obj.GetPath())
	return obj, nil
}

// Assume d.getFiles, d.request, d.refreshToken are defined elsewhere in the package

var _ kikdrv.StorageDriver = (*GoogleDrive)(nil)
var _ kikdrv.Getter = (*GoogleDrive)(nil)
var _ kikdrv.IRootIDGetter = (*GoogleDrive)(nil)

// GetRootID implements IRootIDGetter interface
func (d *GoogleDrive) GetRootID() string {
	return d.Addition.RootFolderID
}
