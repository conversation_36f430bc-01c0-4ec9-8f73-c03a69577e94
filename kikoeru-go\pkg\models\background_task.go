package models

import (
	"encoding/json"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm" // Added for gorm.DB
)

type TaskStatus string
type TaskType string

const (
	TaskStatusPending   TaskStatus = "pending"
	TaskStatusRunning   TaskStatus = "running"
	TaskStatusCompleted TaskStatus = "completed"
	TaskStatusFailed    TaskStatus = "failed"
	TaskStatusCancelled TaskStatus = "cancelled"

	TaskTypeScanLibrary      TaskType = "scan_library"
	TaskTypeScrapeAllWorks   TaskType = "scrape_all_works"
	TaskTypeScrapeSingleWork TaskType = "scrape_single_work"
	// Add other task types as needed
)

// BackgroundTask represents a long-running task managed by the system.
type BackgroundTask struct {
	ID       string     `gorm:"type:varchar(36);primaryKey" json:"id"` // UUID
	TaskType TaskType   `gorm:"type:varchar(50);index;not null" json:"task_type"`
	Status   TaskStatus `gorm:"type:varchar(20);index;not null" json:"status"`
	Progress float64    `gorm:"type:float;default:0.0" json:"progress"` // 0.0 to 1.0
	Message  string     `gorm:"type:text" json:"message,omitempty"`     // Current status message or short description

	Payload json.RawMessage `gorm:"type:json" json:"payload,omitempty"`                            // Task-specific input parameters
	Result  json.RawMessage `gorm:"type:json" json:"result,omitempty"`                             // Task-specific result or error details
	Error   string          `gorm:"column:error_message;type:text" json:"error_message,omitempty"` // Simplified error message string, explicitly mapped

	SubmittedByUserID string     `gorm:"type:varchar(36);index" json:"submitted_by_user_id,omitempty"` // User who submitted the task
	CreatedAt         time.Time  `gorm:"autoCreateTime" json:"created_at"`
	StartedAt         *time.Time `gorm:"index" json:"started_at,omitempty"`
	CompletedAt       *time.Time `gorm:"index" json:"completed_at,omitempty"`
	UpdatedAt         time.Time  `gorm:"autoUpdateTime" json:"updated_at"`
}

func (BackgroundTask) TableName() string {
	return "t_background_task"
}

// BeforeCreate will set a UUID rather than relying on DB auto-increment.
func (bt *BackgroundTask) BeforeCreate(tx *gorm.DB) (err error) {
	if bt.ID == "" {
		bt.ID = uuid.NewString()
	}
	if bt.Status == "" {
		bt.Status = TaskStatusPending
	}
	return
}

// Task Payload structs (ScanLibraryTaskPayload, ScrapeAllWorksTaskPayload, ScrapeSingleWorkTaskPayload)
// have been moved to pkg/dto/task/task_dto.go
