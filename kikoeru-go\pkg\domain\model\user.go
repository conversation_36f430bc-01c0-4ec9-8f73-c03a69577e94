package model

import (
	"time"
)

// User represents a user in the system
type User struct {
	ID                        string `gorm:"primaryKey;type:varchar(36)"`
	Username                  string `gorm:"uniqueIndex;not null"`
	Password                  string `gorm:"not null"`
	Group                     string `gorm:"index;not null"`
	Email                     string `gorm:"uniqueIndex"`
	EmailVerified             bool   `gorm:"default:false"`
	VerificationToken         string
	VerificationTokenExpires  time.Time
	ResetPasswordToken        string
	ResetPasswordTokenExpires time.Time
	CreatedAt                 time.Time `gorm:"autoCreateTime"`
	UpdatedAt                 time.Time `gorm:"autoUpdateTime"`
}

// TableName returns the table name for the user model
func (User) TableName() string {
	return "t_user"
}
