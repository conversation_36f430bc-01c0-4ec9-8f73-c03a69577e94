-- migrations_temp/000002_create_work_related_tables.up.sql
-- Defines tables related to works, circles, tags, VAs, and their relationships,
-- incorporating changes from refactor.md and consolidating previous migrations.

-- Circle Table
CREATE TABLE IF NOT EXISTS t_circle (
    id VARCHAR(36) PRIMARY KEY, -- UUID
    name VA<PERSON><PERSON>R(255) NOT NULL UNIQUE,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Tag Table
CREATE TABLE IF NOT EXISTS t_tag (
    id VARCHAR(36) PRIMARY KEY, -- UUID
    name VARCHAR(255) NOT NULL UNIQUE,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON> (Voice Actor) Table
CREATE TABLE IF NOT EXISTS t_va (
    id VARCHAR(36) PRIMARY KEY, -- UUID
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL UNIQUE,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Work Table
-- This table definition consolidates initial creation and subsequent alterations,
-- and applies changes from refactor.md (section 3.2).
CREATE TABLE IF NOT EXISTS t_work (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    original_id VARCHAR(20) UNIQUE, -- From 000005_add_original_id_to_work.up.sql, made UNIQUE
    title VARCHAR(512) NOT NULL,
    circle_id VARCHAR(36), -- 修改为UUID类型
    
    release_date VARCHAR(10),
    age_rating VARCHAR(20),
    nsfw BOOLEAN, -- 新增NSFW字段
    
    dl_count INTEGER,
    price INTEGER,
    
    review_count INTEGER,
    rate_count INTEGER,
    rate_average_2dp REAL, -- From 000001, ensured REAL by 000017
    rate_count_detail TEXT,
    rank TEXT,
    
    lyric_status VARCHAR(50) NOT NULL DEFAULT '',
    language VARCHAR(10), -- From 000003_add_language_to_work.up.sql
    work_type VARCHAR(10), -- From 000008_add_work_type_to_work.up.sql
    duration INTEGER, -- From 20250517022600_add_duration_to_work.up.sql

    -- Fields from refactor.md (section 3.2)
    storage_id INTEGER NOT NULL, -- New field, replaces root_folder_alias and relative_path logic
    path_in_storage VARCHAR(1024) NOT NULL, -- New field
    cover_filename VARCHAR(255), -- From 000010_add_cover_filename_to_work.up.sql, retained as per refactor.md
    admin_cover VARCHAR(1024), -- From 000016_add_admin_cover_to_work.up.sql

    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (circle_id) REFERENCES t_circle(id) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (storage_id) REFERENCES t_storage_sources(id) ON DELETE CASCADE ON UPDATE CASCADE -- New Foreign Key
);

-- Indexes for t_work
CREATE INDEX IF NOT EXISTS idx_work_title ON t_work(title);
CREATE INDEX IF NOT EXISTS idx_work_circle_id ON t_work(circle_id);
CREATE INDEX IF NOT EXISTS idx_work_release_date ON t_work(release_date);
CREATE INDEX IF NOT EXISTS idx_work_age_rating ON t_work(age_rating);
CREATE INDEX IF NOT EXISTS idx_work_dl_count ON t_work(dl_count);
CREATE INDEX IF NOT EXISTS idx_work_price ON t_work(price);
CREATE INDEX IF NOT EXISTS idx_work_rate_average_2dp ON t_work(rate_average_2dp);
CREATE INDEX IF NOT EXISTS idx_work_original_id ON t_work(original_id); -- From 000005
CREATE INDEX IF NOT EXISTS idx_work_language ON t_work(language); -- From 000003
CREATE INDEX IF NOT EXISTS idx_work_work_type ON t_work(work_type); -- From 000008
CREATE INDEX IF NOT EXISTS idx_work_storage_id ON t_work(storage_id); -- New index
CREATE UNIQUE INDEX IF NOT EXISTS idx_work_storage_path ON t_work(storage_id, path_in_storage); -- New unique index replacing idx_work_path_alias

-- Junction table for Work and Tag (r_work_tags)
CREATE TABLE IF NOT EXISTS r_work_tags (
    work_id INTEGER NOT NULL,
    tag_id VARCHAR(36) NOT NULL,
    PRIMARY KEY (work_id, tag_id),
    FOREIGN KEY (work_id) REFERENCES t_work(id) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES t_tag(id) ON DELETE CASCADE ON UPDATE CASCADE
);

-- Junction table for Work and VA (r_work_vas)
CREATE TABLE IF NOT EXISTS r_work_vas (
    work_id INTEGER NOT NULL,
    va_id VARCHAR(36) NOT NULL,
    PRIMARY KEY (work_id, va_id),
    FOREIGN KEY (work_id) REFERENCES t_work(id) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (va_id) REFERENCES t_va(id) ON DELETE CASCADE ON UPDATE CASCADE
);

-- Trigger to update 'updated_at' timestamp on t_circle
CREATE TRIGGER IF NOT EXISTS trigger_t_circle_updated_at
AFTER UPDATE ON t_circle
FOR EACH ROW
BEGIN
    UPDATE t_circle SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
END;

-- Trigger to update 'updated_at' timestamp on t_tag
CREATE TRIGGER IF NOT EXISTS trigger_t_tag_updated_at
AFTER UPDATE ON t_tag
FOR EACH ROW
BEGIN
    UPDATE t_tag SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
END;

-- Trigger to update 'updated_at' timestamp on t_va
CREATE TRIGGER IF NOT EXISTS trigger_t_va_updated_at
AFTER UPDATE ON t_va
FOR EACH ROW
BEGIN
    UPDATE t_va SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
END;

-- Trigger to update 'updated_at' timestamp on t_work
CREATE TRIGGER IF NOT EXISTS trigger_t_work_updated_at
AFTER UPDATE ON t_work
FOR EACH ROW
BEGIN
    UPDATE t_work SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
END;