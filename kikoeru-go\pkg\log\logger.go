package log

import (
	"context"
	"io"
	"log/slog"
	"os"
	"runtime"
	"strings"
	"time"

	"github.com/Sakura-Byte/kikoeru-go/pkg/config"
)

var (
	globalLogger *slog.Logger
)

// InitGlobalLogger 初始化全局日志记录器
// 应在配置加载后调用
func InitGlobalLogger(cfg config.LogConfig) {
	var level slog.Level
	switch strings.ToLower(cfg.Level) {
	case "debug":
		level = slog.LevelDebug
	case "info":
		level = slog.LevelInfo
	case "warn", "warning":
		level = slog.LevelWarn
	case "error", "err":
		level = slog.LevelError
	default:
		level = slog.LevelInfo // 默认级别
	}

	var handler slog.Handler
	opts := &slog.HandlerOptions{
		Level:     level,
		AddSource: true, // 添加源文件和行号信息
	}

	var output io.Writer = os.Stdout // 默认输出到标准输出

	switch strings.ToLower(cfg.Format) {
	case "json":
		handler = slog.NewJSONHandler(output, opts)
	case "pretty":
		handler = NewPrettyTextHandler(output, opts)
	case "compact":
		handler = NewCompactHandler(output, opts)
	case "text":
		fallthrough
	default:
		handler = slog.NewTextHandler(output, opts)
	}

	globalLogger = slog.New(handler)
	slog.SetDefault(globalLogger) // 将其设置为标准库 slog 的默认 logger
}

// ensureLogger 确保全局日志记录器已初始化
func ensureLogger(ctx context.Context) *slog.Logger {
	if globalLogger == nil {
		// 后备初始化，以防 InitGlobalLogger 未被显式调用
		InitGlobalLogger(config.LogConfig{Level: "info", Format: "text"})
	}
	return globalLogger
}

// logWithCaller logs a message with the correct caller information by skipping stack frames
func logWithCaller(ctx context.Context, level slog.Level, msg string, args ...any) {
	logger := ensureLogger(ctx)
	if !logger.Enabled(ctx, level) {
		return
	}

	var pcs [1]uintptr
	// Skip 3 frames: runtime.Callers, logWithCaller, and the wrapper function (Debug/Info/Warn/Error)
	runtime.Callers(3, pcs[:])

	record := slog.NewRecord(time.Now(), level, msg, pcs[0])
	record.Add(args...)

	_ = logger.Handler().Handle(ctx, record)
}

// Debug 记录调试级别的日志，使用上下文感知的简洁签名
func Debug(ctx context.Context, msg string, args ...any) {
	logWithCaller(ctx, slog.LevelDebug, msg, args...)
}

// Info 记录信息级别的日志，使用上下文感知的简洁签名
func Info(ctx context.Context, msg string, args ...any) {
	logWithCaller(ctx, slog.LevelInfo, msg, args...)
}

// Warn 记录警告级别的日志，使用上下文感知的简洁签名
func Warn(ctx context.Context, msg string, args ...any) {
	logWithCaller(ctx, slog.LevelWarn, msg, args...)
}

// Error 记录错误级别的日志，使用上下文感知的简洁签名
func Error(ctx context.Context, msg string, args ...any) {
	logWithCaller(ctx, slog.LevelError, msg, args...)
}

// L 返回全局日志记录器实例（保留用于向后兼容，但不推荐使用）
// 推荐直接使用 Debug, Info, Warn, Error 函数
func L(ctx context.Context) *slog.Logger {
	return ensureLogger(ctx)
}
