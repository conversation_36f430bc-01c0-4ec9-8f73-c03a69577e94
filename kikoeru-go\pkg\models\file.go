package models

import (
	"io"
)

// FileInterface is an interface for file operations
type FileInterface interface {
	io.Reader
	io.ReaderAt
	io.Seeker
	io.Closer
	GetSize() int64
}

// BasicObj is an interface for objects in a filesystem
type BasicObj interface {
	GetName() string
	GetSize() int64
	ModTime() string
	IsDir() bool
}

// BasicObject is a basic implementation of BasicObj
type BasicObject struct {
	Name     string
	Size     int64
	Modified string
	IsFolder bool
}

// GetName returns the name of the object
func (o *BasicObject) GetName() string {
	return o.Name
}

// GetSize returns the size of the object
func (o *BasicObject) GetSize() int64 {
	return o.Size
}

// ModTime returns the modification time of the object
func (o *BasicObject) ModTime() string {
	return o.Modified
}

// IsDir returns whether the object is a directory
func (o *BasicObject) IsDir() bool {
	return o.IsFolder
}

// BasicObjTree is an interface for objects in a tree structure
type BasicObjTree interface {
	BasicObj
	GetChildren() []BasicObjTree
}

// BasicObjectTree is a basic implementation of BasicObjTree
type BasicObjectTree struct {
	BasicObject
	Children []BasicObjTree
}

// GetChildren returns the children of the object
func (o *BasicObjectTree) GetChildren() []BasicObjTree {
	return o.Children
}
