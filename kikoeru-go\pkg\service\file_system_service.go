package service

import (
	"context"
	"time"
)

// This file defines the FileSystemService interface and potentially shared types.
// The implementation is in file_system_service_impl.go

// FileSystemEntry, ListRequest, LinkResponse, and FileSystemService interface have been moved to github.com/Sakura-Byte/kikoeru-go/pkg/ports

// NewFileSystemServiceImpl creates a new FileSystemService instance.
// This function is implemented in file_system_service_impl.go
// func NewFileSystemServiceImpl(...) ports.FileSystemService { ... } // Removed function definition

// fileSystemService struct and its methods are implemented in file_system_service_impl.go
// type fileSystemService struct { ... }
// var _ ports.FileSystemService = (*fileSystemService)(nil)
// func (s *fileSystemService) getStorageDriver(...) { ... }
// func (s *fileSystemService) List(...) { ... }
// func (s *fileSystemService) Get(...) { ... }
// func (s *fileSystemService) GetLink(...) { ... }
// func (s *fileSystemService) GetFileStream(...) { ... }
// func determineFileType(...) { ... }
// limitedReadCloser is defined in file_system_service_impl.go

// FileSystemService defines the service for file system operations
type FileSystemService interface {
	// Archive-related operations
	ListArchive(ctx context.Context, req ArchiveRequest) ([]ArchiveEntry, error)
	GetArchiveFileStream(ctx context.Context, storageID uint, archivePath string, filePathInArchive string, password string, rangeHeader string) (stream interface{}, contentType string, originalContentLength int64, actualContentLength int64, filename string, httpStatus int, err error)
}

// ArchiveRequest contains parameters for archive operations
type ArchiveRequest struct {
	StorageID     uint
	PathInStorage string
	PathInArchive string
	Password      string
}

// ArchiveEntry represents a file or directory within an archive
type ArchiveEntry struct {
	Name         string
	IsDir        bool
	Path         string
	Size         int64
	ModifiedTime time.Time
}

// Passworder is an interface to set a password for password-protected archives
type Passworder interface {
	SetPassword(password string)
}
