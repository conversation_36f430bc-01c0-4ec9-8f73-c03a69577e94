package models

import "time"

// VoteType 定义投票类型
type VoteType int8 // 使用 int8 以节省空间

const (
	VoteTypeUp   VoteType = 1  // 赞成
	VoteTypeDown VoteType = -1 // 反对
)

// TagVote 对应数据库中的 t_tag_vote 表
// 用于记录用户对作品特定标签的投票
type TagVote struct {
	ID        uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	UserID    string    `gorm:"type:varchar(100);not null;index:idx_user_work_tag_vote,unique" json:"user_id"` // 投票用户 User.Name
	WorkID    uint      `gorm:"not null;index:idx_user_work_tag_vote,unique" json:"work_id"`                   // 关联的作品 ID
	TagID     uint      `gorm:"not null;index:idx_user_work_tag_vote,unique" json:"tag_id"`                    // 关联的标签 ID
	Vote      VoteType  `gorm:"type:tinyint;not null" json:"vote"`                                             // 投票类型 (1 for up, -1 for down)
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"` // 如果允许修改投票，则此字段有用

	// GORM 关联关系
	User *User `gorm:"foreignKey:UserID;references:Name" json:"user,omitempty"`
	Work *Work `gorm:"foreignKey:WorkID;references:ID" json:"work,omitempty"`
	Tag  *Tag  `gorm:"foreignKey:TagID;references:ID" json:"tag,omitempty"`
}

// TableName 指定 GORM 使用的表名
func (TagVote) TableName() string {
	return "t_tag_vote"
}

// GORM 复合唯一索引的另一种方式是在迁移中创建：
// DB.Migrator().CreateIndex(&TagVote{}, "uix_user_work_tag")
// 或者在字段标签中使用 uniqueIndex:"uix_name" (GORM v2)
// 这里使用 gorm:"index:idx_user_work_tag_vote,unique" 为每个字段分别创建唯一索引的一部分，
// GORM 会尝试将同名的 unique 索引组合起来。
// 更可靠的方式是在数据库迁移脚本中显式定义 UNIQUE (user_id, work_id, tag_id)。
