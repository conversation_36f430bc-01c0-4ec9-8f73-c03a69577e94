#!/usr/bin/env node

/**
 * Master CSS Extraction Script
 *
 * This script extracts CSS from all Vue components in the project
 * and moves them to dedicated SCSS files.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { execSync } from 'child_process';

// Get current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const SRC_DIR = path.join(__dirname, '..', 'src');
const COMPONENT_DIRS = [
  path.join(SRC_DIR, 'components'),
  path.join(SRC_DIR, 'pages'),
  path.join(SRC_DIR, 'layouts')
];

console.log('=== MASTER CSS EXTRACTION SCRIPT ===');
console.log('This script will extract CSS from all Vue components in the project.\n');

// Function to process files in a directory recursively
function processDirectory(dir) {
  // Check if directory exists
  if (!fs.existsSync(dir)) {
    console.warn(`Directory doesn't exist, skipping: ${dir}`);
    return;
  }

  console.log(`Processing directory: ${path.relative(SRC_DIR, dir)}`);

  // Read directory contents
  const entries = fs.readdirSync(dir, { withFileTypes: true });

  // Process each entry
  for (const entry of entries) {
    const fullPath = path.join(dir, entry.name);

    if (entry.isDirectory()) {
      // Recursively process subdirectories
      processDirectory(fullPath);
    } else if (entry.isFile() && entry.name.endsWith('.vue')) {
      // Process Vue file
      const componentName = entry.name.replace('.vue', '');
      console.log(`\nProcessing ${componentName}...`);

      // Check if the component has a style tag
      const content = fs.readFileSync(fullPath, 'utf8');
      if (!content.includes('<style')) {
        console.log(`No style tag found in ${componentName}, skipping.`);
        continue;
      }

      try {
        // Execute the extract-css script for this component
        execSync(`node ${path.join(__dirname, 'extract-css.mjs')} ${componentName}`, {
          stdio: 'inherit',
          cwd: path.join(__dirname, '..')
        });
        console.log(`Successfully processed ${componentName}`);
      } catch (error) {
        console.error(`Error processing ${componentName}: ${error.message}`);
      }
    }
  }
}

// Process all component directories
for (const dir of COMPONENT_DIRS) {
  processDirectory(dir);
}

console.log('\n=== CSS EXTRACTION COMPLETED ===');
console.log('All Vue component CSS has been moved to dedicated files.');
console.log('Check the src/css/components directory for the extracted files.');
