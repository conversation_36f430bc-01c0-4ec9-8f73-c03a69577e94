#!/usr/bin/env node

/**
 * CSS Extraction Script for Layout components
 *
 * This script extracts CSS from MainLayout.vue and DashboardLayout.vue
 * and moves them to dedicated SCSS files.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { execSync } from 'child_process';

// Get current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const LAYOUTS_DIR = path.join(__dirname, '..', 'src', 'layouts');

// Layout files to process
const layoutFiles = ['MainLayout', 'DashboardLayout'];

console.log('Starting CSS extraction for layout components...');

// Process each layout file
for (const componentName of layoutFiles) {
  console.log(`\nProcessing ${componentName}...`);
  const filePath = path.join(LAYOUTS_DIR, `${componentName}.vue`);

  // Check if file exists
  if (!fs.existsSync(filePath)) {
    console.error(`File not found: ${filePath}`);
    continue;
  }

  // Check if the component has a style tag
  const content = fs.readFileSync(filePath, 'utf8');
  if (!content.includes('<style')) {
    console.log(`No style tag found in ${componentName}, skipping.`);
    continue;
  }

  try {
    // Execute the extract-css script for this component
    execSync(`node ${path.join(__dirname, 'extract-css.mjs')} ${componentName}`, {
      stdio: 'inherit',
      cwd: path.join(__dirname, '..')
    });
    console.log(`Successfully processed ${componentName}`);
  } catch (error) {
    console.error(`Error processing ${componentName}: ${error.message}`);
  }
}

console.log('\nCSS extraction completed for layout components!');
