package models

import (
	"context"
	"io"
	"time"

	"github.com/Sakura-Byte/kikoeru-go/pkg/http_range"
)

// RangeReadCloserIF is an interface for reading ranges from a stream
type RangeReadCloserIF interface {
	// RangeRead reads a range from the stream
	RangeRead(ctx context.Context, httpRange http_range.Range) (io.ReadCloser, error)
}

// RangeReadCloser implements RangeReadCloserIF with a function
type RangeReadCloser struct {
	RangeReader func(ctx context.Context, httpRange http_range.Range) (io.ReadCloser, error)
}

// RangeRead implements RangeReadCloserIF
func (r *RangeReadCloser) RangeRead(ctx context.Context, httpRange http_range.Range) (io.ReadCloser, error) {
	return r.RangeReader(ctx, httpRange)
}

// StreamWithSeek represents a stream that can be seeked
type StreamWithSeek struct {
	io.ReadCloser
	io.Seeker
	Size int64
	Name string // Add Name field for archive compatibility
}

// GetSize returns the size of the stream
func (s *StreamWithSeek) GetSize() int64 {
	return s.Size
}

// GetName returns the name of the stream
func (s *StreamWithSeek) GetName() string {
	return s.Name
}

// FileStreamObj represents a file stream
type FileStreamObj struct {
	Obj        Obj
	ReadCloser io.ReadCloser
	Seek       io.Seeker
	Start      int64
	Size       int64
	Modified   time.Time
	Mimetype   string
}

// File is an interface for file operations
type File interface {
	io.Reader
	io.ReaderAt
	io.Seeker
	io.Closer
	GetSize() int64
}

// RangeReadCloserIFToFile creates a File from a RangeReadCloserIF
func RangeReadCloserIFToFile(rangeReadCloser RangeReadCloserIF, ctx context.Context, size int64) (File, error) {
	return &rangeReadCloserFile{
		rangeReadCloser: rangeReadCloser,
		ctx:             ctx,
		size:            size,
	}, nil
}

// rangeReadCloserFile implements File with a RangeReadCloserIF
type rangeReadCloserFile struct {
	rangeReadCloser RangeReadCloserIF
	ctx             context.Context
	size            int64
	rc              io.ReadCloser
	offset          int64
}

// Read implements io.Reader
func (r *rangeReadCloserFile) Read(p []byte) (n int, err error) {
	if r.rc == nil {
		r.rc, err = r.rangeReadCloser.RangeRead(r.ctx, http_range.Range{
			Start:  r.offset,
			Length: -1,
		})
		if err != nil {
			return 0, err
		}
	}
	n, err = r.rc.Read(p)
	r.offset += int64(n)
	return
}

// ReadAt implements io.ReaderAt
func (r *rangeReadCloserFile) ReadAt(p []byte, off int64) (n int, err error) {
	if r.rc != nil {
		err = r.rc.Close()
		if err != nil {
			return 0, err
		}
		r.rc = nil
	}
	r.rc, err = r.rangeReadCloser.RangeRead(r.ctx, http_range.Range{
		Start:  off,
		Length: int64(len(p)),
	})
	if err != nil {
		return 0, err
	}
	n, err = io.ReadFull(r.rc, p)
	if err == io.EOF || err == io.ErrUnexpectedEOF {
		err = nil
	}
	r.offset = off + int64(n)
	return
}

// Seek implements io.Seeker
func (r *rangeReadCloserFile) Seek(offset int64, whence int) (int64, error) {
	switch whence {
	case io.SeekStart:
		r.offset = offset
	case io.SeekCurrent:
		r.offset += offset
	case io.SeekEnd:
		r.offset = r.size + offset
	}
	if r.rc != nil {
		err := r.rc.Close()
		if err != nil {
			return 0, err
		}
		r.rc = nil
	}
	return r.offset, nil
}

// Close implements io.Closer
func (r *rangeReadCloserFile) Close() error {
	if r.rc != nil {
		return r.rc.Close()
	}
	return nil
}

// GetSize returns the size of the file
func (r *rangeReadCloserFile) GetSize() int64 {
	return r.size
}
