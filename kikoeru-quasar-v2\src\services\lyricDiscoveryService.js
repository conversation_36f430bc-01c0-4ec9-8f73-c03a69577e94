/**
 * Service for discovering and managing lyric files
 */

import { api } from 'src/boot/axios'
import {
  audioLyricNameMatch,
  isLyricFile,
  basenameWithoutExt,
  calculateSimilarityPercentage
} from 'src/utils/lyricUtils'

/**
 * Recursively search for lyric files in a tree structure
 * @param {Object} treeNode - Tree node from WorkTree API
 * @param {Array} lyricFiles - Array to collect lyric files
 */
function collectLyricFiles(treeNode, lyricFiles = []) {
  if (!treeNode) return lyricFiles

  // If this is a file and it's a lyric file, add it
  if (!treeNode.entry.is_dir && isLyricFile(treeNode.entry.name)) {
    lyricFiles.push({
      name: treeNode.entry.name,
      path: treeNode.entry.path,
      size: treeNode.entry.size,
      isInArchive: false,
      archivePath: '',
      type: 'local'
    })
  }

  // Recursively search children
  if (treeNode.children && Array.isArray(treeNode.children)) {
    for (const child of treeNode.children) {
      collectLyricFiles(child, lyricFiles)
    }
  }

  return lyricFiles
}

/**
 * Find matching lyric files for an audio file
 * @param {string} audioFileName - Name of the audio file
 * @param {Array} lyricFiles - Array of available lyric files
 * @returns {Array} - Array of matching lyric files with similarity scores
 */
function findMatchingLyrics(audioFileName, lyricFiles) {
  const matches = []

  for (const lyricFile of lyricFiles) {
    const lyricBasename = basenameWithoutExt(lyricFile.name)

    if (audioLyricNameMatch(audioFileName, lyricBasename)) {
      const similarity = calculateSimilarityPercentage(audioFileName, lyricFile.name)

      matches.push({
        ...lyricFile,
        similarity,
        displayName: `${lyricFile.name} (相似度 ${similarity}%)`
      })
    }
  }

  // Sort by similarity (highest first)
  matches.sort((a, b) => b.similarity - a.similarity)

  return matches
}

/**
 * Load lyric file content from local file system
 * @param {number} storageId - Storage ID
 * @param {string} filePath - Path to the lyric file (relative to work root)
 * @param {string} workPathInStorage - Work's path_in_storage from work info
 * @returns {Promise<string>} - Lyric file content
 */
async function loadLocalLyricContent(storageId, filePath, workPathInStorage) {
  try {
    // Construct the full path by combining work's path_in_storage with the file path
    const fullPath = workPathInStorage ? `${workPathInStorage}/${filePath}` : filePath
    console.log('Loading local lyric content:', { storageId, filePath, workPathInStorage, fullPath })

    // Get file link first using the file system API
    const linkResponse = await api.get('/api/v1/fs/link', {
      params: {
        storage_id: storageId,
        path: fullPath
      }
    })

    const linkData = linkResponse.data
    console.log('Got file link:', linkData)

    // Fetch the actual content from the file URL
    const contentResponse = await fetch(linkData.url)
    if (!contentResponse.ok) {
      throw new Error(`Failed to fetch lyric content: ${contentResponse.statusText}`)
    }

    const content = await contentResponse.text()
    console.log('Successfully loaded lyric content, length:', content.length)
    return content
  } catch (error) {
    console.error('Failed to load local lyric content:', error)
    throw error
  }
}

/**
 * Get UGC subtitles for a track (database-stored user-generated subtitles)
 * @param {string} originalId - Work original ID
 * @param {string} trackPath - Track path
 * @param {boolean} isInArchive - Whether track is in archive
 * @param {string} archivePath - Archive path if in archive
 * @returns {Promise<Array>} - Array of UGC subtitles
 */
async function getUGCSubtitles(originalId, trackPath, isInArchive = false, archivePath = '') {
  try {
    console.log('Searching for UGC subtitles:', { originalId, trackPath, isInArchive, archivePath })

    const response = await api.get('/api/v1/subtitles/find', {
      params: {
        original_id: originalId,
        track_path: trackPath,
        is_in_archive: isInArchive,
        archive_path: archivePath
      }
    })

    const subtitles = response.data || []
    console.log(`Found ${subtitles.length} UGC subtitles`)

    return subtitles.map(subtitle => ({
      id: subtitle.id,
      name: `${subtitle.description || 'UGC字幕'}.${subtitle.format}`,
      path: '', // UGC subtitles are stored in database, not as files
      size: 0,
      isInArchive: subtitle.is_in_archive || false,
      archivePath: subtitle.archive_path || '',
      type: 'ugc',
      format: subtitle.format,
      description: subtitle.description,
      upVotes: subtitle.up_votes || 0,
      downVotes: subtitle.down_votes || 0,
      uploaderName: subtitle.uploader_name || 'Unknown',
      similarity: Math.max(70 - subtitle.down_votes + subtitle.up_votes, 50), // Adjust based on votes
      displayName: `${subtitle.description || 'UGC字幕'}.${subtitle.format} (UGC) (相似度 ${Math.max(70 - subtitle.down_votes + subtitle.up_votes, 50)}%)`
    }))
  } catch (error) {
    console.warn('Failed to load UGC subtitles:', error)
    return []
  }
}

/**
 * Convert SRT/VTT subtitle format to LRC format
 * @param {string} text - SRT or VTT content
 * @returns {string} - LRC formatted content
 */
function convertSrtVttToLrc(text) {
  let lines = text.split("\n").map(l => l.trim())
  let isVtt = lines[0] == 'WEBVTT'
  if (isVtt) {
    lines = lines.slice(1)
  }

  const timeParseRe = /(\d*):(\d*):(\d*)(\.|,)(\d*)\s*-->\s*[\d:.]*/

  const parsingUnit = [] // [([hour, minute, seconds, milliseconds], '文字\n文字'), (), ..., ()]
  let i = 0
  while(i < lines.length) {
    // 注意 srt 和 vtt 字幕的毫秒区分符号一个是`,`另一个是`.`
    // audio.srt be like
    // 1
    // 00:01:22,343 --> 00:03:22,344
    // 字幕，字幕
    //
    // 2
    // ...

    // audio.vtt be like
    // WEBVTT
    //
    // 1
    // 00:01:22.343 --> 00:03:22.344
    // 字幕，字幕
    //
    // 2
    // ...

    if (/^\d*$/.test(lines[i++])) { /* parse 序号 */
      if (timeParseRe.test(lines[i])) { /* parse 时间戳 */
        const [/* whole string */, h, m, s, /* ignore */, ms] = timeParseRe.exec(lines[i]).map(x => parseInt(x))
        let texts = []
        i++
        while(i < lines.length && lines[i] != "") { /* parse 文字，直到空行 */
          texts.push(lines[i++])
        }
        parsingUnit.push([
          [h, m, s, ms],
          texts.join(' '),
        ])
      }
    }
  } // parse srt vtt 完成

  function padding(n, len) {
    n = Math.ceil(n)
    let s = `${n}`
    let pad = len - s.length
    if (pad > 0) {
      for (let i = 0; i < pad; ++i) {
        s = "0" + s
      }
    }
    return s
  }

  function formatLrcTime([h, m, s, ms]) {
    return padding(h * 60 + m, 2) + ":" + padding(s, 2) + "." + padding(ms, 3)
  }

  const lrcContent = parsingUnit.map(([time, text]) => `[${formatLrcTime(time)}] ${text}`).join("\n")
  return lrcContent
}

/**
 * Load UGC subtitle content
 * @param {number} subtitleId - Subtitle ID
 * @returns {Promise<string>} - Subtitle content
 */
async function loadUGCSubtitleContent(subtitleId) {
  try {
    const response = await api.get(`/api/v1/subtitles/${subtitleId}/content`)
    // The response should contain the subtitle content directly
    return response.data
  } catch (error) {
    console.error('Failed to load UGC subtitle content:', error)
    throw error
  }
}

/**
 * Main service class for lyric discovery
 */
export default class LyricDiscoveryService {
  /**
   * Discover all available lyric files for a track
   * @param {Object} currentFile - Current playing file object
   * @param {Object} treeData - Tree data from WorkTree component
   * @returns {Promise<Array>} - Array of available lyric files
   */
  static async discoverLyrics(currentFile, treeData = null) {
    if (!currentFile || !currentFile.trackPath) {
      return []
    }

    const allLyrics = []

    try {
      // 1. Search local file system if tree data is available
      if (treeData) {
        console.log('Searching for lyrics in tree data for:', currentFile.trackPath)
        const localLyrics = collectLyricFiles(treeData)
        const matchingLocal = findMatchingLyrics(currentFile.trackPath, localLyrics)
        console.log(`Found ${matchingLocal.length} matching local lyric files`)
        allLyrics.push(...matchingLocal)
      } else {
        console.log('No tree data available, skipping local file search')
      }

      // 2. Get UGC subtitles
      if (currentFile.originalId) {
        const ugcSubtitles = await getUGCSubtitles(
          currentFile.originalId,
          currentFile.trackPath,
          currentFile.isInArchive || false,
          currentFile.archivePath || ''
        )
        allLyrics.push(...ugcSubtitles)
      }

      // 3. Sort all lyrics by similarity (local files get priority)
      allLyrics.sort((a, b) => {
        // Local files get priority over UGC
        if (a.type === 'local' && b.type === 'ugc') return -1
        if (a.type === 'ugc' && b.type === 'local') return 1

        // Then sort by similarity
        return b.similarity - a.similarity
      })

      return allLyrics
    } catch (error) {
      console.error('Failed to discover lyrics:', error)
      return []
    }
  }

  /**
   * Load lyric content
   * @param {Object} lyricFile - Lyric file object
   * @param {number} storageId - Storage ID (for local files)
   * @param {string} workPathInStorage - Work's path_in_storage (for local files)
   * @returns {Promise<string>} - Lyric content in LRC format
   */
  static async loadLyricContent(lyricFile, storageId = null, workPathInStorage = null) {
    let content = ''

    if (lyricFile.type === 'ugc') {
      content = await loadUGCSubtitleContent(lyricFile.id)
    } else if (lyricFile.type === 'local') {
      if (!storageId) {
        throw new Error('Storage ID required for local lyric files')
      }
      if (!workPathInStorage) {
        throw new Error('Work path_in_storage required for local lyric files')
      }
      content = await loadLocalLyricContent(storageId, lyricFile.path, workPathInStorage)
    } else {
      throw new Error('Unknown lyric file type')
    }

    // Convert SRT/VTT to LRC format if needed
    const fileExtension = lyricFile.format || lyricFile.name?.split('.').pop()?.toLowerCase()

    if (fileExtension === 'srt' || fileExtension === 'vtt') {
      console.log(`Converting ${fileExtension.toUpperCase()} to LRC format`)
      content = convertSrtVttToLrc(content)
      console.log('Converted content preview:', content.substring(0, 200))
    } else if (fileExtension === 'lrc') {
      console.log('Content is already in LRC format')
    } else {
      console.log('Unknown format, treating as LRC:', fileExtension)
    }

    return content
  }

  /**
   * Open file picker for manual lyric file selection
   * @returns {Promise<string>} - Selected file content
   */
  static async selectLocalFile() {
    return new Promise((resolve, reject) => {
      const input = document.createElement('input')
      input.type = 'file'
      input.accept = '.lrc,.ass,.vtt,.srt'

      let isResolved = false

      input.onchange = async (event) => {
        if (isResolved) return

        const file = event.target.files[0]
        if (!file) {
          isResolved = true
          reject(new Error('No file selected'))
          return
        }

        try {
          const content = await file.text()
          isResolved = true
          resolve(content)
        } catch (error) {
          isResolved = true
          reject(error)
        }
      }

      // Handle cancel case - when user clicks away or presses ESC
      input.oncancel = () => {
        if (!isResolved) {
          isResolved = true
          reject(new Error('File selection cancelled'))
        }
      }

      // Fallback: detect when the input loses focus without selecting a file
      const handleFocusLoss = () => {
        setTimeout(() => {
          if (!isResolved && (!input.files || input.files.length === 0)) {
            isResolved = true
            reject(new Error('File selection cancelled'))
          }
        }, 100) // Small delay to allow onchange to fire first
      }

      // Listen for focus events on window to detect when dialog is dismissed
      const handleWindowFocus = () => {
        handleFocusLoss()
        window.removeEventListener('focus', handleWindowFocus)
      }

      window.addEventListener('focus', handleWindowFocus)

      input.click()
    })
  }
}
