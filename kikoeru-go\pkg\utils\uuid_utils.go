package utils

import (
	"fmt"

	"github.com/google/uuid"
)

// KikoeruScraperNamespaceUUID is the fixed namespace UUID used in Node.js version's nameToUUID.
// Namespace: 699d9c07-b965-4399-bafd-18a3cacf073c
var KikoeruScraperNamespaceUUID uuid.UUID

func init() {
	var err error
	KikoeruScraperNamespaceUUID, err = uuid.Parse("699d9c07-b965-4399-bafd-18a3cacf073c")
	if err != nil {
		// This should not happen if the UUID string is correct.
		// Panic during init to catch this early.
		panic(fmt.Sprintf("Failed to parse KikoeruScraperNamespaceUUID: %v", err))
	}
}

// GenerateUUIDv5ForName generates a UUID v5 for a given name string using the Kikoeru scraper's fixed namespace.
// This is intended to be compatible with Node.js version's nameToUUID function.
func GenerateUUIDv5ForName(name string) (string, error) {
	if name == "" {
		return "", fmt.Errorf("name cannot be empty for UUIDv5 generation")
	}
	// uuid.NewSHA1 generates a UUID v5 (SHA-1 hash based)
	generatedUUID := uuid.NewSHA1(KikoeruScraperNamespaceUUID, []byte(name))
	return generatedUUID.String(), nil
}
