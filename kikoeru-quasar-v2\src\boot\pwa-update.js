import { boot } from 'quasar/wrappers'
import { Notify } from 'quasar'

// This boot file adds PWA update notification functionality

export default boot(({ app }) => {
  // Listen for service worker update events
  window.addEventListener('beforeinstallprompt', (e) => {
    e.preventDefault()
    // Store the event for later use
    app.config.globalProperties.$installPrompt = e
  })

  // Listen for service worker update available
  if ('serviceWorker' in navigator) {
    let refreshing = false
    navigator.serviceWorker.addEventListener('controllerchange', () => {
      if (refreshing) return
      refreshing = true
      window.location.reload() // Auto-refresh the page
    })

    // Check for service worker updates
    navigator.serviceWorker.ready.then((registration) => {
      registration.addEventListener('updatefound', () => {
        const newSW = registration.installing

        newSW.addEventListener('statechange', () => {
          if (newSW.state === 'installed' && navigator.serviceWorker.controller) {
            // New service worker is installed but waiting to activate
            Notify.create({
              message: 'New version available',
              icon: 'cloud_download',
              color: 'primary',
              actions: [{
                label: 'Update',
                color: 'white',
                handler: () => {
                  // Send message to service worker to skip waiting
                  newSW.postMessage({ type: 'SKIP_WAITING' })
                }
              }],
              timeout: 0
            })
          }
        })
      })
    })
  }
})
