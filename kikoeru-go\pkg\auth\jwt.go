package auth

import (
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// UserClaimsKey is the key used to store and retrieve user claims from a context.
// It's defined here to be used pacote auth, middleware, and service layers consistently.
const UserClaimsKey = "userClaimsContextKey" // Using a more specific string to avoid collisions

// Claims 结构定义了 JWT 中存储的自定义声明
type Claims struct {
	UserID    string `json:"user_id"`    // User's UUID
	Username  string `json:"username"`   // User's login name
	UserGroup string `json:"user_group"` // 例如 "admin", "user"
	jwt.RegisteredClaims
}

// GenerateToken 生成一个新的 JWT 字符串
// userID: 用户的唯一标识符 (UUID string)
// username: 用户的登录名
// userGroup: 要包含在 token 中的用户组
// secretKey: 用于签名 token 的密钥
// expiresIn: token 的有效期
func GenerateToken(userID string, username string, userGroup string, secretKey []byte, expiresIn time.Duration) (string, error) {
	if len(secretKey) == 0 {
		return "", fmt.Errorf("jwt secret key cannot be empty")
	}
	if userID == "" {
		return "", fmt.Errorf("userID cannot be empty for token generation")
	}
	if username == "" {
		return "", fmt.Errorf("username cannot be empty for token generation")
	}

	expirationTime := time.Now().Add(expiresIn)
	claims := &Claims{
		UserID:    userID, // Store UserID (UUID)
		Username:  username,
		UserGroup: userGroup,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expirationTime),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Subject:   userID, // Set Subject to UserID (UUID)
			// Issuer:    "kikoeru-go", // Optional: set an issuer
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString(secretKey)
	if err != nil {
		return "", fmt.Errorf("failed to sign token: %w", err)
	}

	return tokenString, nil
}

// ValidateToken 解析并验证 JWT 字符串
func ValidateToken(tokenString string, secretKey []byte) (*Claims, error) {
	if len(secretKey) == 0 {
		return nil, fmt.Errorf("jwt secret key cannot be empty for validation")
	}

	claims := &Claims{}
	token, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return secretKey, nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	if !token.Valid {
		return nil, fmt.Errorf("invalid token")
	}

	// Ensure UserID (which is also subject) is present
	if claims.UserID == "" || claims.Subject == "" || claims.UserID != claims.Subject {
		return nil, fmt.Errorf("token is missing UserID or subject, or they mismatch")
	}
	if claims.Username == "" { // Also ensure username is present
		return nil, fmt.Errorf("token is missing username claim")
	}

	return claims, nil
}
