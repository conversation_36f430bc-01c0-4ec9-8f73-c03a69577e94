package models

import (
	"time"
)

// ArchivePassword represents a password in the archive password list
type ArchivePassword struct {
	ID          uint      `gorm:"primaryKey" json:"id"`
	Password    string    `gorm:"type:varchar(255);not null;uniqueIndex" json:"password"`
	Description string    `gorm:"type:text" json:"description"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// TableName specifies the table name for GORM
func (ArchivePassword) TableName() string {
	return "t_archive_passwords"
}

// ArchivePasswordCache represents a cached password for a specific archive
type ArchivePasswordCache struct {
	ID          uint      `gorm:"primaryKey" json:"id"`
	StorageID   uint      `gorm:"not null;index" json:"storage_id"`
	ArchivePath string    `gorm:"type:varchar(1024);not null" json:"archive_path"`
	Password    string    `gorm:"type:varchar(255);not null" json:"password"`
	LastUsedAt  time.Time `gorm:"not null" json:"last_used_at"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`

	// Relationships
	StorageSource *StorageSource `gorm:"foreignKey:StorageID" json:"storage_source,omitempty"`
}

// TableName specifies the table name for GORM
func (ArchivePasswordCache) TableName() string {
	return "t_archive_password_cache"
}

// ArchivePasswordListRequest represents a request to add passwords to the list
type ArchivePasswordListRequest struct {
	Passwords []ArchivePasswordInput `json:"passwords" binding:"required,min=1"`
}

// ArchivePasswordInput represents input for a single password
type ArchivePasswordInput struct {
	Password    string `json:"password" binding:"required,min=1,max=255"`
	Description string `json:"description" binding:"max=500"`
}

// ArchivePasswordBatchImportRequest represents a request to batch import passwords from text
type ArchivePasswordBatchImportRequest struct {
	PasswordText string `json:"password_text" binding:"required"`
	Separator    string `json:"separator"` // Optional, defaults to newline
}

// ArchivePasswordUpdateRequest represents a request to update a password
type ArchivePasswordUpdateRequest struct {
	Password    string `json:"password" binding:"required,min=1,max=255"`
	Description string `json:"description" binding:"max=500"`
}

// ArchivePasswordResponse represents the response for password operations
type ArchivePasswordResponse struct {
	ID          uint      `json:"id"`
	Password    string    `json:"password"`
	Description string    `json:"description"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// ArchivePasswordCacheResponse represents the response for password cache operations
type ArchivePasswordCacheResponse struct {
	ID          uint      `json:"id"`
	StorageID   uint      `json:"storage_id"`
	ArchivePath string    `json:"archive_path"`
	Password    string    `json:"password"`
	LastUsedAt  time.Time `json:"last_used_at"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// ArchivePasswordBatchImportResponse represents the response for batch import
type ArchivePasswordBatchImportResponse struct {
	Added      int      `json:"added"`
	Duplicates int      `json:"duplicates"`
	Invalid    int      `json:"invalid"`
	Total      int      `json:"total"`
	Errors     []string `json:"errors,omitempty"`
}
