import { computed } from 'vue'
import { useWorkInfoStore } from '../stores/workInfo'

export function useWorkInfo() {
  const workInfoStore = useWorkInfoStore()

  const getTreeData = (originalId) => {
    return workInfoStore.getTreeData(originalId)
  }

  const hasTreeData = (originalId) => {
    return workInfoStore.hasTreeData(originalId)
  }

  const setTreeData = (options) => {
    workInfoStore.setTreeData(options)
  }

  const clearTreeData = (originalId) => {
    workInfoStore.clearTreeData(originalId)
  }

  const treeDataCache = computed(() => workInfoStore.treeDataCache)

  return {
    // State
    treeDataCache,

    // Actions
    getTreeData,
    hasTreeData,
    setTreeData,
    clearTreeData
  }
}
