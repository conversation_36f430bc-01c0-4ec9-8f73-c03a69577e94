package zip

import (
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"testing"

	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/archive/tool"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/driver"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/driver/local"
)

// MockLocalDriver creates a mock local driver for testing
func createMockLocalDriver(rootPath string) (driver.StorageDriver, error) {
	// Create the local driver with the test directory
	additionConfig := map[string]interface{}{
		"root_folder_path": rootPath,
		"show_hidden":      false,
	}

	commonConfig := driver.AlistDriverCommonConfig{}
	localDriver, err := local.NewLocal(commonConfig, additionConfig)
	if err != nil {
		return nil, err
	}

	// Initialize the driver
	err = localDriver.Init(context.Background())
	if err != nil {
		return nil, err
	}

	return localDriver, nil
}

// Helper function to create streams from files
func createStreamsFromFiles(localDriver driver.StorageDriver, filePaths []string) ([]*models.StreamWithSeek, error) {
	var streams []*models.StreamWithSeek

	for _, filePath := range filePaths {
		// Get file object from driver using Getter interface
		getter, ok := localDriver.(driver.Getter)
		if !ok {
			return nil, fmt.Errorf("driver does not implement Getter interface")
		}

		fileObj, err := getter.Get(context.Background(), filePath)
		if err != nil {
			return nil, fmt.Errorf("failed to get file %s: %w", filePath, err)
		}

		// Get file stream using Proxy method (check if driver implements it)
		proxier, ok := localDriver.(interface {
			Proxy(ctx context.Context, file driver.AlistObj, httpRange string) (io.ReadCloser, string, int64, int64, int, error)
		})
		if !ok {
			return nil, fmt.Errorf("driver does not implement Proxy method")
		}

		stream, _, originalSize, actualSize, _, err := proxier.Proxy(context.Background(), fileObj, "")
		if err != nil {
			return nil, fmt.Errorf("failed to get stream for %s: %w", filePath, err)
		}

		// Create StreamWithSeek
		streamWithSeek := &models.StreamWithSeek{
			ReadCloser: stream,
			Seeker:     stream.(io.Seeker), // Local driver returns a seeker
			Size:       actualSize,
			Name:       filepath.Base(filePath),
		}

		// Override size with original size if different
		if originalSize != actualSize {
			streamWithSeek.Size = originalSize
		}

		streams = append(streams, streamWithSeek)
	}

	return streams, nil
}

// TestRealMultipartZipFiles tests with real multipart ZIP files
func TestRealMultipartZipFiles(t *testing.T) {
	// Test directory path - adjust this to your actual test files location
	testDir := `D:\DLsite\**********~**********\**********`

	// Check if test directory exists
	if _, err := os.Stat(testDir); os.IsNotExist(err) {
		t.Skipf("Test directory %s does not exist, skipping real file test", testDir)
		return
	}

	// Create mock local driver
	localDriver, err := createMockLocalDriver(testDir)
	if err != nil {
		t.Fatalf("Failed to create mock local driver: %v", err)
	}

	// Test files in the directory
	expectedFiles := []string{"test.zip", "test.z01", "test.z02", "test.z03"}

	// Check if all expected files exist
	getter, ok := localDriver.(driver.Getter)
	if !ok {
		t.Fatal("Local driver does not implement Getter interface")
	}

	for _, fileName := range expectedFiles {
		filePath := fileName
		_, err := getter.Get(context.Background(), filePath)
		if err != nil {
			t.Skipf("Required test file %s not found, skipping test", fileName)
			return
		}
	}

	t.Log("All test files found, proceeding with multipart ZIP test")

	// First, let's test if we can read the main ZIP file alone (it might be complete)
	t.Run("TestMainZipFile", func(t *testing.T) {
		// Try to read just the main ZIP file
		filePaths := []string{"test.zip"}
		streams, err := createStreamsFromFiles(localDriver, filePaths)
		if err != nil {
			t.Fatalf("Failed to create streams: %v", err)
		}
		defer func() {
			for _, stream := range streams {
				stream.Close()
			}
		}()

		z := &Zip{}

		// Test with password "1234"
		args := models.ArchiveArgs{
			Filename: "test.zip",
			Encoding: "",
			Password: "1234",
		}

		meta, err := z.GetMeta(streams[0], args)
		if err != nil {
			t.Logf("GetMeta on main ZIP file failed: %v", err)
			t.Log("This suggests the ZIP file is incomplete and requires all parts")
		} else {
			t.Log("Successfully read main ZIP file")
			tree := meta.GetTree()
			t.Logf("Found %d files in main ZIP", len(tree))

			if meta.IsEncrypted() {
				t.Log("Main ZIP file is encrypted")
			}

			// Test listing files
			listArgs := models.ArchiveInnerArgs{
				ArchiveArgs: args,
				InnerPath:   "/",
			}

			// Create new stream for listing
			streams2, err := createStreamsFromFiles(localDriver, filePaths)
			if err != nil {
				t.Fatalf("Failed to recreate streams for listing: %v", err)
			}
			defer func() {
				for _, stream := range streams2 {
					stream.Close()
				}
			}()

			objects, err := z.List(streams2[0], listArgs)
			if err != nil {
				t.Fatalf("List failed: %v", err)
			}

			t.Logf("List returned %d objects", len(objects))
			for i, obj := range objects {
				t.Logf("Object %d: %s (size: %d, folder: %t)", i, obj.GetName(), obj.GetSize(), obj.IsDir())
			}

			// Test extraction if we have files
			if len(objects) > 0 {
				firstFile := objects[0]
				if !firstFile.IsDir() {
					extractArgs := models.ArchiveInnerArgs{
						ArchiveArgs: args,
						InnerPath:   firstFile.GetPath(),
					}

					// Create new stream for extraction
					streams3, err := createStreamsFromFiles(localDriver, filePaths)
					if err != nil {
						t.Fatalf("Failed to recreate streams for extraction: %v", err)
					}
					defer func() {
						for _, stream := range streams3 {
							stream.Close()
						}
					}()

					rc, size, err := z.Extract(streams3[0], extractArgs)
					if err != nil {
						t.Fatalf("Extract failed: %v", err)
					}
					defer rc.Close()

					t.Logf("Successfully extracted file %s (size: %d)", firstFile.GetName(), size)

					// Read a small portion to verify
					buffer := make([]byte, 1024)
					n, err := rc.Read(buffer)
					if err != nil && err != io.EOF {
						t.Fatalf("Failed to read extracted data: %v", err)
					}

					t.Logf("Read %d bytes from extracted file", n)
				}
			}
		}
	})

	// Test Case 1: Test with .zip + .z01/.z02/.z03 format
	t.Run("MultipartZipWithZ01Format", func(t *testing.T) {
		// Create streams for all parts
		filePaths := []string{"test.zip", "test.z01", "test.z02", "test.z03"}
		streams, err := createStreamsFromFiles(localDriver, filePaths)
		if err != nil {
			t.Fatalf("Failed to create streams: %v", err)
		}
		defer func() {
			for _, stream := range streams {
				stream.Close()
			}
		}()

		// Test the ZIP handler
		z := &Zip{}

		// Test GetMetaMultipart without password first
		args := models.ArchiveArgs{
			Filename: "test.zip",
			Encoding: "",
			Password: "",
		}

		meta, err := z.GetMetaMultipart(streams, args)
		if err != nil {
			t.Logf("GetMetaMultipart without password failed (expected if encrypted): %v", err)
		} else {
			t.Logf("GetMetaMultipart without password succeeded, archive may not be encrypted")
			tree := meta.GetTree()
			t.Logf("Found %d files in archive", len(tree))
		}

		// Test with password "1234"
		argsWithPassword := models.ArchiveArgs{
			Filename: "test.zip",
			Encoding: "",
			Password: "1234",
		}

		// Recreate streams as they may have been consumed
		streams2, err := createStreamsFromFiles(localDriver, filePaths)
		if err != nil {
			t.Fatalf("Failed to recreate streams: %v", err)
		}
		defer func() {
			for _, stream := range streams2 {
				stream.Close()
			}
		}()

		meta, err = z.GetMetaMultipart(streams2, argsWithPassword)
		if err != nil {
			t.Fatalf("GetMetaMultipart with password failed: %v", err)
		}

		if !meta.IsEncrypted() {
			t.Log("Archive is not encrypted")
		} else {
			t.Log("Archive is encrypted and password worked")
		}

		tree := meta.GetTree()
		if len(tree) == 0 {
			t.Fatal("Expected files in multipart archive")
		}

		t.Logf("Successfully read multipart ZIP with %d files", len(tree))

		// Test ListMultipart
		listArgs := models.ArchiveInnerArgs{
			ArchiveArgs: argsWithPassword,
			InnerPath:   "/",
		}

		// Recreate streams for listing
		streams3, err := createStreamsFromFiles(localDriver, filePaths)
		if err != nil {
			t.Fatalf("Failed to recreate streams for listing: %v", err)
		}
		defer func() {
			for _, stream := range streams3 {
				stream.Close()
			}
		}()

		objects, err := z.ListMultipart(streams3, listArgs)
		if err != nil {
			t.Fatalf("ListMultipart failed: %v", err)
		}

		t.Logf("ListMultipart returned %d objects", len(objects))
		for i, obj := range objects {
			t.Logf("Object %d: %s (size: %d, folder: %t)", i, obj.GetName(), obj.GetSize(), obj.IsDir())
		}

		// Test ExtractMultipart - try to extract the first file
		if len(objects) > 0 {
			firstFile := objects[0]
			if !firstFile.IsDir() {
				extractArgs := models.ArchiveInnerArgs{
					ArchiveArgs: argsWithPassword,
					InnerPath:   firstFile.GetPath(),
				}

				// Recreate streams for extraction
				streams4, err := createStreamsFromFiles(localDriver, filePaths)
				if err != nil {
					t.Fatalf("Failed to recreate streams for extraction: %v", err)
				}
				defer func() {
					for _, stream := range streams4 {
						stream.Close()
					}
				}()

				rc, size, err := z.ExtractMultipart(streams4, extractArgs)
				if err != nil {
					t.Fatalf("ExtractMultipart failed: %v", err)
				}
				defer rc.Close()

				t.Logf("Successfully extracted file %s (size: %d)", firstFile.GetName(), size)

				// Read a small portion to verify
				buffer := make([]byte, 1024)
				n, err := rc.Read(buffer)
				if err != nil && err != io.EOF {
					t.Fatalf("Failed to read extracted data: %v", err)
				}

				t.Logf("Read %d bytes from extracted file", n)
			}
		}
	})

	t.Log("Real multipart ZIP test completed successfully!")
}

// TestZipToolRegistration tests that the ZIP tool is properly registered
func TestZipToolRegistration(t *testing.T) {
	// Test that ZIP tool is registered
	zipTool, multipartExt, err := tool.GetArchiveTool("test.zip")
	if err != nil {
		t.Fatalf("ZIP tool not registered: %v", err)
	}

	if zipTool == nil {
		t.Fatal("ZIP tool is nil")
	}

	isMultipart := multipartExt != nil
	t.Logf("ZIP tool registered successfully, multipart: %t", isMultipart)

	// Test if it's a multipart tool
	if multipartTool, ok := zipTool.(tool.MultipartTool); ok {
		extensions := multipartTool.AcceptedMultipartExtensions()
		if len(extensions) == 0 {
			t.Fatal("No multipart extensions found")
		}

		for ext, multipartExtInfo := range extensions {
			t.Logf("Multipart extension: %s -> pattern: %s, start: %d", ext, multipartExtInfo.Pattern, multipartExtInfo.StartFrom)
		}
	} else {
		t.Log("ZIP tool does not implement MultipartTool interface")
	}
}
