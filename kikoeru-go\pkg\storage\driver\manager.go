package driver

import (
	"context"
	"encoding/json"
	"fmt"
	"reflect"
	"sync"

	"github.com/Sakura-Byte/kikoeru-go/pkg/config"
	"github.com/Sakura-Byte/kikoeru-go/pkg/log"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"

	// "github.com/Sakura-Byte/kikoeru-go/pkg/storage/cachedriver" // Removed to break import cycle
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/database"
)

// globalDriverWrapper holds the registered wrapper function.
var globalDriverWrapper DriverWrapperFunc

// RegisterDriverWrapper sets the global wrapper function to be used by StorageDriverManager.
// This should be called once during application initialization (e.g., in main.go)
// if a wrapper like CachedStorageDriver is to be used.
func RegisterDriverWrapper(wrapperFunc DriverWrapperFunc) {
	if globalDriverWrapper != nil {
		// Log or panic if it's registered multiple times, depending on desired behavior
		log.Warn(context.Background(), "RegisterDriverWrapper called multiple times. Overwriting existing wrapper.")
	}
	globalDriverWrapper = wrapperFunc
}

type StorageDriverManager struct {
	drivers       map[uint]StorageDriver
	constructors  map[string]DriverConstructor
	storageRepo   database.StorageSourceRepository
	mu            sync.RWMutex
	appConfig     *config.AppConfig
	appCommonCfg  AlistDriverCommonConfig
	driverWrapper DriverWrapperFunc // Store the wrapper func for this manager instance
	pathResolver  *PathResolver     // Add path resolver
}

func NewStorageDriverManager(
	storageRepo database.StorageSourceRepository,
	appCfg *config.AppConfig,
	appCommonCfg AlistDriverCommonConfig,
) *StorageDriverManager {
	return &StorageDriverManager{
		drivers:       make(map[uint]StorageDriver),
		constructors:  make(map[string]DriverConstructor),
		storageRepo:   storageRepo,
		appConfig:     appCfg,
		appCommonCfg:  appCommonCfg,
		driverWrapper: globalDriverWrapper, // Use the globally registered wrapper
		pathResolver:  &PathResolver{},     // Initialize path resolver
	}
}

func (m *StorageDriverManager) RegisterDriver(driverType string, constructor DriverConstructor) {
	m.mu.Lock()
	defer m.mu.Unlock()
	if _, exists := m.constructors[driverType]; exists {
		log.Warn(context.Background(), "Driver type already registered, overwriting.", "type", driverType)
	}
	m.constructors[driverType] = constructor
	log.Info(context.Background(), "Registered driver constructor", "type", driverType)
}

// GetDriver returns a driver for the given storage ID
func (m *StorageDriverManager) GetDriver(storageID uint) (StorageDriver, bool) {
	m.mu.RLock()
	driver, ok := m.drivers[storageID]
	m.mu.RUnlock()

	if !ok {
		log.Error(context.Background(), "Driver not found in manager", "storage_id", storageID)
		return nil, false
	}

	// Add debug to see what interfaces are implemented
	if driver != nil {
		driverType := reflect.TypeOf(driver)
		log.Debug(context.Background(), "GetDriver returning driver",
			"storage_id", storageID,
			"driver_type", driverType.String(),
			"implements_IRootIDGetter", reflect.TypeOf(driver).Implements(reflect.TypeOf((*IRootIDGetter)(nil)).Elem()),
			"implements_IRootPathGetter", reflect.TypeOf(driver).Implements(reflect.TypeOf((*IRootPathGetter)(nil)).Elem()))
	}

	// We don't need to wrap the driver here since it should already be wrapped during initialization
	// in the InitDrivers method where m.driverWrapper is applied

	return driver, true
}

func (m *StorageDriverManager) InitDrivers(ctx context.Context) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.drivers = make(map[uint]StorageDriver)

	log.Info(context.Background(), "StorageDriverManager: Loading storages only from database.")
	dbSources, totalCount, err := m.storageRepo.ListAll(ctx, false, 1, 0)
	if err != nil {
		log.Error(context.Background(), "Failed to load storage sources from database", "error", err)
		return fmt.Errorf("failed to load storage sources from DB: %w", err)
	}

	if totalCount == 0 {
		log.Info(context.Background(), "No enabled storage sources found in database to initialize.")
		return nil
	}

	log.Info(context.Background(), "Initializing storage drivers from database...", "count", totalCount)
	for _, dbSource := range dbSources {
		constructor, exists := m.constructors[dbSource.Driver]
		if !exists {
			log.Error(context.Background(), "No constructor registered for driver type", "type", dbSource.Driver, "storage_id", dbSource.ID)
			continue
		}

		var additionCfg map[string]interface{}
		if dbSource.Addition != "" {
			if errUnmarshal := json.Unmarshal([]byte(dbSource.Addition), &additionCfg); errUnmarshal != nil {
				log.Error(context.Background(), "Failed to unmarshal driver addition from JSON in DB to map", "storage_id", dbSource.ID, "driver", dbSource.Driver, "error", errUnmarshal)
				continue
			}
		}

		// driverLogger := m.logger.With("driver_type", dbSource.Driver, "storage_id", dbSource.ID)

		instanceCommonCfg := m.appCommonCfg
		instanceCommonCfg.Name = dbSource.Driver
		instanceCommonCfg.StorageID = dbSource.ID

		rawDriverInstance, errConstruct := constructor(instanceCommonCfg, additionCfg)
		if errConstruct != nil {
			log.Error(context.Background(), "Failed to construct raw driver instance", "storage_id", dbSource.ID, "driver_type", dbSource.Driver, "error", errConstruct)
			continue
		}

		rawDriverInstance.SetStorage(*dbSource)
		if errInit := rawDriverInstance.Init(ctx); errInit != nil {
			log.Error(context.Background(), "Failed to initialize raw driver", "storage_id", dbSource.ID, "driver_type", dbSource.Driver, "error", errInit)
			continue
		}

		var finalDriverInstance StorageDriver = rawDriverInstance
		if m.driverWrapper != nil {
			finalDriverInstance = m.driverWrapper(rawDriverInstance, dbSource.ID, dbSource.CacheExpiration)
			log.Info(context.Background(), "Wrapped driver with registered wrapper function", "storage_id", dbSource.ID, "type", dbSource.Driver)
		}

		m.drivers[dbSource.ID] = finalDriverInstance
		log.Info(context.Background(), "Successfully initialized and registered storage driver from DB", "storage_id", dbSource.ID, "type", dbSource.Driver)
	}

	if len(m.drivers) == 0 {
		log.Warn(context.Background(), "No storage drivers were successfully initialized from database.")
	} else {
		log.Info(context.Background(), "Storage drivers initialized from database.", "count", len(m.drivers))
	}
	return nil
}

func (m *StorageDriverManager) ReloadAllDrivers(ctx context.Context) error {
	log.Info(context.Background(), "ReloadAllDrivers called. Re-initializing all storage drivers...")
	return m.InitDrivers(ctx)
}

func (m *StorageDriverManager) TestNewDriverConnection(
	ctx context.Context,
	driverType string,
	commonConfig AlistDriverCommonConfig,
	additionConfigJSON string,
) error {
	m.mu.RLock()
	constructor, exists := m.constructors[driverType]
	m.mu.RUnlock()

	if !exists {
		return fmt.Errorf("no constructor registered for driver type: %s", driverType)
	}

	var additionCfgForTest map[string]interface{}
	if additionConfigJSON != "" {
		if err := json.Unmarshal([]byte(additionConfigJSON), &additionCfgForTest); err != nil {
			return fmt.Errorf("failed to unmarshal driver addition_config_json to map for test: %w", err)
		}
	}

	// testDriverLogger := m.logger.With("test_driver_type", driverType)

	testInstanceCommonCfg := commonConfig
	testInstanceCommonCfg.Name = driverType

	tempDriver, err := constructor(testInstanceCommonCfg, additionCfgForTest)
	if err != nil {
		return fmt.Errorf("failed to construct temporary driver instance for testing (type: %s): %w", driverType, err)
	}

	tempStorageModel := models.StorageSource{
		Driver:   driverType,
		Addition: additionConfigJSON,
	}
	tempDriver.SetStorage(tempStorageModel)

	if err := tempDriver.Init(ctx); err != nil {
		return fmt.Errorf("failed to initialize temporary driver instance for testing (type: %s): %w", driverType, err)
	}

	if err := tempDriver.Drop(ctx); err != nil {
		log.Warn(ctx, "Error during Drop of temporary test driver, TestConnection itself succeeded.", "error", err)
	}
	return nil
}

func (m *StorageDriverManager) GetDriverDefinitions() []APIDriverDefinition {
	m.mu.RLock()
	defer m.mu.RUnlock()

	log.Info(context.Background(), "GetDriverDefinitions called", "registered_constructors_count", len(m.constructors))

	definitions := make([]APIDriverDefinition, 0, len(m.constructors))

	for driverNameKey, constructor := range m.constructors {
		log.Info(context.Background(), "Processing driver for definitions", "driver", driverNameKey)

		tempCommonConfig := AlistDriverCommonConfig{
			Name:           driverNameKey,
			RequestTimeout: m.appCommonCfg.RequestTimeout,
		}

		tempDriverInstance, err := constructor(tempCommonConfig, nil)
		if err != nil {
			log.Error(context.Background(), "Failed to instantiate driver for GetDriverDefinitions", "driver", driverNameKey, "error", err)
			continue
		}

		driverCfg := tempDriverInstance.Config()

		log.Info(context.Background(), "Driver config obtained", "driver", driverNameKey, "display_name", driverCfg.DisplayName, "params_count", len(driverCfg.Params))

		// Determine default download strategy programmatically
		defaultDownload := driverCfg.DefaultDownload
		if defaultDownload == "" {
			// If both redirect and proxy available, prefer redirect
			hasRedirect := false
			hasProxy := false
			for _, opt := range driverCfg.DownloadOptions {
				if opt == DownloadStrategyRedirect {
					hasRedirect = true
				} else if opt == DownloadStrategyProxy {
					hasProxy = true
				}
			}

			if hasRedirect && hasProxy {
				defaultDownload = DownloadStrategyRedirect
			} else if hasProxy {
				defaultDownload = DownloadStrategyProxy
			} else if len(driverCfg.DownloadOptions) > 0 {
				// If neither redirect nor proxy, use the first available option
				defaultDownload = driverCfg.DownloadOptions[0]
			} else {
				// Fallback if no options are available (should not happen)
				defaultDownload = DownloadStrategyProxy
				log.Warn(context.Background(), "Driver has no download options, using proxy as fallback", "driver", driverNameKey)
			}
		}

		def := APIDriverDefinition{
			DriverName:        driverCfg.Name,
			DisplayName:       driverCfg.DisplayName,
			DefaultDriverRoot: driverCfg.DefaultDriverRoot,
			DownloadOptions:   driverCfg.DownloadOptions,
			DefaultDownload:   defaultDownload,
			Params:            driverCfg.Params,
		}
		definitions = append(definitions, def)

		log.Info(context.Background(), "Driver definition added", "driver", driverNameKey, "definition_count", len(definitions))
	}

	log.Info(context.Background(), "GetDriverDefinitions completed", "total_definitions", len(definitions))
	return definitions
}

// GetDriverType returns whether the driver is ID-based or path-based
func (m *StorageDriverManager) GetDriverType(storageID uint) (isIDBased bool, isPathBased bool, err error) {
	driver, ok := m.GetDriver(storageID)
	if !ok {
		return false, false, fmt.Errorf("driver for storage ID %d not found", storageID)
	}

	// If the driver is a wrapper, unwrap it to check the type of the raw driver.
	rawDriver := driver
	if wrapper, ok := driver.(Wrapper); ok {
		rawDriver = wrapper.Unwrap()
	}

	// Log driver information
	log.Debug(context.Background(), "Checking driver type interfaces",
		"storage_id", storageID,
		"driver_type", reflect.TypeOf(driver).String(),
		"raw_driver_type", reflect.TypeOf(rawDriver).String(),
		"implements_IRootIDGetter", reflect.TypeOf(rawDriver).Implements(reflect.TypeOf((*IRootIDGetter)(nil)).Elem()),
		"implements_IRootPathGetter", reflect.TypeOf(rawDriver).Implements(reflect.TypeOf((*IRootPathGetter)(nil)).Elem()))

	// Check for path-based driver
	_, isPathBased = rawDriver.(IRootPathGetter)
	if isPathBased {
		log.Debug(context.Background(), "Driver is path-based", "storage_id", storageID)
	}

	// Check for ID-based driver
	_, isIDBased = rawDriver.(IRootIDGetter)
	if isIDBased {
		log.Debug(context.Background(), "Driver is ID-based", "storage_id", storageID)
	}

	if !isPathBased && !isIDBased {
		log.Error(context.Background(), "Driver neither implements IRootPathGetter nor IRootIDGetter",
			"storage_id", storageID,
			"driver_type", reflect.TypeOf(rawDriver).String())
	}

	return isIDBased, isPathBased, nil
}

// ResolvePath resolves a path to an ID (for ID-based drivers) or returns the cleaned path (for path-based drivers)
func (m *StorageDriverManager) ResolvePath(ctx context.Context, storageID uint, path string) (string, error) {
	driver, ok := m.GetDriver(storageID)
	if !ok {
		return "", fmt.Errorf("driver for storage ID %d not found", storageID)
	}

	// Try to directly resolve the path through our path resolver
	resolver := &PathResolver{}
	resolvedPath, err := resolver.ResolvePath(ctx, driver, path)
	if err != nil {
		log.Error(context.Background(), "Failed to resolve path",
			"storage_id", storageID,
			"path", path,
			"error", err)
		return "", fmt.Errorf("failed to resolve path '%s': %w", path, err)
	}

	return resolvedPath, nil
}
