import { useNotificationStore } from '../stores/notification'

export function useNotification() {
  const notificationStore = useNotificationStore()

  const showSuccessNotification = (message, timeout = 5000) => {
    notificationStore.notify({ type: 'positive', message, timeout })
  }

  const showErrorNotification = (message, timeout = 5000) => {
    notificationStore.notify({ type: 'negative', message, timeout })
  }

  const showWarningNotification = (message, timeout = 5000) => {
    notificationStore.notify({ type: 'warning', message, timeout })
  }

  const showInfoNotification = (message, timeout = 5000) => {
    notificationStore.notify({ type: 'info', message, timeout })
  }

  const notify = (type, message, timeout = 5000) => {
    notificationStore.notify({ type, message, timeout })
  }

  return {
    showSuccessNotification,
    showErrorNotification,
    showWarningNotification,
    showInfoNotification,
    notify
  }
}
