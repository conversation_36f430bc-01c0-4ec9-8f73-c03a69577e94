package ports

import (
	"context"
	"io"
	"mime/multipart"
	"time"
)

// SubtitleInfo represents the metadata of a subtitle
type SubtitleInfo struct {
	ID              uint      `json:"id"`
	UUID            string    `json:"uuid"`
	OriginalID      string    `json:"original_id"`
	WorkID          uint      `json:"work_id"`
	TrackPaths      []string  `json:"track_paths"`
	Format          string    `json:"format"`
	UploaderID      string    `json:"uploader_id"`
	UploaderName    string    `json:"uploader_name,omitempty"`
	SubtitleType    string    `json:"subtitle_type"`
	Description     string    `json:"description,omitempty"`
	IsPublic        bool      `json:"is_public"`
	UpVotes         int       `json:"up_votes"`
	DownVotes       int       `json:"down_votes"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
	CurrentUserVote int       `json:"current_user_vote,omitempty"` // -1, 0, or 1
}

// SubtitleUploadRequest represents a request to upload a subtitle
type SubtitleUploadRequest struct {
	OriginalID  string   `json:"original_id" form:"original_id" binding:"required"`
	TrackPaths  []string `json:"track_paths" form:"track_paths" binding:"required"`
	Format      string   `json:"format" form:"format" binding:"required"`
	Description string   `json:"description" form:"description"`
	IsPublic    bool     `json:"is_public" form:"is_public"`
}

// SubtitleVoteRequest represents a request to vote on a subtitle
type SubtitleVoteRequest struct {
	SubtitleID uint `json:"subtitle_id" binding:"required"`
	Vote       int  `json:"vote" binding:"required"`
}

// SubtitleQueryRequest represents a request to find subtitles for a track
type SubtitleQueryRequest struct {
	OriginalID  string `form:"original_id" binding:"required"`
	TrackPath   string `form:"track_path" binding:"required"`
	IsInArchive bool   `form:"is_in_archive"`
	ArchivePath string `form:"archive_path"`
	IsGuest     bool   `form:"-"` // Not bound from form, set by handler
}

// SubtitleService defines the service for managing subtitles
type SubtitleService interface {
	// User and admin operations
	UploadSubtitle(ctx context.Context, userID string, file *multipart.FileHeader, req SubtitleUploadRequest) (*SubtitleInfo, error)
	GetSubtitleContent(ctx context.Context, subtitleID uint) (io.ReadCloser, string, string, error)
	FindSubtitlesForTrack(ctx context.Context, req SubtitleQueryRequest, userID string) ([]SubtitleInfo, error)
	VoteOnSubtitle(ctx context.Context, userID string, req SubtitleVoteRequest) error
	DeleteSubtitle(ctx context.Context, userID string, subtitleID uint, isAdmin bool) error

	// Admin-only operations
	UpdateSubtitleVisibility(ctx context.Context, subtitleID uint, isPublic bool) error
	GetAllSubtitles(ctx context.Context, page, pageSize int) ([]SubtitleInfo, int, error)
}
