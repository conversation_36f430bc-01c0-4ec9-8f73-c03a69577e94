package handler

import (
	"net/http"

	"github.com/Sakura-Byte/kikoeru-go/pkg/config"
	"github.com/Sakura-Byte/kikoeru-go/pkg/ports"
	"github.com/gin-gonic/gin"
)

type AdminSystemHandler struct {
	appConfig      *config.AppConfig
	userService    ports.UserService
	workService    ports.WorkService
	storageService ports.StorageAdminService
	versionString  string // Application version
}

func NewAdminSystemHandler(
	appConfig *config.AppConfig,
	userService ports.UserService,
	workService ports.WorkService,
	storageService ports.StorageAdminService,
) *AdminSystemHandler {
	return &AdminSystemHandler{
		appConfig:      appConfig,
		userService:    userService,
		workService:    workService,
		storageService: storageService,
		versionString:  "0.1.0", // Set your application version here
	}
}

// UpdateSystemConfig handles updating system configuration
func (h *AdminSystemHandler) UpdateSystemConfig(c *gin.Context) {
	var configUpdate struct {
		SiteName            string `json:"site_name"`
		AllowRegistration   bool   `json:"allow_registration"`
		EnableEmailFeatures bool   `json:"enable_email_features"`
		EnsureEmail         bool   `json:"ensure_email"`
	}

	if err := c.ShouldBindJSON(&configUpdate); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	// Update configuration
	// The appConfig doesn't have a Site.SiteName field directly, this would need to be added
	// or stored in a different way
	h.appConfig.Auth.AllowRegistration = configUpdate.AllowRegistration
	h.appConfig.Auth.EnableEmailFeatures = configUpdate.EnableEmailFeatures
	h.appConfig.Auth.EnsureEmail = configUpdate.EnsureEmail

	// Prevent invalid configurations
	if !h.appConfig.Auth.EnableEmailFeatures && h.appConfig.Auth.EnsureEmail {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Cannot enable email verification when email features are disabled",
		})
		return
	}

	// Save configuration
	err := h.appConfig.Save()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save configuration: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"success": true, "message": "System configuration updated successfully"})
}

// GetSystemInfo returns system information
func (h *AdminSystemHandler) GetSystemInfo(c *gin.Context) {
	// Get total works count
	totalWorks, err := h.workService.CountWorks()
	if err != nil {
		totalWorks = 0 // Fallback if error
	}

	// Get total users count
	totalUsers, err := h.userService.CountUsers()
	if err != nil {
		totalUsers = 0 // Fallback if error
	}

	// Get total storages count
	totalStorages, err := h.storageService.CountStorageSources()
	if err != nil {
		totalStorages = 0 // Fallback if error
	}

	// Check database status (simplified - in production you'd do a real check)
	dbStatus := "healthy"

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"version":        h.versionString,
			"db_status":      dbStatus,
			"total_works":    totalWorks,
			"total_users":    totalUsers,
			"total_storages": totalStorages,
		},
	})
}
