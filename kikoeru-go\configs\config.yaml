# Kikoeru-Go Example Configuration
# Copy this file to 'config.yaml' in this directory, or in $HOME/.kikoeru-go/, or /etc/kikoeru-go/
# and modify it to your needs.

server:
  listen_port: ":5666"
  read_timeout: 15s
  write_timeout: 15s
  idle_timeout: 60s
  enable_gzip: true
  behind_proxy: false
  https_enabled: false
  https_port: ":8443"
  https_cert_file: "cert.pem" # Relative to executable or absolute path
  https_key_file: "key.pem"   # Relative to executable or absolute path
  use_self_signed_cert: true
  block_remote_connection: false
  page_size: 24
  public_url: "http://localhost:9000"

auth:
  enable_auth: true
  jwt_secret: "PLEASE_CHANGE_THIS_IN_YOUR_ACTUAL_CONFIG_OR_LET_APP_GENERATE_ONE" # Will be auto-generated if config file is created by app
  jwt_expires_in: 720h # 30 days
  bcrypt_cost: 10
  allow_guest: true
  allow_registration: true # Set to false in production if you want to control user creation
  min_password_length: 8
  enable_email_features: false # Default: false. Set to true to enable email verification, password reset, etc.
  ensure_email: false # Default: true. If true AND enable_email_features is true, email is mandatory during registration and requires verification.
                     # If false (even if enable_email_features is true), email is optional at registration, and if provided, will require separate verification.
                     # This setting has no effect if enable_email_features is false (it cannot be true if enable_email_features is false, enforced at config load).
  verification_token_ttl: 24h # Default: 24h. How long email verification tokens are valid.
  password_reset_token_ttl: 1h # Default: 1h. How long password reset tokens are valid.
  email_cooldown_seconds: 60 # Default: 60. Email cooldown per email address in seconds.
  ip_cooldown_seconds: 60    # Default: 60. Email cooldown per IP address in seconds.
  email_daily_limit: 5       # Default: 5. Daily email limit per email address.
  ip_daily_limit: 10         # Default: 10. Daily email limit per IP address.

smtp:
  host: "" # Default: "". SMTP server host. Required if enable_email_features is true.
  port: 587 # Default: 587. SMTP server port.
  username: "" # Default: "". SMTP authentication username.
  password: "" # Default: "". SMTP authentication password.
  sender: "" # Default: "". Sender email address.
  enable_tls: true # Default: true. Enable STARTTLS.
  skip_tls_verify: false # Default: false. Skip TLS certificate verification (use with caution).

database:
  driver: "sqlite" # "sqlite" or "mysql"
  sqlite:
    path: "kikoeru.db" # Relative to paths.data_dir, or absolute path
  mysql:
    host: "localhost"
    port: 3306
    user: "kikoeru"
    password: "password"
    dbname: "kikoeru"
    params: "charset=utf8mb4&parseTime=True&loc=Local"
  busy_timeout_ms: 5000

log:
  level: "debug" # "debug", "info", "warn", "error"
  format: "pretty" # "text" or "json"

scanner:
  max_parallelism: 4
  max_recursion_depth: 5
  skip_cleanup: false
  force_scrape_on_scan: true # Changed from false to true. Whether to force scrape metadata during library scan.
  default_scraper_lang: "" # Default: "". Default language for scraper during scan-triggered scrapes.
  cover_jpeg_quality: 85 # Default: 85. JPEG quality for converted covers (1-100).

scraper:
  tag_language: "zh-cn" # "zh-cn", "ja-jp"
  retry_attempts: 3
  retry_delay: 2s
  timeout: 20s
  http_proxy: "" # e.g., "http://localhost:8080" or "socks5://localhost:1080"

paths:
  data_dir: ".kikoeru-data" # Example: store data in a subfolder of where kikoeru-go runs
  temp_dir: "temp"          # Example: relative to data_dir
  covers_dir: "covers"      # Example: relative to data_dir
  lyrics_dir: "lyrics"      # Directory to store subtitle files, relative to data_dir
