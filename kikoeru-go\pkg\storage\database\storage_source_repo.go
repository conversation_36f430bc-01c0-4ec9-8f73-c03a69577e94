package database

import (
	"context"
	"errors"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors" // Added for apperrors
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"gorm.io/gorm"
)

// Error definitions moved to kikoeru-go/pkg/apperrors/errors.go

// StorageSourceRepository defines the interface for storage source data operations.
type StorageSourceRepository interface {
	Create(ctx context.Context, source *models.StorageSource) error
	GetByID(ctx context.Context, id uint) (*models.StorageSource, error)
	GetByRemark(ctx context.Context, remark string) (*models.StorageSource, error)
	ListAll(ctx context.Context, includeDisabled bool, page int, pageSize int) (sources []*models.StorageSource, totalCount int64, err error)
	Update(ctx context.Context, source *models.StorageSource) error
	Delete(ctx context.Context, id uint) error
}

// storageSourceRepository is the GORM implementation of StorageSourceRepository.
type storageSourceRepository struct {
	db *gorm.DB
}

// NewStorageSourceRepository creates a new StorageSourceRepository instance.
func NewStorageSourceRepository(db *gorm.DB) StorageSourceRepository {
	return &storageSourceRepository{db: db}
}

// Create saves a new storage source entry to the database.
func (r *storageSourceRepository) Create(ctx context.Context, source *models.StorageSource) error {
	if source == nil {
		return errors.New("storage source cannot be nil")
	}
	if source.Driver == "" {
		return errors.New("storage source driver cannot be empty")
	}
	// Note: Remark uniqueness check might be better handled by DB constraint + error parsing
	// For now, if ErrStorageSourceRemarkConflict is needed, service layer might check before calling Create.
	return r.db.WithContext(ctx).Create(source).Error
}

// GetByID retrieves a storage source entry by its ID.
func (r *storageSourceRepository) GetByID(ctx context.Context, id uint) (*models.StorageSource, error) {
	var source models.StorageSource
	result := r.db.WithContext(ctx).First(&source, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, apperrors.ErrStorageNotFound // Changed to apperrors
		}
		return nil, result.Error
	}
	return &source, nil
}

// GetByRemark retrieves a storage source entry by its Remark.
// If multiple sources have the same remark, it returns the first one found (ordered by ID).
func (r *storageSourceRepository) GetByRemark(ctx context.Context, remark string) (*models.StorageSource, error) {
	var source models.StorageSource
	result := r.db.WithContext(ctx).Where("remark = ?", remark).Order("id ASC").First(&source)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, apperrors.ErrStorageNotFound // Changed to apperrors
		}
		return nil, result.Error
	}
	return &source, nil
}

// ListAll retrieves storage source entries based on the provided parameters.
func (r *storageSourceRepository) ListAll(ctx context.Context, includeDisabled bool, page int, pageSize int) ([]*models.StorageSource, int64, error) {
	var sources []*models.StorageSource
	var totalCount int64

	query := r.db.WithContext(ctx).Model(&models.StorageSource{})
	countQuery := r.db.WithContext(ctx).Model(&models.StorageSource{})

	if !includeDisabled {
		query = query.Where("disabled = ?", false)
		countQuery = countQuery.Where("disabled = ?", false)
	}

	if err := countQuery.Count(&totalCount).Error; err != nil {
		return nil, 0, err
	}

	if totalCount == 0 {
		return []*models.StorageSource{}, 0, nil
	}

	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		// No limit if pageSize is 0 or less (list all)
	} else {
		if pageSize > 200 {
			pageSize = 200
		}
		offset := (page - 1) * pageSize
		query = query.Offset(offset).Limit(pageSize)
	}

	err := query.Order("\"order\" ASC, created_at ASC").Find(&sources).Error
	if err != nil {
		return nil, 0, err
	}
	return sources, totalCount, nil
}

// Update modifies an existing storage source entry.
func (r *storageSourceRepository) Update(ctx context.Context, source *models.StorageSource) error {
	if source == nil || source.ID == 0 {
		return errors.New("storage source for update must not be nil and must have an ID")
	}
	if source.Driver == "" {
		return errors.New("storage source driver cannot be empty for update")
	}
	return r.db.WithContext(ctx).Save(source).Error
}

// Delete removes a storage source entry by its ID.
func (r *storageSourceRepository) Delete(ctx context.Context, id uint) error {
	result := r.db.WithContext(ctx).Delete(&models.StorageSource{}, id)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return apperrors.ErrStorageNotFound // Changed to apperrors
	}
	return nil
}
