package database

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
	// feedback_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/feedback" // Removed unused import
	common_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/common"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"gorm.io/gorm"
)

// ListFeedbackParams defines parameters for listing feedback entries.
type ListFeedbackParams struct {
	common_dto.PaginationParams
	Status            string
	Type              string
	SubmittedByUserID string
}

// FeedbackRepository defines the repository interface for feedback entries.
type FeedbackRepository interface {
	Create(ctx context.Context, feedback *models.Feedback) error
	GetByID(ctx context.Context, feedbackID uint) (*models.Feedback, error)
	List(ctx context.Context, params ListFeedbackParams) ([]*models.Feedback, int64, error)
	Update(ctx context.Context, feedback *models.Feedback) error
}

type feedbackRepository struct {
	db *gorm.DB
}

func NewFeedbackRepository(db *gorm.DB) FeedbackRepository {
	return &feedbackRepository{db: db}
}

func (r *feedbackRepository) Create(ctx context.Context, feedback *models.Feedback) error {
	if feedback == nil {
		return errors.New("feedback cannot be nil")
	}
	// ID and timestamps are set by GORM hooks
	result := r.db.WithContext(ctx).Create(feedback)
	if result.Error != nil {
		return fmt.Errorf("failed to create feedback in DB: %w", result.Error)
	}
	return nil
}

func (r *feedbackRepository) GetByID(ctx context.Context, feedbackID uint) (*models.Feedback, error) {
	var feedback models.Feedback
	result := r.db.WithContext(ctx).Where("id = ?", feedbackID).First(&feedback)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, apperrors.ErrFeedbackNotFound
		}
		return nil, result.Error
	}
	return &feedback, nil
}

func (r *feedbackRepository) List(ctx context.Context, params ListFeedbackParams) ([]*models.Feedback, int64, error) {
	query := r.db.WithContext(ctx).Model(&models.Feedback{})

	if params.Status != "" {
		query = query.Where("status = ?", params.Status)
	}
	if params.Type != "" {
		query = query.Where("type = ?", params.Type)
	}
	if params.SubmittedByUserID != "" {
		query = query.Where("submitted_by_user_id = ?", params.SubmittedByUserID)
	}

	var totalCount int64
	if err := query.Count(&totalCount).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count feedback entries: %w", err)
	}

	// Apply pagination
	if params.Page > 0 && params.PageSize > 0 {
		offset := (params.Page - 1) * params.PageSize
		query = query.Offset(offset).Limit(params.PageSize)
	}

	// Apply sorting
	if params.SortBy == "" {
		params.SortBy = "created_at" // Default sort
	}
	order := params.SortBy
	if params.SortOrder != "" && (strings.ToLower(params.SortOrder) == "asc" || strings.ToLower(params.SortOrder) == "desc") {
		order += " " + strings.ToLower(params.SortOrder)
	} else {
		order += " desc" // Default to descending
	}
	query = query.Order(order)

	var feedbackEntries []*models.Feedback
	if err := query.Find(&feedbackEntries).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list feedback entries: %w", err)
	}

	return feedbackEntries, totalCount, nil
}

func (r *feedbackRepository) Update(ctx context.Context, feedback *models.Feedback) error {
	if feedback == nil || feedback.ID == 0 {
		return errors.New("feedback for update must not be nil and must have an ID")
	}
	result := r.db.WithContext(ctx).Save(feedback)
	return result.Error
}
