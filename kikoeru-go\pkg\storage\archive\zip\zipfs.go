package zip

import (
	"io"
	"io/fs"
	"path"
	"strings"
	"time"

	archivefs "github.com/Sakura-Byte/kikoeru-go/pkg/storage/archive/fs"
	"github.com/yeka/zip"
)

// ZipFS implements fs.FS interface for ZIP archives
type ZipFS struct {
	reader   *zip.Reader
	encoding string
}

// ReadDir reads the named directory
func (z *ZipFS) ReadDir(name string) ([]fs.DirEntry, error) {
	// Normalize directory name
	name = path.Clean(name)
	if name == "." {
		name = ""
	} else {
		name = name + "/"
	}

	var entries []fs.DirEntry
	dirSet := make(map[string]bool)

	// Iterate through all files to find matching directory entries
	for _, zipFile := range z.reader.File {
		fileName := decodeName(zipFile.Name, z.encoding)

		// Skip files not in the requested directory
		if !strings.HasPrefix(fileName, name) {
			continue
		}

		// Get the relative path to the requested directory
		relPath := strings.TrimPrefix(fileName, name)
		if relPath == "" {
			// This is the directory itself
			continue
		}

		// Check if it's a direct child or a deeper descendant
		parts := strings.SplitN(relPath, "/", 2)
		entryName := parts[0]

		if len(parts) > 1 || strings.HasSuffix(relPath, "/") {
			// This is a subdirectory, add it if we haven't seen it yet
			if !dirSet[entryName] {
				dirSet[entryName] = true
				entries = append(entries, &zipDirEntry{
					name:  entryName,
					isDir: true,
				})
			}
		} else {
			// This is a file, add it
			entries = append(entries, &zipDirEntry{
				name:  entryName,
				isDir: false,
				file:  zipFile,
			})
		}
	}

	return entries, nil
}

// Open opens the named file
func (z *ZipFS) Open(name string) (fs.File, error) {
	// Handle root directory
	if name == "." || name == "/" {
		return &zipDir{
			name: name,
			fs:   z,
		}, nil
	}

	// Clean the path
	name = strings.TrimPrefix(name, "/")

	// First, check if it's a directory
	dirName := name
	if !strings.HasSuffix(dirName, "/") {
		dirName = dirName + "/"
	}

	for _, zipFile := range z.reader.File {
		fileName := decodeName(zipFile.Name, z.encoding)
		if fileName == dirName || strings.HasPrefix(fileName, dirName) {
			// It's a directory
			return &zipDir{
				name: name,
				fs:   z,
			}, nil
		}

		if fileName == name {
			// It's a file
			rc, err := zipFile.Open()
			if err != nil {
				return nil, err
			}

			info := zipFile.FileInfo()
			return &zipFileEntry{
				name:       path.Base(name),
				size:       info.Size(),
				mode:       info.Mode(),
				modTime:    info.ModTime(),
				isDir:      info.IsDir(),
				sys:        zipFile,
				readCloser: rc,
			}, nil
		}
	}

	return nil, fs.ErrNotExist
}

// Stat returns FileInfo for the named file
func (z *ZipFS) Stat(name string) (fs.FileInfo, error) {
	// Handle root directory
	if name == "." || name == "/" {
		return &zipFileInfo{
			name:    "/",
			size:    0,
			mode:    fs.ModeDir,
			modTime: time.Time{},
			isDir:   true,
		}, nil
	}

	// Clean the path
	name = strings.TrimPrefix(name, "/")

	// First, check if it's a directory
	dirName := name
	if !strings.HasSuffix(dirName, "/") {
		dirName = dirName + "/"
	}

	for _, zipFile := range z.reader.File {
		fileName := decodeName(zipFile.Name, z.encoding)
		if fileName == dirName {
			// It's a directory
			return &zipFileInfo{
				name:    path.Base(name),
				size:    0,
				mode:    fs.ModeDir,
				modTime: zipFile.ModTime(),
				isDir:   true,
			}, nil
		}

		if fileName == name {
			// It's a file
			return zipFile.FileInfo(), nil
		}
	}

	// Check if it might be a virtual directory (not explicitly in the ZIP)
	dirName = name
	if !strings.HasSuffix(dirName, "/") {
		dirName = dirName + "/"
	}

	for _, zipFile := range z.reader.File {
		fileName := decodeName(zipFile.Name, z.encoding)
		if strings.HasPrefix(fileName, dirName) {
			// It's a virtual directory
			return &zipFileInfo{
				name:    path.Base(name),
				size:    0,
				mode:    fs.ModeDir,
				modTime: time.Time{},
				isDir:   true,
			}, nil
		}
	}

	return nil, fs.ErrNotExist
}

// zipDirEntry implements fs.DirEntry
type zipDirEntry struct {
	name  string
	isDir bool
	file  *zip.File
}

func (e *zipDirEntry) Name() string {
	return e.name
}

func (e *zipDirEntry) IsDir() bool {
	return e.isDir
}

func (e *zipDirEntry) Type() fs.FileMode {
	if e.isDir {
		return fs.ModeDir
	}
	if e.file == nil {
		return 0
	}
	return e.file.FileInfo().Mode().Type()
}

func (e *zipDirEntry) Info() (fs.FileInfo, error) {
	if e.isDir {
		return &zipFileInfo{
			name:    e.name,
			size:    0,
			mode:    fs.ModeDir,
			modTime: time.Time{},
			isDir:   true,
		}, nil
	}
	if e.file == nil {
		return nil, fs.ErrNotExist
	}
	return e.file.FileInfo(), nil
}

// zipDir implements fs.ReadDirFile for directories
type zipDir struct {
	name string
	fs   *ZipFS
}

func (d *zipDir) Stat() (fs.FileInfo, error) {
	return &zipFileInfo{
		name:    path.Base(d.name),
		size:    0,
		mode:    fs.ModeDir,
		modTime: time.Time{},
		isDir:   true,
	}, nil
}

func (d *zipDir) Read([]byte) (int, error) {
	return 0, fs.ErrInvalid
}

func (d *zipDir) Close() error {
	return nil
}

func (d *zipDir) ReadDir(n int) ([]fs.DirEntry, error) {
	entries, err := d.fs.ReadDir(d.name)
	if err != nil {
		return nil, err
	}

	if n <= 0 {
		return entries, nil
	}

	if n > len(entries) {
		n = len(entries)
	}

	return entries[:n], nil
}

// zipFileEntry implements fs.File for files
type zipFileEntry struct {
	name       string
	size       int64
	mode       fs.FileMode
	modTime    time.Time
	isDir      bool
	sys        interface{}
	readCloser io.ReadCloser
}

func (f *zipFileEntry) Read(p []byte) (int, error) {
	return f.readCloser.Read(p)
}

func (f *zipFileEntry) Close() error {
	return f.readCloser.Close()
}

func (f *zipFileEntry) Stat() (fs.FileInfo, error) {
	return &zipFileInfo{
		name:    f.name,
		size:    f.size,
		mode:    f.mode,
		modTime: f.modTime,
		isDir:   f.isDir,
		sys:     f.sys,
	}, nil
}

// zipFileInfo implements fs.FileInfo
type zipFileInfo struct {
	name    string
	size    int64
	mode    fs.FileMode
	modTime time.Time
	isDir   bool
	sys     interface{}
}

func (fi *zipFileInfo) Name() string {
	return fi.name
}

func (fi *zipFileInfo) Size() int64 {
	return fi.size
}

func (fi *zipFileInfo) Mode() fs.FileMode {
	return fi.mode
}

func (fi *zipFileInfo) ModTime() time.Time {
	return fi.modTime
}

func (fi *zipFileInfo) IsDir() bool {
	return fi.isDir
}

func (fi *zipFileInfo) Sys() interface{} {
	return fi.sys
}

// Ensure ZipFS implements archivefs.ArchiveFS
var _ archivefs.ArchiveFS = (*ZipFS)(nil)
