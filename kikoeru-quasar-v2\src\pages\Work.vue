<template>
  <div>
    <WorkDetails
      v-if="metadata && metadata.original_id"
      :metadata="metadata"
      @reset="requestData()"
    />
    <div v-else class="q-pa-md text-center">
      <q-spinner-dots size="50px" color="primary" />
      <div class="q-mt-md">{{ t('workPage.loading') }}</div>
    </div>
    <WorkTree
      v-if="metadata && metadata.storage_id"
      ref="workTreeRef"
      :storage-id="metadata.storage_id"
      :work-id="metadata.id"
      :original-id="metadata.original_id"
      :base-path="metadata.path_in_storage"
      :work-title="metadata.title"
    />
  </div>
</template>

<script setup>
import { ref, watch, onMounted, getCurrentInstance, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'
import WorkDetails from '../components/WorkDetails.vue'
import WorkTree from '../components/WorkTree.vue'
import { useNotification } from '../composables/useNotification'
import { useAudioPlayer } from '../composables/useAudioPlayer'

defineOptions({
  name: 'WorkPage'
})

const { t } = useI18n()
const route = useRoute()
const { proxy } = getCurrentInstance()
const { showErrorNotification } = useNotification()
const { setPendingSeekTime } = useAudioPlayer()

// Reactive data
const originalId = ref(route.params.id)
const metadata = ref(null)
const workTreeRef = ref(null)

// Methods
const requestData = async () => {
  try {
    // Get work details using new API
    const response = await proxy.$api.get(`/api/v1/work/${originalId.value}/info`)
    // Extract the actual data from the response wrapper
    metadata.value = response.data

    // After metadata is loaded, handle hash navigation
    await nextTick()
    handleHashNavigation()
  } catch (error) {
    if (error.response?.status !== 401) {
      showErrorNotification(error.response?.data?.error || error.message || t('notification.loadWorkDetailsFailed'))
    }
  }
}

const parseHashParams = () => {
  const hash = window.location.hash.substring(1) // Remove the # symbol
  if (!hash) return null

  const params = new URLSearchParams(hash)
  return {
    folder: params.get('folder'),
    file: params.get('file'),
    time: params.get('time') ? parseInt(params.get('time')) : null
  }
}

const handleHashNavigation = async () => {
  const hashParams = parseHashParams()
  if (!hashParams || !workTreeRef.value) return

  console.log('Handling hash navigation:', hashParams)

  try {
    // Navigate to folder if specified
    if (hashParams.folder) {
      await workTreeRef.value.navigateToFolder(hashParams.folder)
    }

    // Auto-play file if specified
    if (hashParams.file) {
      await nextTick() // Wait for folder navigation to complete
      const success = await workTreeRef.value.playFileByName(hashParams.file)

      // If file was found and played, handle seeking
      if (success && hashParams.time) {
        console.log('File started playing, setting pending seek time to:', hashParams.time)
        setPendingSeekTime(hashParams.time)
      }
    }
  } catch (error) {
    console.error('Error handling hash navigation:', error)
  }
}

// Watchers
watch(
  () => route.params.id,
  (newId) => {
    originalId.value = newId
    requestData()
  }
)

// Watch for hash changes
watch(
  () => route.hash,
  () => {
    if (metadata.value && workTreeRef.value) {
      handleHashNavigation()
    }
  }
)

// Lifecycle
onMounted(() => {
  requestData()
})


</script>
