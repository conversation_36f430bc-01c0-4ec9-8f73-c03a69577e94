package ports

import (
	"context"

	"github.com/Sakura-Byte/kikoeru-go/pkg/auth" // For auth.Claims, assuming it might be needed by implementations
)

// WorkScrapeTriggerService defines methods for triggering work scraping operations,
// which are typically orchestrated by WorkService.
type WorkScrapeTriggerService interface {
	// TriggerScrapeForWorkByAdmin triggers a scrape for a specific work.
	// Claims can be nil if the trigger is system-initiated (e.g., by scanner).
	TriggerScrapeForWorkByAdmin(ctx context.Context, claims *auth.Claims, workID uint, req ScrapeOptions) error

	// TriggerScrapeForAllWorksByAdmin triggers a scrape for all works.
	// Claims can be nil if the trigger is system-initiated.
	TriggerScrapeForAllWorksByAdmin(ctx context.Context, claims *auth.Claims, req ScrapeOptions) error
}
