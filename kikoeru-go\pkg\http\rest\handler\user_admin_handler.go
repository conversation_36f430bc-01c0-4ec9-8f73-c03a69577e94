package handler

import (
	"errors"
	"net/http"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
	"github.com/Sakura-Byte/kikoeru-go/pkg/http/rest/common"
	"github.com/Sakura-Byte/kikoeru-go/pkg/ports" // Added ports import

	// "github.com/Sakura-Byte/kikoeru-go/pkg/service" // Removed service import

	user_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/user"
	"github.com/gin-gonic/gin"
)

type UserAdminHandler struct {
	userService ports.UserService // Changed to ports.UserService
}

func NewUserAdminHandler(userService ports.UserService) *UserAdminHandler { // Changed to ports.UserService
	return &UserAdminHandler{userService: userService}
}

// ListUsers godoc
// @Summary List all users (Admin)
// @Description Retrieves a paginated list of all users with filtering and sorting options. Requires admin privileges.
// @Tags admin-users
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param pageSize query int false "Number of users per page" default(20)
// @Param sortBy query string false "Sort by field (e.g., id, username, email, created_at)" default(id)
// @Param sortOrder query string false "Sort order (asc/desc)" default(asc)
// @Param username query string false "Filter by username (contains)"
// @Param email query string false "Filter by email (contains)"
// @Param group query string false "Filter by group (exact match)"
// @Success 200 {object} common.PaginatedResponse{data=[]user_dto.UserResponse} "A list of users"
// @Failure 401 {object} common.ErrorResponse "Unauthorized (not admin)"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /admin/users [get]
// @Security BearerAuth
func (h *UserAdminHandler) ListUsers(c *gin.Context) {
	var params user_dto.ListUsersParams
	if err := c.ShouldBindQuery(&params); err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid query parameters: "+err.Error())
		return
	}
	if params.Page <= 0 {
		params.Page = 1
	}
	if params.PageSize <= 0 {
		params.PageSize = 20
	}
	if params.PageSize > 100 {
		params.PageSize = 100
	}
	if params.SortBy == "" {
		params.SortBy = "id"
	}
	if params.SortOrder == "" {
		params.SortOrder = "asc"
	}

	users, totalCount, err := h.userService.ListUsers(c.Request.Context(), params)
	if err != nil {
		common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to list users: "+err.Error())
		return
	}
	common.SendPaginatedResponse(c, http.StatusOK, users, totalCount, params.Page, params.PageSize)
}

// GetUserByIDByAdmin godoc
// @Summary Get a specific user by ID (Admin)
// @Description Retrieves details for a specific user by their ID. Requires admin privileges.
// @Tags admin-users
// @Produce json
// @Param userID path string true "User ID (UUID)"
// @Success 200 {object} user_dto.UserResponse "User details"
// @Failure 400 {object} common.ErrorResponse "Invalid user ID format"
// @Failure 401 {object} common.ErrorResponse "Unauthorized (not admin)"
// @Failure 404 {object} common.ErrorResponse "User not found"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /admin/users/{userID} [get]
// @Security BearerAuth
func (h *UserAdminHandler) GetUserByIDByAdmin(c *gin.Context) {
	userID := c.Param("userID")
	if userID == "" {
		common.SendErrorResponse(c, http.StatusBadRequest, "User ID cannot be empty")
		return
	}

	userResponse, err := h.userService.GetUserForAdmin(c.Request.Context(), userID)
	if err != nil {
		if errors.Is(err, apperrors.ErrUserNotFound) {
			common.SendErrorResponse(c, http.StatusNotFound, err.Error())
		} else {
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to get user: "+err.Error())
		}
		return
	}
	c.JSON(http.StatusOK, userResponse)
}

// UpdateUserByAdmin godoc
// @Summary Update a user's details (Admin)
// @Description Updates details for a specific user. Requires admin privileges.
// @Tags admin-users
// @Accept json
// @Produce json
// @Param userID path string true "User ID (UUID)"
// @Param user_update_request body user_dto.UpdateUserByAdminRequest true "User details to update"
// @Success 200 {object} user_dto.UserResponse "Updated user details"
// @Failure 400 {object} common.ErrorResponse "Invalid request payload or user ID"
// @Failure 401 {object} common.ErrorResponse "Unauthorized (not admin)"
// @Failure 404 {object} common.ErrorResponse "User not found"
// @Failure 409 {object} common.ErrorResponse "Conflict (e.g., username or email already taken)"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /admin/users/{userID} [put]
// @Security BearerAuth
func (h *UserAdminHandler) UpdateUserByAdmin(c *gin.Context) {
	userID := c.Param("userID")
	if userID == "" {
		common.SendErrorResponse(c, http.StatusBadRequest, "User ID cannot be empty")
		return
	}

	var req user_dto.UpdateUserByAdminRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid request payload: "+err.Error())
		return
	}

	updatedUserResponse, err := h.userService.UpdateUserByAdmin(c.Request.Context(), userID, req)
	if err != nil {
		if errors.Is(err, apperrors.ErrUserNotFound) {
			common.SendErrorResponse(c, http.StatusNotFound, err.Error())
		} else if errors.Is(err, apperrors.ErrUserAlreadyExists) || errors.Is(err, apperrors.ErrEmailInUseByAnotherAccount) {
			common.SendErrorResponse(c, http.StatusConflict, err.Error())
		} else if errors.Is(err, apperrors.ErrValidation) {
			common.SendErrorResponse(c, http.StatusBadRequest, err.Error())
		} else {
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to update user: "+err.Error())
		}
		return
	}
	c.JSON(http.StatusOK, updatedUserResponse)
}

// DeleteUserByAdmin godoc
// @Summary Delete a user (Admin)
// @Description Deletes a specific user. Requires admin privileges.
// @Tags admin-users
// @Produce json
// @Param userID path string true "User ID (UUID)"
// @Success 204 "User successfully deleted"
// @Failure 400 {object} common.ErrorResponse "Invalid user ID format"
// @Failure 401 {object} common.ErrorResponse "Unauthorized (not admin)"
// @Failure 404 {object} common.ErrorResponse "User not found"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /admin/users/{userID} [delete]
// @Security BearerAuth
func (h *UserAdminHandler) DeleteUserByAdmin(c *gin.Context) {
	userID := c.Param("userID")
	if userID == "" {
		common.SendErrorResponse(c, http.StatusBadRequest, "User ID cannot be empty")
		return
	}

	err := h.userService.DeleteUserByAdmin(c.Request.Context(), userID)
	if err != nil {
		if errors.Is(err, apperrors.ErrUserNotFound) {
			common.SendErrorResponse(c, http.StatusNotFound, err.Error())
		} else {
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to delete user: "+err.Error())
		}
		return
	}
	c.Status(http.StatusNoContent)
}
