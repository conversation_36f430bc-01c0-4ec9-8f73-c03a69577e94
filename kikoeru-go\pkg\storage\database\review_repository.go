package database

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors" // Added for apperrors
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// Error definitions moved to kikoeru-go/pkg/apperrors/errors.go

// ListReviewParams DTO for listing reviews
type ListReviewParams struct {
	Page           int
	PageSize       int
	Order          string // updated_at, userRating, release, review_count, sell_count
	Sort           string // asc, desc
	FilterProgress string // marked/listening/listened/replay/postponed or empty
	UserID         string // To filter by current user
}

// ReviewRepository defines the interface for review data storage operations.
type ReviewRepository interface {
	CreateOrUpdate(ctx context.Context, review *models.Review) error
	GetByUserIDAndWorkOriginalID(ctx context.Context, userID string, workOriginalID string) (*models.Review, error)
	ListByUserID(ctx context.Context, params ListReviewParams) ([]models.Review, int64, error)
	Delete(ctx context.Context, userID string, workOriginalID string) error
}

type gormReviewRepository struct {
	db *gorm.DB
}

// NewReviewRepository creates a new GORM implementation of ReviewRepository.
func NewReviewRepository(db *gorm.DB) ReviewRepository {
	return &gormReviewRepository{db: db}
}

// CreateOrUpdate creates a new review or updates an existing one based on UserID and WorkID.
func (r *gormReviewRepository) CreateOrUpdate(ctx context.Context, review *models.Review) error {
	err := r.db.WithContext(ctx).Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "user_id"}, {Name: "work_id"}},
		DoUpdates: clause.AssignmentColumns([]string{"rating", "review_text", "progress", "updated_at"}),
	}).Create(review).Error

	if err != nil {
		// GORM's OnConflict should handle unique constraint violations gracefully by updating.
		// If another error occurs, return it.
		// Check if the error is specifically about the unique constraint, which CreateOrUpdate should handle.
		// If it's a different error, then it's unexpected.
		// However, the current logic assumes OnConflict handles ErrReviewAlreadyExists implicitly.
		return fmt.Errorf("failed to create or update review: %w", err)
	}
	return nil
}

// GetByUserIDAndWorkOriginalID retrieves a single review by user ID and work original ID.
func (r *gormReviewRepository) GetByUserIDAndWorkOriginalID(ctx context.Context, userID string, workOriginalID string) (*models.Review, error) {
	var review models.Review
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND work_id = ?", userID, workOriginalID).
		First(&review).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, apperrors.ErrReviewNotFound // Changed to apperrors
		}
		return nil, fmt.Errorf("failed to get review by user_id and work_id: %w", err)
	}
	return &review, nil
}

// ListByUserID retrieves a paginated list of reviews for a given user, with filtering and sorting.
func (r *gormReviewRepository) ListByUserID(ctx context.Context, params ListReviewParams) ([]models.Review, int64, error) {
	var reviews []models.Review
	var totalCount int64

	query := r.db.WithContext(ctx).Model(&models.Review{}).Where("user_id = ?", params.UserID)

	if params.FilterProgress != "" {
		query = query.Where("progress = ?", params.FilterProgress)
	}

	countQuery := query
	if err := countQuery.Count(&totalCount).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count reviews: %w", err)
	}

	orderByClause := ""
	switch params.Order {
	case "updated_at":
		orderByClause = "t_reviews.updated_at"
	case "userRating":
		orderByClause = "t_reviews.rating"
	case "release":
		query = query.Joins("JOIN t_work ON t_work.original_id = t_reviews.work_id")
		orderByClause = "t_work.release_date"
	case "review_count":
		query = query.Joins("JOIN t_work ON t_work.original_id = t_reviews.work_id")
		orderByClause = "t_work.review_count"
	case "sell_count":
		query = query.Joins("JOIN t_work ON t_work.original_id = t_reviews.work_id")
		orderByClause = "t_work.dl_count"
	default:
		orderByClause = "t_reviews.updated_at"
	}

	sortDirection := "DESC"
	if strings.ToLower(params.Sort) == "asc" {
		sortDirection = "ASC"
	}

	if params.Order == "userRating" {
		if sortDirection == "ASC" {
			orderByClause = fmt.Sprintf("CASE WHEN t_reviews.rating IS NULL THEN 0 ELSE 1 END ASC, t_reviews.rating %s", sortDirection)
		} else {
			orderByClause = fmt.Sprintf("CASE WHEN t_reviews.rating IS NULL THEN 0 ELSE 1 END DESC, t_reviews.rating %s", sortDirection)
		}
	} else {
		orderByClause = fmt.Sprintf("%s %s", orderByClause, sortDirection)
	}

	query = query.Order(orderByClause)

	offset := (params.Page - 1) * params.PageSize
	if offset < 0 {
		offset = 0
	}
	query = query.Offset(offset).Limit(params.PageSize)

	query = query.Preload("User")

	if err := query.Find(&reviews).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list reviews: %w", err)
	}

	return reviews, totalCount, nil
}

// Delete removes a review by user ID and work original ID.
func (r *gormReviewRepository) Delete(ctx context.Context, userID string, workOriginalID string) error {
	// First, check if the review exists to return ErrReviewNotFound correctly
	_, err := r.GetByUserIDAndWorkOriginalID(ctx, userID, workOriginalID)
	if err != nil {
		// This already returns apperrors.ErrReviewNotFound if not found, or other errors
		return err
	}

	// If found, proceed to delete
	err = r.db.WithContext(ctx).
		Where("user_id = ? AND work_id = ?", userID, workOriginalID).
		Delete(&models.Review{}).Error

	if err != nil {
		// This should ideally not be gorm.ErrRecordNotFound if the above GetByID succeeded,
		// but to be safe, we can check. However, Delete doesn't usually return ErrRecordNotFound.
		return fmt.Errorf("failed to delete review: %w", err)
	}
	// GORM's Delete doesn't return RowsAffected in a straightforward way to check if something was deleted
	// if the query itself succeeds. The check above with GetByID handles the "not found" case.
	return nil
}
