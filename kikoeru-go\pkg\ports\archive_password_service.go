package ports

import (
	"context"
	"time"

	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
)

// ArchivePasswordService defines the interface for archive password management
type ArchivePasswordService interface {
	// Password list management
	GetAllPasswords(ctx context.Context) ([]*models.ArchivePasswordResponse, error)
	CreatePassword(ctx context.Context, req *models.ArchivePasswordInput) (*models.ArchivePasswordResponse, error)
	UpdatePassword(ctx context.Context, id uint, req *models.ArchivePasswordUpdateRequest) (*models.ArchivePasswordResponse, error)
	DeletePassword(ctx context.Context, id uint) error
	BatchImportPasswords(ctx context.Context, req *models.ArchivePasswordBatchImportRequest) (*models.ArchivePasswordBatchImportResponse, error)

	// Password list for archive operations
	GetPasswordList(ctx context.Context) ([]string, error)

	// Password cache management
	GetCachedPassword(ctx context.Context, storageID uint, archivePath string) (string, error)
	SetCachedPassword(ctx context.Context, storageID uint, archivePath, password string) error
	GetAllCachedPasswords(ctx context.Context) ([]*models.ArchivePasswordCacheResponse, error)
	DeleteCachedPassword(ctx context.Context, storageID uint, archivePath string) error
	CleanupOldCachedPasswords(ctx context.Context, olderThan time.Duration) error
}
