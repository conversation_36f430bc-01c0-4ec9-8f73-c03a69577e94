<template>
  <div class="q-pa-md">
    <div class="text-h5 text-weight-regular q-mb-lg">{{ t('adminTasks.title') }}</div>

    <div class="row justify-start q-mb-md q-gutter-sm">
      <q-btn
        color="primary"
        icon="library_books"
        :label="t('adminTasks.scanLibrary')"
        @click="showScanDialog = true"
      />
      <q-btn
        color="secondary"
        icon="download"
        :label="t('adminTasks.scrapeAllWorks')"
        @click="showScrapeAllDialog = true"
      />
      <q-btn
        color="info"
        icon="refresh"
        :label="t('adminTasks.refreshTaskList')"
        @click="loadTasks"
      />

      <q-select
        v-model="statusFilter"
        :options="statusOptions"
        dense
        outlined
        style="min-width: 150px"
        label="状态筛选"
        class="q-ml-md"
        emit-value
        map-options
        clearable
        @update:model-value="onStatusFilterChange"
      />
    </div>

    <!-- 任务列表 -->
    <q-table
      :rows="tasks"
      :columns="tableColumns"
      row-key="id"
      :loading="loading"
      flat
      bordered
      class="shadow-2"
      :pagination="paginationState"
      @request="onRequest"
    >
      <template v-slot:body-cell-status="props">
        <q-td :props="props">
          <q-chip
            :color="getStatusColor(props.row.status)"
            text-color="white"
            dense
          >
            {{ getStatusLabel(props.row.status) }}
          </q-chip>
        </q-td>
      </template>

      <template v-slot:body-cell-progress="props">
        <q-td :props="props">
          <q-linear-progress
            :value="props.row.progress"
            color="primary"
            size="20px"
            class="q-my-sm"
          >
            <div class="absolute-full flex flex-center">
              <q-badge color="white" text-color="accent" :label="`${Math.round(props.row.progress * 100)}%`" />
            </div>
          </q-linear-progress>
        </q-td>
      </template>

      <template v-slot:body-cell-actions="props">
        <q-td :props="props">
          <div class="row q-gutter-xs">
            <q-btn
              dense
              round
              color="info"
              icon="visibility"
              size="sm"
              @click="viewTask(props.row)"
            >
              <q-tooltip>{{ t('adminTasks.viewDetails') }}</q-tooltip>
            </q-btn>

            <q-btn
              v-if="canCancel(props.row.status)"
              dense
              round
              color="negative"
              icon="stop"
              size="sm"
              @click="cancelTask(props.row)"
            >
              <q-tooltip>{{ t('adminTasks.cancelTask') }}</q-tooltip>
            </q-btn>
          </div>
        </q-td>
      </template>
    </q-table>

    <!-- 扫描图库对话框 -->
    <q-dialog v-model="showScanDialog" persistent>
      <q-card style="min-width: 400px">
        <q-card-section>
          <div class="text-h6">{{ t('adminTasks.scanLibraryDialog.title') }}</div>
        </q-card-section>

        <q-card-section class="q-pt-none">
          <q-select
            v-model="scanForm.storageId"
            :options="storageOptionsComputed"
            option-label="label"
            option-value="value"
            emit-value
            map-options
            :label="t('adminTasks.scanLibraryDialog.selectStorage')"
            dense
            outlined
          />
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="t('common.cancel')" @click="showScanDialog = false" />
          <q-btn flat :label="t('adminTasks.scanLibraryDialog.startScan')" color="primary" @click="submitScanTask" :loading="submittingTask" />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 刮削所有作品对话框 -->
    <q-dialog v-model="showScrapeAllDialog" persistent>
      <q-card style="min-width: 400px">
        <q-card-section>
          <div class="text-h6">{{ t('adminTasks.scrapeAllDialog.title') }}</div>
        </q-card-section>

        <q-card-section class="q-pt-none">
          <q-checkbox
            v-model="scrapeAllForm.forceUpdate"
            :label="t('adminTasks.scrapeAllDialog.forceUpdate')"
          />

          <q-select
            v-model="scrapeAllForm.preferredLang"
            :options="languageOptionsComputed"
            :label="t('adminTasks.scrapeAllDialog.preferredLang')"
            dense
            outlined
            class="q-mt-md"
          />
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="t('common.cancel')" @click="showScrapeAllDialog = false" />
          <q-btn flat :label="t('adminTasks.scrapeAllDialog.startScraping')" color="primary" @click="submitScrapeAllTask" :loading="submittingTask" />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 任务详情对话框 -->
    <q-dialog v-model="showTaskDetail" maximized>
      <q-card v-if="selectedTask">
        <q-card-section class="row items-center q-pb-none">
          <div class="text-h6">{{ t('adminTasks.taskDetailDialog.title') }}</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section>
          <q-list>
            <q-item>
              <q-item-section>
                <q-item-label>{{ t('adminTasks.taskDetailDialog.taskId') }}</q-item-label>
                <q-item-label caption>{{ selectedTask.id }}</q-item-label>
              </q-item-section>
            </q-item>

            <q-item>
              <q-item-section>
                <q-item-label>{{ t('adminTasks.taskDetailDialog.taskType') }}</q-item-label>
                <q-item-label caption>{{ selectedTask.task_type }}</q-item-label>
              </q-item-section>
            </q-item>

            <q-item>
              <q-item-section>
                <q-item-label>{{ t('adminTasks.taskDetailDialog.status') }}</q-item-label>
                <q-item-label caption>
                  <q-chip
                    :color="getStatusColor(selectedTask.status)"
                    text-color="white"
                    dense
                  >
                    {{ getStatusLabel(selectedTask.status) }}
                  </q-chip>
                </q-item-label>
              </q-item-section>
            </q-item>

            <q-item>
              <q-item-section>
                <q-item-label>{{ t('adminTasks.taskDetailDialog.progress') }}</q-item-label>
                <q-item-label caption>
                  <q-linear-progress
                    :value="selectedTask.progress"
                    color="primary"
                    size="20px"
                    class="q-my-sm"
                  >
                    <div class="absolute-full flex flex-center">
                      <q-badge color="white" text-color="accent" :label="`${Math.round(selectedTask.progress * 100)}%`" />
                    </div>
                  </q-linear-progress>
                </q-item-label>
              </q-item-section>
            </q-item>

            <q-item v-if="selectedTask.message">
              <q-item-section>
                <q-item-label>{{ t('adminTasks.taskDetailDialog.message') }}</q-item-label>
                <q-item-label caption>{{ selectedTask.message }}</q-item-label>
              </q-item-section>
            </q-item>

            <q-item v-if="selectedTask.error_message">
              <q-item-section>
                <q-item-label>{{ t('adminTasks.taskDetailDialog.errorMessage') }}</q-item-label>
                <q-item-label caption class="text-red">{{ selectedTask.error_message }}</q-item-label>
              </q-item-section>
            </q-item>

            <q-item>
              <q-item-section>
                <q-item-label>{{ t('adminTasks.taskDetailDialog.createdAt') }}</q-item-label>
                <q-item-label caption>{{ formatDate(selectedTask.created_at) }}</q-item-label>
              </q-item-section>
            </q-item>

            <q-item v-if="selectedTask.started_at">
              <q-item-section>
                <q-item-label>{{ t('adminTasks.taskDetailDialog.startedAt') }}</q-item-label>
                <q-item-label caption>{{ formatDate(selectedTask.started_at) }}</q-item-label>
              </q-item-section>
            </q-item>

            <q-item v-if="selectedTask.completed_at">
              <q-item-section>
                <q-item-label>{{ t('adminTasks.taskDetailDialog.completedAt') }}</q-item-label>
                <q-item-label caption>{{ formatDate(selectedTask.completed_at) }}</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>

          <div v-if="selectedTask.payload" class="q-mt-md">
            <div class="text-h6 q-mb-sm">{{ t('adminTasks.taskDetailDialog.payload') }}</div>
            <pre class="bg-grey-2 q-pa-sm">{{ JSON.stringify(selectedTask.payload, null, 2) }}</pre>
          </div>

          <div v-if="selectedTask.result" class="q-mt-md">
            <div class="text-h6 q-mb-sm">{{ t('adminTasks.taskDetailDialog.result') }}</div>
            <pre class="bg-grey-2 q-pa-sm">{{ JSON.stringify(selectedTask.result, null, 2) }}</pre>
          </div>
        </q-card-section>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, getCurrentInstance } from 'vue'
import { useI18n } from 'vue-i18n'
import { useNotification } from '../../composables/useNotification'

defineOptions({
  name: 'TaskManagePage'
})

const { t, locale } = useI18n()
const { proxy } = getCurrentInstance()
const { showSuccessNotification, showErrorNotification } = useNotification()

// Reactive data
const tasks = ref([])
const storageOptionsData = ref([])
const loading = ref(false)
const submittingTask = ref(false)
const showScanDialog = ref(false)
const showScrapeAllDialog = ref(false)
const showTaskDetail = ref(false)
const selectedTask = ref(null)
const scanForm = ref({
  storageId: null
})
const scrapeAllForm = ref({
  forceUpdate: false,
  preferredLang: 'zh-cn'
})
const paginationState = ref({
  page: 1,
  rowsPerPage: 20,
  rowsNumber: 0
})
const statusFilter = ref(null)
const statusOptions = ref([
  { label: 'Pending', value: 'pending' },
  { label: 'Running', value: 'running' },
  { label: 'Completed', value: 'completed' },
  { label: 'Failed', value: 'failed' },
  { label: 'Cancelled', value: 'cancelled' }
])

// Computed properties
const tableColumns = computed(() => [
  {
    name: 'task_type',
    label: t('adminTasks.table.taskType'),
    field: 'task_type',
    sortable: true,
    align: 'left'
  },
  {
    name: 'status',
    label: t('adminTasks.table.status'),
    field: 'status',
    sortable: true,
    align: 'center'
  },
  {
    name: 'progress',
    label: t('adminTasks.table.progress'),
    field: 'progress',
    sortable: true,
    align: 'center'
  },
  {
    name: 'message',
    label: t('adminTasks.table.message'),
    field: 'message',
    sortable: false,
    align: 'left'
  },
  {
    name: 'created_at',
    label: t('adminTasks.table.createdAt'),
    field: 'created_at',
    sortable: true,
    align: 'center',
    format: val => formatDate(val)
  },
  {
    name: 'actions',
    label: t('adminTasks.table.actions'),
    field: 'actions',
    align: 'center'
  }
])

const languageOptionsComputed = computed(() => [
  { label: t('adminTasks.languageOptions.zh'), value: 'zh-cn' },
  { label: t('adminTasks.languageOptions.ja'), value: 'ja-jp' },
  { label: t('adminTasks.languageOptions.en'), value: 'en-us' }
])

const storageOptionsComputed = computed(() => {
  return storageOptionsData.value.map(storage => {
    let label = ''
    if (storage.remark && storage.remark.trim()) {
      label = `${storage.remark} (${storage.driver}) [${t('adminTasks.storageIdLabel')}: ${storage.id}]`
    } else {
      label = `${storage.driver} [${t('adminTasks.storageIdLabel')}: ${storage.id}]`
    }
    return {
      label: label,
      value: storage.id.toString()
    }
  })
})

// Methods
const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString(locale.value)
}

const loadTasks = async () => {
  loading.value = true
  try {
    const params = {
      page: paginationState.value.page,
      page_size: paginationState.value.rowsPerPage
    }

    // 添加状态过滤
    if (statusFilter.value) {
      // 检查statusFilter是否是对象并且有value属性
      if (typeof statusFilter.value === 'object' && statusFilter.value.value) {
        params.status = statusFilter.value.value
      } else {
        params.status = statusFilter.value
      }
    }

    const response = await proxy.$api.get('/api/v1/admin/tasks', { params })
    const data = response.data
    tasks.value = data.items || []
    paginationState.value.rowsNumber = data.pagination?.total_items || 0
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('notification.adminLoadTasksFailed'))
  } finally {
    loading.value = false
  }
}

const loadStorages = async () => {
  try {
    const response = await proxy.$api.get('/api/v1/admin/storages')
    let storagesRaw = []
    if (response.data && Array.isArray(response.data)) {
      storagesRaw = response.data
    }
    storageOptionsData.value = storagesRaw
  } catch (error) {
    console.warn('Failed to load storages:', error)
    showErrorNotification(t('notification.adminLoadStoragesForTasksFailed'))
  }
}

const onRequest = (props) => {
  paginationState.value = props.pagination
  loadTasks()
}

const onStatusFilterChange = () => {
  paginationState.value.page = 1
  loadTasks()
}

const getStatusColor = (status) => {
  const colors = {
    'pending': 'orange',
    'running': 'blue',
    'completed': 'green',
    'failed': 'red',
    'cancelled': 'grey'
  }
  return colors[status.toLowerCase()] || 'grey'
}

const getStatusLabel = (status) => {
  const key = `adminTasks.statusLabels.${status.toLowerCase()}`
  const label = t(key)
  return label === key ? status : label
}

const canCancel = (status) => {
  return ['pending', 'running'].includes(status.toLowerCase())
}

const submitScanTask = async () => {
  if (!scanForm.value.storageId) {
    showErrorNotification(t('adminTasks.validation.selectStorage'))
    return
  }

  submittingTask.value = true
  try {
    await proxy.$api.post('/api/v1/admin/tasks/scan-library', {
      storage_id: scanForm.value.storageId
    })
    showSuccessNotification(t('notification.adminScanTaskSubmitted'))
    showScanDialog.value = false
    scanForm.value.storageId = null
    loadTasks()
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('notification.adminScanTaskSubmitFailed'))
  } finally {
    submittingTask.value = false
  }
}

const submitScrapeAllTask = async () => {
  submittingTask.value = true
  try {
    await proxy.$api.post('/api/v1/admin/tasks/scrape-all-works', {
      force_update: scrapeAllForm.value.forceUpdate,
      preferred_lang: scrapeAllForm.value.preferredLang
    })
    showSuccessNotification(t('notification.adminScrapeAllTaskSubmitted'))
    showScrapeAllDialog.value = false
    loadTasks()
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('notification.adminScrapeAllTaskSubmitFailed'))
  } finally {
    submittingTask.value = false
  }
}

const viewTask = (task) => {
  selectedTask.value = task
  showTaskDetail.value = true
}

const cancelTask = async (task) => {
  try {
    await proxy.$api.post(`/api/v1/admin/tasks/${task.id}/cancel`)
    showSuccessNotification(t('notification.adminTaskCancelled'))
    loadTasks()
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('notification.adminTaskCancelFailed'))
  }
}

// Lifecycle
onMounted(() => {
  loadTasks()
  loadStorages()

  // Set default preferredLang based on current locale if available in options
  const currentLangValue = locale.value.toLowerCase()
  if (languageOptionsComputed.value.some(opt => opt.value === currentLangValue)) {
    scrapeAllForm.value.preferredLang = currentLangValue
  }
})
</script>
