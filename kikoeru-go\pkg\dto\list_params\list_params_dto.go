package list_params_dto

// PaginationParams defines common parameters for pagination and sorting.
type PaginationParams struct {
	Page      int    `form:"page,default=1" json:"page"`
	PageSize  int    `form:"page_size,default=20" json:"page_size"`
	SortBy    string `form:"sort_by,default=created_at" json:"sort_by"` // Field to sort by
	SortOrder string `form:"sort_order,default=desc" json:"sort_order"` // "asc" or "desc"
}

// GetOffset calculates the offset for database queries based on Page and PageSize.
func (p *PaginationParams) GetOffset() int {
	if p.Page <= 0 {
		p.Page = 1
	}
	if p.PageSize <= 0 {
		p.PageSize = 20 // Default page size
	}
	return (p.Page - 1) * p.PageSize
}

// GetLimit returns the PageSize.
func (p *PaginationParams) GetLimit() int {
	if p.PageSize <= 0 {
		p.PageSize = 20 // Default page size
	}
	return p.PageSize
}
