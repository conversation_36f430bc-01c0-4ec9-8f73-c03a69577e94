<template>
  <div>
    <div v-if="playlist" class="q-ma-md">
      <!-- 播放列表信息 -->
      <div class="row q-col-gutter-md q-mb-lg">
        <div class="col-12 col-md-3">
          <q-img
            :src="playlistCoverUrl"
            :ratio="1"
            style="max-width: 300px;"
            class="rounded-borders shadow-2"
          />
        </div>

        <div class="col-12 col-md-9">
          <div class="text-h4 text-weight-regular q-mb-sm">{{ playlist.name }}</div>

          <div v-if="playlist.description" class="text-body1 text-grey-7 q-mb-md">
            {{ playlist.description }}
          </div>

          <div class="row q-gutter-sm items-center q-mb-md">
            <q-chip
              :color="visibilityColor(playlist.visibility)"
              text-color="white"
              dense
            >
              {{ visibilityLabel(playlist.visibility) }}
            </q-chip>

            <span class="text-body2">{{ t('playlist.itemCount', { count: playlist.item_count || 0 }) }}</span>
            <span class="text-caption text-grey-6">
              {{ t('playlist.updatedAt', { date: formatDate(playlist.updated_at) }) }}
            </span>
          </div>

          <div class="row q-gutter-sm">
            <q-btn
              color="primary"
              icon="play_arrow"
              :label="t('playlist.playAll')"
              @click="playAll"
              :disable="!playlist.items || playlist.items.length === 0"
            />
            <q-btn
              color="secondary"
              icon="shuffle"
              :label="t('playlist.shufflePlay')"
              @click="shufflePlay"
              :disable="!playlist.items || playlist.items.length === 0"
            />
            <q-btn
              outline
              color="primary"
              icon="edit"
              :label="t('playlist.editInfo')"
              @click="editPlaylist"
            />
          </div>
        </div>
      </div>

      <!-- 播放列表项目 -->
      <div class="q-mb-md">
        <div class="text-h6 q-mb-md">
          {{ t('playlist.content') }}
          <q-btn
            dense
            round
            color="primary"
            icon="add"
            size="sm"
            class="q-ml-sm"
            @click="showAddDialog = true"
          >
            <q-tooltip>{{ t('playlist.addItem') }}</q-tooltip>
          </q-btn>
        </div>

        <q-list v-if="playlist.items && playlist.items.length > 0" bordered separator class="shadow-2">
          <draggable
            v-model="playlist.items"
            item-key="id"
            @change="onOrderChange"
            handle=".drag-handle"
          >
            <template #item="{ element: item, index }">
              <PlaylistItemCard
                :item="item"
                :index="index + 1"
                @play="playItem(item)"
                @remove="removeItem(item)"
                @move-up="moveUp(item)"
                @move-down="moveDown(item)"
              />
            </template>
          </draggable>
        </q-list>

        <div v-else class="text-center q-my-xl text-grey-6">
          {{ t('playlist.emptyPlaylist') }}
          <div class="q-mt-sm">
            <q-btn
              color="primary"
              icon="add"
              :label="t('playlist.addItem')"
              @click="showAddDialog = true"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-else class="row justify-center q-my-xl">
      <q-spinner-dots color="primary" size="40px" />
    </div>

    <!-- 编辑播放列表对话框 -->
    <q-dialog v-model="showEditDialog" persistent>
      <q-card style="min-width: 400px">
        <q-card-section>
          <div class="text-h6">{{ t('playlist.edit') }}</div>
        </q-card-section>

        <q-card-section class="q-pt-none">
          <q-input
            dense
            v-model="editForm.name"
            :label="t('playlist.nameLabel')"
            maxlength="100"
            counter
            :rules="[val => val && val.length > 0 || t('validation.playlistNameRequired')]"
          />

          <q-input
            dense
            v-model="editForm.description"
            :label="t('playlist.descriptionLabel')"
            type="textarea"
            rows="3"
            maxlength="500"
            counter
            class="q-mt-md"
          />

          <q-select
            dense
            v-model="editForm.visibility"
            :options="visibilityOptions"
            :label="t('playlist.visibilityLabel')"
            class="q-mt-md"
          />
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="t('common.cancel')" @click="cancelEdit" />
          <q-btn flat :label="t('common.save')" color="primary" @click="savePlaylist" :loading="saving" />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 添加项目对话框 -->
    <q-dialog v-model="showAddDialog" persistent>
      <q-card style="min-width: 500px">
        <q-card-section>
          <div class="text-h6">{{ t('playlist.addItemsToPlaylist') }}</div>
        </q-card-section>

        <q-card-section class="q-pt-none">
          <q-input
            dense
            v-model="searchQuery"
            :label="t('playlist.searchWorks')"
            :placeholder="t('playlist.searchPlaceholder')"
            @keyup.enter="searchWorks"
          >
            <template v-slot:append>
              <q-btn flat round dense icon="search" @click="searchWorks" />
            </template>
          </q-input>

          <div v-if="searchResults.length > 0" class="q-mt-md" style="max-height: 300px; overflow-y: auto;">
            <q-list>
              <q-item
                v-for="work_item in searchResults"
                :key="work_item.id"
                clickable
                @click="addWorkToPlaylist(work_item)"
              >
                <q-item-section avatar>
                  <q-img :src="`/api/v1/cover/${work_item.original_id}?type=main`" style="height: 40px; width: 40px;" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>{{ work_item.title }}</q-item-label>
                  <q-item-label caption>{{ work_item.circle?.name }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </div>
          <div v-else-if="searchQuery && !searchingWorks" class="text-center q-my-md text-grey-6">
            {{ t('playlist.noResultsFound') }}
          </div>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="t('common.cancel')" @click="closeAddDialog" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, getCurrentInstance } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import draggable from 'vuedraggable'
import PlaylistItemCard from '../components/PlaylistItemCard.vue'
import { useAudioPlayer } from '../composables/useAudioPlayer'
import { useNotification } from '../composables/useNotification'

defineOptions({
  name: 'PlaylistDetailPage'
})

const { t, locale } = useI18n()
const route = useRoute()
const router = useRouter()
const { proxy } = getCurrentInstance()
const { setQueue } = useAudioPlayer()
const { showSuccessNotification, showErrorNotification, showInfoNotification } = useNotification()

// Reactive data
const playlist = ref(null)
const showEditDialog = ref(false)
const showAddDialog = ref(false)
const saving = ref(false)
const searchQuery = ref('')
const searchResults = ref([])
const searchingWorks = ref(false)
const editForm = ref({
  name: '',
  description: '',
  visibility: 'private'
})



// Computed properties
const playlistId = computed(() => route.params.id)

const playlistCoverUrl = computed(() => '/icons/playlist-default.png')

const visibilityOptions = computed(() => [
  { label: t('playlist.visibilityOptions.private'), value: 'private' },
  { label: t('playlist.visibilityOptions.unlisted'), value: 'unlisted' },
  { label: t('playlist.visibilityOptions.public'), value: 'public' }
])

// Methods

const loadPlaylist = async () => {
  try {
    const response = await proxy.$api.get(`/api/v1/playlists/${playlistId.value}`)
    playlist.value = response.data
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('notification.loadPlaylistFailed'))
    router.push('/playlists')
  }
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString(locale.value, {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const visibilityLabel = (visibility) => {
  const labels = {
    'private': t('playlist.visibilityOptions.private'),
    'unlisted': t('playlist.visibilityOptions.unlisted'),
    'public': t('playlist.visibilityOptions.public')
  }
  return labels[visibility] || visibility
}

const visibilityColor = (visibility) => {
  const colors = {
    'private': 'red',
    'unlisted': 'orange',
    'public': 'green'
  }
  return colors[visibility] || 'grey'
}

const editPlaylist = () => {
  editForm.value = {
    name: playlist.value.name,
    description: playlist.value.description || '',
    visibility: playlist.value.visibility
  }
  showEditDialog.value = true
}

const cancelEdit = () => {
  showEditDialog.value = false
}

const savePlaylist = async () => {
  if (!editForm.value.name.trim()) {
    showErrorNotification(t('validation.playlistNameRequired'))
    return
  }

  saving.value = true
  try {
    await proxy.$api.put(`/api/v1/playlists/${playlist.value.id}`, editForm.value)
    showSuccessNotification(t('notification.playlistUpdated'))
    playlist.value.name = editForm.value.name
    playlist.value.description = editForm.value.description
    playlist.value.visibility = editForm.value.visibility
    cancelEdit()
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('notification.playlistUpdateFailed'))
  } finally {
    saving.value = false
  }
}

const playAll = () => {
  if (!playlist.value || !playlist.value.items || playlist.value.items.length === 0) return
  const tracks = playlist.value.items.map(item => createAudioTrack(item.work, item.track_path))
  setQueue({ queue: tracks, index: 0, resetPlaying: true })
}

const shufflePlay = () => {
  if (!playlist.value || !playlist.value.items || playlist.value.items.length === 0) return
  let tracks = playlist.value.items.map(item => createAudioTrack(item.work, item.track_path))
  // Shuffle tracks
  for (let i = tracks.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [tracks[i], tracks[j]] = [tracks[j], tracks[i]];
  }
  setQueue({ queue: tracks, index: 0, resetPlaying: true })
}

const playItem = (item) => {
  const index = playlist.value.items.findIndex(i => i.id === item.id)
  setQueue({
    queue: playlist.value.items.map(i => createAudioTrack(i.work, i.track_path)),
    index: index,
    resetPlaying: true
  })
}

const removeItem = async (item) => {
  try {
    await proxy.$api.delete(`/api/v1/playlists/${playlist.value.id}/items/${item.id}`)
    playlist.value.items = playlist.value.items.filter(i => i.id !== item.id)
    playlist.value.item_count = playlist.value.items.length
    showSuccessNotification(t('notification.playlistItemRemoved'))
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('notification.playlistItemRemoveFailed'))
  }
}

const onOrderChange = async (event) => {
  if (!event.moved) return;

  const { newIndex } = event.moved;
  const itemId = playlist.value.items[newIndex].id;

  try {
    await proxy.$api.patch(`/api/v1/playlists/${playlist.value.id}/items/${itemId}`, {
      new_position: newIndex + 1
    });
    showSuccessNotification(t('notification.playlistOrderUpdated'));
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('notification.playlistOrderUpdateFailed'));
    loadPlaylist();
  }
}

const moveUp = (item) => {
  const index = playlist.value.items.findIndex(i => i.id === item.id)
  if (index > 0) {
    const newItems = [...playlist.value.items];
    [newItems[index - 1], newItems[index]] = [newItems[index], newItems[index - 1]];
    playlist.value.items = newItems;
    onOrderChange({ moved: { oldIndex: index, newIndex: index -1 }});
  }
}

const moveDown = (item) => {
  const index = playlist.value.items.findIndex(i => i.id === item.id)
  if (index < playlist.value.items.length - 1) {
    const newItems = [...playlist.value.items];
    [newItems[index + 1], newItems[index]] = [newItems[index], newItems[index + 1]];
    playlist.value.items = newItems;
    onOrderChange({ moved: { oldIndex: index, newIndex: index + 1 }});
  }
}

const searchWorks = async () => {
  if (!searchQuery.value.trim()) {
    searchResults.value = []
    return
  }
  searchingWorks.value = true;
  try {
    const response = await proxy.$api.get('/api/v1/works', {
      params: { title: searchQuery.value, original_id: searchQuery.value, mode: 'or' }
    })
    searchResults.value = response.data.items || []
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('notification.searchWorksFailed'))
  } finally {
    searchingWorks.value = false;
  }
}

const addWorkToPlaylist = async (work_item) => {
  if (playlist.value.items.some(item => item.work_id === work_item.id)) {
    showInfoNotification(t('notification.workAlreadyInPlaylist'))
    return;
  }
  try {
    const response = await proxy.$api.post(`/api/v1/playlists/${playlist.value.id}/items`, { work_id: work_item.id })
    const newItem = response.data
    playlist.value.items.push(newItem)
    playlist.value.item_count = playlist.value.items.length
    showSuccessNotification(t('notification.playlistItemAdded'))
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('notification.playlistItemAddFailed'))
  }
}

const closeAddDialog = () => {
  showAddDialog.value = false;
  searchQuery.value = '';
  searchResults.value = [];
}

const createAudioTrack = (work_item, trackPath) => {
  return {
    title: trackPath ? trackPath.split('/').pop() : work_item.title,
    trackPath: trackPath,
    workId: work_item.id,
    originalId: work_item.original_id,
    workTitle: work_item.title,
    duration: null,
    isInArchive: false,
    archivePath: ''
  }
}

// Lifecycle
onMounted(() => {
  loadPlaylist()
})
</script>
