package model

import "time"

// SubtitleTrack represents the relationship between a subtitle and an audio track
type SubtitleTrack struct {
	ID          uint   `gorm:"primaryKey"`
	SubtitleID  uint   `gorm:"index;not null"`
	TrackPath   string `gorm:"index;not null"`
	IsInArchive bool   `gorm:"default:false"`
	ArchivePath string
	CreatedAt   time.Time `gorm:"autoCreateTime"`
	UpdatedAt   time.Time `gorm:"autoUpdateTime"`

	// Association
	// Subtitle Subtitle `gorm:"foreignKey:SubtitleID"`
}

// TableName returns the table name for the subtitle track model
func (SubtitleTrack) TableName() string {
	return "t_subtitle_track"
}
