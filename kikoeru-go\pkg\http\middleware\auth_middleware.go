package middleware

import (
	// Used by c.Request.Context()

	"net/http"
	"strings"
	"time" // Added for guest claims expiration

	"github.com/Sakura-Byte/kikoeru-go/pkg/auth"
	"github.com/Sakura-Byte/kikoeru-go/pkg/config"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"

	// "github.com/Sakura-Byte/kikoeru-go/pkg/service" // Removed to break import cycle
	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
)

// AuthMiddleware creates a middleware for JWT authentication.
// It will use auth.UserClaimsKey from the pkg/auth package.
func AuthMiddleware(appConfig *config.AppConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Helper function to set guest claims
		setGuestAndContinue := func() {
			guestClaims := &auth.Claims{
				UserID:    "guest", // Specific ID for guest
				Username:  models.UserGroupGuest,
				UserGroup: models.UserGroupGuest,
				RegisteredClaims: jwt.RegisteredClaims{
					Subject:   "guest",
					ExpiresAt: jwt.NewNumericDate(time.Now().Add(1 * time.Hour)), // Guest session
					IssuedAt:  jwt.NewNumericDate(time.Now()),
					NotBefore: jwt.NewNumericDate(time.Now()),
				},
			}
			c.Set(auth.UserClaimsKey, guestClaims)
			c.Next()
		}

		if !appConfig.Auth.EnableAuth {
			// If auth is globally disabled, all access is effectively anonymous.
			// We can choose to set guest claims or no claims. Setting no claims is simpler.
			// Or, if we want a consistent "guest" object for handlers when auth is off:
			// setGuestAndContinue()
			// For now, if auth is disabled, just pass through without any claims.
			c.Next()
			return
		}

		// Auth is enabled, proceed with token check or set guest if token is missing/invalid
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			setGuestAndContinue() // No token, set guest
			return
		}

		parts := strings.Split(authHeader, " ")
		if len(parts) != 2 || strings.ToLower(parts[0]) != "bearer" {
			setGuestAndContinue() // Invalid token format, set guest
			return
		}
		tokenString := parts[1]

		claims, err := auth.ValidateToken(tokenString, appConfig.GetJWTSecretBytes())
		if err != nil {
			setGuestAndContinue() // Invalid token, set guest
			return                // Exit after setting guest claims
		}
		// If token was valid, err is nil, and we proceed to set the actual claims.

		c.Set(auth.UserClaimsKey, claims)
		c.Next()
	}
}

// GetUserClaims retrieves user claims from the Gin context.
// Returns nil if no claims are set.
func GetUserClaims(c *gin.Context) *auth.Claims {
	claims, exists := c.Get(auth.UserClaimsKey) // Use exported key from auth package
	if !exists {
		return nil
	}
	userClaims, ok := claims.(*auth.Claims)
	if !ok {
		// This should not happen if claims are set correctly by AuthMiddleware
		// log.L(c.Request.Context()).Error("Failed to cast user claims from context", "type_found", fmt.Sprintf("%T", claims))
		return nil
	}
	return userClaims
}

// AdminRequiredMiddleware creates a middleware that ensures the user is authenticated and is an admin.
// It should be used AFTER AuthMiddleware.
// It now relies on UserGroup being present in the JWT claims.
func AdminRequiredMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		claims := GetUserClaims(c)
		if claims == nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Authentication required for admin access. No claims found."})
			return
		}

		// Check UserGroup directly from claims
		// Assumes auth.Claims struct has UserGroup field of type models.UserGroup
		if claims.UserGroup != models.UserGroupAdmin {
			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{"error": "Admin privileges required."})
			return
		}

		c.Next()
	}
}

// Helper min function (if not available globally or in a utils package)
// func min(a, b int) int {
// 	if a < b { return a }
// 	return b
// }
