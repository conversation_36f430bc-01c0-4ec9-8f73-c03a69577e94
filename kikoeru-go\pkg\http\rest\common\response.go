package common

import (
	"math"

	"github.com/gin-gonic/gin"

	common_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/common" // Updated import
)

// SendSuccessResponse sends a direct data response without wrapping.
func SendSuccessResponse(c *gin.Context, statusCode int, data interface{}) {
	c.<PERSON>(statusCode, data)
}

// SendErrorResponse sends a standardized error response.
func SendErrorResponse(c *gin.Context, statusCode int, message string) {
	c.AbortWithStatusJSON(statusCode, common_dto.APIError{
		Message: message,
		Error:   message, // Use the same message for both fields for simplicity
		Code:    statusCode,
	})
}

// SendPaginatedResponse sends a standardized paginated success response.
// ItemCount is removed from PaginationMeta; client can derive it from len(Items).
func SendPaginatedResponse(c *gin.Context, statusCode int, items interface{}, totalItems int64, page int, pageSize int) {
	totalPages := 0
	if pageSize > 0 {
		totalPages = int(math.Ceil(float64(totalItems) / float64(pageSize)))
	}

	SendSuccessResponse(c, statusCode, common_dto.PaginatedDataResponse{
		Items: items,
		Pagination: common_dto.PaginationMeta{
			TotalItems:  totalItems,
			PerPage:     pageSize,
			CurrentPage: page,
			TotalPages:  totalPages,
		},
	})
}
