<template>
  <div :class="visibility" class="topClass">
    <canvas ref="canvas" width="500" height="60" class="sized"></canvas>
    <video ref="video" class="sized" muted="muted" playsinline preload="metadata" controls="controls" style="display: inline;"></video>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue'
import { debounce, Notify, Dark, useQuasar } from 'quasar'
import { useAudioPlayer } from '../composables/useAudioPlayer'

defineOptions({
  name: 'PIPLyrics'
})

// Composables
    const $q = useQuasar()
    const audioPlayerComposable = useAudioPlayer()
    console.log('PIPLyrics: useAudioPlayer composable:', audioPlayerComposable)

    const {
      currentLyric,
      enablePIPLyrics,
      playing: isPlaying,
      isQueueEmpty,
      setEnablePIPLyrics,
      play,
      pause
    } = audioPlayerComposable

    console.log('PIPLyrics: Destructured functions - play:', play, 'pause:', pause)

    // Refs
    const canvas = ref(null)
    const video = ref(null)

    // Reactive data
    const ctx = ref(null)
    const stopRafObject = ref({ stopped: false })
    const visibility = ref("hide")
    const isFireFox = navigator.userAgent.toLowerCase().indexOf('firefox') > -1
    const isVideoCanPlay = ref(false)
    const pixelRatio = window.devicePixelRatio
    const pipWindow = ref(null)

    // Computed properties
    const isDarkMode = computed(() => Dark.isActive)

    // Define stopPIPLyric for reference in event listener
    const stopPIPLyric = () => {
      const videoEl = video.value
      if (!videoEl) return
      if (typeof document.exitPictureInPicture === 'function') {
        document.pictureInPictureElement && document.exitPictureInPicture()
      } else if (typeof videoEl.webkitPresentationMode === 'function') {
        videoEl.webkitSetPresentationMode('inline')
      }
      videoEl.pause()
      videoEl.onplay = null;
      videoEl.onpause = null;
    }

    // Methods
    const initCanvas = () => {
      ctx.value = canvas.value.getContext("2d")
      const canvasEl = canvas.value
      canvasEl.width = pixelRatio * window.innerWidth
      canvasEl.height = canvasEl.width / 500 * 60
      console.warn(`pip canvas init size: ${canvasEl.width} x ${canvasEl.height}`)
    }

    const drawLyric = (str) => {
      console.log('draw lyric: ', str)
      const fontScale = 0.7
      const expectCharCount = 30
      const cvs = canvas.value
      const ctxValue = ctx.value

      const fontSize = fontScale * Math.round(Math.sqrt((cvs.width * cvs.height) / expectCharCount))

      const isDark = isDarkMode.value

      // background
      ctxValue.clearRect(0, 0, cvs.width, cvs.height)
      ctxValue.fillStyle = isDark ? 'rgba(50, 50, 50, 1.0)' : "rgba(255, 255, 255, 1.0)"
      ctxValue.fillRect(0, 0, cvs.width, cvs.height)

      ctxValue.font = `bold ${fontSize}px "-apple-system", "BlinkMacSystemFont", "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", "Helvetica", "Arial", "sans-serif", "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"`
      ctxValue.fillStyle = '#9c27b0'

      // 可绘制参数
      const padWidth = 5
      const padHeight = 0

      const allowedLines = Math.floor((cvs.height - padHeight * 2) / fontSize)
      const allowedWidth = cvs.width - padWidth * 2

      // 首次测量全部字符串
      const allTxt = ctxValue.measureText(str)
      const neededLines = Math.ceil(allTxt.width / allowedWidth)
      const drawLines = Math.min(neededLines, allowedLines)
      const restLineHeight = cvs.height - drawLines * fontSize
      let readCharIdx = 0 // 将要添加到绘制行的字符序号

      const chars = str.split("")

      // 遍历每一行
      for (let line = 0; line < drawLines && readCharIdx < chars.length; line++) {
        // 填充当前行直到allowedWidth
        let lineStr = ""
        let lineStrMetric = null
        while (readCharIdx < str.length) {
          lineStr += chars[readCharIdx]
          lineStrMetric = ctxValue.measureText(lineStr)
          if (lineStrMetric.width > allowedWidth) {
            // 当前行已满，准备绘制lineStr
            lineStr = lineStr.substring(0, lineStr.length - 1)
            break;
          } else {
            // 当前行仍然有剩余空间，继续往lineStr添加字符
            readCharIdx++;
          }
        }

        if (line == drawLines - 1 && readCharIdx < chars.length) {
          // 已到达最后一行，然而还有文字没有上屏，将当前行末尾的几个文字变为省略号，忽略后续其他文字
          lineStr = lineStr.substring(0, lineStr.length - 3) + "..."
        }

        lineStrMetric = ctxValue.measureText(lineStr)

        // 绘制lineStr
        const drawX = padWidth + (cvs.width - lineStrMetric.width) / 2

        const drawY = restLineHeight / 2 + line * fontSize + lineStrMetric.actualBoundingBoxAscent
        ctxValue.fillText(lineStr, drawX, drawY)
      }

      video.value.srcObject.getTracks().forEach((t => t.requestFrame && t.requestFrame()));
    }

    const initVideos = () => {
      const stream = canvas.value.captureStream()
      video.value.srcObject = stream

      isVideoCanPlay.value = true
      video.value.addEventListener("loadedmetadata", () => {
        console.log("pip video ready")
        isVideoCanPlay.value = true
      })
      video.value.addEventListener("enterpictureinpicture", (event) => {
        pipWindow.value = event.pictureInPictureWindow
        pipWindow.value.onresize = () => {
          debouncedOnPipWindowResize()
        }
        setTimeout(() => debouncedOnPipWindowResize(), 500)
        console.warn("enter pip")
      })
      video.value.addEventListener("leavepictureinpicture", () => {
        if (!stopPIPLyric) return // 组件已经被销毁
        stopPIPLyric()
        setEnablePIPLyrics(false)
        pipWindow.value = null
      })
      video.value.play()
      forceVideoStartLoadMetadata()
    }

    const onPipWindowResize = () => {
      if (!pipWindow.value) return
      canvas.value.width = Math.round(pixelRatio * pipWindow.value.width)
      canvas.value.height = Math.round(pixelRatio * pipWindow.value.height)
      drawLyric(currentLyric.value)
    }

    const forceVideoStartLoadMetadata = () => {
      // 首先绘制一次，初始化video状态，保证dataloaded，是的后续画中画状态能够立即进入
      let forceDrawCount = 5
      const draw = () => {
        if (forceDrawCount < 0) {
          if (!enablePIPLyrics.value || !isPlaying.value) video.value.pause() // 停止强制渲染后，根据播放器状态决定video是否暂停
          return
        }
        forceDrawCount--
        requestAnimationFrame(draw)
        drawLyric(currentLyric.value)
      }
      requestAnimationFrame(draw)
    }

// Additional methods
const openPIPVideoMode = () => {
  video.value.play()
  console.log("打开桌面歌词")

  if (
    typeof video.value.requestPictureInPicture === 'function' &&
    document.pictureInPictureEnabled
  ) {
    video.value.requestPictureInPicture().then(() => {
      // 解决歌词video播放、暂停事件无法传递到音频播放状态的问题
      if (!isPlaying.value) video.value.pause()
      // from user
      video.value.onplay = () => {
        debouncedSyncPlayingStateFromPIPVideoToAudio(true)
      }
      video.value.onpause = () => {
        debouncedSyncPlayingStateFromPIPVideoToAudio(false)
      }
    }).catch((err) => {
      console.log("PIP lyric open video failed, msg = ", err.message)
      stopPIPLyric()
    })
  } else if (typeof video.value.webkitPresentationMode === 'function') {
    video.value.webkitPresentationMode('picture-in-picture')
  }

  if (isFireFox) {
    // 对火狐浏览器，将video强制显示出来，让用户自己设置video进入画中画模式，然后自动隐藏
    visibility.value = "manulSet"
    setTimeout(() => {
      visibility.value = "hide"
    }, 10000)
  }
}

const showUserPrompt = () => {
  let msg = "请点击'打开'按钮确认显示桌面歌词，或者点击'取消'关闭桌面歌词。（请注意，桌面歌词打开后，原先网页内的歌词就会被隐藏掉）"
  let okMsg = "请继续"
  if (isFireFox) {
    msg = "检测到FireFox浏览器，此浏览器下必须由用户手动选择开启画中画功能，请在10秒内手动选择左上角出现的video组件并开启画中画功能，10秒后video组件将会隐藏并无法操作。如果错过，您也可以重新关闭、打开桌面歌词功能，来再次操作。"
    okMsg = "好的"
  }

  $q.dialog({
    title: '桌面歌词',
    message: msg,
    ok: okMsg,
    cancel: "关闭桌面歌词",
    persistent: false
  }).onOk(async () => {
    openPIPVideoMode()
  }).onCancel(() => {
    setEnablePIPLyrics(false)
    stopPIPLyric()
  }).onDismiss(() => {
    // console.log('I am triggered on both OK and Cancel')
  })
}

const tryEnterPIPAndShowUserPrompt = () => {
  if (isVideoCanPlay.value) {
    showUserPrompt()
  } else {
    Notify.create({message: "桌面歌词打开失败，请播放音频5秒后再次尝试打开", timeout: 500})
  }
}

const syncPlayingStateFromAudioToPIPVideo = () => {
  // 将音频状态 同步到 歌词video上
  if (!enablePIPLyrics.value) return
  if (isPlaying.value && video.value.paused) video.value.play()
  else if (!isPlaying.value && !video.value.paused) video.value.pause()
}

const syncPlayingStateFromPIPVideoToAudio = (isPIPPlaying) => {
  console.log('PIPLyrics: syncPlayingStateFromPIPVideoToAudio called with:', isPIPPlaying)
  console.log('PIPLyrics: play function:', play)
  console.log('PIPLyrics: pause function:', pause)

  try {
    if (isPIPPlaying) {
      if (typeof play === 'function') {
        play()
      } else {
        console.error('PIPLyrics: play is not a function:', play)
      }
    } else {
      if (typeof pause === 'function') {
        pause()
      } else {
        console.error('PIPLyrics: pause is not a function:', pause)
      }
    }
  } catch (error) {
    console.error('PIPLyrics: Error in syncPlayingStateFromPIPVideoToAudio:', error)
  }
}

// Create debounced versions - 防止快速切换导致video/audio相互之间的状态递归
const debouncedSyncPlayingStateFromAudioToPIPVideo = debounce(syncPlayingStateFromAudioToPIPVideo, 500)
const debouncedSyncPlayingStateFromPIPVideoToAudio = debounce(syncPlayingStateFromPIPVideoToAudio, 500)
const debouncedOnPipWindowResize = debounce(onPipWindowResize, 100, true)

// Watchers
watch(enablePIPLyrics, (value) => {
  if (!value) stopPIPLyric()
  else tryEnterPIPAndShowUserPrompt()
})

// 监听播放列表，如果有新增，一般是打开的桌面歌词状态的时候，还没有播放作品，
// 如果这个时候突然播放作品，就需要做检查并进入歌词画中画模式
watch(isQueueEmpty, (value) => {
  if (value) stopPIPLyric()
  else if (enablePIPLyrics.value) tryEnterPIPAndShowUserPrompt()
})

watch(currentLyric, (newLyric) => {
  if (!enablePIPLyrics.value) return
  drawLyric(newLyric)
})

watch(isPlaying, () => {
  debouncedSyncPlayingStateFromAudioToPIPVideo()
  drawLyric(currentLyric.value)
})

watch(isDarkMode, () => {
  // 监听黑夜模式，立即重新绘制
  drawLyric(currentLyric.value)
})

// Lifecycle
onMounted(() => {
  initCanvas()
  initVideos()
})

onBeforeUnmount(() => {
  stopRafObject.value.stopped = true
  stopPIPLyric()
})
</script>

<style lang="scss" scoped>
  .sized {
      width: 500px;
      height: 60px;
      border: 1px solid black;
      position: absolute;
  }

  .hide {
      opacity: 0.1;
      position: fixed;
      right: 5px;
      bottom: 5px;
      width: 5px;
      height: 5px;
      overflow: hidden;
  }
  .topClass {
    z-index: 999;
  }
  .show {
      opacity: 1.0;
      position: fixed;
      left: 0;
      top: 0;
  }

  .manulSet {
    opacity: 1.0;
    position: fixed;
    left: 0;
    top: 0;
    width: 100vw;
    height: 50vh;
  }

  .manulSet > canvas {
    display: none;
  }
</style>
