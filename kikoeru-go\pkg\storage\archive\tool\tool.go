package tool

import (
	"context"
	"errors"
	"fmt"
	"io"
	"io/fs"
	"path/filepath"
	"regexp"
	"strings"

	"github.com/Sakura-Byte/kikoeru-go/pkg/log"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
)

var (
	ErrUnsupportedArchiveFormat = errors.New("unsupported archive format")
	ErrPasswordRequired         = errors.New("password required to extract this archive")
	ErrInvalidPassword          = errors.New("invalid password for this archive")
)

// MultipartExtension defines a multi-part archive extension pattern
type MultipartExtension struct {
	Pattern   string // e.g., ".part%d.rar"
	StartFrom int    // Starting number (usually 1 or 2)
}

// Tool is the interface for archive operations
type Tool interface {
	// AcceptedExtensions returns a list of file extensions that this tool can handle
	AcceptedExtensions() []string
	// GetMeta gets metadata about an archive
	GetMeta(ss *models.StreamWithSeek, args models.ArchiveArgs) (models.ArchiveMeta, error)
	// List lists files in an archive
	List(ss *models.StreamWithSeek, args models.ArchiveInnerArgs) ([]models.Obj, error)
	// Extract extracts a file from an archive
	Extract(ss *models.StreamWithSeek, args models.ArchiveInnerArgs) (io.ReadCloser, int64, error)
	// Decompress decompresses an archive to a directory
	Decompress(ss *models.StreamWithSeek, outputPath string, args models.ArchiveInnerArgs, up func(float64)) error
	// GetFS provides an fs.FS interface for the archive
	GetFS(ss *models.StreamWithSeek, args models.ArchiveArgs) (fs.FS, error)
}

// MultipartTool extends Tool to support multi-part archives
type MultipartTool interface {
	Tool
	// AcceptedMultipartExtensions returns a map of multi-part extensions this tool can handle
	AcceptedMultipartExtensions() map[string]MultipartExtension
	// GetMetaMultipart gets metadata about a multi-part archive
	GetMetaMultipart(streams []*models.StreamWithSeek, args models.ArchiveArgs) (models.ArchiveMeta, error)
	// ListMultipart lists files in a multi-part archive
	ListMultipart(streams []*models.StreamWithSeek, args models.ArchiveInnerArgs) ([]models.Obj, error)
	// ExtractMultipart extracts a file from a multi-part archive
	ExtractMultipart(streams []*models.StreamWithSeek, args models.ArchiveInnerArgs) (io.ReadCloser, int64, error)
	// DecompressMultipart decompresses a multi-part archive to a directory
	DecompressMultipart(streams []*models.StreamWithSeek, outputPath string, args models.ArchiveInnerArgs, up func(float64)) error
	// GetFSMultipart provides an fs.FS interface for the multi-part archive
	GetFSMultipart(streams []*models.StreamWithSeek, args models.ArchiveArgs) (fs.FS, error)
}

var (
	Tools               = make(map[string]Tool)
	MultipartExtensions = make(map[string]MultipartExtension)
	patternRegexCache   = make(map[string]*regexp.Regexp)
)

// RegisterTool registers an archive tool.
func RegisterTool(tool Tool) {
	for _, ext := range tool.AcceptedExtensions() {
		log.Debug(context.Background(), "Registering tool", "tool", fmt.Sprintf("%T", tool), "extension", ext)
		Tools[ext] = tool
	}

	if multipartTool, ok := tool.(MultipartTool); ok {
		for ext, multipartExt := range multipartTool.AcceptedMultipartExtensions() {
			log.Debug(context.Background(), "Registering multipart tool", "tool", fmt.Sprintf("%T", tool), "extension", ext)
			MultipartExtensions[ext] = multipartExt
			if _, exists := Tools[ext]; !exists {
				Tools[ext] = tool
			}
		}
	}
}

// getAllExtensions returns all possible extensions for a filename.
// For example, for "archive.part1.rar", it returns [".part1.rar", ".rar"].
func getAllExtensions(filename string) []string {
	name := filepath.Base(filename)
	parts := strings.Split(name, ".")
	if len(parts) <= 1 {
		if len(parts) == 1 && strings.HasPrefix(name, ".") {
			return []string{name}
		}
		ext := filepath.Ext(name)
		if ext != "" {
			return []string{ext}
		}
		return []string{}
	}

	var exts []string
	for i := 1; i < len(parts); i++ {
		exts = append(exts, "."+strings.Join(parts[i:], "."))
	}
	return exts
}

// GetArchiveTool gets the appropriate archive tool for a filename.
// It returns the tool and multipart extension information if available.
func GetArchiveTool(filename string) (Tool, *MultipartExtension, error) {
	possibleExts := getAllExtensions(filename)

	// Check for extensions that are explicitly defined as multipart starts
	for _, ext := range possibleExts {
		if tool, ok := Tools[ext]; ok {
			if me, ok := MultipartExtensions[ext]; ok {
				return tool, &me, nil
			}
		}
	}

	// Check if filename matches any multipart pattern (e.g. it's a subsequent part)
	for ext, me := range MultipartExtensions {
		if me.Pattern != "" && matchesMultipartPattern(filename, me.Pattern) {
			if tool, ok := Tools[ext]; ok {
				return tool, &me, nil
			}
		}
	}

	// Check for regular single-file archives
	for _, ext := range possibleExts {
		if tool, ok := Tools[ext]; ok {
			return tool, nil, nil
		}
	}

	return nil, nil, ErrUnsupportedArchiveFormat
}

// matchesMultipartPattern checks if a filename matches a multi-part pattern.
// The pattern is a fmt.Sprintf-style pattern, e.g., ".part%d.rar".
func matchesMultipartPattern(filename, pattern string) bool {
	regex, ok := patternRegexCache[pattern]
	if !ok {
		// Convert fmt-style pattern to regex
		reStr := regexp.QuoteMeta(pattern)
		// A more robust replacement for various integer formats like %d, %02d, etc.
		reStr = regexp.MustCompile(`%0?\d*d`).ReplaceAllString(reStr, `[0-9]+`)
		// Replacement for string formats
		reStr = regexp.MustCompile(`%s`).ReplaceAllString(reStr, `.+`)
		reStr = reStr + `$`

		var err error
		regex, err = regexp.Compile(reStr)
		if err != nil {
			log.Error(context.Background(), "Invalid multipart pattern to compile regex", "pattern", pattern, "error", err)
			return false
		}
		patternRegexCache[pattern] = regex
	}
	return regex.MatchString(filename)
}

// IsMultipartArchive checks if a filename represents a multi-part archive.
func IsMultipartArchive(filename string) bool {
	_, me, err := GetArchiveTool(filename)
	return err == nil && me != nil
}
