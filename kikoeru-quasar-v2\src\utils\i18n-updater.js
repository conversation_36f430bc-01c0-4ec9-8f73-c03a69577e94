/**
 * This is a utility script to help apply i18n to Vue files
 * It provides a set of common translation patterns and helper functions
 */

// Common text patterns to replace with i18n translations
const textPatterns = {
  // Auth related
  '登录': 'auth.login',
  '注册': 'auth.register',
  '用户名': 'auth.username',
  '用户名或邮箱': 'auth.usernameOrEmail',
  '密码': 'auth.password',
  '确认密码': 'auth.confirmPassword',
  '邮箱': 'auth.email',
  '邮箱地址': 'auth.email',
  '忘记密码？': 'auth.forgotPassword',
  '重置密码': 'auth.resetPassword',
  '发送重置邮件': 'auth.sendResetEmail',
  '注册新账户': 'auth.createAccount',

  // Navigation
  '媒体库': 'nav.mediaLibrary',
  '我的评价': 'nav.myReviews',
  '播放列表': 'nav.playlists',
  '播放历史': 'nav.history',
  '社团': 'nav.circles',
  '标签': 'nav.tags',
  '声优': 'nav.voiceActors',
  '随心听': 'nav.randomListen',
  '睡眠模式': 'nav.sleepMode',
  '管理面板': 'nav.adminPanel',
  '夜间模式': 'nav.darkMode',
  '日间模式': 'nav.lightMode',
  '登出': 'nav.logout',

  // Dialog
  '取消': 'dialog.cancel',
  '确认': 'dialog.confirm',
  '删除': 'dialog.delete',

  // Work related
  '详情': 'work.details',
  '曲目': 'work.tracks',
  '评价': 'work.reviews',
  '发售日': 'work.releasedAt',
  '添加到播放列表': 'work.addToPlaylist',
  '创建播放列表': 'work.createPlaylist',
  '写评价': 'work.writeReview',
  '暂无评价': 'work.noReviews',
  '你的评分': 'work.yourRating',
  '评分': 'work.rate',
  '全部播放': 'work.playAll',

  // Player related
  '播放': 'player.play',
  '暂停': 'player.pause',
  '下一首': 'player.next',
  '上一首': 'player.previous',
  '歌词': 'player.lyrics',
  '暂无歌词': 'player.noLyrics',
  '正在播放': 'player.currentlyPlaying',
  '播放队列': 'player.queue',
  '添加到队列': 'player.addToQueue',
  '从队列中移除': 'player.removeFromQueue',
  '清空队列': 'player.clearQueue',

  // Playlist related
  '编辑播放列表': 'playlist.edit',
  '删除播放列表': 'playlist.delete',
  '播放列表名称': 'playlist.name',
  '描述': 'playlist.description',
  '可见性': 'playlist.visibility',
  '公开': 'playlist.public',
  '私人': 'playlist.private',
  '添加曲目': 'playlist.addTrack',
  '移除曲目': 'playlist.removeTrack',
  '移动曲目': 'playlist.moveTracks',
  '保存更改': 'playlist.saveChanges',
  '我的播放列表': 'playlist.myPlaylists',
  '创建新播放列表': 'playlist.createNew',
  '此播放列表为空': 'playlist.emptyPlaylist',
  '确定要删除此播放列表吗？': 'playlist.confirmDelete'
};

/**
 * Helper function to convert a label to i18n format
 * @param {string} label - The text label to convert
 * @param {boolean} bind - Whether to use v-bind syntax (:label) or interpolation ({{ }})
 * @returns {string} - The i18n formatted string
 */
function convertToI18n(label, bind = true) {
  const key = textPatterns[label];
  if (!key) return label; // If no match found, return original

  return bind ? `:label="$t('${key}')"` : `{{ $t('${key}') }}`;
}

/**
 * Helper function to replace notification messages with i18n
 * @param {string} code - The code containing notification messages
 * @returns {string} - Updated code with i18n notifications
 */
function replaceNotifications(code) {
  // Common notification patterns
  const notificationPatterns = {
    '登录成功': 'notification.loginSuccess',
    '用户名或密码错误': 'notification.loginFailed',
    '网络错误': 'notification.networkError',
    '注册成功': 'notification.registerSuccess',
    '密码修改成功': 'notification.passwordChanged',
    '邮件已发送': 'notification.emailSent',
    '已切换到夜间模式': 'notification.darkModeEnabled',
    '已切换到日间模式': 'notification.lightModeEnabled',
    '已退出登录': 'notification.loggedOut',
    '随机播放': 'notification.randomPlayStart',
    '没有找到可播放的音频': 'notification.noPlayableAudio',
    '获取随机播放列表失败': 'notification.randomPlayError'
  };

  let updatedCode = code;

  // Replace notification messages
  for (const [pattern, i18nKey] of Object.entries(notificationPatterns)) {
    const regex = new RegExp(`this.showSuccNotif\\(['"]${pattern}.*?['"]\\)`, 'g');
    updatedCode = updatedCode.replace(regex, `this.showSuccNotif(this.$t('${i18nKey}'))`);

    const warnRegex = new RegExp(`this.showWarnNotif\\(['"]${pattern}.*?['"]\\)`, 'g');
    updatedCode = updatedCode.replace(warnRegex, `this.showWarnNotif(this.$t('${i18nKey}'))`);

    const errRegex = new RegExp(`this.showErrNotif\\(['"]${pattern}.*?['"]\\)`, 'g');
    updatedCode = updatedCode.replace(errRegex, `this.showErrNotif(this.$t('${i18nKey}'))`);
  }

  return updatedCode;
}

export {
  textPatterns,
  convertToI18n,
  replaceNotifications
};
