package database

import (
	"context"
	"database/sql"
	"time"

	"github.com/Sakura-Byte/kikoeru-go/pkg/domain/model"
	"github.com/Sakura-Byte/kikoeru-go/pkg/domain/repository"
	list_params_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/list_params"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
)

// userRepositoryAdapter adapts the database UserRepository to the domain repository.UserRepository interface
type userRepositoryAdapter struct {
	repo UserRepository
}

// NewUserRepositoryAdapter creates a new adapter that implements repository.UserRepository
func NewUserRepositoryAdapter(repo UserRepository) repository.UserRepository {
	return &userRepositoryAdapter{repo: repo}
}

// convertToModelUser converts models.User to model.User
func convertToModelUser(user *models.User) *model.User {
	if user == nil {
		return nil
	}
	var email string
	if user.Email.Valid {
		email = user.Email.String
	}
	var verificationToken string
	if user.VerificationToken.Valid {
		verificationToken = user.VerificationToken.String
	}
	return &model.User{
		ID:                user.ID,
		Username:          user.Username,
		Password:          user.Password,
		Email:             email,
		EmailVerified:     user.EmailVerified,
		Group:             user.Group,
		CreatedAt:         user.CreatedAt,
		UpdatedAt:         user.UpdatedAt,
		VerificationToken: verificationToken,
	}
}

// convertFromModelUser converts model.User to models.User
func convertFromModelUser(user *model.User) *models.User {
	if user == nil {
		return nil
	}
	var email sql.NullString
	if user.Email != "" {
		email = sql.NullString{String: user.Email, Valid: true}
	}
	var verificationToken sql.NullString
	if user.VerificationToken != "" {
		verificationToken = sql.NullString{String: user.VerificationToken, Valid: true}
	}
	return &models.User{
		ID:                user.ID,
		Username:          user.Username,
		Password:          user.Password,
		Email:             email,
		EmailVerified:     user.EmailVerified,
		Group:             user.Group,
		CreatedAt:         user.CreatedAt,
		UpdatedAt:         user.UpdatedAt,
		VerificationToken: verificationToken,
	}
}

// Create implements repository.UserRepository
func (a *userRepositoryAdapter) Create(ctx context.Context, user *model.User) error {
	return a.repo.Create(ctx, convertFromModelUser(user))
}

// GetByID implements repository.UserRepository
func (a *userRepositoryAdapter) GetByID(ctx context.Context, id string) (*model.User, error) {
	user, err := a.repo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}
	return convertToModelUser(user), nil
}

// GetByUsername implements repository.UserRepository
func (a *userRepositoryAdapter) GetByUsername(ctx context.Context, username string) (*model.User, error) {
	user, err := a.repo.GetByUsername(ctx, username)
	if err != nil {
		return nil, err
	}
	return convertToModelUser(user), nil
}

// GetByEmail implements repository.UserRepository
func (a *userRepositoryAdapter) GetByEmail(ctx context.Context, email string) (*model.User, error) {
	user, err := a.repo.GetByEmail(ctx, email)
	if err != nil {
		return nil, err
	}
	return convertToModelUser(user), nil
}

// GetByVerificationToken implements repository.UserRepository
func (a *userRepositoryAdapter) GetByVerificationToken(ctx context.Context, token string) (*model.User, error) {
	user, err := a.repo.GetByVerificationToken(ctx, token)
	if err != nil {
		return nil, err
	}
	return convertToModelUser(user), nil
}

// GetByPasswordResetToken implements repository.UserRepository
func (a *userRepositoryAdapter) GetByPasswordResetToken(ctx context.Context, token string) (*model.User, error) {
	user, err := a.repo.GetByResetPasswordToken(ctx, token)
	if err != nil {
		return nil, err
	}
	return convertToModelUser(user), nil
}

// Update implements repository.UserRepository
func (a *userRepositoryAdapter) Update(ctx context.Context, user *model.User) error {
	return a.repo.Update(ctx, convertFromModelUser(user))
}

// Delete implements repository.UserRepository
func (a *userRepositoryAdapter) Delete(ctx context.Context, id string) error {
	return a.repo.DeleteByID(ctx, id)
}

// ClearVerificationToken implements repository.UserRepository
func (a *userRepositoryAdapter) ClearVerificationToken(ctx context.Context, userID string) error {
	user, err := a.repo.GetByID(ctx, userID)
	if err != nil {
		return err
	}
	user.VerificationToken = sql.NullString{Valid: false}
	user.VerificationTokenExpiresAt = sql.NullTime{Valid: false}
	return a.repo.Update(ctx, user)
}

// ClearPasswordResetToken implements repository.UserRepository
func (a *userRepositoryAdapter) ClearPasswordResetToken(ctx context.Context, userID string) error {
	return a.repo.ClearPasswordResetToken(ctx, userID)
}

// ListAll implements repository.UserRepository
func (a *userRepositoryAdapter) ListAll(ctx context.Context) ([]model.User, error) {
	users, _, err := a.repo.List(ctx, ListUsersAdminDbParams{})
	if err != nil {
		return nil, err
	}
	result := make([]model.User, len(users))
	for i, user := range users {
		if converted := convertToModelUser(user); converted != nil {
			result[i] = *converted
		}
	}
	return result, nil
}

// ListWithPagination implements repository.UserRepository
func (a *userRepositoryAdapter) ListWithPagination(ctx context.Context, page, pageSize int) ([]model.User, int, error) {
	users, total, err := a.repo.List(ctx, ListUsersAdminDbParams{
		PaginationParams: list_params_dto.PaginationParams{
			Page:     page,
			PageSize: pageSize,
		},
	})
	if err != nil {
		return nil, 0, err
	}
	result := make([]model.User, len(users))
	for i, user := range users {
		if converted := convertToModelUser(user); converted != nil {
			result[i] = *converted
		}
	}
	return result, int(total), nil
}

// UpdateVerificationToken implements repository.UserRepository
func (a *userRepositoryAdapter) UpdateVerificationToken(ctx context.Context, userID, token string, expires time.Time) error {
	user, err := a.repo.GetByID(ctx, userID)
	if err != nil {
		return err
	}
	user.VerificationToken = sql.NullString{String: token, Valid: true}
	user.VerificationTokenExpiresAt = sql.NullTime{Time: expires, Valid: true}
	return a.repo.Update(ctx, user)
}

// UpdatePasswordResetToken implements repository.UserRepository
func (a *userRepositoryAdapter) UpdatePasswordResetToken(ctx context.Context, userID, token string, expires time.Time) error {
	user, err := a.repo.GetByID(ctx, userID)
	if err != nil {
		return err
	}
	user.ResetPasswordToken = sql.NullString{String: token, Valid: true}
	user.ResetPasswordTokenExpiresAt = sql.NullTime{Time: expires, Valid: true}
	return a.repo.Update(ctx, user)
}
