package utils

import (
	"path/filepath"
	"strings"
)

// DetermineFileType determines the general type of a file based on its name and whether it's a directory.
// Used by both FileSystemService and ArchiveService.
func DetermineFileType(name string, isDir bool) string {
	if isDir {
		return "folder"
	}
	ext := strings.ToLower(filepath.Ext(name))
	switch ext {
	case ".mp3", ".wav", ".flac", ".aac", ".ogg", ".opus", ".m4a":
		return "audio"
	case ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp":
		return "image"
	case ".txt", ".lrc", ".md":
		return "text"
	case ".srt", ".ass", ".vtt", ".sub", ".sbv", ".ssa":
		return "subtitle"
	case ".mp4", ".mkv", ".avi", ".mov", ".webm":
		return "video"
	case ".zip", ".7z", ".rar", ".z01", ".001", ".tar", ".gz", ".bz2", ".xz":
		return "archive"
	default:
		return "unknown"
	}
}

// IsArchiveFile returns true if the filename has an archive extension
func IsArchiveFile(filename string) bool {
	extension := strings.ToLower(filepath.Ext(filename))

	// List of common archive extensions
	archiveExtensions := map[string]bool{
		".zip":  true,
		".rar":  true,
		".7z":   true,
		".tar":  true,
		".gz":   true,
		".bz2":  true,
		".xz":   true,
		".zst":  true,
		".lz4":  true,
		".lzma": true,
		".sz":   true,
		".s2":   true,
		".zz":   true,
		".br":   true,
	}

	return archiveExtensions[extension]
}
