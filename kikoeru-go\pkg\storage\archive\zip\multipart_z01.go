package zip

import (
	"context"
	"errors"
	"fmt"
	"io"
	"path/filepath"
	"regexp"
	"sort"
	"strconv"
	"strings"

	"github.com/Sakura-Byte/kikoeru-go/pkg/log"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
)

// MultiPartZipReader provides a single io.Reader interface for multi-part ZIP files
// Based on your colleague's implementation, adapted for StreamWithSeek interface
// This handles .zip + .z01/.z02/.z03 format specifically
type MultiPartZipReader struct {
	streams     []*models.StreamWithSeek // List of streams in correct order
	currentPart int                      // Index of the current part being read
	offset      int64                    // Current offset in the virtual combined file
	sizes       []int64                  // Size of each part stream
	totalSize   int64                    // Total size of all parts combined
}

// NewMultiPartZipReader creates a new reader for a multi-part ZIP file
// The streams should contain the .zip file and all .z01, .z02, etc. parts
func NewMultiPartZipReader(streams []*models.StreamWithSeek) (*MultiPartZipReader, error) {
	if len(streams) == 0 {
		return nil, errors.New("no streams provided")
	}

	// Find and sort the parts according to your colleague's logic
	sortedStreams, err := sortZ01Streams(streams)
	if err != nil {
		return nil, fmt.Errorf("error sorting streams: %w", err)
	}

	// Calculate sizes
	sizes := make([]int64, len(sortedStreams))
	var totalSize int64
	for i, stream := range sortedStreams {
		sizes[i] = stream.GetSize()
		totalSize += stream.GetSize()
	}

	log.Debug(context.Background(), "Created MultiPartZipReader", 
		"stream_count", len(sortedStreams), "total_size", totalSize)
	for i, stream := range sortedStreams {
		log.Debug(context.Background(), "Stream order", 
			"index", i, "name", stream.GetName(), "size", stream.GetSize())
	}

	return &MultiPartZipReader{
		streams:     sortedStreams,
		currentPart: 0,
		offset:      0,
		sizes:       sizes,
		totalSize:   totalSize,
	}, nil
}

// sortZ01Streams sorts streams according to your colleague's logic
// For .zip + .z01/.z02/.z03 format, the order should be: .z01, .z02, .z03, .zip
func sortZ01Streams(streams []*models.StreamWithSeek) ([]*models.StreamWithSeek, error) {
	if len(streams) <= 1 {
		return streams, nil
	}

	// Find the .zip file
	var zipStream *models.StreamWithSeek
	var zipIndex = -1
	for i, stream := range streams {
		if strings.HasSuffix(strings.ToLower(stream.GetName()), ".zip") {
			zipStream = stream
			zipIndex = i
			break
		}
	}

	if zipStream == nil {
		return nil, errors.New("no .zip file found in streams")
	}

	// Get base name without .zip extension
	zipName := zipStream.GetName()
	baseWithoutExt := strings.TrimSuffix(filepath.Base(zipName), ".zip")
	
	// Create regex pattern for .z01, .z02, etc.
	pattern := regexp.MustCompile(regexp.QuoteMeta(baseWithoutExt) + `\.z(\d+)$`)
	
	// Find all .zXX parts and their numbers
	partMap := make(map[int]*models.StreamWithSeek)
	
	for i, stream := range streams {
		if i == zipIndex {
			continue // Skip the .zip file for now
		}
		
		name := filepath.Base(stream.GetName())
		matches := pattern.FindStringSubmatch(name)
		if len(matches) == 2 {
			partNum, err := strconv.Atoi(matches[1])
			if err == nil {
				partMap[partNum] = stream
			}
		}
	}
	
	// Get sorted part numbers
	partNums := make([]int, 0, len(partMap))
	for num := range partMap {
		partNums = append(partNums, num)
	}
	sort.Ints(partNums)
	
	// Build the final sorted list: .z01, .z02, .z03, then .zip
	result := make([]*models.StreamWithSeek, 0, len(streams))
	
	// Add .zXX parts in order
	for _, num := range partNums {
		result = append(result, partMap[num])
	}
	
	// Add .zip file at the end
	result = append(result, zipStream)
	
	return result, nil
}

// Read implements the io.Reader interface
func (mz *MultiPartZipReader) Read(p []byte) (n int, err error) {
	if mz.currentPart >= len(mz.streams) {
		return 0, io.EOF
	}

	// Read from current stream
	bytesRead, err := mz.streams[mz.currentPart].Read(p)
	mz.offset += int64(bytesRead)

	// If we reach EOF in current stream but there are more parts
	if err == io.EOF && mz.currentPart < len(mz.streams)-1 {
		// Move to next part
		mz.currentPart++
		
		// Reset the next stream to the beginning
		_, seekErr := mz.streams[mz.currentPart].Seek(0, io.SeekStart)
		if seekErr != nil {
			return bytesRead, fmt.Errorf("error seeking to start of next part: %w", seekErr)
		}

		log.Debug(context.Background(), "Switched to next multipart stream", 
			"stream_index", mz.currentPart, "stream_name", mz.streams[mz.currentPart].GetName())

		// If we didn't read anything and need to continue with next stream
		if bytesRead == 0 {
			return mz.Read(p)
		}

		// Return bytesRead with no error to indicate more data is available
		return bytesRead, nil
	}

	return bytesRead, err
}

// Seek implements the io.Seeker interface
func (mz *MultiPartZipReader) Seek(offset int64, whence int) (int64, error) {
	var targetOffset int64

	switch whence {
	case io.SeekStart:
		targetOffset = offset
	case io.SeekCurrent:
		targetOffset = mz.offset + offset
	case io.SeekEnd:
		targetOffset = mz.totalSize + offset
	default:
		return 0, errors.New("invalid whence value")
	}

	if targetOffset < 0 {
		return 0, errors.New("negative offset")
	}

	if targetOffset > mz.totalSize {
		return 0, fmt.Errorf("offset beyond end of file: %d > %d", targetOffset, mz.totalSize)
	}

	// Find which part contains the target offset
	var cumulativeSize int64
	targetPart := 0
	var partOffset int64

	for i, size := range mz.sizes {
		if targetOffset < cumulativeSize+size {
			targetPart = i
			partOffset = targetOffset - cumulativeSize
			break
		}
		cumulativeSize += size
	}

	// Update current part
	mz.currentPart = targetPart

	// Seek within the stream
	_, err := mz.streams[targetPart].Seek(partOffset, io.SeekStart)
	if err != nil {
		return 0, err
	}

	// Update current offset
	mz.offset = targetOffset
	return targetOffset, nil
}

// ReadAt implements io.ReaderAt interface
func (mz *MultiPartZipReader) ReadAt(p []byte, off int64) (n int, err error) {
	// Save current position
	currentOffset := mz.offset
	currentPart := mz.currentPart

	// Seek to the requested offset
	_, err = mz.Seek(off, io.SeekStart)
	if err != nil {
		return 0, err
	}

	// Read data
	n, err = mz.Read(p)

	// Restore position
	mz.currentPart = currentPart
	_, seekErr := mz.Seek(currentOffset, io.SeekStart)
	if seekErr != nil && err == nil {
		err = seekErr
	}

	return n, err
}

// Size returns the total size of all parts combined
func (mz *MultiPartZipReader) Size() int64 {
	return mz.totalSize
}

// Close closes all streams
func (mz *MultiPartZipReader) Close() error {
	var firstErr error
	for _, stream := range mz.streams {
		if err := stream.Close(); err != nil && firstErr == nil {
			firstErr = err
		}
	}
	return firstErr
}

// isZ01Format checks if the streams represent a .zip + .z01/.z02/.z03 format
func isZ01Format(streams []*models.StreamWithSeek) bool {
	hasZip := false
	hasZ01 := false
	
	for _, stream := range streams {
		name := strings.ToLower(stream.GetName())
		if strings.HasSuffix(name, ".zip") {
			hasZip = true
		}
		if strings.Contains(name, ".z01") || strings.Contains(name, ".z02") {
			hasZ01 = true
		}
	}
	
	return hasZip && hasZ01
}
