package database

import (
	"context"
	"database/sql"
	"errors"
	"fmt"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors" // Added for apperrors
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"gorm.io/gorm"
)

// Error constants are now defined in errors.go

type PlaylistRepository interface {
	Create(ctx context.Context, playlist *models.Playlist) error
	GetByID(ctx context.Context, playlistID uint, userID string) (*models.Playlist, error)                                               // Changed username to userID
	ListByUserID(ctx context.Context, userID string, page int, pageSize int) (playlists []*models.Playlist, totalCount int64, err error) // Changed ListByUser to ListByUserID and username to userID
	Update(ctx context.Context, playlist *models.Playlist, userID string) error                                                          // Changed username to userID
	Delete(ctx context.Context, playlistID uint, userID string) error                                                                    // Changed username to userID
	AddItem(ctx context.Context, item *models.PlaylistItem) error
	GetItemByID(ctx context.Context, itemID uint) (*models.PlaylistItem, error)
	RemoveItem(ctx context.Context, itemID uint, userID string) error                                          // Changed username to userID
	ListItemsByPlaylistID(ctx context.Context, playlistID uint, userID string) ([]*models.PlaylistItem, error) // Changed username to userID
	GetMaxOrderItem(ctx context.Context, playlistID uint) (int, error)
	UpdateItemOrders(ctx context.Context, playlistID uint, itemOrders map[uint]int) error
}

type playlistRepository struct {
	db *gorm.DB
}

func NewPlaylistRepository(db *gorm.DB) PlaylistRepository {
	return &playlistRepository{db: db}
}

func (r *playlistRepository) Create(ctx context.Context, playlist *models.Playlist) error {
	if playlist.UserID == "" { // Changed from Username to UserID
		return errors.New("UserID is required to create a playlist")
	}
	return r.db.WithContext(ctx).Create(playlist).Error
}

func (r *playlistRepository) GetByID(ctx context.Context, playlistID uint, userID string) (*models.Playlist, error) { // Changed username to userID
	var playlist models.Playlist
	// First try to get the playlist regardless of ownership
	result := r.db.WithContext(ctx).Where("id = ?", playlistID).First(&playlist)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, apperrors.ErrPlaylistNotFound
		}
		return nil, result.Error
	}

	// If it's a private playlist, verify ownership
	if playlist.Visibility == models.PlaylistVisibilityPrivate && playlist.UserID != userID {
		return nil, apperrors.ErrPlaylistNotFound
	}

	return &playlist, nil
}

func (r *playlistRepository) ListByUserID(ctx context.Context, userID string, page int, pageSize int) ([]*models.Playlist, int64, error) { // Changed ListByUser to ListByUserID and username to userID
	var playlists []*models.Playlist
	var totalCount int64
	query := r.db.WithContext(ctx).Model(&models.Playlist{}).Where("user_id = ?", userID) // Changed username to user_id
	if err := query.Count(&totalCount).Error; err != nil {
		return nil, 0, err
	}
	if totalCount == 0 {
		return []*models.Playlist{}, 0, nil
	}
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}
	offset := (page - 1) * pageSize
	err := query.Order("updated_at DESC").Offset(offset).Limit(pageSize).Find(&playlists).Error
	if err != nil {
		return nil, 0, err
	}
	return playlists, totalCount, nil
}

func (r *playlistRepository) Update(ctx context.Context, playlist *models.Playlist, userID string) error { // Changed username to userID
	if playlist.ID == 0 {
		return errors.New("playlist ID is required for update")
	}
	var existingPlaylist models.Playlist
	res := r.db.WithContext(ctx).Where("id = ? AND user_id = ?", playlist.ID, userID).First(&existingPlaylist) // Changed username to user_id
	if res.Error != nil {
		if errors.Is(res.Error, gorm.ErrRecordNotFound) {
			return apperrors.ErrPlaylistNotFound
		}
		return res.Error
	}
	// Note: When using Updates with a struct, GORM will only update non-zero fields.
	// If you need to update fields to their zero values (e.g., Description to ""), use .Select or map[string]interface{}.
	// For simplicity, assuming direct struct update is sufficient here.
	return r.db.WithContext(ctx).Model(&existingPlaylist).Updates(models.Playlist{
		Name:        playlist.Name,
		Description: playlist.Description,
		Visibility:  playlist.Visibility,
	}).Error
}

func (r *playlistRepository) Delete(ctx context.Context, playlistID uint, userID string) error { // Changed username to userID
	var playlist models.Playlist
	// Ensure the user owns the playlist before deleting
	res := r.db.WithContext(ctx).Where("id = ? AND user_id = ?", playlistID, userID).First(&playlist) // Changed username to user_id
	if res.Error != nil {
		if errors.Is(res.Error, gorm.ErrRecordNotFound) {
			return apperrors.ErrPlaylistNotFound
		}
		return res.Error
	}
	// GORM's Delete with a primary key will delete the record.
	// Need to ensure cascading delete for playlist items is handled by DB or service layer if necessary.
	// The migration script has ON DELETE CASCADE for t_playlist_item.
	return r.db.WithContext(ctx).Delete(&models.Playlist{}, playlistID).Error
}

func (r *playlistRepository) AddItem(ctx context.Context, item *models.PlaylistItem) error {
	if item.PlaylistID == 0 || item.WorkID == 0 {
		return errors.New("playlist ID and work ID are required to add an item")
	}
	return r.db.WithContext(ctx).Create(item).Error
}

func (r *playlistRepository) GetItemByID(ctx context.Context, itemID uint) (*models.PlaylistItem, error) {
	var item models.PlaylistItem
	result := r.db.WithContext(ctx).Preload("Work").First(&item, itemID)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, apperrors.ErrPlaylistItemNotFound
		}
		return nil, result.Error
	}
	return &item, nil
}

func (r *playlistRepository) RemoveItem(ctx context.Context, itemID uint, userID string) error { // Changed username to userID
	item, err := r.GetItemByID(ctx, itemID)
	if err != nil {
		return err
	}
	// Verify the user owns the playlist from which the item is being removed
	_, err = r.GetByID(ctx, item.PlaylistID, userID) // Changed username to userID
	if err != nil {
		if errors.Is(err, apperrors.ErrPlaylistNotFound) {
			// This implies the playlist doesn't exist or doesn't belong to the user
			return apperrors.ErrNotPlaylistOwner // Using ErrNotPlaylistOwner
		}
		return err
	}
	return r.db.WithContext(ctx).Delete(&models.PlaylistItem{}, itemID).Error
}

func (r *playlistRepository) ListItemsByPlaylistID(ctx context.Context, playlistID uint, userID string) ([]*models.PlaylistItem, error) { // Changed username to userID
	// Get the playlist to check its visibility
	_, err := r.GetByID(ctx, playlistID, userID)
	if err != nil {
		return nil, err
	}

	// GetByID already verifies access permissions based on visibility and ownership

	var items []*models.PlaylistItem
	err = r.db.WithContext(ctx).
		Where("playlist_id = ?", playlistID).
		Order("\"order\" ASC").
		Preload("Work").Preload("Work.Circle").Preload("Work.Tags").Preload("Work.VAs").
		Find(&items).Error
	if err != nil {
		return nil, err
	}
	return items, nil
}

func (r *playlistRepository) GetMaxOrderItem(ctx context.Context, playlistID uint) (int, error) {
	var result struct{ MaxOrder sql.NullInt64 }
	err := r.db.WithContext(ctx).Model(&models.PlaylistItem{}).
		Where("playlist_id = ?", playlistID).
		Select("MAX(\"order\") as max_order").
		Scan(&result).Error
	if err != nil {
		return -1, err
	}
	if !result.MaxOrder.Valid {
		return -1, nil
	}
	return int(result.MaxOrder.Int64), nil
}

// UpdateItemOrders updates the 'order' field for multiple items in a playlist within a transaction.
// itemOrders is a map of PlaylistItem.ID to its new order value.
func (r *playlistRepository) UpdateItemOrders(ctx context.Context, playlistID uint, itemOrders map[uint]int) error {
	if len(itemOrders) == 0 {
		return nil // Nothing to update
	}

	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for itemID, newOrder := range itemOrders {
			// Ensure the item belongs to the specified playlist to prevent accidental updates across playlists
			// Although the service layer should already ensure this by validating playlist ownership.
			// This is an extra safety check.
			result := tx.Model(&models.PlaylistItem{}).
				Where("id = ? AND playlist_id = ?", itemID, playlistID).
				UpdateColumn("order", newOrder) // Use UpdateColumn to update a single field without hooks

			if result.Error != nil {
				return fmt.Errorf("failed to update order for item %d: %w", itemID, result.Error)
			}
			if result.RowsAffected == 0 {
				// This means the item was not found in the specified playlist, or already had that order (less likely for order change)
				// Or item simply doesn't exist. Service layer should ideally validate itemIDs belong to playlist.
				// For now, we can treat this as a potential issue or ignore if service layer handles it.
				// Let's return an error to indicate not all items were updated as expected.
				return fmt.Errorf("item %d not found in playlist %d or no change in order needed", itemID, playlistID)
			}
		}
		return nil
	})
}
