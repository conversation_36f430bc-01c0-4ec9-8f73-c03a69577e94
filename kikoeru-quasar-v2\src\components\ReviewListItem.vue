<template>
  <q-item v-if="review.work" clickable :to="`/work/${review.work.original_id}`" style="padding: 5px;">
    <q-item-section avatar style="padding: 0px 5px 0px 0px;">
      <router-link :to="`/work/${review.work.original_id}`">
        <q-img transition="fade" :src="samCoverUrl" style="height: 60px; width: 60px;" />
      </router-link>
    </q-item-section>

    <q-item-section>
      <q-item-label lines="2" class="text">
        <router-link :to="`/work/${review.work.original_id}`" class="text-black">
          {{ review.work.title }}
        </router-link>
      </q-item-label>

      <q-item-label>
        <div class="row q-gutter-x-sm q-gutter-y-xs items-center">
          <!-- 进度状态 -->
          <q-chip
            v-if="review.progress"
            :color="getProgressColor(review.progress)"
            text-color="white"
            dense
            size="sm"
            class="col-auto"
          >
            {{ getProgressLabel(review.progress) }}
          </q-chip>

          <!-- 评分 -->
          <div v-if="review.rating" class="col-auto">
            <q-rating
              :model-value="review.rating"
              readonly
              size="sm"
              color="amber"
              icon="star"
            />
          </div>

          <!-- 社团名 -->
          <router-link v-if="review.work.circle" :to="`/works?circle=${review.work.circle.name}`" class="col-auto text-grey">
            {{ review.work.circle.name }}
          </router-link>
        </div>
      </q-item-label>

      <!-- 评论文本 -->
      <q-item-label v-if="review.review_text" lines="2" class="text-grey-7">
        {{ review.review_text }}
      </q-item-label>

      <!-- 时间信息 -->
      <q-item-label caption>
        {{ t('review.updatedAt') }}: {{ formatDate(review.updated_at) }}
      </q-item-label>
    </q-item-section>

    <!-- 操作按钮 -->
    <q-item-section side>
      <div class="column q-gutter-xs">
        <q-btn
          dense
          round
          color="primary"
          icon="edit"
          size="sm"
          @click.stop="editReview"
        >
          <q-tooltip>{{ t('review.editReview') }}</q-tooltip>
        </q-btn>
        <q-btn
          dense
          round
          color="negative"
          icon="delete"
          size="sm"
          @click.stop="confirmDelete"
        >
          <q-tooltip>{{ t('review.deleteReview') }}</q-tooltip>
        </q-btn>
      </div>
    </q-item-section>

    <!-- 删除确认对话框 -->
    <q-dialog v-model="showDeleteDialog" persistent>
      <q-card>
        <q-card-section>
          <div class="text-h6">{{ t('review.confirmDelete.title') }}</div>
          <div class="text-body2">{{ t('review.confirmDelete.message') }}</div>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="t('common.cancel')" v-close-popup />
          <q-btn flat :label="t('common.delete')" color="negative" @click="deleteReview" />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 编辑评论对话框 -->
    <WriteReview
      v-if="showEditDialog"
      :originalId="review.work.original_id"
      :metadata="review.work"
      :existingReview="review"
      @closed="handleEditClosed"
    />
  </q-item>
</template>

<script setup>
import { ref, computed, getCurrentInstance } from 'vue'
import { useI18n } from 'vue-i18n'
import WriteReview from './WriteReview.vue'
import { useNotification } from '../composables/useNotification'

defineOptions({
  name: 'ReviewListItem'
})

const props = defineProps({
  review: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['refresh'])

const { t, locale } = useI18n()
const { proxy } = getCurrentInstance()
const { showSuccessNotification, showErrorNotification } = useNotification()

// Reactive data
const showDeleteDialog = ref(false)
const showEditDialog = ref(false)

// Computed properties
const samCoverUrl = computed(() => {
  // Use the new API format for thumbnail images
  return props.review.work?.original_id ? `/api/v1/cover/${props.review.work.original_id}?type=sam` : ""
})

// Methods
const getProgressLabel = (progress) => {
  const labels = {
    'marked': t('review.progress.wantToListen'),
    'listening': t('review.progress.listening'),
    'listened': t('review.progress.listened'),
    'replay': t('review.progress.relistening'),
    'postponed': t('review.progress.postponed')
  }
  return labels[progress] || progress
}

const getProgressColor = (progress) => {
  const colors = {
    'marked': 'blue',
    'listening': 'orange',
    'listened': 'green',
    'replay': 'purple',
    'postponed': 'grey'
  }
  return colors[progress] || 'grey'
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString(locale.value, {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

const editReview = () => {
  showEditDialog.value = true
}

const handleEditClosed = (wasModified) => {
  showEditDialog.value = false
  if (wasModified) {
    emit('refresh')
  }
}

const confirmDelete = () => {
  showDeleteDialog.value = true
}

const deleteReview = async () => {
  try {
    await proxy.$api.delete(`/api/v1/review/${props.review.work_id}`)
    showDeleteDialog.value = false
    showSuccessNotification(t('notification.reviewDeleted'))
    emit('refresh')
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('notification.reviewDeleteFailed'))
  }
}


</script>
