<template>
  <div>
      <q-dialog v-model="showReviewDialog" @hide="closeDialog">
        <q-card>
          <q-card-section class="q-pb-sm">
            <div class="text-body1">{{ t('review.myReview') }}</div>
          </q-card-section>

          <q-card-section class="q-pt-none">
            <q-rating
              v-model="rating"
              size="sm"
              color="blue"
              icon="star_border"
              icon-selected="star"
              icon-half="star_half"
              class="col-auto"
            />
            <q-btn-toggle
              v-model="progress"
              no-caps
              :class="$q.screen.lt.sm ? 'my-custom-toggle q-mx-none q-mt-sm' : 'my-custom-toggle q-mx-md'"
              rounded
              unelevated
              :padding="$q.screen.width < 400 ? 'sm': ''"
              toggle-color="primary"
              color="white"
              text-color="primary"
              :options="progressOptions"
            />
          </q-card-section>

          <q-card-section class="q-pt-none" >
            <div style="min-width: 300px">
              <q-input
                v-model="reviewText"
                filled
                type="textarea"
              />
            </div>
          </q-card-section>

          <div class="row justify-between">
            <q-card-actions  class="text-red">
              <q-btn flat :label="t('review.deleteTag')" v-close-popup @click="deleteConfirm = true" />
            </q-card-actions>

            <q-card-actions align="right" class="text-teal">
              <q-btn flat :label="t('common.confirm')" v-close-popup @click="submitReview()" />
              <q-btn flat :label="t('common.cancel')" v-close-popup @click="closeDialog()" />
            </q-card-actions>
          </div>
        </q-card>
      </q-dialog>

      <q-dialog v-model="deleteConfirm" persistent transition-show="scale" transition-hide="scale">
        <q-card class="bg-teal text-white" style="width: 300px">
          <q-card-section>
            <div class="text-h6">{{ t('review.confirmDeleteTag') }}</div>
          </q-card-section>

          <q-card-actions align="right" class="text-teal">
              <q-btn flat :label="t('common.confirm')" v-close-popup @click="deleteReview()" />
              <q-btn flat :label="t('common.cancel')" v-close-popup @click="closeDialog()"/>
          </q-card-actions>
        </q-card>
      </q-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, getCurrentInstance } from 'vue'
import { useI18n } from 'vue-i18n'
import { useQuasar } from 'quasar'
import { useNotification } from '../composables/useNotification'

defineOptions({
  name: 'WriteReview'
})

const props = defineProps({
  originalId: {
    type: String,
    required: true
  },
  metadata: {
    type: Object,
    default: null
  },
  existingReview: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['closed'])

const { t } = useI18n()
const $q = useQuasar()
const { proxy } = getCurrentInstance()
const { showSuccessNotification, showErrorNotification } = useNotification()

// Reactive data
const showReviewDialog = ref(true)
const deleteConfirm = ref(false)
const rating = ref(0)
const reviewText = ref('')
const modified = ref(false)
const progress = ref('')

// Computed properties
const progressOptions = computed(() => [
  { label: t('review.progress.wantToListen'), value: 'marked' },
  { label: t('review.progress.listening'), value: 'listening' },
  { label: t('review.progress.listened'), value: 'listened' },
  { label: t('review.progress.relistening'), value: 'replay' },
  { label: t('review.progress.postponed'), value: 'postponed' }
])

// Methods
const fetchExistingReview = async () => {
  try {
    // Use the new efficient endpoint to get user's review for this specific work
    const response = await proxy.$api.get(`/api/v1/review/work/${props.originalId}`)

    if (response.data) {
      // response.data is the review model with lowercase field names
      progress.value = response.data.progress || ''
      reviewText.value = response.data.review_text || ''
      rating.value = response.data.rating || 0
    }
  } catch (error) {
    // Ignore 404 errors - user hasn't reviewed this work yet, which is normal
    if (error.response?.status !== 404) {
      console.warn('Failed to fetch existing review:', error)
    }
  }
}

const closeDialog = () => {
  // Do not emit anything if the second dialog is shown
  // If the user clicks anywhere outside of the main dialog, emit 'closed'
  if (!deleteConfirm.value) {
    if (modified.value) {
      emit('closed', true)
    } else {
      emit('closed', false)
    }
  }
}

const reviewPayload = () => {
  const submitPayload = {
    'work_id': props.originalId,
    'rating': rating.value,
    'review_text': reviewText.value,
    'progress': progress.value
  }
  return submitPayload
}

const submitReview = () => {
  const payload = reviewPayload()
  proxy.$api.put('/api/v1/review', payload)
    .then(() => {
      modified.value = true
      showSuccessNotification(t('notification.reviewSubmitted'))
    })
    .then(() => closeDialog())
    .catch((error) => {
      showErrorNotification(error.response?.data?.error || error.message || t('notification.reviewSubmitFailed'))
    })
}

const deleteReview = () => {
  proxy.$api.delete(`/api/v1/review/${props.originalId}`)
    .then(() => {
      modified.value = true
      showSuccessNotification(t('notification.reviewDeleted'))
    })
    .then(() => closeDialog())
    .catch((error) => {
      showErrorNotification(error.response?.data?.error || error.message || t('notification.reviewDeleteFailed'))
    })
}

// Lifecycle
onMounted(() => {
  // If existing review data is passed as prop, use it directly
  if (props.existingReview) {
    progress.value = props.existingReview.progress || ''
    reviewText.value = props.existingReview.review_text || ''
    rating.value = props.existingReview.rating || 0
  } else {
    // Fallback: check metadata for user_rating
    if (props.metadata?.user_rating) {
      rating.value = props.metadata.user_rating
    }

    // Fetch existing review data from API
    fetchExistingReview()
  }
})


</script>

<style lang="sass" scoped>
.my-custom-toggle
  border: 1px solid #027be3
</style>
