package va_dto

import (
	"time"

	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
)

// VADTO represents a basic voice actor in API responses.
type VADTO struct {
	ID        string    `json:"id"`
	Name      string    `json:"name"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// VANameDTO represents a voice actor by its name, typically for requests.
type VANameDTO struct {
	Name string `json:"name" binding:"required"`
}

// MapVAModelToDTO converts a models.VA to a VADTO.
func MapVAModelToDTO(vaModel *models.VA) *VADTO {
	if vaModel == nil {
		return nil
	}
	return &VADTO{
		ID:        vaModel.ID,
		Name:      vaModel.Name,
		CreatedAt: vaModel.CreatedAt,
		UpdatedAt: vaModel.UpdatedAt,
	}
}

// MapVAModelsToDTOs converts a slice of models.VA to a slice of VADTO.
func MapVAModelsToDTOs(vaModels []*models.VA) []*VADTO {
	if vaModels == nil {
		return nil
	}
	vaDTOs := make([]*VADTO, len(vaModels))
	for i, model := range vaModels {
		vaDTOs[i] = MapVAModelToDTO(model)
	}
	return vaDTOs
}
