function prefixRoutes(prefix, routes) {
  return routes.map((route) => {
    route.path = prefix + '' + route.path;
    return route;
  });
}

const routes = [
  {
    path: '/admin',
    component: () => import('layouts/DashboardLayout.vue'),
    children: [
      {
        path: '',
        component: () => import('pages/Dashboard/Folders.vue')
      },
      {
        path: 'scanner',
        component: () => import('pages/Dashboard/Scanner.vue')
      },
      {
        path: 'advanced',
        component: () => import('pages/Dashboard/Advanced.vue')
      },
      {
        path: 'usermanage',
        component: () => import('pages/Dashboard/UserManage.vue')
      },
      {
        path: 'profile',
        component: () => import('pages/admin/AdminProfile.vue')
      },
      {
        path: 'storages',
        component: () => import('pages/admin/StorageManage.vue')
      },
      {
        path: 'tasks',
        component: () => import('pages/admin/TaskManage.vue')
      },
      {
        path: 'feedback',
        component: () => import('pages/Dashboard/FeedbackManage.vue')
      },
      {
        path: 'archive-passwords',
        component: () => import('pages/admin/ArchivePasswordManage.vue')
      }
    ],
    meta: {
      auth: true,
      requiresAdmin: true
    }
  },
  {
    path: '/',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      {
        path: '',
        redirect: {
          name: 'works'
        }
      },
      {
        path: 'works',
        name: 'works',
        component: () => import('pages/Works.vue')
      },
      {
        path: 'work/:id',
        component: () => import('pages/Work.vue')
      },
      {
        path: 'circles',
        props: { restrict: "circles" },
        component: () => import('pages/List.vue')
      },
      {
        path: 'tags',
        props: { restrict: "tags" },
        component: () => import('pages/List.vue')
      },
      {
        path: 'vas',
        props: { restrict: "vas" },
        component: () => import('pages/List.vue')
      },
      {
        path: 'playlists',
        component: () => import('pages/Playlists.vue'),
        meta: { auth: true }
      },
      {
        path: 'playlist/:id',
        component: () => import('pages/PlaylistDetail.vue'),
        meta: { auth: true }
      },
      ...prefixRoutes('reviews', [
        {
          path: '',
          props: { route: 'review'},
          component: () => import('pages/Reviews.vue'),
          meta: { auth: true }
        },
        {
          path: '/review',

          props: { route: 'review'},
          component: () => import('pages/Reviews.vue'),
          meta: { auth: true }
        },
        ...prefixRoutes('/progress', [
          {
            path: '',
            props: { route: 'progress', progress: 'marked'},
            component: () => import('pages/Reviews.vue'),
            meta: { auth: true }
          },
          {
            path: '/marked',
            props: { route: 'progress', progress: 'marked'},
            component: () => import('pages/Reviews.vue'),
            meta: { auth: true }
          },
          {
            path: '/listening',
            props: { route: 'progress', progress: 'listening'},
            component: () => import('pages/Reviews.vue'),
            meta: { auth: true }
          },
          {
            path: '/listened',
            props: { route: 'progress', progress: 'listened'},
            component: () => import('pages/Reviews.vue'),
            meta: { auth: true }
          },
          {
            path: '/replay',
            props: { route: 'progress', progress: 'replay'},
            component: () => import('pages/Reviews.vue'),
            meta: { auth: true }
          },
          {
            path: '/postponed',
            props: { route: 'progress', progress: 'postponed'},
            component: () => import('pages/Reviews.vue'),
            meta: { auth: true }
          },
        ]),
      ]),
      {
        path: 'history',
        component: () => import('pages/PlayHistory.vue'),
        meta: { auth: true }
      },
      {
        path: 'settings',
        component: () => import('pages/Settings.vue'),
        meta: { auth: true }
      }
    ]
  },
  {
    path: '/:catchAll(.*)*',
    component: () => import('pages/ErrorNotFound.vue')
  }
]

export default routes
