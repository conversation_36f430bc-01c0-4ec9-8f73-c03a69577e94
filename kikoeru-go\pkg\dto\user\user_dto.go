package user_dto

import (
	list_params_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/list_params" // Import list_params_dto
	// "github.com/Sakura-Byte/kikoeru-go/pkg/storage/database" // Removed unused import
)

// UserRegistrationRequest is the DTO for user registration.
type UserRegistrationRequest struct {
	Username string `json:"username" binding:"required"`
	Email    string `json:"email"`
	Password string `json:"password" binding:"required"`
}

// UserLoginRequest is the DTO for user login.
type UserLoginRequest struct {
	Identifier string `json:"identifier" binding:"required"` // Can be username or email
	Password   string `json:"password" binding:"required"`
}

// ChangePasswordRequest is the DTO for changing a user's password.
type ChangePasswordRequest struct {
	OldPassword string `json:"old_password" binding:"required"`
	NewPassword string `json:"new_password" binding:"required"`
}

// RequestLinkEmailRequest is the DTO for requesting an email linking/verification.
type RequestLinkEmailRequest struct {
	Email string `json:"email" binding:"required"`
}

// VerifyEmailRequest is the DTO for verifying an email address.
type VerifyEmailRequest struct {
	Token string `json:"token" binding:"required"`
}

// UnlinkEmailRequest is the DTO for unlinking an email address.
type UnlinkEmailRequest struct {
	Password string `json:"password" binding:"required"`
}

// RequestPasswordResetRequest is the DTO for requesting a password reset email.
type RequestPasswordResetRequest struct {
	Email string `json:"email" binding:"required"`
}

// ResetPasswordWithTokenRequest is the DTO for resetting a password using a token.
type ResetPasswordWithTokenRequest struct {
	Token       string `json:"token" binding:"required"`
	NewPassword string `json:"new_password" binding:"required"`
}

// ListUsersParams is the DTO for listing users (admin).
type ListUsersParams struct {
	list_params_dto.PaginationParams        // Embedded pagination and sorting
	Username                         string `form:"username" json:"username,omitempty"` // Search term for username (uses LIKE)
	Email                            string `form:"email" json:"email,omitempty"`       // Search term for email (uses LIKE)
	Group                            string `form:"group" json:"group,omitempty"`       // Exact match for group
}

// UpdateUserByAdminRequest is the DTO for updating a user by admin.
type UpdateUserByAdminRequest struct {
	Username      *string `json:"username"`
	Password      *string `json:"password"`
	Email         *string `json:"email"`
	EmailVerified *bool   `json:"email_verified"`
	Group         *string `json:"group"`
}

// UserResponse is the DTO for returning user details.
type UserResponse struct {
	ID            string `json:"id"`
	Username      string `json:"username"`
	Group         string `json:"group"`
	Email         string `json:"email,omitempty"`
	EmailVerified bool   `json:"email_verified"`
	CreatedAt     string `json:"created_at"`
	UpdatedAt     string `json:"updated_at"`
}

// UserLoginResponse is the DTO for the login response.
type UserLoginResponse struct {
	Token string       `json:"token"`
	User  UserResponse `json:"user"`
}

// AdminUpdateSelfRequest is the DTO for admin updating their own username and password.
type AdminUpdateSelfRequest struct {
	Username    *string `json:"username"`
	NewPassword *string `json:"new_password"`
}

// AdminCreateUserRequest is the DTO for admin creating a new user.
type AdminCreateUserRequest struct {
	Username      string  `json:"username" binding:"required"`
	Password      string  `json:"password" binding:"required"`
	Group         string  `json:"group" binding:"required"`
	Email         *string `json:"email"`
	EmailVerified *bool   `json:"email_verified"`
}
