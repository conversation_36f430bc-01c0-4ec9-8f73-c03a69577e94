package repository

import (
	"context"

	"github.com/Sakura-Byte/kikoeru-go/pkg/domain/model"
)

// WorkRepository defines the interface for work data access
type WorkRepository interface {
	// CRUD operations
	Create(ctx context.Context, work *model.Work) error
	GetByID(ctx context.Context, id uint) (*model.Work, error)
	Update(ctx context.Context, work *model.Work) error
	Delete(ctx context.Context, id uint) error

	// Query operations
	FindWorkByOriginalID(ctx context.Context, originalID string) (*model.Work, error)
	ListWorks(ctx context.Context, page, pageSize int, sortBy, sortOrder string, filters map[string]interface{}) ([]model.Work, int, error)
	SearchWorks(ctx context.Context, query string, page, pageSize int) ([]model.Work, int, error)
	GetRandomWorks(ctx context.Context, limit int, filters map[string]interface{}) ([]model.Work, error)

	// Metadata operations
	AddTagToWork(ctx context.Context, workID uint, tagID string) error
	RemoveTagFromWork(ctx context.Context, workID uint, tagID string) error
	ReplaceWorkTags(ctx context.Context, workID uint, tagIDs []string) error

	AddVAToWork(ctx context.Context, workID uint, vaID string) error
	RemoveVAFromWork(ctx context.Context, workID uint, vaID string) error
	ReplaceWorkVAs(ctx context.Context, workID uint, vaIDs []string) error

	// Statistics operations
	GetWorkCount(ctx context.Context) (int, error)
	GetTracksCount(ctx context.Context) (int, error)
}
