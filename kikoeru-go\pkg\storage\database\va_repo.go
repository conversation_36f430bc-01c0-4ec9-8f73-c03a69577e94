package database

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
	common_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/common" // Import common_dto
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"gorm.io/gorm"
)

// ListVAsParams defines parameters for listing VAs.
type ListVAsParams struct {
	common_dto.PaginationParams        // Embedded pagination and sorting
	Name                        string // Search term for name (uses LIKE)
}

// VARepository defines the repository interface for VAs.
type VARepository interface {
	Create(ctx context.Context, va *models.VA) error
	GetByID(ctx context.Context, id string) (*models.VA, error)
	GetByName(ctx context.Context, name string) (*models.VA, error)
	List(ctx context.Context, params ListVAsParams) ([]*models.VA, int64, error)
	Update(ctx context.Context, va *models.VA) error
	DeleteByID(ctx context.Context, vaID string) error
	GetOrCreateVA(ctx context.Context, name string) (*models.VA, error) // Added GetOrCreateVA
	ListAllWithWorkCount(ctx context.Context) ([]*models.VA, error)     // Added ListAllWithWorkCount
}

type vaRepository struct {
	db *gorm.DB
}

func NewVARepository(db *gorm.DB) VARepository {
	return &vaRepository{db: db}
}

func (r *vaRepository) Create(ctx context.Context, va *models.VA) error {
	if va == nil {
		return errors.New("va cannot be nil")
	}
	// ID is primary key and set by BeforeCreate hook. Name is unique.
	// Create will fail if name is not unique due to DB constraints.
	result := r.db.WithContext(ctx).Create(va)
	if result.Error != nil {
		// TODO: Check for specific DB errors like unique constraint violation
		// and return a more specific error like service.ErrVAAlreadyExists
		return fmt.Errorf("failed to create VA in DB: %w", result.Error)
	}
	return nil
}

func (r *vaRepository) GetByID(ctx context.Context, id string) (*models.VA, error) {
	var va models.VA
	if id == "" {
		return nil, errors.New("VA ID cannot be empty")
	}
	result := r.db.WithContext(ctx).Where("id = ?", id).First(&va)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, apperrors.ErrVANotFound
		}
		return nil, result.Error
	}
	return &va, nil
}

func (r *vaRepository) GetByName(ctx context.Context, name string) (*models.VA, error) {
	var va models.VA
	if name == "" {
		return nil, errors.New("VA name cannot be empty")
	}
	result := r.db.WithContext(ctx).Where("name = ?", name).First(&va)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, apperrors.ErrVANotFound
		}
		return nil, result.Error
	}
	return &va, nil
}

func (r *vaRepository) List(ctx context.Context, params ListVAsParams) ([]*models.VA, int64, error) {
	query := r.db.WithContext(ctx).Model(&models.VA{})

	if params.Name != "" {
		// Case-insensitive search for name
		query = query.Where("LOWER(name) LIKE LOWER(?)", "%"+params.Name+"%")
	}

	var totalCount int64
	if err := query.Count(&totalCount).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count VAs: %w", err)
	}

	// Apply pagination
	if params.Page > 0 && params.PageSize > 0 {
		offset := (params.Page - 1) * params.PageSize
		query = query.Offset(offset).Limit(params.PageSize)
	}

	// Apply sorting
	if params.SortBy == "" {
		params.SortBy = "name" // Default sort
	}
	order := params.SortBy
	if params.SortOrder != "" && (strings.ToLower(params.SortOrder) == "asc" || strings.ToLower(params.SortOrder) == "desc") {
		order += " " + strings.ToLower(params.SortOrder)
	} else {
		order += " asc" // Default to ascending
	}
	query = query.Order(order)

	var vas []*models.VA
	if err := query.Find(&vas).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list VAs: %w", err)
	}

	return vas, totalCount, nil
}

func (r *vaRepository) Update(ctx context.Context, va *models.VA) error {
	if va == nil || va.ID == "" { // Changed to check va.ID as primary key
		return errors.New("VA for update must not be nil and must have an ID (primary key)")
	}
	result := r.db.WithContext(ctx).Save(va)
	return result.Error
}

func (r *vaRepository) DeleteByID(ctx context.Context, vaID string) error {
	if vaID == "" {
		return errors.New("VA ID cannot be empty for deletion")
	}

	// GORM's Delete with a primary key value will only delete if the record exists.
	// We check RowsAffected to confirm deletion.
	result := r.db.WithContext(ctx).Where("id = ?", vaID).Delete(&models.VA{})
	if result.Error != nil {
		return fmt.Errorf("failed to delete VA %s: %w", vaID, result.Error)
	}
	if result.RowsAffected == 0 {
		return apperrors.ErrVANotFound
	}
	return nil
}

func (r *vaRepository) GetOrCreateVA(ctx context.Context, name string) (*models.VA, error) {
	if name == "" {
		return nil, errors.New("VA name cannot be empty for GetOrCreateVA")
	}
	var va models.VA
	// Try to find the VA first
	result := r.db.WithContext(ctx).Where("name = ?", name).First(&va)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			// VA not found, create it
			newVA := &models.VA{Name: name}
			// BeforeCreate hook in models.VA handles ID generation.
			if createErr := r.Create(ctx, newVA); createErr != nil {
				// Check for unique constraint violation in case of concurrent creation
				// TODO: More robust check for unique constraint violation error type
				if strings.Contains(createErr.Error(), "Duplicate entry") || strings.Contains(createErr.Error(), "UNIQUE constraint failed") {
					// If it's a unique constraint error, try to get the existing VA again
					return r.GetByName(ctx, name)
				}
				return nil, fmt.Errorf("failed to create new VA '%s': %w", name, createErr)
			}
			return newVA, nil
		}
		// Other database error
		return nil, fmt.Errorf("failed to get VA by name '%s': %w", name, result.Error)
	}
	// VA found
	return &va, nil
}

// ListAllWithWorkCount retrieves all VAs and their associated work count.
func (r *vaRepository) ListAllWithWorkCount(ctx context.Context) ([]*models.VA, error) {
	var vas []*models.VA
	// Join with r_work_vas and t_work to count associated works
	// Select VAs and count of works, grouping by VA ID
	// Note: This query might be slow for a very large number of VAs or works.
	// Consider pagination if needed in the future.

	type VAWithCount struct {
		models.VA
		WorkCount int64 `json:"work_count"`
	}

	var results []*VAWithCount

	result := r.db.WithContext(ctx).
		Model(&models.VA{}).
		Select("t_va.*, COUNT(r_work_vas.work_id) as work_count").
		Joins("LEFT JOIN r_work_vas ON t_va.id = r_work_vas.va_id").
		Group("t_va.id").
		Order("t_va.name asc").
		Scan(&results)

	if result.Error != nil {
		return nil, fmt.Errorf("failed to list VAs with work count: %w", result.Error)
	}

	// Convert to []*models.VA with WorkCount populated
	vas = make([]*models.VA, len(results))
	for i, r := range results {
		vas[i] = &models.VA{
			ID:        r.ID,
			Name:      r.Name,
			CreatedAt: r.CreatedAt,
			UpdatedAt: r.UpdatedAt,
			WorkCount: r.WorkCount,
		}
	}

	return vas, nil
}
