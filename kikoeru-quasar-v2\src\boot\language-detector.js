import { defineBoot } from '#q-app/wrappers'
import { LocalStorage } from 'quasar'

// Map our locale codes to Quasar language pack codes
const quasarLangMap = {
  'en-US': 'en-US',
  'zh-CN': 'zh-CN',
  'ja-JP': 'ja'
}

// Pre-import language packs to avoid Vite dynamic import issues
import enUS from 'quasar/lang/en-US'
import zhCN from 'quasar/lang/zh-CN'
import ja from 'quasar/lang/ja'

// Map of imported language packs
const langPacks = {
  'en-US': enUS,
  'zh-CN': zhCN,
  'ja': ja
}

export default defineBoot(async ({ app }) => {
  // Get stored locale from LocalStorage
  let locale = LocalStorage.getItem('locale')

  // If no locale is stored, try to detect from browser
  if (!locale) {
    const browserLocale = navigator.language

    // Check if we support the exact browser locale
    if (['en-US', 'zh-CN', 'ja-JP'].includes(browserLocale)) {
      locale = browserLocale
    } else {
      // Check the language prefix
      const lang = browserLocale.split('-')[0]
      if (lang === 'en') locale = 'en-US'
      else if (lang === 'zh') locale = 'zh-CN'
      else if (lang === 'ja') locale = 'ja-JP'
      else locale = 'en-US' // Default
    }

    // Store the detected locale
    LocalStorage.set('locale', locale)
  }

  // Set Quasar language pack
  try {
    const quasarLangCode = quasarLangMap[locale] || 'en-US'

    // Use pre-imported language packs
    if (langPacks[quasarLangCode]) {
      app.config.globalProperties.$q.lang.set(langPacks[quasarLangCode])
      console.log(`Successfully loaded language pack: ${quasarLangCode}`)
    } else {
      // Fallback to English
      app.config.globalProperties.$q.lang.set(langPacks['en-US'])
      console.log('Loaded fallback English language pack')
    }
  } catch (error) {
    console.error('Failed to set up language:', error)
    // Ensure we at least try to load English
    try {
      app.config.globalProperties.$q.lang.set(langPacks['en-US'])
    } catch (fallbackErr) {
      console.error('Failed to load fallback language pack:', fallbackErr)
    }
  }
})
