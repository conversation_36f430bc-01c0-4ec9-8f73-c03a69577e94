package ports

import (
	"context"

	metadata_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/metadata"
)

// SummaryService provides methods to get summarized lists of circles, tags, and VAs.
// This interface is a "driven" port, implemented by the service layer and used by handlers.
type SummaryService interface {
	ListAllCirclesWithWorkCount(ctx context.Context) ([]*metadata_dto.CircleWithWorkCount, int64, error)
	ListAllTagsWithWorkCount(ctx context.Context) ([]*metadata_dto.TagWithWorkCount, error)
	ListAllVAsWithWorkCount(ctx context.Context) ([]*metadata_dto.VAWithWorkCount, error)
}
