<template>
  <div class="custom-audio-player">
    <!-- HTML5 Audio Element -->
    <audio
      ref="audioElement"
      crossorigin="anonymous"
      preload="auto"
      v-bind:src="source || null"
      @loadstart="onLoadStart"
      @loadedmetadata="onLoadedMetadata"
      @durationchange="onDurationChange"
      @canplay="onCanplay"
      @timeupdate="onTimeupdate"
      @ended="onEnded"
      @seeked="onSeeked"
      @seeking="onSeeking"
      @play="onPlay"
      @pause="onPause"
      @waiting="onWaiting"
      @progress="onProgress"
      @error="onError"
    />

    <!-- Custom Progress Bar -->
    <div class="custom-progress-container" @click="onProgressClick" ref="progressContainer">
      <div class="progress-track">
        <div class="progress-buffered" :style="{ width: bufferedPercentage + '%' }"></div>
        <div class="progress-played" :style="{ width: progressPercentage + '%' }"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, getCurrentInstance, nextTick } from 'vue'
import { useQuasar } from 'quasar'
import Lyric from 'lrc-file-parser'
import { useAudioPlayer } from '../composables/useAudioPlayer'
import { useAuth } from '../composables/useAuth'
import { useWorkInfo } from '../composables/useWorkInfo'
import LyricDiscoveryService from 'src/services/lyricDiscoveryService'

// Component name for multi-word requirement
defineOptions({
  name: 'AudioElement'
})

const $q = useQuasar()
const { proxy } = getCurrentInstance()

const {
  playing,
  queue,
  queueIndex,
  playMode,
  muted,
  volume,
  sleepTime,
  sleepMode,
  rewindSeekTime,
  forwardSeekTime,
  rewindSeekMode,
  forwardSeekMode,
  seekToTime,
  pendingSeekTime,
  currentPlayingFile,
  setDuration,
  setCurrentTime,
  pause,
  play,
  setTrack,
  nextTrack,
  previousTrack,
  setCurrentLyric,
  setSleepMode,
  setRewindSeekMode,
  setForwardSeekMode,
  setPendingSeekTime,
  setAvailableLyrics,
  setCurrentLyricFile,
  setLyricContent
} = useAudioPlayer()

const { isLoggedIn } = useAuth()
const { getTreeData } = useWorkInfo()

// Reactive data
const audioElement = ref(null)
const lrcObj = ref(null)
const lrcAvailable = ref(false)
const lastRecordTime = ref(0)
const progressPercentage = ref(0)
const bufferedPercentage = ref(0)

// Helper for base64 URL safe encoding
const safeBase64Encode = (str) => {
  if (typeof str !== 'string' || !str) {
    console.error('Base64 encoding received invalid input:', str);
    return null; // Return null to trigger error handling
  }

  try {
    // First convert string to UTF-8 bytes via TextEncoder
    const encoder = new TextEncoder();
    const data = encoder.encode(str);

    // Convert bytes to binary string
    let binary = '';
    const bytes = new Uint8Array(data);
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }

    // Convert binary string to base64
    const base64 = btoa(binary);

    // Make base64 URL-safe (replace '+' with '-', '/' with '_')
    // but KEEP padding ('=') as Go's base64.URLEncoding expects it
    return base64.replace(/\+/g, '-').replace(/\//g, '_');
  } catch (error) {
    console.error('Base64 encoding error in AudioElement:', error, 'Input:', str);
    return null; // Return null to trigger error handling instead of using encodeURIComponent
  }
};

    // Computed properties
    const player = computed(() => audioElement.value)

    const source = computed(() => {
      const currentFile = currentPlayingFile.value;
      if (!currentFile || typeof currentFile !== 'object') {
        console.log('Audio source: currentFile is not an object or is null');
        return null;
      }

      if (currentFile.isInArchive) {
        if (
          currentFile.storageId != null &&
          currentFile.actualArchivePath &&
          currentFile.trackPath // This is now path *inside* the archive
        ) {
          const encodedArchivePath = safeBase64Encode(currentFile.actualArchivePath);
          const encodedFilePathInArchive = safeBase64Encode(currentFile.trackPath);

          if (encodedArchivePath === null || encodedFilePathInArchive === null) {
             console.error('Audio source: Base64 encoding failed for one or more archive paths',
                {
                  archivePath: currentFile.actualArchivePath,
                  filePathInArchive: currentFile.trackPath
                });
            return null; // Prevent URL construction with invalid encoded components
          }

          let url = `/api/v1/archive/proxy/${currentFile.storageId}/${encodedArchivePath}/${encodedFilePathInArchive}`;
          const queryParams = [];
          if (currentFile.archivePassword) {
            queryParams.push(`password=${encodeURIComponent(currentFile.archivePassword)}`);
          }
          if (currentFile.archiveEncoding) {
            queryParams.push(`encoding=${encodeURIComponent(currentFile.archiveEncoding)}`);
          }
          if (queryParams.length > 0) {
            url += `?${queryParams.join('&')}`;
          }
          console.log('Using archive proxy URL (constructed in AudioElement):', url);
          return url;
        }
        console.log('Audio source: missing required fields for archive URL construction', currentFile);
        return null;
      } else { // Not in archive: regular file stream
        if (!currentFile.workId) {
          console.log('Audio source: missing workId for non-archive file', currentFile);
          return null;
        }
        if (!currentFile.trackPath) {
          console.log('Audio source: missing trackPath for non-archive file', currentFile);
          return null;
        }
        // Validate workId is a number or a valid string ID
        if (typeof currentFile.workId !== 'number' && !/^[a-zA-Z0-9_-]+$/.test(String(currentFile.workId))) {
           console.log('Audio source: invalid workId format for non-archive file', currentFile.workId);
           return null;
        }

        // trackPath for regular files is relative and should not be empty
        if (currentFile.trackPath === '') {
            console.log('Audio source: empty trackPath for non-archive file', currentFile);
            return null;
        }

        const url = `/api/v1/media/stream/work/${currentFile.workId}/track/${currentFile.trackPath}`;
        console.log('Using regular media stream URL for playback:', url);
        return url;
      }
    })

    // Initialize lyric object
    const initLrcObj = () => {
      lrcObj.value = new Lyric({
        onPlay: (line, text) => {
          console.log('Lyric line changed:', { line, text })
          setCurrentLyric(text)
        },
      })
    }

    // Watchers
    watch(playing, (flag) => {
      if (!player.value) return

      if (flag && !player.value.paused) {
        console.log('Already playing, no action needed')
        return
      }

      if (!flag && player.value.paused) {
        console.log('Already paused, no action needed')
        return
      }

      if (flag) {
        console.log('Playing state changed to true, attempting to play')
        player.value.play().catch(error => {
          console.warn('Failed to play audio:', error)
          // If play fails, update state
          if (playing.value) {
            pause()
          }
        })
      } else {
        console.log('Playing state changed to false, pausing')
        player.value.pause()
      }
    })

    watch(source, (url) => {
      if (url) {
        // Reset player state
        if (player.value) {
          player.value.currentTime = 0
          player.value.load()
        }

        // Load subtitles
        loadSubtitles()

        // Record position if playing
        if (playing.value) {
          recordPlayPosition()
        }
      }
    })

    watch(muted, (flag) => {
      if (player.value) {
        player.value.muted = flag
      }
    })

    watch(volume, (val) => {
      if (val < 0 || val > 1) {
        return
      }

      if (player.value) {
        player.value.volume = val
      }
    })

    watch(rewindSeekMode, (rewind) => {
      if (rewind && player.value) {
        player.value.currentTime = Math.max(0, player.value.currentTime - rewindSeekTime.value)
        setRewindSeekMode(false)
      }
    })

    watch(forwardSeekMode, (forward) => {
      if (forward && player.value) {
        player.value.currentTime = Math.min(player.value.duration || 0, player.value.currentTime + forwardSeekTime.value)
        setForwardSeekMode(false)
      }
    })

    watch(currentPlayingFile, (newFile, oldFile) => {
      if (newFile?.trackPath !== oldFile?.trackPath) {
        // Update media session metadata and load lyrics
        nextTick(() => {
          updateMediaSessionMetadata()

          if (newFile?.trackPath) {
            console.log('Track changed, loading lyrics automatically')
            loadSubtitles()
          }
        })
      }
    })

    watch(seekToTime, (newSeekTime) => {
      if (newSeekTime !== null && player.value) {
        console.log('Seeking to time from lyric dialog:', newSeekTime)
        player.value.currentTime = Math.min(newSeekTime, player.value.duration || 0)

        // Update lyric position if available
        if (lrcAvailable.value && lrcObj.value) {
          lrcObj.value.play(newSeekTime * 1000)
        }
      }
    })

// Audio event handlers
const onPlay = () => {
  console.log('Audio: play event fired')
  playLrc(true)
  if (!playing.value) {
    console.log('Updating state to playing')
    play()
  }
}

const onPause = () => {
  console.log('Audio: pause event fired')
  playLrc(false)
  if (playing.value) {
    console.log('Updating state to paused')
    pause()
  }
  recordPlayPosition()
}

const onPlaying = () => { // eslint-disable-line no-unused-vars
  console.log('Audio: playing event fired (deprecated)')
  onPlay()
}

const onWaiting = () => {
  console.log('Audio: waiting event fired')
  playLrc(false)
  // Don't change state, this is just buffering
}

const onCanplay = () => {
  console.log('Audio: canplay event fired')
  console.log('Player state in canplay:', {
    duration: player.value.duration,
    readyState: player.value.readyState,
    networkState: player.value.networkState,
    currentTime: player.value.currentTime
  })

  if (player.value.duration && !isNaN(player.value.duration)) {
    setDuration(player.value.duration)
    console.log('Duration set in canplay to:', player.value.duration)
  } else {
    console.warn('Duration not available or invalid in canplay:', player.value.duration)
  }

  // Handle pending seek time from hash navigation
  if (pendingSeekTime.value !== null) {
    console.log('Audio is ready, applying pending seek to:', pendingSeekTime.value)
    const seekTime = pendingSeekTime.value
    setPendingSeekTime(null) // Clear the pending seek

    // Use a small delay to ensure audio is fully ready
    setTimeout(() => {
      if (player.value && player.value.duration) {
        const targetTime = Math.min(seekTime, player.value.duration)
        console.log('Seeking to time:', targetTime)
        player.value.currentTime = targetTime

        // Update lyric position if available
        if (lrcAvailable.value && lrcObj.value) {
          lrcObj.value.play(targetTime * 1000)
        }
      }
    }, 100)
  }

  // Auto-play
  if (player.value.currentTime !== player.value.duration) {
    console.log('Auto-playing after metadata loaded')
    player.value.play().then(() => {
      if (!playing.value) {
        console.log('Updating state to playing')
        play()
      }
    }).catch(error => {
      console.warn('Auto-play failed:', error)
    })
  }
}

const onTimeupdate = () => {
  setCurrentTime(player.value.currentTime)

  // Check duration validity
  if ((!player.value.duration || player.value.duration === 0 || !isFinite(player.value.duration)) && player.value.readyState >= 1) {
    console.warn('Duration still invalid during timeupdate, attempting to refresh:', {
      duration: player.value.duration,
      readyState: player.value.readyState,
      currentTime: player.value.currentTime
    })

    if (player.value.duration && isFinite(player.value.duration) && player.value.duration > 0) {
      setDuration(player.value.duration)
    }
  }

  // Update progress percentage
  if (player.value.duration > 0) {
    progressPercentage.value = (player.value.currentTime / player.value.duration) * 100
    if (Math.floor(player.value.currentTime) % 5 === 0 && Math.floor(player.value.currentTime * 10) % 10 === 0) {
      console.log('Progress update:', {
        currentTime: player.value.currentTime,
        duration: player.value.duration,
        progressPercentage: progressPercentage.value.toFixed(1) + '%'
      })
    }
  } else {
    progressPercentage.value = 0
    if (Math.floor(player.value.currentTime) % 5 === 0) {
      console.warn('Cannot calculate progress - duration is:', player.value.duration)
    }
  }

  // Update buffered progress
  if (player.value.buffered.length > 0 && player.value.duration > 0) {
    const bufferedEnd = player.value.buffered.end(player.value.buffered.length - 1)
    bufferedPercentage.value = (bufferedEnd / player.value.duration) * 100
  }

  // Record play position
  recordPlayPosition()

  // Update lyrics sync
  if (playing.value && lrcAvailable.value && lrcObj.value) {
    lrcObj.value.play(player.value.currentTime * 1000)
  }

  // Sleep mode check
  if (sleepMode.value && sleepTime.value) {
    const currentTime = new Date()
    const currentHourStr = currentTime.getHours().toString().padStart(2, '0')
    const currentMinuteStr = currentTime.getMinutes().toString().padStart(2, '0')
    const sleepHourStr = sleepTime.value.match(/\d+/g)[0]
    const sleepMinuteStr = sleepTime.value.match(/\d+/g)[1]
    if (currentHourStr === sleepHourStr && currentMinuteStr === sleepMinuteStr) {
      pause()
      setSleepMode(false)
      $q.sessionStorage.set('sleepTime', null)
      $q.sessionStorage.set('sleepMode', false)
    }
  }

  // Update MediaSession position state
  if (player.value && 'mediaSession' in navigator && 'setPositionState' in navigator.mediaSession) {
    try {
      navigator.mediaSession.setPositionState({
        duration: player.value.duration || 0,
        playbackRate: player.value.playbackRate || 1,
        position: player.value.currentTime || 0
      })
    } catch {
      // Ignore errors
    }
  }
}

const onEnded = () => {
  // Track ended
  recordPlayPosition(true) // Mark as completed

  switch (playMode.value.name) {
    case "all repeat":
      // Loop all
      if (queueIndex.value === queue.value.length - 1) {
        setTrack(0)
      } else {
        nextTrack()
      }
      break
    case "repeat once":
      // Repeat single
      player.value.currentTime = 0
      player.value.play()
      play()
      break
    case "shuffle": {
      // Random
      const index = Math.floor(Math.random() * queue.value.length)
      setTrack(index)
      if (index === queueIndex.value) {
        player.value.currentTime = 0
      }
      break
    }
    default:
      // Sequential
      if (queueIndex.value === queue.value.length - 1) {
        pause()
      } else {
        nextTrack()
      }
  }
}

// Additional event handlers (simplified for now)
const onSeeked = () => {
  recordPlayPosition()
  if (lrcAvailable.value && lrcObj.value) {
    if (playing.value) {
      lrcObj.value.play(player.value.currentTime * 1000)
    } else {
      lrcObj.value.play(player.value.currentTime * 1000)
      lrcObj.value.pause()
    }
  }
}

const onSeeking = () => {
  if (player.value && player.value.currentTime !== undefined) {
    playLrc(false)
    console.log('Seeking to:', player.value.currentTime)
  }
}

const onProgress = () => {
  console.log('Audio: progress event fired, buffered ranges:', player.value.buffered.length)
}

const onError = () => {
  console.error('Audio error occurred')
  const errorDetails = player.value ? player.value.error : 'No error details available'
  console.error('Audio error details:', errorDetails)

  // If error is due to invalid src, clear it
  if (player.value && (!source.value || source.value === '') && player.value.error?.code === 4) {
    console.log('Error due to invalid src, clearing it')
    player.value.removeAttribute('src')
    player.value.load()
  }
}

const onLoadStart = () => {
  console.log('Audio: loadstart event fired')
  console.log('Audio element src attribute:', player.value ? player.value.getAttribute('src') : 'no player')
  console.log('Audio element current src:', player.value ? player.value.currentSrc : 'no player')
  console.log('Source computed value:', source.value)

  // Safety check - if src is incorrect, clear it
  if (player.value && player.value.src && !source.value) {
    console.log('Detected invalid src, clearing it')
    player.value.removeAttribute('src')
    // Abort any pending loads
    player.value.load()
  }
}

const onLoadedMetadata = () => {
  console.log('Audio: loadedmetadata event fired')
  console.log('Player state:', {
    duration: player.value.duration,
    readyState: player.value.readyState,
    networkState: player.value.networkState,
    src: player.value.src
  })

  // Set duration when metadata is loaded
  if (player.value.duration && !isNaN(player.value.duration)) {
    setDuration(player.value.duration)
    console.log('Duration set to:', player.value.duration)
  } else {
    console.warn('Duration not available yet:', player.value.duration)
  }
}

const onDurationChange = () => {
  console.log('Audio: durationchange event fired')
  console.log('New duration:', player.value.duration)

  if (player.value.duration && !isNaN(player.value.duration) && isFinite(player.value.duration)) {
    setDuration(player.value.duration)
    console.log('Duration updated via durationchange to:', player.value.duration)
  } else {
    console.warn('Invalid duration in durationchange:', player.value.duration)
  }
}

const playLrc = (playStatus) => {
  if (lrcAvailable.value) {
    if (playStatus) {
      lrcObj.value.play(player.value.currentTime * 1000)
    } else {
      lrcObj.value.pause()
    }
  }
}

const recordPlayPosition = async (isFinished = false) => {
  if (!isLoggedIn.value) return

  const currentFile = currentPlayingFile.value
  if (!currentFile.workId || !player.value.duration) return

  const now = Date.now()
  if (!lastRecordTime.value || now - lastRecordTime.value > 10000 || isFinished) {
    lastRecordTime.value = now
    try {
      await proxy.$api.post('/api/v1/me/history', {
        work_id: currentFile.workId,
        track_path: currentFile.trackPath || '',
        playback_position_seconds: Math.floor(player.value.currentTime),
        progress_percentage: player.value.currentTime / player.value.duration,
        is_finished: isFinished
      })
    } catch (error) {
      if (error.response?.status !== 401) {
        console.warn('Failed to record play position:', error)
      }
    }
  }
}

// Load subtitles for current track
const loadSubtitles = async () => {
  const currentFile = currentPlayingFile.value
  if (!currentFile.originalId || !currentFile.trackPath) {
    lrcAvailable.value = false
    setCurrentLyric('')
    return
  }

  try {
    console.log('Loading subtitles for:', currentFile.trackPath)

    // Get tree data from store for local lyric discovery
    const treeData = getTreeData(currentFile.originalId)?.treeData || null

    // Use LyricDiscoveryService to find all available lyrics
    const availableLyrics = await LyricDiscoveryService.discoverLyrics(currentFile, treeData)

    // Store available lyrics in store for LyricDialog to reuse
    setAvailableLyrics(availableLyrics)

    if (availableLyrics.length > 0) {
      // Use the first available lyric (highest priority)
      const selectedLyric = availableLyrics[0]
      console.log('Auto-selected lyric:', selectedLyric.displayName)

      // Store current lyric file in store
      setCurrentLyricFile(selectedLyric)

      // Get storage info for local files
      let storageId = null
      let workPathInStorage = ''

      if (treeData && selectedLyric.type === 'local') {
        const cachedData = getTreeData(currentFile.originalId)
        storageId = cachedData?.storageId
        workPathInStorage = cachedData?.pathInStorage || ''
      }

      // Load lyric content
      const content = await LyricDiscoveryService.loadLyricContent(
        selectedLyric,
        storageId,
        workPathInStorage
      )

      // Store lyric content in store
      setLyricContent(content)

      lrcAvailable.value = true
      lrcObj.value.setLyric(content)

      // Start playing if audio is currently playing
      if (playing.value) {
        lrcObj.value.play(player.value.currentTime * 1000)
      }

      console.log('Lyrics loaded successfully')
    } else {
      // No lyrics available
      console.log('No lyrics found for this track')
      lrcAvailable.value = false
      lrcObj.value.setLyric('')
      setCurrentLyric('')
    }
  } catch (error) {
    console.warn('Failed to load lyrics:', error)
    lrcAvailable.value = false
    lrcObj.value.setLyric('')
    setCurrentLyric('')
  }
}

// Update media session metadata
const updateMediaSessionMetadata = () => {
  if (!('mediaSession' in navigator)) return

  const currentFile = currentPlayingFile.value
  if (!currentFile || !currentFile.trackPath) return

  // Create artwork array with different sizes
  const artworkSrc = currentFile.originalId ?
    `/api/v1/cover/${currentFile.originalId}?type=sam` : null

  const artwork = artworkSrc ? [
    { src: artworkSrc, sizes: '512x512', type: 'image/jpeg' }
  ] : []

  try {
    navigator.mediaSession.metadata = new MediaMetadata({
      title: currentFile.title || 'Unknown Title',
      artist: '', // No artist info available
      album: currentFile.workTitle || 'Unknown Work',
      artwork: artwork
    })
  } catch (error) {
    console.warn('Failed to update media session metadata:', error)
  }
}

// Progress bar click handler
const onProgressClick = (event) => {
  const container = progressContainer.value
  if (!container) return

  const rect = container.getBoundingClientRect()
  const clickX = event.clientX - rect.left
  const clickPercentage = (clickX / rect.width) * 100

  if (player.value && player.value.duration > 0) {
    const newTime = (clickPercentage / 100) * player.value.duration
    console.log('Progress bar clicked, seeking to:', newTime, 'seconds')

    // Set new playback position
    player.value.currentTime = newTime

    // Manually trigger seeking state update
    onSeeking()
  }
}

const progressContainer = ref(null)

const setupMediaSession = () => {
  if ('mediaSession' in navigator) {
    navigator.mediaSession.setActionHandler('play', () => {
      console.log('MediaSession: play action triggered')
      play()
    })
    navigator.mediaSession.setActionHandler('pause', () => {
      console.log('MediaSession: pause action triggered')
      pause()
    })
    navigator.mediaSession.setActionHandler('previoustrack', () => {
      console.log('MediaSession: previous track action triggered')
      previousTrack()
    })
    navigator.mediaSession.setActionHandler('nexttrack', () => {
      console.log('MediaSession: next track action triggered')
      nextTrack()
    })

    // Setup seekbackward/seekforward handlers if possible
    try {
      navigator.mediaSession.setActionHandler('seekbackward', (details) => {
        console.log('MediaSession: seekbackward action triggered')
        const skipTime = details.seekOffset || rewindSeekTime.value
        player.value.currentTime = Math.max(0, player.value.currentTime - skipTime)
      })

      navigator.mediaSession.setActionHandler('seekforward', (details) => {
        console.log('MediaSession: seekforward action triggered')
        const skipTime = details.seekOffset || forwardSeekTime.value
        player.value.currentTime = Math.min(player.value.duration, player.value.currentTime + skipTime)
      })

      // Only supported in Chrome 81+
      if ('seekto' in navigator.mediaSession) {
        navigator.mediaSession.setActionHandler('seekto', (details) => {
          console.log('MediaSession: seekto action triggered')
          if (details.fastSeek && 'fastSeek' in player.value) {
            player.value.fastSeek(details.seekTime)
            return
          }
          player.value.currentTime = details.seekTime
        })
      }
    } catch (error) {
      console.warn('MediaSession: Some action handlers not supported', error)
    }

    // Update metadata immediately if we have a current file
    updateMediaSessionMetadata()
  }
}

// Handle seek to time from store (used by lyric dialog)
const handleSeekToTime = (time) => {
  if (player.value && typeof time === 'number' && time >= 0) {
    console.log('Seeking to time from lyric dialog:', time)
    player.value.currentTime = Math.min(time, player.value.duration || 0)

    // Update lyric position if available
    if (lrcAvailable.value && lrcObj.value) {
      lrcObj.value.play(time * 1000)
    }
  }
}

// Watch for seek events from lyric dialog via store
watch(seekToTime, (newSeekTime) => {
  if (newSeekTime !== null) {
    handleSeekToTime(newSeekTime)
  }
})

// Lifecycle
onMounted(() => {
  setupMediaSession()

  // Initialize volume from store
  if (player.value) {
    if (volume.value !== undefined) {
      player.value.volume = volume.value
    }
    if (muted.value !== undefined) {
      player.value.muted = muted.value
    }
  }

  // Wait for next tick to ensure player is fully initialized
  nextTick(() => {
    // Initialize volume only if player and its volume property are available
    if (player.value && typeof player.value.volume !== 'undefined') {
      // Volume is already set above, no need to call setVolume again
    } else {
      // Fallback to default volume if player is not ready
      if (player.value) {
        player.value.volume = 0.8
      }
    }

    initLrcObj()
    // Only load subtitles if there's a valid source
    if (source.value) {
      loadSubtitles()
    } else if (player.value) {
      // Ensure no src is set initially if source is invalid
      player.value.removeAttribute('src')
    }
  })
})
</script>

<!-- Styles moved to /src/css/components/AudioElement.scss -->
