/* List.scss - List page styles */

.list-page {
  .ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: calc(100% - 50px);
  }
  
  .list-item {
    border-bottom: 1px solid;
    
    .q-dark &, &.bg-dark {
      border-bottom-color: rgba(255, 255, 255, 0.1);
    }
    
    body:not(.body--dark) & {
      border-bottom-color: rgba(0, 0, 0, 0.1);
    }
  }
} 