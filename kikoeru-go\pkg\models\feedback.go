package models

import (
	"database/sql"

	feedback_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/feedback"
	"gorm.io/gorm"
)

const (
	FeedbackTypeBug            = "bug_report"
	FeedbackTypeFeatureRequest = "feature_request"
	FeedbackTypeGeneralComment = "general_comment"
	FeedbackTypeQuestion       = "question"
	FeedbackTypeOther          = "other"
)

const (
	FeedbackStatusNew        = "new"
	FeedbackStatusSeen       = "seen"
	FeedbackStatusInProgress = "in_progress"
	FeedbackStatusResolved   = "resolved"
	FeedbackStatusWontFix    = "wont_fix"
	FeedbackStatusClosed     = "closed" // Generic closed status if not resolved/wont_fix
)

// Feedback 对应数据库中的 t_feedback 表
type Feedback struct {
	gorm.Model // Provides ID, CreatedAt, UpdatedAt, DeletedAt
	// ID        uint           `gorm:"primaryKey;autoIncrement" json:"id"` // ID is provided by gorm.Model
	UserID    sql.NullString `gorm:"type:varchar(36);index;column:user_id" json:"user_id,omitempty"` // Changed from Username
	User      *User          `gorm:"foreignKey:UserID;references:ID" json:"user,omitempty"`          // Changed FK and references
	Email     sql.NullString `gorm:"type:varchar(255)" json:"email,omitempty"`
	Content   string         `gorm:"type:text;not null" json:"content"`
	Category  string         `gorm:"type:varchar(50);not null;default:'general_comment';index;column:category" json:"category"` // Changed from Type
	Status    string         `gorm:"type:varchar(50);not null;default:'new';index" json:"status"`
	UserAgent sql.NullString `gorm:"type:varchar(512)" json:"user_agent,omitempty"`
	Referrer  sql.NullString `gorm:"type:varchar(512)" json:"referrer,omitempty"`
	// AdminNotes or ResolutionNotes
	ResolutionNotes sql.NullString `gorm:"type:text" json:"resolution_notes,omitempty"`    // 管理员填写的处理意见
	ResolvedBy      sql.NullString `gorm:"type:varchar(100)" json:"resolved_by,omitempty"` // 处理该反馈的管理员用户名
	ResolvedAt      sql.NullTime   `json:"resolved_at,omitempty"`

	// CreatedAt and UpdatedAt are provided by gorm.Model
}

// TableName 指定 GORM 使用的表名
func (Feedback) TableName() string {
	return "t_feedback"
}

// ToFeedbackResponse converts a Feedback model to a feedback_dto.FeedbackResponse DTO.
func (f *Feedback) ToFeedbackResponse() *feedback_dto.FeedbackResponse {
	if f == nil {
		return nil
	}
	resp := &feedback_dto.FeedbackResponse{
		ID:        f.ID, // From gorm.Model
		Category:  f.Category,
		Content:   f.Content,
		Status:    f.Status,
		CreatedAt: f.CreatedAt, // From gorm.Model
		UpdatedAt: f.UpdatedAt, // From gorm.Model
	}

	if f.User != nil {
		// Assuming f.User is already populated and User.ToUserResponse() returns user_dto.UserResponse (not a pointer)
		userResp := f.User.ToUserResponse()
		resp.User = &userResp
	}

	if f.ResolutionNotes.Valid {
		resp.ResolutionNotes = &f.ResolutionNotes.String
	}
	if f.ResolvedBy.Valid {
		resp.ResolvedBy = &f.ResolvedBy.String
	}
	if f.ResolvedAt.Valid {
		resolvedAtTime := f.ResolvedAt.Time // Assign to a new variable to take its address
		resp.ResolvedAt = &resolvedAtTime
	}

	return resp
}
