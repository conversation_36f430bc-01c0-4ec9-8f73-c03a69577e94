package log

import (
	"context"
	"fmt"
	"io"
	"log/slog"
	"os"
	"runtime"
	"sort"
	"strings"

	"github.com/mattn/go-isatty"
)

// PrettyTextHandler 美观文本日志 handler
// 字段顺序: level, time, message, source, 其他
// 长字段（如 error, request）换行缩进

type PrettyTextHandler struct {
	out  io.Writer
	opts *slog.HandlerOptions
}

func NewPrettyTextHandler(w io.Writer, opts *slog.HandlerOptions) slog.Handler {
	if opts == nil {
		opts = &slog.HandlerOptions{}
	}
	return &PrettyTextHandler{out: w, opts: opts}
}

func (h *PrettyTextHandler) Enabled(ctx context.Context, level slog.Level) bool {
	minLevel := slog.LevelInfo
	if h.opts != nil {
		minLevel = h.opts.Level.Level()
	}
	return level >= minLevel
}

// 颜色代码
var (
	colorReset     = "\033[0m"
	colorRed       = "\033[31m"
	colorYellow    = "\033[33m"
	colorBlue      = "\033[34m"
	colorGreen     = "\033[32m"
	colorCyan      = "\033[36m"
	colorMagenta   = "\033[35m"
	colorGray      = "\033[90m"
	colorBold      = "\033[1m"
	colorItalic    = "\033[3m"
	colorUnderline = "\033[4m"

	// 亮色版本
	colorBrightRed     = "\033[91m"
	colorBrightGreen   = "\033[92m"
	colorBrightYellow  = "\033[93m"
	colorBrightBlue    = "\033[94m"
	colorBrightMagenta = "\033[95m"
	colorBrightCyan    = "\033[96m"
	colorBrightWhite   = "\033[97m"

	// 背景色
	bgBlack   = "\033[40m"
	bgRed     = "\033[41m"
	bgGreen   = "\033[42m"
	bgYellow  = "\033[43m"
	bgBlue    = "\033[44m"
	bgMagenta = "\033[45m"
	bgCyan    = "\033[46m"
)

// emoji
var levelEmoji = map[slog.Level]string{
	slog.LevelDebug: "🐛",
	slog.LevelInfo:  "ℹ️",
	slog.LevelWarn:  "⚠️",
	slog.LevelError: "❌",
}

// 字段图标
var fieldIcons = map[string]string{
	"error":    "🔴",
	"err":      "🔴",
	"request":  "🔍",
	"user":     "👤",
	"path":     "📁",
	"file":     "📄",
	"time":     "⏱️",
	"duration": "⏱️",
	"status":   "📊",
	"method":   "📡",
	"url":      "🔗",
	"ip":       "🌐",
	"id":       "🔑",
	"caller":   "📍",
	"message":  "💬",
}

// 获取字段图标
func getFieldIcon(key string) string {
	if icon, ok := fieldIcons[key]; ok {
		return icon + " "
	}

	// 尝试匹配部分键名
	for k, icon := range fieldIcons {
		if strings.Contains(strings.ToLower(key), k) {
			return icon + " "
		}
	}

	return "🔹 " // 默认图标
}

// level颜色
func levelColor(level slog.Level) string {
	switch level {
	case slog.LevelDebug:
		return colorBlue
	case slog.LevelInfo:
		return colorGreen
	case slog.LevelWarn:
		return colorYellow
	case slog.LevelError:
		return colorRed
	default:
		return colorReset
	}
}

// 判断是否是终端
func isTerminal() bool {
	if isatty.IsTerminal(os.Stdout.Fd()) {
		return true
	} else if isatty.IsCygwinTerminal(os.Stdout.Fd()) {
		return true
	}
	return false
}

func (h *PrettyTextHandler) Handle(ctx context.Context, r slog.Record) error {
	var b strings.Builder
	useColor := isTerminal()

	// --- Separator ---
	if useColor {
		b.WriteString(colorGray)
		b.WriteString(strings.Repeat("─", 100))
		b.WriteString(colorReset)
	} else {
		b.WriteString(strings.Repeat("-", 100))
	}
	b.WriteString("\n")

	// ===== 级别和时间部分 =====
	// Level with emoji and color
	emoji := levelEmoji[r.Level]
	levelColor := levelColor(r.Level)

	if useColor {
		b.WriteString(levelColor)
		if r.Level == slog.LevelError {
			b.WriteString(bgRed + colorBrightWhite)
		}
		b.WriteString(" ")
		b.WriteString(emoji)
		b.WriteString(" ")
		b.WriteString(strings.ToUpper(r.Level.String()))
		b.WriteString(" ")
		b.WriteString(colorReset)
	} else {
		b.WriteString(" ")
		b.WriteString(emoji)
		b.WriteString(" ")
		b.WriteString(strings.ToUpper(r.Level.String()))
		b.WriteString(" ")
	}

	// 时间戳
	if useColor {
		b.WriteString(colorCyan)
	}
	b.WriteString("[")
	b.WriteString(r.Time.Format("2006-01-02 15:04:05.000"))
	b.WriteString("]")
	if useColor {
		b.WriteString(colorReset)
	}

	b.WriteString(" ")

	// ===== 消息部分 =====
	if useColor {
		b.WriteString(colorBold)
		// 根据日志级别为消息着色
		switch r.Level {
		case slog.LevelError:
			b.WriteString(colorBrightRed)
		case slog.LevelWarn:
			b.WriteString(colorBrightYellow)
		case slog.LevelInfo:
			b.WriteString(colorBrightWhite)
		case slog.LevelDebug:
			b.WriteString(colorBrightGreen)
		}
	}
	b.WriteString(r.Message)
	if useColor {
		b.WriteString(colorReset)
	}
	b.WriteString("\n")

	// ===== 来源信息 =====
	if h.opts != nil && h.opts.AddSource && r.PC != 0 {
		frame, _ := runtime.CallersFrames([]uintptr{r.PC}).Next()
		b.WriteString("  ") // Indent
		if useColor {
			b.WriteString(colorGray)
			b.WriteString(getFieldIcon("caller"))
			b.WriteString("caller: ")
			b.WriteString(colorBrightBlue)
			b.WriteString(frame.File)
			b.WriteString(colorGray)
			b.WriteString(":")
			b.WriteString(colorYellow)
			b.WriteString(fmt.Sprintf("%d", frame.Line))
			b.WriteString(colorReset)
		} else {
			b.WriteString("  ")
			b.WriteString(getFieldIcon("caller"))
			b.WriteString(fmt.Sprintf("caller: %s:%d", frame.File, frame.Line))
		}
		b.WriteString("\n")
	}

	// ===== 属性字段 =====
	// 收集所有属性
	attrs := make(map[string]any)
	r.Attrs(func(a slog.Attr) bool {
		attrs[a.Key] = a.Value.Any()
		return true
	})

	if len(attrs) > 0 {
		b.WriteString("\n") // Add a newline before attributes for spacing
	}

	// 确保字段显示顺序固定 - 先显示错误和请求，再按字母排序其他字段
	var priorityKeys = []string{"error", "err", "request", "user", "method", "status"}
	var keys []string

	// 先处理优先字段
	for _, key := range priorityKeys {
		if _, exists := attrs[key]; exists {
			keys = append(keys, key)
			delete(attrs, key) // 避免重复
		}
	}

	// 再处理其他字段（按字母排序）
	var remainingKeys []string
	for k := range attrs {
		remainingKeys = append(remainingKeys, k)
	}
	sort.Strings(remainingKeys)
	keys = append(keys, remainingKeys...)

	if len(keys) > 0 {
		if useColor {
			b.WriteString(colorGray)
		}
		b.WriteString("  " + strings.Repeat("┈", 60) + "\n")
		if useColor {
			b.WriteString(colorReset)
		}
	}

	// 显示字段
	for _, key := range keys {
		val := attrs[key]
		icon := getFieldIcon(key)

		str := fmt.Sprintf("%v", val)
		isLongValue := len(str) > 60 || strings.Contains(str, "\n")
		isErrorField := key == "error" || key == "err"

		b.WriteString("    ") // Indent attributes

		// 字段名
		if useColor {
			if isErrorField {
				b.WriteString(colorBrightRed)
			} else {
				b.WriteString(colorBrightCyan)
			}
			b.WriteString(icon)
			b.WriteString(key)
			b.WriteString(colorReset)
			b.WriteString(": ")
		} else {
			b.WriteString(icon)
			b.WriteString(key)
			b.WriteString(": ")
		}

		// 字段值
		if isLongValue {
			// 长字段值换行显示
			b.WriteString("\n")
			indentedValue := indentMultiline(str, 6)
			if useColor && isErrorField {
				b.WriteString(colorRed)
				b.WriteString(indentedValue)
				b.WriteString(colorReset)
			} else if useColor {
				b.WriteString(colorGray)
				b.WriteString(indentedValue)
				b.WriteString(colorReset)
			} else {
				b.WriteString(indentedValue)
			}
		} else {
			// 短字段值直接显示
			if useColor && isErrorField {
				b.WriteString(colorRed)
				b.WriteString(str)
				b.WriteString(colorReset)
			} else {
				b.WriteString(str)
			}
		}
		b.WriteString("\n")
	}

	// Add a final blank line for spacing.
	b.WriteString("\n")
	_, err := h.out.Write([]byte(b.String()))
	return err
}

func (h *PrettyTextHandler) WithAttrs(attrs []slog.Attr) slog.Handler {
	// 不做特殊处理，直接返回自身
	return h
}

func (h *PrettyTextHandler) WithGroup(name string) slog.Handler {
	// 不做分组
	return h
}

// indentMultiline 缩进多行字符串
func indentMultiline(s string, spaces int) string {
	pad := strings.Repeat(" ", spaces)
	lines := strings.Split(s, "\n")
	for i, l := range lines {
		lines[i] = pad + l
	}
	return strings.Join(lines, "\n")
}
