/*
 * This file (which will be your service worker)
 * is picked up by the build system ONLY if
 * quasar.config.js > pwa > workboxMode is set to "injectManifest"
 */

import { clientsClaim } from 'workbox-core'
import { precacheAndRoute, cleanupOutdatedCaches, createHandlerBoundToURL } from 'workbox-precaching'
import { registerRoute, NavigationRoute } from 'workbox-routing'
import { StaleWhileRevalidate, CacheFirst } from 'workbox-strategies'
import { ExpirationPlugin } from 'workbox-expiration'
import { CacheableResponsePlugin } from 'workbox-cacheable-response'

// Use with precache injection
self.skipWaiting()
clientsClaim()

// Clean old assets
cleanupOutdatedCaches()

// Precache all webpack-generated assets
// Make sure the manifest is available before calling precacheAndRoute
const manifest = self.__WB_MANIFEST || []
precacheAndRoute(manifest)

// Cache api calls
registerRoute(
  /\/api\//,
  new StaleWhileRevalidate({
    cacheName: 'api-cache',
    plugins: [
      new CacheableResponsePlugin({
        statuses: [0, 200]
      }),
      new ExpirationPlugin({
        maxEntries: 100,
        maxAgeSeconds: 60 * 60 * 24 // 1 day
      })
    ]
  })
)

// Cache audio files with a cache-first strategy
registerRoute(
  /\.(mp3|wav|ogg)$/,
  new CacheFirst({
    cacheName: 'audio-cache',
    plugins: [
      new CacheableResponsePlugin({
        statuses: [0, 200]
      }),
      new ExpirationPlugin({
        maxEntries: 50,
        maxAgeSeconds: 60 * 60 * 24 * 30 // 30 days
      })
    ]
  })
)

// Cache images with a cache-first strategy
registerRoute(
  /\.(png|jpg|jpeg|svg|gif)$/,
  new CacheFirst({
    cacheName: 'image-cache',
    plugins: [
      new CacheableResponsePlugin({
        statuses: [0, 200]
      }),
      new ExpirationPlugin({
        maxEntries: 60,
        maxAgeSeconds: 60 * 60 * 24 * 30 // 30 days
      })
    ]
  })
)

// Default SPA fallback
const defaultNavigationHandler = createHandlerBoundToURL('index.html')
const navigationRoute = new NavigationRoute(defaultNavigationHandler, {
  denylist: [
    /\/api\//
  ]
})
registerRoute(navigationRoute)
