#!/usr/bin/env node

/**
 * CSS Extraction Script
 *
 * This script helps extract CSS from Vue components and move it to dedicated SCSS files.
 * Usage: node extract-css.js <component-name>
 *
 * Example: node extract-css.js WorkCard
 */

const fs = require('fs');
const path = require('path');

// Configuration
const SRC_DIR = path.join(__dirname, '..', 'src');
const CSS_DIR = path.join(SRC_DIR, 'css', 'components');
const COMPONENT_DIRS = [
  path.join(SRC_DIR, 'components'),
  path.join(SRC_DIR, 'pages'),
  path.join(SRC_DIR, 'layouts')
];

// Ensure CSS components directory exists
if (!fs.existsSync(CSS_DIR)) {
  fs.mkdirSync(CSS_DIR, { recursive: true });
}

// Get component name from command line
const componentName = process.argv[2];
if (!componentName) {
  console.error('Please provide a component name');
  console.log('Usage: node extract-css.js <component-name>');
  process.exit(1);
}

// Find the component file
let componentFile = null;
for (const dir of COMPONENT_DIRS) {
  const filePath = path.join(dir, `${componentName}.vue`);
  if (fs.existsSync(filePath)) {
    componentFile = filePath;
    break;
  }
}

if (!componentFile) {
  console.error(`Component ${componentName}.vue not found`);
  process.exit(1);
}

// Read the component file
const componentContent = fs.readFileSync(componentFile, 'utf8');

// Extract CSS
const styleRegex = /<style.*?>([\s\S]*?)<\/style>/;
const styleMatch = componentContent.match(styleRegex);

if (!styleMatch) {
  console.error(`No <style> tag found in ${componentName}.vue`);
  process.exit(1);
}

// Extract style content
const styleContent = styleMatch[1].trim();

// Create SCSS file
const scssFileName = `${componentName}.scss`;
const scssFilePath = path.join(CSS_DIR, scssFileName);

// Add a comment with the component name
const scssContent = `/* ${scssFileName} - Styles for ${componentName} component */\n\n${styleContent}`;

// Write the SCSS file
fs.writeFileSync(scssFilePath, scssContent);
console.log(`Created ${scssFilePath}`);

// Update app.scss to import the new file
const appScssPath = path.join(SRC_DIR, 'css', 'app.scss');
const appScssContent = fs.readFileSync(appScssPath, 'utf8');

if (!appScssContent.includes(`@import './components/${scssFileName}'`)) {
  const importLine = `@import './components/${scssFileName}';`;

  // Find the last import line
  const importRegex = /@import.*?;/g;
  const imports = [...appScssContent.matchAll(importRegex)];

  if (imports.length > 0) {
    const lastImport = imports[imports.length - 1];
    const lastImportEndIndex = lastImport.index + lastImport[0].length;

    // Insert after the last import
    const newContent =
      appScssContent.substring(0, lastImportEndIndex) +
      '\n' + importLine +
      appScssContent.substring(lastImportEndIndex);

    fs.writeFileSync(appScssPath, newContent);
    console.log(`Updated ${appScssPath} with import for ${scssFileName}`);
  } else {
    // No imports found, add at the beginning
    const newContent = importLine + '\n\n' + appScssContent;
    fs.writeFileSync(appScssPath, newContent);
    console.log(`Updated ${appScssPath} with import for ${scssFileName}`);
  }
}

// Replace style tag in component with comment
const updatedComponentContent = componentContent.replace(
  styleRegex,
  `<!-- Styles moved to /src/css/components/${scssFileName} -->`
);

fs.writeFileSync(componentFile, updatedComponentContent);
console.log(`Updated ${componentFile} to use external styles`);

console.log('\nDone! CSS extraction completed successfully.');
console.log(`\nNext steps:
1. Check the generated ${scssFileName} file and make any necessary adjustments
2. Make sure the component has the appropriate class names to match the CSS selectors
3. Test the component to ensure styles are applied correctly`);
