package playhistory_dto

import (
	"time"

	work_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/work"
)

// RecordPlayPositionRequest defines the DTO for recording play position.
type RecordPlayPositionRequest struct {
	WorkID                  uint    `json:"work_id" binding:"required"`
	TrackPath               string  `json:"track_path"`
	PlaybackPositionSeconds int     `json:"playback_position_seconds" binding:"gte=0"`
	ProgressPercentage      float64 `json:"progress_percentage" binding:"gte=0,lte=1"`
	IsFinished              bool    `json:"is_finished"`
}

// PlayHistoryResponseDTO is the DTO for PlayHistory responses.
type PlayHistoryResponseDTO struct {
	ID                      uint              `json:"id"`
	CreatedAt               time.Time         `json:"created_at"`
	UpdatedAt               time.Time         `json:"updated_at"` // Effectively LastPlayedAt
	UserID                  string            `json:"user_id"`
	TrackPath               string            `json:"track_path,omitempty"`
	PlaybackPositionSeconds int               `json:"playback_position_seconds"`
	ProgressPercentage      float64           `json:"progress_percentage"`
	IsFinished              bool              `json:"is_finished"`
	Work                    *work_dto.WorkDTO `json:"work,omitempty"`
}
