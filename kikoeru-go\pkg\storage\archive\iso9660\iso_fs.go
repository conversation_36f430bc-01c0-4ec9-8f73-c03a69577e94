package iso9660

import (
	"io"
	"io/fs"
	"path/filepath"
	"strings"
	"time"

	"github.com/kdomanski/iso9660"
)

// isoFS implements fs.FS interface for ISO9660 images
type isoFS struct {
	image *iso9660.Image
}

// Open opens the named file
func (fsys *isoFS) Open(name string) (fs.File, error) {
	if !fs.ValidPath(name) {
		return nil, &fs.PathError{Op: "open", Path: name, Err: fs.ErrInvalid}
	}

	// Root directory
	if name == "." {
		rootDir, err := fsys.image.RootDir()
		if err != nil {
			return nil, &fs.PathError{Op: "open", Path: name, Err: err}
		}
		return &isoDir{
			file: rootDir,
			name: ".",
		}, nil
	}

	// Clean the path
	name = filepath.ToSlash(name)
	name = strings.TrimPrefix(name, "/")

	// Get root dir
	rootDir, err := fsys.image.RootDir()
	if err != nil {
		return nil, &fs.PathError{Op: "open", Path: name, Err: err}
	}

	// For empty path or root, return the root directory
	if name == "" {
		return &isoDir{
			file: rootDir,
			name: ".",
		}, nil
	}

	// Split the path into components
	components := strings.Split(name, "/")
	currentDir := rootDir

	// Navigate to the file/directory
	for i, component := range components {
		if !currentDir.IsDir() {
			return nil, &fs.PathError{Op: "open", Path: name, Err: fs.ErrNotExist}
		}

		children, err := currentDir.GetChildren()
		if err != nil {
			return nil, &fs.PathError{Op: "open", Path: name, Err: err}
		}

		found := false
		for _, child := range children {
			if child.Name() == component {
				currentDir = child
				found = true
				break
			}
		}

		if !found {
			return nil, &fs.PathError{Op: "open", Path: name, Err: fs.ErrNotExist}
		}

		// If this is the last component
		if i == len(components)-1 {
			if currentDir.IsDir() {
				return &isoDir{
					file: currentDir,
					name: component,
				}, nil
			} else {
				return &isoFile{
					file: currentDir,
					name: component,
				}, nil
			}
		}
	}

	// This should not be reached
	return nil, &fs.PathError{Op: "open", Path: name, Err: fs.ErrNotExist}
}

// isoFile implements fs.File for a file in an ISO9660 image
type isoFile struct {
	file   *iso9660.File
	name   string
	reader io.Reader
	offset int64
}

func (f *isoFile) Stat() (fs.FileInfo, error) {
	return &isoFileInfo{
		file: f.file,
		name: f.name,
	}, nil
}

func (f *isoFile) Read(p []byte) (int, error) {
	if f.reader == nil {
		f.reader = f.file.Reader()
	}
	return f.reader.Read(p)
}

func (f *isoFile) Close() error {
	// Reset reader for potential reuse
	f.reader = nil
	f.offset = 0
	return nil
}

// isoDir implements fs.File and fs.ReadDirFile for a directory in an ISO9660 image
type isoDir struct {
	file *iso9660.File
	name string
}

func (d *isoDir) Stat() (fs.FileInfo, error) {
	return &isoFileInfo{
		file: d.file,
		name: d.name,
	}, nil
}

func (d *isoDir) Read(p []byte) (int, error) {
	return 0, fs.ErrInvalid
}

func (d *isoDir) Close() error {
	return nil
}

// ReadDir reads the directory entries
func (d *isoDir) ReadDir(n int) ([]fs.DirEntry, error) {
	children, err := d.file.GetChildren()
	if err != nil {
		return nil, err
	}

	entries := make([]fs.DirEntry, 0, len(children))
	for _, child := range children {
		entries = append(entries, &isoDirEntry{
			file: child,
		})
	}

	if n <= 0 {
		return entries, nil
	}

	if n > len(entries) {
		n = len(entries)
		entries = entries[:n]
		return entries, io.EOF
	}

	return entries[:n], nil
}

// isoFileInfo implements fs.FileInfo for a file or directory in an ISO9660 image
type isoFileInfo struct {
	file *iso9660.File
	name string
}

func (i *isoFileInfo) Name() string {
	if i.name != "" {
		return i.name
	}
	return i.file.Name()
}

func (i *isoFileInfo) Size() int64 {
	return i.file.Size()
}

func (i *isoFileInfo) Mode() fs.FileMode {
	if i.file.IsDir() {
		return fs.ModeDir | 0555
	}
	return 0444
}

func (i *isoFileInfo) ModTime() time.Time {
	return i.file.ModTime()
}

func (i *isoFileInfo) IsDir() bool {
	return i.file.IsDir()
}

func (i *isoFileInfo) Sys() interface{} {
	return nil
}

// isoDirEntry implements fs.DirEntry for a directory entry in an ISO9660 image
type isoDirEntry struct {
	file *iso9660.File
}

func (e *isoDirEntry) Name() string {
	return e.file.Name()
}

func (e *isoDirEntry) IsDir() bool {
	return e.file.IsDir()
}

func (e *isoDirEntry) Type() fs.FileMode {
	if e.file.IsDir() {
		return fs.ModeDir
	}
	return 0
}

func (e *isoDirEntry) Info() (fs.FileInfo, error) {
	return &isoFileInfo{file: e.file}, nil
}
