package service

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
	"github.com/Sakura-Byte/kikoeru-go/pkg/log"
	dto_review "github.com/Sakura-Byte/kikoeru-go/pkg/dto/review"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"github.com/Sakura-Byte/kikoeru-go/pkg/ports" // Import ports package
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/database"
)

// ValidationError is now defined in github.com/Sakura-Byte/kikoeru-go/pkg/apperrors
// ReviewService interface has been moved to github.com/Sakura-Byte/kikoeru-go/pkg/ports

type reviewService struct {
	reviewRepo  database.ReviewRepository
	workRepo    database.WorkRepository
	workService ports.WorkService // Added WorkService dependency
}

// Ensure reviewService implements ports.ReviewService
var _ ports.ReviewService = (*reviewService)(nil)

// NewReviewService creates a new instance of ReviewService.
func NewReviewService(
	reviewRepo database.ReviewRepository,
	workRepo database.WorkRepository,
	workService ports.WorkService, // Added WorkService parameter
) ports.ReviewService { // Changed return type to ports.ReviewService
	return &reviewService{
		reviewRepo:  reviewRepo,
		workRepo:    workRepo,
		workService: workService,
	}
}

// ReviewResponseItem has been moved to kikoeru-go/pkg/dto/review/review_dto.go

func (s *reviewService) PutReview(ctx context.Context, userID string, req dto_review.PutReviewRequest) (*dto_review.ReviewResponseItem, error) {
	log.Debug(ctx, "Attempting to put review", "user_id", userID, "work_original_id", req.WorkID)

	// Validate WorkID exists
	work, err := s.workRepo.GetByOriginalID(ctx, req.WorkID)
	if err != nil {
		if errors.Is(err, apperrors.ErrWorkNotFound) {
			log.Warn(ctx, "Work not found for review", "work_original_id", req.WorkID)
			return nil, apperrors.ErrWorkNotFound
		}
		log.Error(ctx, "Failed to get work while putting review", "work_original_id", req.WorkID, "error", err)
		return nil, fmt.Errorf("failed to verify work: %w", err)
	}

	// Validate rating if provided
	if req.Rating != nil && (*req.Rating < 1 || *req.Rating > 5) {
		return nil, apperrors.NewValidationError("rating must be between 1 and 5, or null")
	}

	// Validate progress status if provided
	if req.Progress != "" {
		isValidProgress := false
		validProgresses := []models.ProgressStatus{
			models.ProgressMarked, models.ProgressListening, models.ProgressListened,
			models.ProgressReplay, models.ProgressPostponed,
		}
		for _, vp := range validProgresses {
			if req.Progress == vp {
				isValidProgress = true
				break
			}
		}
		if !isValidProgress {
			return nil, apperrors.NewValidationError(fmt.Sprintf("invalid progress status: %s", req.Progress))
		}
	}

	// Get existing review if it exists, or create a new one
	existingReview, err := s.reviewRepo.GetByUserIDAndWorkOriginalID(ctx, userID, *work.OriginalID)
	var review *models.Review

	if err != nil && !errors.Is(err, apperrors.ErrReviewNotFound) {
		log.Error(ctx, "Failed to check for existing review", "user_id", userID, "work_original_id", req.WorkID, "error", err)
		return nil, fmt.Errorf("failed to check existing review: %w", err)
	}

	if existingReview != nil {
		// Update existing review - only modify fields that were provided
		review = existingReview
		if req.Rating != nil {
			review.Rating = req.Rating
		}
		if req.ReviewText != nil {
			review.ReviewText = req.ReviewText
		}
		if req.Progress != "" {
			review.Progress = req.Progress
		}
		review.UpdatedAt = time.Now()
	} else {
		// Create new review with provided fields
		review = &models.Review{
			UserID:     userID,
			WorkID:     *work.OriginalID,
			Rating:     req.Rating,
			ReviewText: req.ReviewText,
			Progress:   req.Progress,
			UpdatedAt:  time.Now(),
		}
	}

	if err := s.reviewRepo.CreateOrUpdate(ctx, review); err != nil {
		log.Error(ctx, "Failed to create or update review in repository", "user_id", userID, "work_original_id", req.WorkID, "error", err)
		return nil, fmt.Errorf("failed to save review: %w", err)
	}

	savedReview, err := s.reviewRepo.GetByUserIDAndWorkOriginalID(ctx, userID, *work.OriginalID)
	if err != nil {
		log.Error(ctx, "Failed to fetch review after save", "user_id", userID, "work_original_id", *work.OriginalID, "error", err)
		return nil, fmt.Errorf("failed to fetch review after save for DTO conversion: %w", err)
	}

	responseItem := &dto_review.ReviewResponseItem{
		Review: *savedReview,
	}

	if savedReview.WorkID != "" && s.workService != nil {
		workDTO, errWork := s.workService.GetWorkInfoByOriginalID(ctx, savedReview.WorkID)
		if errWork != nil {
			log.Error(ctx, "Failed to get WorkDTO for ReviewResponseItem in PutReview", "work_original_id", savedReview.WorkID, "review_id", savedReview.ID, "error", errWork)
			// Log error but still return the review part of the DTO
		} else {
			responseItem.Work = workDTO
		}
	}

	log.Info(ctx, "Review created/updated successfully", "review_id", savedReview.ID, "user_id", userID, "work_original_id", *work.OriginalID)
	return responseItem, nil
}

// GetUserReviewForWork retrieves a user's review for a specific work
func (s *reviewService) GetUserReviewForWork(ctx context.Context, userID string, workOriginalID string) (*models.Review, error) {
	log.Debug(ctx, "Getting user review for specific work", "user_id", userID, "work_original_id", workOriginalID)

	// Get the review from repository
	review, err := s.reviewRepo.GetByUserIDAndWorkOriginalID(ctx, userID, workOriginalID)
	if err != nil {
		if errors.Is(err, apperrors.ErrReviewNotFound) {
			log.Debug(ctx, "No review found for user and work", "user_id", userID, "work_original_id", workOriginalID)
			return nil, apperrors.ErrReviewNotFound
		}
		log.Error(ctx, "Failed to get review from repository", "user_id", userID, "work_original_id", workOriginalID, "error", err)
		return nil, fmt.Errorf("failed to get review: %w", err)
	}

	return review, nil
}

func (s *reviewService) GetUserReviews(ctx context.Context, userID string, params database.ListReviewParams) ([]dto_review.ReviewResponseItem, int64, error) {
	log.Debug(ctx, "Listing user reviews", "user_id", userID, "params", fmt.Sprintf("%+v", params))
	params.UserID = userID

	if params.Page <= 0 {
		params.Page = 1
	}
	if params.PageSize <= 0 {
		params.PageSize = 20
	}
	if params.PageSize > 100 {
		params.PageSize = 100
	}

	// Validate Order and Sort parameters
	validOrders := map[string]bool{
		"updated_at": true, "userRating": true, "release": true,
		"review_count": true, "sell_count": true, "": true,
	}
	if _, ok := validOrders[params.Order]; !ok {
		log.Warn(ctx, "Invalid order parameter for GetUserReviews", "order", params.Order)
		return nil, 0, apperrors.NewValidationError(fmt.Sprintf("invalid order parameter: %s", params.Order))
	}
	if params.Sort != "" && strings.ToLower(params.Sort) != "asc" && strings.ToLower(params.Sort) != "desc" {
		log.Warn(ctx, "Invalid sort parameter for GetUserReviews", "sort", params.Sort)

		return nil, 0, apperrors.NewValidationError(fmt.Sprintf("invalid sort parameter: %s. Must be 'asc' or 'desc'", params.Sort))
	}

	reviews, totalCount, err := s.reviewRepo.ListByUserID(ctx, params)
	if err != nil {
		log.Error(ctx, "Failed to list reviews from repository", "user_id", userID, "error", err)
		return nil, 0, fmt.Errorf("failed to retrieve reviews: %w", err)
	}

	responseItems := make([]dto_review.ReviewResponseItem, len(reviews))
	for i, rev := range reviews {
		item := dto_review.ReviewResponseItem{
			Review: rev,
		}
		if rev.WorkID != "" && s.workService != nil { // Check if workService is injected
			workDTO, workErr := s.workService.GetWorkInfoByOriginalID(ctx, rev.WorkID) // Use workService to get DTO
			if workErr == nil && workDTO != nil {
				item.Work = workDTO // workModel is already *dto_work.WorkDTO
			} else if workErr != nil && !errors.Is(workErr, apperrors.ErrWorkNotFound) {
				log.Warn(ctx, "Failed to fetch work details for review item", "work_original_id", rev.WorkID, "error", workErr)
			}
		}
		responseItems[i] = item
	}

	log.Info(ctx, "Successfully listed user reviews", "user_id", userID, "count", len(responseItems), "total_available", totalCount)
	return responseItems, totalCount, nil
}

func (s *reviewService) DeleteReview(ctx context.Context, userID string, originalID string) error {
	log.Debug(ctx, "Attempting to delete review", "user_id", userID, "original_id", originalID)

	// Validate WorkID exists
	work, err := s.workRepo.GetByOriginalID(ctx, originalID)
	if err != nil {
		if errors.Is(err, apperrors.ErrWorkNotFound) {
			log.Warn(ctx, "Work not found for review deletion", "work_original_id", originalID)
			return apperrors.ErrWorkNotFound
		}
		log.Error(ctx, "Failed to get work while deleting review", "work_original_id", originalID, "error", err)
		return fmt.Errorf("failed to verify work: %w", err)
	}
	// Validate review exists
	review, err := s.reviewRepo.GetByUserIDAndWorkOriginalID(ctx, userID, *work.OriginalID)
	if err != nil {
		if errors.Is(err, apperrors.ErrReviewNotFound) {
			log.Warn(ctx, "Review not found for deletion", "user_id", userID, "work_original_id", originalID)
			return apperrors.ErrReviewNotFound
		}
		log.Error(ctx, "Failed to get review while deleting", "user_id", userID, "work_original_id", originalID, "error", err)
		return fmt.Errorf("failed to verify review: %w", err)
	}
	if review.UserID != userID {
		log.Warn(ctx, "User does not own the review", "user_id", userID, "review_user_id", review.UserID)
		return apperrors.ErrUserNotFound
	}
	// Proceed to delete the review
	if err := s.reviewRepo.Delete(ctx, userID, originalID); err != nil {
		log.Error(ctx, "Failed to delete review from repository", "user_id", userID, "work_original_id", originalID, "error", err)
		return fmt.Errorf("failed to delete review: %w", err)
	}
	log.Info(ctx, "Review deleted successfully", "user_id", userID, "work_original_id", originalID)
	return nil
}
