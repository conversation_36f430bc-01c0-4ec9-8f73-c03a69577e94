package common_dto

// APIError represents a standardized error structure for API responses.
type APIError struct {
	Message string `json:"message"`        // Human-readable error message
	Error   string `json:"error"`          // Machine-readable error identifier, compatible with Quasar notification
	Code    int    `json:"code,omitempty"` // HTTP status code or custom error code
	// Add more fields if needed, e.g., details, validation_errors
}

// SuccessResponse is a simple success response with a message
type SuccessResponse struct {
	Message string `json:"message"`
}

// PaginationMeta holds pagination metadata for list responses.
type PaginationMeta struct {
	TotalItems  int64 `json:"total_items"`
	PerPage     int   `json:"per_page"`
	CurrentPage int   `json:"current_page"`
	TotalPages  int   `json:"total_pages"`
	// ItemCount is removed; client can derive it from len(Items)
}

// PaginatedDataResponse is a standardized response structure for paginated data.
type PaginatedDataResponse struct {
	Items      interface{}    `json:"items"`
	Pagination PaginationMeta `json:"pagination"`
}
