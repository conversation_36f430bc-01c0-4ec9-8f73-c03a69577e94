package driver

import (
	"context"
	"errors"
	"time"

	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
)

// Download strategies constants
const (
	DownloadStrategyProxy    = "proxy"
	DownloadStrategyRedirect = "redirect"
)

// RootPathConfig and RootIDConfig are defined in alist_model.go

// AlistDriverCommonConfig holds common configuration passed from DriverManager to DriverConstructor.
type AlistDriverCommonConfig struct {
	Name           string        `json:"name"`
	StorageID      uint          `json:"storage_id"`
	RequestTimeout time.Duration `json:"request_timeout"`
}

// DriverConstructor defines the function signature for creating a new driver instance.
type DriverConstructor func(commonConfig AlistDriverCommonConfig, additionConfig interface{}) (StorageDriver, error)

// DriverWrapperFunc defines a function signature for wrapping a raw StorageDriver,
// typically to add capabilities like caching.
type DriverWrapperFunc func(rawDriver StorageDriver, storageID uint, cacheExpiration int) StorageDriver

// ParamInfo describes a configuration parameter for a storage driver.
type ParamInfo struct {
	Name         string      `json:"name"`
	Label        string      `json:"label,omitempty"`
	Type         string      `json:"type"`
	Required     bool        `json:"required"`
	DefaultValue interface{} `json:"default_value,omitempty"`
	Description  string      `json:"description,omitempty"`
	Secret       bool        `json:"secret,omitempty"`
	Options      []string    `json:"options,omitempty"`
}

// Config holds static configuration and definition information for a driver type,
// returned by the driver's Config() method.
type Config struct {
	Name              string      `json:"name"`
	DisplayName       string      `json:"display_name"`
	DefaultDriverRoot string      `json:"default_driver_root,omitempty"`
	DownloadOptions   []string    `json:"download_options"`
	DefaultDownload   string      `json:"default_download,omitempty"`
	Params            []ParamInfo `json:"params"`
}

// APIDriverDefinition is the structure returned by the API endpoint that lists driver definitions.
type APIDriverDefinition struct {
	DriverName        string      `json:"driver_name"`
	DisplayName       string      `json:"display_name"`
	DefaultDriverRoot string      `json:"default_driver_root,omitempty"`
	DownloadOptions   []string    `json:"download_options"`
	DefaultDownload   string      `json:"default_download"`
	Params            []ParamInfo `json:"params"`
}

// Storage is an interface for drivers to get/set their associated StorageSource model.
type Storage interface {
	GetStorage() *models.StorageSource
	SetStorage(storage models.StorageSource)
}

// StorageMeta defines metadata and lifecycle methods for a storage driver.
type StorageMeta interface {
	AlistAdditionalConfigProvider
	Init(ctx context.Context) error
	Drop(ctx context.Context) error
	Config() Config
	Storage
}

// AlistAdditionalConfigProvider allows access to the driver's specific addition struct.
type AlistAdditionalConfigProvider interface {
	GetAddition() interface{}
}

type StorageReader interface {
	List(ctx context.Context, dir AlistObj, args AlistListArgs) ([]AlistObj, error)
	Link(ctx context.Context, file AlistObj, args AlistLinkArgs) (*AlistLink, error)
}

type Getter interface {
	Get(ctx context.Context, pathOrID string) (AlistObj, error)
}

type IRootPathGetter interface {
	GetRootPath() string
}

type IRootIDGetter interface {
	GetRootID() string
}

// Wrapper can be implemented by a driver that wraps another driver.
type Wrapper interface {
	Unwrap() StorageDriver
}

type StorageDriver interface {
	StorageMeta
	StorageReader
}

// DriverTypeDetector provides utilities to detect if a driver is path-based or ID-based
// without requiring them to implement additional interfaces
type DriverTypeDetector struct{}

// IsIDBased checks if a driver is ID-based by examining if it implements IRootIDGetter
func (d DriverTypeDetector) IsIDBased(driver StorageDriver) bool {
	_, ok := driver.(IRootIDGetter)
	return ok
}

// IsPathBased checks if a driver is path-based by examining if it implements IRootPathGetter
func (d DriverTypeDetector) IsPathBased(driver StorageDriver) bool {
	_, ok := driver.(IRootPathGetter)
	return ok
}

// IDResolver attempts to resolve a path to an ID for ID-based drivers
// This is extracted as a separate utility to avoid burdening all drivers with this method
type IDResolver interface {
	// ResolvePath attempts to find the ID for a path
	// Returns the ID if found, or an error if the path cannot be resolved
	ResolvePath(ctx context.Context, driver StorageDriver, path string) (string, error)
}

type DriverManagerGetter interface {
	GetDriver(storageID uint) (StorageDriver, bool)
	ResolvePath(ctx context.Context, storageID uint, path string) (string, error)
	GetDriverType(storageID uint) (isIDBased bool, isPathBased bool, err error)
}

var (
	ErrFileNotFound          = errors.New("file or directory not found")
	ErrNotADirectory         = errors.New("not a directory")
	ErrNotAFile              = errors.New("not a file")
	ErrOperationNotSupported = errors.New("operation not supported by this driver")
	ErrInvalidPath           = errors.New("invalid path provided")
	ErrAuthenticationFailed  = errors.New("driver authentication failed")
	ErrPermissionDenied      = errors.New("permission denied by driver")
	ErrConfiguration         = errors.New("driver configuration error")
	ErrRateLimited           = errors.New("driver rate limited")
	ErrDriverSpecific        = errors.New("driver specific error")
)
