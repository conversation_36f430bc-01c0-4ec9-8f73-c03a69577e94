package ports

import (
	"context"

	"github.com/Sakura-Byte/kikoeru-go/pkg/auth"
	feedback_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/feedback"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/database"
)

// FeedbackService defines the application service interface for feedback-related operations.
type FeedbackService interface {
	SubmitFeedback(ctx context.Context, req feedback_dto.FeedbackSubmissionRequest, userID *string, ipAddress string) (*models.Feedback, error)
	ListFeedback(ctx context.Context, params database.ListFeedbackParams, claims *auth.Claims) ([]*models.Feedback, int64, error)
	GetFeedbackByID(ctx context.Context, feedbackID uint, claims *auth.Claims) (*models.Feedback, error)
	UpdateFeedback(ctx context.Context, feedbackID uint, req feedback_dto.UpdateFeedbackRequest, adminUserID string) (*models.Feedback, error)
}
