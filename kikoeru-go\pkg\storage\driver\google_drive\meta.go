package google_drive

import (
	kikdrv "github.com/Sakura-Byte/kikoeru-go/pkg/storage/driver"
)

const (
	// DriverName is the programmatic name of this driver.
	DriverName = "google_drive"
	// DriverDisplayName is the human-readable name for this driver.
	DriverDisplayName = "Google Drive"
	// DriverDefaultDriverRoot is the Google Drive API's own default root ID.
	// This is used by the driver's Config() method and potentially by NewDriver
	// if no instance-specific root is configured.
	DriverDefaultDriverRoot = "root"
)

// Addition contains the Google Drive specific configuration.
// Tags on these fields are used by the driver's Config() method (via reflection through ParamsFromStruct)
// to generate the []ParamInfo list for the API.
type Addition struct {
	// Embed RootIDConfig for RootFolderID.
	// Tags for RootFolderID (label, description, required, type) are defined in driver.RootIDConfig
	// within alist_model.go.
	kikdrv.RootIDConfig `mapstructure:",squash"`

	RefreshToken string `json:"refresh_token" mapstructure:"refresh_token" label:"Refresh Token" description:"OAuth2 Refresh Token for Google Drive API access. This is a sensitive value." required:"true" type:"string"`
	ClientID     string `json:"client_id" mapstructure:"client_id" label:"Client ID" description:"OAuth2 Client ID. Optional, Kikoeru may use a built-in default if left empty." required:"true" type:"string" `
	ClientSecret string `json:"client_secret" mapstructure:"client_secret" label:"Client Secret" description:"OAuth2 Client Secret. Optional, Kikoeru may use a built-in default if left empty." required:"true" type:"string" `
	// Example of another field if needed in future:
	// CustomUserAgent string `json:"custom_user_agent" mapstructure:"custom_user_agent" label:"Custom User-Agent" description:"Optional custom User-Agent string for API requests." type:"string" required:"false"`
}

// Note: The Alist 'config' variable and 'init()' function for driver registration
// are removed as kikoeru-go uses a different mechanism.
// An *instance's* root_folder_id is taken from Addition.RootFolderID, which can be populated
// from StorageSource.DefaultRoot by NewDriver if not directly in the addition JSON,
// or fall back to DriverDefaultDriverRoot if both are empty.
