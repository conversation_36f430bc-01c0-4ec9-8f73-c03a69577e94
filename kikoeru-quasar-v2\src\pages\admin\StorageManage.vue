<template>
  <div class="q-pa-md">
    <div class="text-h5 text-weight-regular q-mb-lg">{{ t('adminStorage.title') }}</div>

    <div class="row justify-between q-mb-md">
      <q-btn
        color="primary"
        icon="add"
        :label="t('adminStorage.addStorage')"
        @click="showCreateDialog = true"
      />

      <div class="row q-gutter-sm">
        <q-btn
          color="secondary"
          icon="refresh"
          :label="t('adminStorage.reloadDrivers')"
          @click="reloadDrivers"
          :loading="reloadingDrivers"
        />
        <q-btn
          color="info"
          icon="help"
          :label="t('adminStorage.driverHelp')"
          @click="showDriversDialog = true"
        />
      </div>
    </div>

    <!-- 存储源列表 -->
    <q-table
      :rows="storages"
      :columns="tableColumns"
      row-key="id"
      :loading="loading"
      flat
      bordered
      class="shadow-2"
    >
      <template v-slot:body-cell-status="props">
        <q-td :props="props">
          <q-chip
            :color="props.row.disabled ? 'negative' : 'positive'"
            text-color="white"
            dense
          >
            {{ props.row.disabled ? t('adminStorage.statusDisabled') : t('adminStorage.statusEnabled') }}
          </q-chip>
        </q-td>
      </template>

      <template v-slot:body-cell-actions="props">
        <q-td :props="props">
          <div class="row q-gutter-xs">
            <q-btn
              dense
              round
              color="primary"
              icon="edit"
              size="sm"
              @click="editStorage(props.row)"
            >
              <q-tooltip>{{ t('common.edit') }}</q-tooltip>
            </q-btn>

            <q-btn
              dense
              round
              color="info"
              icon="cable"
              size="sm"
              @click="testConnection(props.row)"
            >
              <q-tooltip>{{ t('adminStorage.testConnection') }}</q-tooltip>
            </q-btn>

            <q-btn
              dense
              round
              color="negative"
              icon="delete"
              size="sm"
              @click="confirmDelete(props.row)"
            >
              <q-tooltip>{{ t('common.delete') }}</q-tooltip>
            </q-btn>
          </div>
        </q-td>
      </template>
    </q-table>

    <!-- 创建存储源对话框 -->
    <StorageFormDialog
      v-if="showCreateDialog"
      key="create-dialog"
      :drivers="driverDefinitions"
      @close="onCreateDialogClose"
      @success="onDialogSuccess"
    />

    <!-- 编辑存储源对话框 -->
    <StorageFormDialog
      v-if="showEditDialog"
      key="edit-dialog"
      :drivers="driverDefinitions"
      :storage="editingStorage"
      @close="onEditDialogClose"
      @success="onDialogSuccess"
    />

    <!-- 驱动说明对话框 -->
    <q-dialog v-model="showDriversDialog" maximized>
      <q-card>
        <q-card-section class="row items-center q-pb-none">
          <div class="text-h6">{{ t('adminStorage.driverHelpTitle') }}</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section>
          <div v-for="driver in driverDefinitions" :key="driver.driver_name" class="q-mb-lg">
            <div class="text-h6 q-mb-md">{{ driver.display_name }}</div>

            <q-list bordered>
              <q-item>
                <q-item-section>
                  <q-item-label>{{ t('adminStorage.driverInfo.supportedStrategies') }}</q-item-label>
                  <q-item-label caption>
                    {{ driver.download_options.join(', ') }}
                    ({{ t('adminStorage.driverInfo.defaultStrategy') }}: {{ driver.default_download }})
                  </q-item-label>
                </q-item-section>
              </q-item>
            </q-list>

            <div class="text-subtitle1 q-mt-md q-mb-sm">{{ t('adminStorage.driverInfo.configParams') }}</div>
            <q-list bordered>
              <q-item v-for="param in driver.params" :key="param.name">
                <q-item-section>
                  <q-item-label>{{ param.label || param.name }}</q-item-label>
                  <q-item-label caption>
                    {{ param.description }}
                    <span v-if="param.required" class="text-red">({{ t('adminStorage.driverInfo.required') }})</span>
                    <span v-if="param.secret" class="text-orange">({{ t('adminStorage.driverInfo.sensitive') }})</span>
                  </q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </div>
        </q-card-section>
      </q-card>
    </q-dialog>

    <!-- 删除确认对话框 -->
    <q-dialog v-model="showDeleteDialog" persistent>
      <q-card>
        <q-card-section>
          <div class="text-h6">{{ t('adminStorage.confirmDeleteDialog.title') }}</div>
          <div class="text-body2">{{ t('adminStorage.confirmDeleteDialog.message', { remark: deletingStorage?.remark }) }}</div>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="t('common.cancel')" v-close-popup />
          <q-btn flat :label="t('common.delete')" color="negative" @click="deleteStorage" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, getCurrentInstance } from 'vue'
import { useI18n } from 'vue-i18n'
import StorageFormDialog from '../../components/admin/StorageFormDialog.vue'
import { useNotification } from '../../composables/useNotification'

defineOptions({
  name: 'StorageManagePage'
})

const { t, locale } = useI18n()
const { proxy } = getCurrentInstance()
const { showSuccessNotification, showErrorNotification } = useNotification()

// Reactive data
const storages = ref([])
const driverDefinitions = ref([])
const loading = ref(false)
const reloadingDrivers = ref(false)
const showCreateDialog = ref(false)
const showEditDialog = ref(false)
const showDriversDialog = ref(false)
const showDeleteDialog = ref(false)
const editingStorage = ref(null)
const deletingStorage = ref(null)

// Computed properties
const tableColumns = computed(() => [
  {
    name: 'order',
    label: t('adminStorage.table.order'),
    field: 'order',
    sortable: true,
    align: 'center'
  },
  {
    name: 'remark',
    label: t('adminStorage.table.remark'),
    field: 'remark',
    sortable: true,
    align: 'left'
  },
  {
    name: 'driver',
    label: t('adminStorage.table.driver'),
    field: 'driver',
    sortable: true,
    align: 'center'
  },
  {
    name: 'download_strategy',
    label: t('adminStorage.table.downloadStrategy'),
    field: 'download_strategy',
    sortable: true,
    align: 'center'
  },
  {
    name: 'status',
    label: t('adminStorage.table.status'),
    field: 'disabled',
    sortable: true,
    align: 'center'
  },
  {
    name: 'created_at',
    label: t('adminStorage.table.createdAt'),
    field: 'created_at',
    sortable: true,
    align: 'center',
    format: val => new Date(val).toLocaleDateString(locale.value)
  },
  {
    name: 'actions',
    label: t('adminStorage.table.actions'),
    field: 'actions',
    align: 'center'
  }
])

// Methods
const loadStorages = async () => {
  loading.value = true
  try {
    const response = await proxy.$api.get('/api/v1/admin/storages')
    if (response.data && Array.isArray(response.data)) {
      storages.value = response.data
    } else {
      storages.value = []
    }
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('notification.adminLoadStoragesFailed'))
  } finally {
    loading.value = false
  }
}

const loadDriverDefinitions = async () => {
  try {
    const response = await proxy.$api.get('/api/v1/admin/storages/driver-definitions')
    if (response.data && Array.isArray(response.data)) {
      driverDefinitions.value = response.data
    } else {
      driverDefinitions.value = []
    }
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('notification.adminLoadDriverDefinitionsFailed'))
  }
}

const reloadDrivers = async () => {
  reloadingDrivers.value = true
  try {
    await proxy.$api.post('/api/v1/admin/storages/reload-drivers')
    showSuccessNotification(t('notification.adminDriversReloaded'))
    loadDriverDefinitions()
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('notification.adminDriversReloadFailed'))
  } finally {
    reloadingDrivers.value = false
  }
}

const editStorage = (storage) => {
  editingStorage.value = storage
  showEditDialog.value = true
}

const testConnection = async (storage) => {
  try {
    await proxy.$api.post('/api/v1/admin/storages/test-connection', storage)
    showSuccessNotification(t('notification.adminConnectionTestSuccess'))
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('notification.adminConnectionTestFailed'))
  }
}

const confirmDelete = (storage) => {
  deletingStorage.value = storage
  showDeleteDialog.value = true
}

const deleteStorage = async () => {
  try {
    await proxy.$api.delete(`/api/v1/admin/storages/${deletingStorage.value.id}`)
    showSuccessNotification(t('notification.adminStorageDeleted'))
    showDeleteDialog.value = false
    deletingStorage.value = null
    loadStorages()
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('notification.adminStorageDeleteFailed'))
  }
}

const onCreateDialogClose = () => {
  showCreateDialog.value = false
}

const onDialogSuccess = () => {
  showCreateDialog.value = false
  showEditDialog.value = false
  editingStorage.value = null
  loadStorages()
}

const onEditDialogClose = () => {
  showEditDialog.value = false
}

// Lifecycle
onMounted(() => {
  loadStorages()
  loadDriverDefinitions()
})
</script>
