package ports

import (
	"context"
	"io"
)

// CoverType represents the type of cover image. Moved from service.
type CoverType string

const (
	CoverTypeMain CoverType = "main"
	CoverTypeSam  CoverType = "sam"
)

// MediaService defines the application service interface for media-related operations.
// This interface is a "driven" port, implemented by the service layer and used by handlers.
type MediaService interface {
	GetMediaStream(ctx context.Context, workID uint, trackPath string, clientIP string, rangeHeader string) (stream io.ReadCloser, contentType string, originalSize int64, actualLength int64, filename string, httpStatus int, err error)
	GetCoverPathByOriginalIDAndFilename(ctx context.Context, originalID string, filename string) (string, error)
	ProcessAndStoreCover(ctx context.Context, originalID string, coverBytes []byte, coverType CoverType) (storedFilename string, err error)
}
