<template>
  <div>
    <!-- 播放器 -->
    <q-slide-transition>
      <q-card square v-show="hasValidTrack && !hide" class="fixed-bottom-right audio-player q-pa-sm text-black" :style="{'--cover-url': `url(${coverUrl})`}" @mousewheel.prevent @touchmove.prevent>
        <!-- Pull handler -->
        <div class="pull-handler" @click="toggleHide"></div>

        <!-- 音声封面 -->
        <div class="row items-center albumart q-my-lg q-pa-sm relative-position non-selectable">
          <q-img
            :src="coverUrl"
            class="rounded-borders shadow-2 no-long-press-menu full-width"
            :ratio="4/3"
            style="max-height: calc(100% - 20px)"
            contain
          />
        </div>

        <!-- 标题和作品信息 -->
        <div class="text-center q-mb-sm column">
          <div class="ellipsis-2-lines text-bold q-pb-xs" style="filter: opacity(1);">
            {{ currentPlayingFile?.title || '' }}
          </div>
          <div class="container full-width">
            <div class="scrolling" :style="{'animation-duration': '26.5667s', 'animation-play-state': 'running', '--max-scroll': '-837px'}">
              <div class="one-line-expand">
                <span class="text-caption" style="opacity: 0.54;">
                  {{ currentPlayingFile?.workTitle || '' }}
                </span>
                <span class="spacer" style="width: 80px;"></span>
                <span class="text-caption" style="opacity: 0.54;">
                  {{ currentPlayingFile?.workTitle || '' }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 进度条控件 -->
        <div class="row items-center q-mx-lg" style="height: 40px;">
          <div class="col-12 q-pt-xs">
            <!-- Only render AudioElement when there's a valid track -->
            <AudioElement v-if="hasValidTrack" class="plyr" />
          </div>
          <div class="col-12 flex justify-between">
            <div style="display: inline;">{{ formatSeconds(currentTime) }}</div>
            <div style="display: inline;">{{ formatSeconds(duration) }}</div>
          </div>
        </div>

        <!-- 播放按钮控件 -->
        <div class="row flex-center">
          <q-btn flat dense size="md" icon="skip_previous" class="col-auto" style="font-size: 20px; width: 55px;" @click="skipPrev">
            <q-tooltip>{{ $t('player.previous') }}</q-tooltip>
          </q-btn>
          <q-btn flat dense size="md" :icon="rewindIcon" class="col-auto" style="font-size: 20px; width: 55px;" @click="rewind(true)">
            <q-tooltip>{{ $t('player.rewind') }}</q-tooltip>
          </q-btn>
          <q-btn flat dense size="md" :icon="playingIcon" class="col-auto" style="font-size: 30px; width: 60px;" @click="handlePlayButtonClick()">
            <q-tooltip>{{ playing ? $t('player.pause') : $t('player.play') }}</q-tooltip>
          </q-btn>
          <q-btn flat dense size="md" :icon="forwardIcon" class="col-auto" style="font-size: 20px; width: 55px;" @click="forward(true)">
            <q-tooltip>{{ $t('player.forward') }}</q-tooltip>
          </q-btn>
          <q-btn flat dense size="md" icon="skip_next" class="col-auto" style="font-size: 20px; width: 55px;" @click="skipNext">
            <q-tooltip>{{ $t('player.next') }}</q-tooltip>
          </q-btn>
        </div>

        <!-- 音量控件 -->
        <div class="row items-center q-mx-lg q-pt-sm volume-slider" v-if="!$q.platform.is.ios">
          <q-icon name="volume_down" size="sm" class="col-auto" />
          <q-slider
            v-model="volumeModel"
            :min="0"
            :max="1"
            :step="0.01"
            class="col q-mx-sm"
            color="primary"
          />
          <q-icon name="volume_up" size="sm" class="col-auto" />
        </div>

        <!-- 底部控制栏 -->
        <div class="row self-center justify-center">
          <q-btn flat dense size="md" icon="queue_music" class="q-ma-sm" style="font-size: 14px;" @click="showCurrentPlayList = !showCurrentPlayList">
            <q-tooltip>{{ $t('player.showQueue') }}</q-tooltip>
          </q-btn>
          <q-btn flat dense size="md" icon="playlist_play" class="q-ma-sm" style="font-size: 14px;" @click="changePlayMode()">
            <q-tooltip>{{ $t('player.playMode') }}</q-tooltip>
          </q-btn>
          <q-btn
            flat
            dense
            size="md"
            icon="subtitles"
            class="q-ma-sm"
            :class="lyricsAvailable ? 'text-black' : 'text-grey'"
            style="font-size: 14px;"
            @click="openLyricDialog"
          >
            <q-tooltip>字幕</q-tooltip>
          </q-btn>
          <q-btn
            v-if="lyricsAvailable"
            flat
            dense
            size="md"
            icon="picture_in_picture_alt"
            class="q-ma-sm"
            style="font-size: 14px;"
            @click="togglePIPLyrics"
          >
            <q-tooltip>桌面歌词</q-tooltip>
          </q-btn>
          <q-btn flat dense size="md" icon="more_horiz" class="q-ma-sm text-black" style="font-size: 14px;">
            <q-menu anchor="bottom right" self="top right">
              <q-item clickable v-ripple @click="hideSeekButton = !hideSeekButton">
                <q-item-section avatar>
                  <q-icon :name="hideSeekButton ? 'done' : ''" />
                </q-item-section>
                <q-item-section>
                  {{ $t('player.hideCoverButtons') }}
                </q-item-section>
              </q-item>
              <q-item clickable v-ripple @click="swapSeekButton = !swapSeekButton">
                <q-item-section avatar>
                  <q-icon :name="swapSeekButton ? 'done' : ''" />
                </q-item-section>
                <q-item-section>
                  {{ $t('player.swapSeekButtons') }}
                </q-item-section>
              </q-item>
              <q-item clickable v-ripple @click="openWorkDetail()" v-close-popup>
                <q-item-section avatar>
                  <!-- placeholder -->
                </q-item-section>
                <q-item-section>
                  {{ $t('player.openWorkDetails') }}
                </q-item-section>
              </q-item>
            </q-menu>
          </q-btn>
        </div>

        <!-- iOS safe area placeholder -->
        <div id="ios-place-holder" style="height: min(10px, env(safe-area-inset-bottom, 0px));"></div>
      </q-card>
    </q-slide-transition>

    <!-- 当前播放列表 -->
    <q-dialog v-model="showCurrentPlayList">
      <q-card class="current-play-list">
        <!-- 操作当前播放列表的控制按钮 -->
        <div class="row" style="padding: 5px; height: 45px;">
          <q-btn dense round size="md" icon="edit" color="primary" @click="editCurrentPlayList = !editCurrentPlayList" style="height: 35px; width: 35px;" class="col-auto">
            <q-tooltip>{{ $t('common.edit') }}</q-tooltip>
          </q-btn>
          <q-btn dense round size="md" icon="save" color="teal" style="height: 35px; width: 35px;" class="col-auto q-mx-sm" @click="savePlaylist">
            <q-tooltip>{{ $t('common.save') }}</q-tooltip>
          </q-btn>
          <q-space />
          <q-btn dense round size="md" icon="delete_forever" color="red" @click="emptyQueue()" style="height: 35px; width: 35px;" class="col-auto">
            <q-tooltip>{{ $t('player.clearQueue') }}</q-tooltip>
          </q-btn>
        </div>

        <q-separator />

        <!-- 音频文件列表 -->
        <q-list style="max-height: 450px" class="scroll">
          <VueDraggable
            handle=".handle"
            v-model="queueCopy"
            @change="onQueueReorder"
            item-key="trackPath"
          >
            <template #item="{ element: track, index }">
              <q-item
                clickable
                v-ripple
                :active="queueIndex === index"
                active-class="text-white bg-teal"
                class="non-selectable"
                style="height: 48px; padding: 0px 10px;"
                @click="onClickTrack(index)"
              >
                <q-item-section side v-show="editCurrentPlayList">
                  <q-icon name="clear" :color="queueIndex === index ? 'white' : 'red'" @click="removeFromQueue(index)">
                    <q-tooltip>{{ $t('player.removeFromQueue') }}</q-tooltip>
                  </q-icon>
                </q-item-section>

                <q-item-section avatar>
                  <q-img transition="fade" :src="samCoverUrl(track)" style="height: 38px; width: 38px" class="rounded-borders" />
                </q-item-section>

                <q-item-section>
                  <q-item-label lines="1">{{ track.title }}</q-item-label>
                  <q-item-label caption lines="1">{{ track.workTitle }}</q-item-label>
                </q-item-section>

                <q-item-section side class="handle" v-show="editCurrentPlayList">
                  <q-icon name="reorder" :color="queueIndex === index ? 'white' : 'dark'">
                     <q-tooltip>{{ $t('player.reorder') }}</q-tooltip>
                  </q-icon>
                </q-item-section>
              </q-item>
            </template>
          </VueDraggable>
        </q-list>
      </q-card>
    </q-dialog>

    <!-- Lyric Dialog - Only render when there's a valid track -->
    <LyricDialog
      v-if="hasValidTrack"
      v-model="showLyricDialog"
      :tree-data="workTreeData"
    />

    <!-- PIP Lyrics Component - Only render when there's a valid track and PIP is enabled -->
    <PIPLyrics v-if="hasValidTrack && enablePIPLyrics" />
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick, getCurrentInstance } from 'vue'
import { useQuasar } from 'quasar'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import VueDraggable from 'vuedraggable'
import AudioElement from './AudioElement.vue'
import LyricDialog from './LyricDialog.vue'
import PIPLyrics from './PIPLyrics.vue'
import { useAudioPlayer } from '../composables/useAudioPlayer'
import { useWorkInfo } from '../composables/useWorkInfo'
import { useNotification } from '../composables/useNotification'

defineOptions({
  name: 'AudioPlayer'
})

const $q = useQuasar()
const router = useRouter()
const { t } = useI18n()
const { proxy } = getCurrentInstance()
const { showSuccessNotification } = useNotification() // eslint-disable-line no-unused-vars

// Get composables
const {
  playing,
  hide,
  currentTime,
  duration,
  queueIndex,
  playMode,
  rewindSeekTime,
  forwardSeekTime,
  queue,
  volume,
  availableLyrics,
  enablePIPLyrics,
  currentPlayingFile,
  toggleHide,
  togglePlaying,
  nextTrack,
  previousTrack,
  changePlayMode,
  setVolume,
  setRewindSeekMode,
  setForwardSeekMode,
  setRewindSeekTime,
  setForwardSeekTime,
  setTrack,
  setQueue,
  removeFromQueue,
  emptyQueue,
  setEnablePIPLyrics
} = useAudioPlayer()

const { getTreeData } = useWorkInfo()

// Reactive data
const showCurrentPlayList = ref(false)
const editCurrentPlayList = ref(false)
const queueCopy = ref([])
const hideSeekButton = ref(false)
const swapSeekButton = ref(false)
const showLyricDialog = ref(false)
const workTreeData = ref(null)
const lyricsAvailable = ref(false)

// Computed properties
const volumeModel = computed({
  get() {
    return volume.value || 0.8
  },
  set(val) {
    setVolume(val)
  }
})

const coverUrl = computed(() => {
  const currentFile = currentPlayingFile.value
  if (!currentFile || !currentFile.originalId) return ""
  return `/api/v1/cover/${currentFile.originalId}?type=main`
})

const workDetailUrl = computed(() => {
  const currentFile = currentPlayingFile.value
  return currentFile && currentFile.originalId ? `/work/${currentFile.originalId}` : ""
})

const playModeIcon = computed(() => { // eslint-disable-line no-unused-vars
  if (!playMode.value || !playMode.value.name) return "playlist_play"

  switch (playMode.value.name) {
    case "all repeat":
      return "repeat"
    case "repeat once":
      return "repeat_one"
    case "shuffle":
      return "shuffle"
    default:
      return "playlist_play"
  }
})

const playingIcon = computed(() => {
  return playing.value ? "pause" : "play_arrow"
})

const rewindIcon = computed(() => {
  if (!rewindSeekTime.value) return 'replay_5'

  switch (rewindSeekTime.value) {
    case 5:
      return 'replay_5'
    case 10:
      return 'replay_10'
    case 30:
      return 'replay_30'
    default:
      return 'restore'
  }
})

const forwardIcon = computed(() => {
  if (!forwardSeekTime.value) return 'forward_30'

  switch (forwardSeekTime.value) {
    case 5:
      return 'forward_5'
    case 10:
      return 'forward_10'
    case 30:
      return 'forward_30'
    default:
      return 'update'
  }
})

const isDark = computed(() => { // eslint-disable-line no-unused-vars
  return $q.dark.isActive
})

const hasValidTrack = computed(() => {
  const currentFile = currentPlayingFile.value
  return currentFile &&
         currentFile.workId &&
         currentFile.trackPath &&
         currentFile.trackPath !== ''
})

// Watchers
watch(queue, (val) => {
  queueCopy.value = val.concat()
  // 在删除最后一个 track 时关闭当前播放列表
  if (queueCopy.value.length === 0) {
    showCurrentPlayList.value = false
  }
})

watch(showCurrentPlayList, (flag) => {
  // 关闭当前播放列表后，重置 editCurrentPlayList 状态为 false
  if (flag === false) {
    editCurrentPlayList.value = false
  }
})

watch(hideSeekButton, (option) => {
  if ($q && $q.localStorage) {
    $q.localStorage.set('hideSeekButton', option)
  }
})

watch(swapSeekButton, (option) => {
  if ($q && $q.localStorage) {
    $q.localStorage.set('swapSeekButton', option)
  }
})

watch(currentPlayingFile, (newFile) => {
  if (newFile && newFile.originalId) {
    loadWorkTreeData()
  }
}, { immediate: true })

watch(availableLyrics, () => {
  // Update lyrics availability when lyrics are loaded
  checkLyricsAvailability()
})

// Methods
const skipPrev = () => {
  console.log('Skipping to previous track')
  previousTrack()
}

const skipNext = () => {
  console.log('Skipping to next track')
  nextTrack()
}

const handlePlayButtonClick = () => {
  togglePlaying()
}

const rewind = (enable) => {
  setRewindSeekMode(enable)
}

const forward = (enable) => {
  setForwardSeekMode(enable)
}

const formatSeconds = (seconds) => {
  // 更全面的无效值检查
  if (seconds === undefined || seconds === null || isNaN(seconds) || !isFinite(seconds) || seconds < 0) {
    console.warn('formatSeconds received invalid value:', seconds)
    return "00:00"
  }

  // Treat 0 as a valid value
  seconds = Number(seconds) // Ensure seconds is a number

  let h = Math.floor(seconds / 3600) < 10
    ? '0' + Math.floor(seconds / 3600)
    : Math.floor(seconds / 3600)

  let m = Math.floor((seconds / 60 % 60)) < 10
    ? '0' + Math.floor((seconds / 60 % 60))
    : Math.floor((seconds / 60 % 60))

  let s = Math.floor((seconds % 60)) < 10
    ? '0' + Math.floor((seconds % 60))
    : Math.floor((seconds % 60))

  return h === "00"
    ? m + ":" + s
    : h + ":" + m + ":" + s
}

const samCoverUrl = (track) => {
  if (!track || !track.originalId) return ""
  return `/api/v1/cover/${track.originalId}?type=sam`
}

const onClickTrack = (index) => {
  if (!editCurrentPlayList.value) {
    setTrack(index)
    showCurrentPlayList.value = false
  }
}

const onQueueReorder = () => {
  // Handle drag and drop reordering
  setQueue({
    queue: queueCopy.value.concat(),
    index: queueIndex.value,
    resetPlaying: false
  })
}

const loadWorkTreeData = async () => {
  const currentFile = currentPlayingFile.value
  if (!currentFile || !currentFile.originalId) {
    workTreeData.value = null
    return
  }

  try {
    // Get tree data from store cache first
    const cachedData = getTreeData(currentFile.originalId)
    if (cachedData && cachedData.treeData) {
      workTreeData.value = cachedData.treeData
      return
    }

    // If not cached, fetch from API
    const response = await proxy.$api.get(`/api/v1/work/${currentFile.originalId}/tree`)
    workTreeData.value = response.data
  } catch (error) {
    console.warn('Failed to load work tree data:', error)
    workTreeData.value = null
  }
}

const checkLyricsAvailability = () => {
  // Check if lyrics are available
  lyricsAvailable.value = availableLyrics.value && availableLyrics.value.length > 0
}

const openLyricDialog = () => {
  if (!lyricsAvailable.value) {
    // If no lyrics available, directly open file picker
    selectLocalLyricFile()
  } else {
    // Open the lyric dialog
    showLyricDialog.value = true
  }
}

const togglePIPLyrics = () => {
  setEnablePIPLyrics(!enablePIPLyrics.value)
}

const openWorkDetail = () => {
  if (workDetailUrl.value) {
    router.push(workDetailUrl.value)
  }
}

const savePlaylist = async () => {
  // TODO: Implement save to playlist functionality using new API
  $q.notify({
    type: 'info',
    message: t('playlist.saveInProgress')
  })
}

const selectLocalLyricFile = async () => {
  try {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.lrc,.ass,.vtt,.srt'

    input.onchange = async (event) => {
      const file = event.target.files[0]
      if (file) {
        // For now, just show a notification
        $q.notify({
          color: 'positive',
          message: `已选择文件: ${file.name}`
        })

        // TODO: Load and display the lyric file
        showLyricDialog.value = true
      }
    }

    input.click()
  } catch (error) {
    console.error('Failed to select local lyric file:', error)
    $q.notify({
      color: 'negative',
      message: '选择文件失败'
    })
  }
}

// Lifecycle
onMounted(() => {
  // Use nextTick to ensure component is fully mounted
  nextTick(() => {
    try {
      if ($q && $q.localStorage) {
        // Load seek button visibility settings
        if ($q.localStorage.has('hideSeekButton')) {
          hideSeekButton.value = $q.localStorage.getItem('hideSeekButton')
        }
        if ($q.localStorage.has('swapSeekButton')) {
          swapSeekButton.value = $q.localStorage.getItem('swapSeekButton')
        }

        // Load seek time settings and apply to audio player store
        if ($q.localStorage.has('rewindSeekTime')) {
          const rewindTime = parseInt($q.localStorage.getItem('rewindSeekTime'))
          if (!isNaN(rewindTime)) {
            setRewindSeekTime(rewindTime)
          }
        }
        if ($q.localStorage.has('forwardSeekTime')) {
          const forwardTime = parseInt($q.localStorage.getItem('forwardSeekTime'))
          if (!isNaN(forwardTime)) {
            setForwardSeekTime(forwardTime)
          }
        }
      }
    } catch (err) {
      console.warn('Could not access localStorage:', err)
    }
  })
})
</script>

<style lang="scss">
.audio-player {
    overflow: hidden;
    display: flex;
    flex-direction: column
}

@media (min-width: 600px) {
    .audio-player {
        width:330px;
        margin: 0px 10px 10px 0px
    }
}

@media (max-width: 599.98px) {
    .audio-player {
        width:100%;
        height: 100%
    }
}

.audio-player:before {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    content: "";
    background-image: var(--cover-url);
    background-position: 50% 50%;
    background-size: contain;
    background-repeat: repeat;
    filter: blur(40px) opacity(0.2);
    transform: translate3d(0,0,0)
}

.body--dark .audio-player:before {
    filter: blur(40px) opacity(0.2)
}

.pull-handler {
    height: 6px;
    width: 100px;
    background: rgba(150,122,116,0.5);
    position: absolute;
    border-radius: 4px!important;
    left: 50%;
    top: 12px;
    transform: translateX(-50%);
    transition: 0.3s;
    cursor: pointer
}

.pull-handler:hover {
    transition: 0.3s;
    background: rgba(150,122,116,0.8)
}

.pull-handler:before {
    content: "";
    position: absolute;
    left: -50px;
    right: -50px;
    top: -10px;
    bottom: -10px;
    cursor: pointer
}

.albumart {
    flex-grow: 1
}

@media (max-width: 599.98px) {
    .albumart {
        width:100%
    }
}

.upload-subtitle {
    max-height: 500px
}

@media (min-width: 599.98px) {
    .upload-subtitle {
        width:450px
    }
}

@media (max-width: 599.98px) {
    .upload-subtitle {
        min-width:280px
    }
}

.current-play-list {
    max-height: 500px
}

@media (min-width: 599.98px) {
    .current-play-list {
        width:450px
    }
}

@media (max-width: 599.98px) {
    .current-play-list {
        min-width:280px
    }
}

.volume-slider {
    height: 40px;
    margin-bottom: 8px;
}

.no-long-press-menu {
    pointer-events: none;
    -moz-user-select: none;
    -webkit-user-select: none;
    -o-user-select: none;
    user-select: none
}

/* Scrolling animation for long work titles */
.container {
  overflow: hidden;
}

.scrolling {
  white-space: nowrap;
  animation-name: marquee;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
}

.one-line-expand {
  display: inline-block;
}

.spacer {
  display: inline-block;
}

@keyframes marquee {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(var(--max-scroll, -100%));
  }
}

.ellipsis-2-lines {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
</style>
