package google_drive

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/Sakura-Byte/kikoeru-go/pkg/log"
	kikoerudriver "github.com/Sakura-Byte/kikoeru-go/pkg/storage/driver"
	"github.com/go-resty/resty/v2"
)

// TokenResp is a local equivalent of <PERSON><PERSON>'s base.TokenResp
type TokenResp struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresIn    int64  `json:"expires_in"`
}

// ReqCallback is a callback function to customize a resty request.
type ReqCallback func(req *resty.Request)

func (d *GoogleDrive) refreshToken(ctx context.Context) error {

	url := "https://www.googleapis.com/oauth2/v4/token"
	var resp TokenResp
	var e TokenError

	if d.httpClient == nil {
		log.Warn(ctx, "httpClient is nil in refreshToken, initializing with default timeout")
		d.httpClient = resty.New().SetTimeout(30 * time.Second)
	}

	log.Debug(ctx, "Attempting to refresh Google Drive token")
	res, err := d.httpClient.R().
		SetContext(ctx).
		SetResult(&resp).
		SetError(&e).
		SetFormData(map[string]string{
			"client_id":     d.Addition.ClientID,
			"client_secret": d.Addition.ClientSecret,
			"refresh_token": d.Addition.RefreshToken,
			"grant_type":    "refresh_token",
		}).
		Post(url)

	if err != nil {
		log.Error(ctx, "Failed to refresh Google Drive token (request error)", "error", err)
		return err
	}

	log.Debug(ctx, "Google Drive token refresh response", "status_code", res.StatusCode(), "body_preview", string(res.Body()[:min(1024, len(res.Body()))]))
	if e.Error != "" {
		log.Error(ctx, "Google Drive token refresh API error", "api_error", e.Error, "description", e.ErrorDescription)
		return fmt.Errorf("google drive token refresh error: %s - %s", e.Error, e.ErrorDescription)
	}
	if resp.AccessToken == "" {
		log.Error(ctx, "Google Drive token refresh: got empty access token", "response_body_preview", string(res.Body()[:min(1024, len(res.Body()))]))
		return fmt.Errorf("failed to refresh token, got empty access token")
	}
	d.AccessToken = resp.AccessToken
	log.Info(ctx, "Google Drive token refreshed successfully")
	return nil
}

func (d *GoogleDrive) request(ctx context.Context, url string, method string, callback ReqCallback, respBody interface{}) ([]byte, int, error) {

	if d.httpClient == nil {
		log.Warn(ctx, "httpClient is nil in request, initializing with default timeout")
		d.httpClient = resty.New().SetTimeout(30 * time.Second)
	}

	req := d.httpClient.R().SetContext(ctx)
	req.SetHeader("Authorization", "Bearer "+d.AccessToken)
	req.SetQueryParam("includeItemsFromAllDrives", "true")
	req.SetQueryParam("supportsAllDrives", "true")

	if callback != nil {
		callback(req)
	}
	if respBody != nil {
		req.SetResult(respBody)
	}

	var apiError Error
	req.SetError(&apiError)

	res, err := req.Execute(method, url)
	if err != nil {
		log.Error(ctx, "Google Drive API request execution error", "url", url, "method", method, "error", err)
		return nil, 0, err
	}

	if apiError.Error.Code != 0 {
		log.Warn(ctx, "Google Drive API returned a structured error", "url", url, "method", method, "code", apiError.Error.Code, "message", apiError.Error.Message)
		if apiError.Error.Code == http.StatusUnauthorized {
			log.Info(ctx, "Google Drive token expired (401), attempting refresh", "url", url)
			errRefresh := d.refreshToken(ctx)
			if errRefresh != nil {
				log.Error(ctx, "Failed to refresh token after 401", "original_url", url, "error", errRefresh)
				return nil, apiError.Error.Code, fmt.Errorf("token refresh failed after 401: %w (original API error: %s)", errRefresh, apiError.Error.Message)
			}
			log.Info(ctx, "Token refreshed, retrying original request", "url", url)
			return d.request(ctx, url, method, callback, respBody)
		}
		if apiError.Error.Code == http.StatusNotFound {
			return nil, apiError.Error.Code, kikoerudriver.ErrFileNotFound
		}
		return nil, apiError.Error.Code, fmt.Errorf("google drive api error (code %d): %s (%v)", apiError.Error.Code, apiError.Error.Message, apiError.Error.Errors)
	}

	if res.IsError() {
		log.Warn(ctx, "Google Drive API request returned HTTP error status", "url", url, "method", method, "status_code", res.StatusCode(), "body_preview", string(res.Body()[:min(1024, len(res.Body()))]))
		if res.StatusCode() == http.StatusNotFound {
			return nil, res.StatusCode(), kikoerudriver.ErrFileNotFound
		}
		return res.Body(), res.StatusCode(), fmt.Errorf("google drive api http error: status %d", res.StatusCode())
	}

	return res.Body(), res.StatusCode(), nil
}

func (d *GoogleDrive) getFiles(ctx context.Context, id string) ([]File, error) {

	pageToken := "first"
	resFiles := make([]File, 0)
	fields := "files(id,name,mimeType,size,modifiedTime,createdTime,thumbnailLink,shortcutDetails),nextPageToken"

	for pageToken != "" {
		select {
		case <-ctx.Done():
			log.Info(ctx, "getFiles cancelled", "folder_id", id)
			return nil, ctx.Err()
		default:
		}
		if pageToken == "first" {
			pageToken = ""
		}
		var respData Files

		query := map[string]string{
			"orderBy":   "folder,name,modifiedTime desc",
			"fields":    fields,
			"pageSize":  "1000",
			"q":         fmt.Sprintf("'%s' in parents and trashed = false", id),
			"pageToken": pageToken,
		}

		log.Debug(ctx, "Fetching files from Google Drive", "folder_id", id, "page_token", pageToken)
		_, statusCode, err := d.request(ctx, "https://www.googleapis.com/drive/v3/files", http.MethodGet, func(req *resty.Request) {
			req.SetQueryParams(query)
		}, &respData)

		if err != nil {
			log.Error(ctx, "Failed to get files page from Google Drive", "folder_id", id, "page_token", pageToken, "status_code", statusCode, "error", err)
			return nil, err
		}

		pageToken = respData.NextPageToken
		resFiles = append(resFiles, respData.Files...)
		log.Debug(ctx, "Fetched page of files", "folder_id", id, "count_on_page", len(respData.Files), "next_page_token", pageToken)
	}
	log.Info(ctx, "Finished fetching all files for folder", "folder_id", id, "total_files_fetched", len(resFiles))
	return resFiles, nil
}

// min is a helper function to find the minimum of two integers.
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
