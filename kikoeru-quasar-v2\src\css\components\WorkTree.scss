/* WorkTree.scss - Styles for WorkTree component */

// Breadcrumbs styling
.q-breadcrumbs {
  margin-bottom: 12px;
}

// Card styling - removing all shadow effects in dark mode
.body--dark {
  .q-card,
  .q-breadcrumbs-el,
  .q-btn {
    box-shadow: none !important;
  }

  // Remove highlight from active/hover state
  .q-item__section--avatar .q-icon {
    text-shadow: none !important;
  }

  // Remove border highlights
  .q-separator {
    opacity: 0.5;
  }

  // Remove highlight from active tracks
  .q-item.q-item--active {
    border: none !important;
    box-shadow: none !important;
  }
} 