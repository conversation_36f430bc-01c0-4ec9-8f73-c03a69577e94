<template>
  <div class="q-pa-md">

    <div class="row justify-center">
      <div class="col-12 col-md-8 col-lg-6">

        <!-- 个人信息 -->
        <div v-if="user && authEnabled" class="settings-section q-mb-lg">
          <span class="text-weight-medium text-center flex">{{ t('settingsPage.personalInfo') }}</span>
          <div class="rounded-borders q-list q-list--bordered" :class="{ 'q-list--dark': $q.dark.isActive }">
            <q-item :class="{ 'setting-item': true, 'q-item--dark': $q.dark.isActive }">
              <q-item-section>
                <q-item-label>{{ t('settingsPage.username') }}</q-item-label>
                <q-item-label caption>{{ user ? user.username : '' }}</q-item-label>
              </q-item-section>
            </q-item>

            <q-item :class="{ 'setting-item': true, 'q-item--dark': $q.dark.isActive }">
              <q-item-section>
                <q-item-label>{{ t('settingsPage.userGroup') }}</q-item-label>
                <q-item-label caption>{{ user ? user.group : '' }}</q-item-label>
              </q-item-section>
            </q-item>

            <q-item :class="{ 'setting-item': true, 'q-item--dark': $q.dark.isActive }">
              <q-item-section>
                <q-item-label>{{ t('settingsPage.registrationDate') }}</q-item-label>
                <q-item-label caption>{{ formatDate(user ? user.created_at : null) }}</q-item-label>
              </q-item-section>
            </q-item>

            <q-item :class="{ 'setting-item': true, 'q-item--dark': $q.dark.isActive }">
              <q-item-section>
                <q-item-label>{{ t('settingsPage.lastUpdate') }}</q-item-label>
                <q-item-label caption>{{ formatDate(user ? user.updated_at : null) }}</q-item-label>
              </q-item-section>
            </q-item>
          </div>
        </div>

        <!-- 播放设置 -->
        <div class="settings-section q-mb-lg">
          <span class="text-weight-medium text-center flex">{{ t('settingsPage.playbackSettings') }}</span>
          <div class="rounded-borders q-list q-list--bordered" :class="{ 'q-list--dark': $q.dark.isActive }">
            <q-item role="listitem" :class="{ 'q-py-sm q-item q-item-type row no-wrap': true, 'q-item--dark': $q.dark.isActive }">
              <q-item-section avatar class="q-item__section column q-item__section--avatar q-item__section--side justify-center">
                <q-icon name="fast_forward" style="font-size: 28px;" />
              </q-item-section>

              <q-item-section class="q-item__section column q-item__section--main justify-center">
                <div class="q-item__label">
                  <span class="text-weight-medium">{{ t('settingsPage.forwardJump') }}</span>
                </div>
              </q-item-section>

              <q-item-section side class="q-item__section column q-item__section--side justify-center">
                <q-select
                  v-model="forwardTime"
                  :options="seekTimeOptions"
                  dense
                  borderless
                  dropdown-icon="arrow_drop_down"
                  class="time-select"
                  option-label="label"
                  option-value="value"
                  emit-value
                  map-options
                  @update:model-value="saveForwardTime"
                  :dark="$q.dark.isActive"
                >
                  <template v-slot:selected>
                    <div class="text-right full-width">{{ forwardTime }} {{ t('settingsPage.seconds') }}</div>
                  </template>
                </q-select>
              </q-item-section>
            </q-item>

            <q-separator :dark="$q.dark.isActive" />

            <q-item role="listitem" :class="{ 'q-py-sm q-item q-item-type row no-wrap': true, 'q-item--dark': $q.dark.isActive }">
              <q-item-section avatar class="q-item__section column q-item__section--avatar q-item__section--side justify-center">
                <q-icon name="fast_rewind" style="font-size: 28px;" />
              </q-item-section>

              <q-item-section class="q-item__section column q-item__section--main justify-center">
                <div class="q-item__label">
                  <span class="text-weight-medium">{{ t('settingsPage.rewindJump') }}</span>
                </div>
              </q-item-section>

              <q-item-section side class="q-item__section column q-item__section--side justify-center">
                <q-select
                  v-model="rewindTime"
                  :options="seekTimeOptions"
                  dense
                  borderless
                  dropdown-icon="arrow_drop_down"
                  class="time-select"
                  option-label="label"
                  option-value="value"
                  emit-value
                  map-options
                  @update:model-value="saveRewindTime"
                  :dark="$q.dark.isActive"
                >
                  <template v-slot:selected>
                    <div class="text-right full-width">{{ rewindTime }} {{ t('settingsPage.seconds') }}</div>
                  </template>
                </q-select>
              </q-item-section>
            </q-item>
          </div>
        </div>

        <!-- Interface Settings -->
        <div class="settings-section q-mb-lg">
          <span class="text-weight-medium text-center flex">{{ t('settingsPage.interfaceSettings') }}</span>
          <div class="rounded-borders q-list q-list--bordered" :class="{ 'q-list--dark': $q.dark.isActive }">
            <language-switcher />
          </div>
        </div>

        <!-- 游客提示 -->
        <q-banner class="guest-banner q-mb-md" v-if="!authEnabled">
          <template v-slot:avatar>
            <q-icon name="info" color="primary" />
          </template>
          {{ t('settingsPage.guestBanner') }}
          <template v-slot:action>
            <q-btn flat color="primary" :label="t('nav.login')" @click="showLoginDialog" />
          </template>
        </q-banner>

        <!-- 邮箱设置 -->
        <div class="settings-section q-mb-lg" v-if="user && authEnabled">
          <span class="text-weight-medium text-center flex">{{ t('settingsPage.emailSettings') }}</span>
          <div class="rounded-borders q-list q-list--bordered" :class="{ 'q-list--dark': $q.dark.isActive }">
            <q-item>
              <q-item-section>
                <q-item-label>{{ t('settingsPage.currentEmail') }}</q-item-label>
                <q-item-label caption>
                  {{ user.email || t('settingsPage.notSet') }}
                  <q-chip v-if="user.email"
                        :color="user.email_verified ? 'green' : 'orange'"
                        text-color="white"
                        dense
                        size="sm"
                        class="q-ml-sm">
                    {{ user.email_verified ? t('settingsPage.verified') : t('settingsPage.unverified') }}
                  </q-chip>
                </q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-btn
                  v-if="user.email"
                  flat
                  color="primary"
                  :label="t('settingsPage.changeEmail')"
                  @click="showChangeEmail = true"
                />
                <q-btn
                  v-else
                  flat
                  color="primary"
                  :label="t('settingsPage.setEmail')"
                  @click="showLinkEmail = true"
                />
              </q-item-section>
            </q-item>
            <hr aria-orientation="horizontal" class="q-separator q-separator q-separator--horizontal" :class="{ 'q-separator--dark': $q.dark.isActive }" v-if="user.email && !user.email_verified || user.email">
            <q-item v-if="user.email && !user.email_verified">
              <q-item-section>
                <q-btn
                  color="orange"
                  :label="t('settingsPage.resendVerification')"
                  @click="resendVerification"
                  :loading="resendingVerification"
                />
              </q-item-section>
            </q-item>
            <hr aria-orientation="horizontal" class="q-separator q-separator q-separator--horizontal" :class="{ 'q-separator--dark': $q.dark.isActive }" v-if="user.email">
            <q-item v-if="user.email">
              <q-item-section>
                <q-btn
                  flat
                  color="negative"
                  :label="t('settingsPage.unlinkEmail')"
                  @click="showUnlinkEmail = true"
                />
              </q-item-section>
            </q-item>
          </div>
        </div>

        <!-- 安全设置 -->
        <div class="settings-section q-mb-lg" v-if="user && authEnabled">
          <span class="text-weight-medium text-center flex">{{ t('settingsPage.securitySettings') }}</span>
          <div class="rounded-borders q-list q-list--bordered" :class="{ 'q-list--dark': $q.dark.isActive }">
            <div class="q-pa-md">
              <q-form @submit="changePassword" class="q-gutter-md">
                <q-input
                  v-model="passwordForm.oldPassword"
                  type="password"
                  :label="t('settingsPage.currentPassword')"
                  dense
                  outlined
                  :rules="[val => val && val.length > 0 || t('validation.required')]"
                />
                <q-input
                  v-model="passwordForm.newPassword"
                  type="password"
                  :label="t('settingsPage.newPassword')"
                  dense
                  outlined
                  :rules="[val => val && val.length >= 6 || t('settingsPage.passwordRequirements')]"
                />
                <q-input
                  v-model="passwordForm.confirmPassword"
                  type="password"
                  :label="t('settingsPage.confirmNewPassword')"
                  dense
                  outlined
                  :rules="[
                    val => val && val.length > 0 || t('validation.required'),
                    val => val === passwordForm.newPassword || t('validation.passwordMismatch')
                  ]"
                />
                <div class="text-center">
                  <q-btn
                    type="submit"
                    color="primary"
                    :label="t('settingsPage.changePassword')"
                    :loading="changingPassword"
                  />
                </div>
              </q-form>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Dialogs -->
    <q-dialog v-model="showLinkEmail" persistent>
      <q-card style="min-width: 400px">
        <q-card-section>
          <div class="text-h6">{{ t('settingsPage.setEmail') }}</div>
        </q-card-section>
        <q-card-section class="q-pt-none">
          <q-input
            v-model="emailForm.email"
            :label="t('auth.email')"
            type="email"
            dense
            outlined
            :rules="[
              val => val && val.length > 0 || t('validation.required'),
              val => /.+@.+\..+/.test(val) || t('validation.invalidEmail')
            ]"
          />
        </q-card-section>
        <q-card-actions align="right">
          <q-btn flat :label="t('dialog.cancel')" @click="cancelEmailDialog" />
          <q-btn flat :label="t('settingsPage.resendVerification')" color="primary" @click="linkEmail" :loading="linkingEmail" />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <q-dialog v-model="showChangeEmail" persistent>
      <q-card style="min-width: 400px">
        <q-card-section>
          <div class="text-h6">{{ t('settingsPage.changeEmail') }}</div>
        </q-card-section>
        <q-card-section class="q-pt-none">
          <q-input
            v-model="emailForm.email"
            :label="t('settingsPage.currentEmail')"
            type="email"
            dense
            outlined
            :rules="[
              val => val && val.length > 0 || t('validation.required'),
              val => /.+@.+\..+/.test(val) || t('validation.invalidEmail')
            ]"
          />
        </q-card-section>
        <q-card-actions align="right">
          <q-btn flat :label="t('dialog.cancel')" @click="cancelEmailDialog" />
          <q-btn flat :label="t('settingsPage.resendVerification')" color="primary" @click="changeEmail" :loading="changingEmail" />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <q-dialog v-model="showUnlinkEmail" persistent>
      <q-card style="min-width: 400px">
        <q-card-section>
          <div class="text-h6">{{ t('settingsPage.unlinkEmail') }}</div>
          <div class="text-body2 q-mt-sm">{{ t('auth.password') }}</div>
        </q-card-section>
        <q-card-section class="q-pt-none">
          <q-input
            v-model="unlinkForm.password"
            type="password"
            :label="t('auth.password')"
            dense
            outlined
            :rules="[val => val && val.length > 0 || t('validation.required')]"
          />
        </q-card-section>
        <q-card-actions align="right">
          <q-btn flat :label="t('dialog.cancel')" @click="cancelUnlinkEmail" />
          <q-btn flat :label="t('dialog.confirm')" color="negative" @click="unlinkEmail" :loading="unlinkingEmail" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, getCurrentInstance } from 'vue'
import { useI18n } from 'vue-i18n'
import { useQuasar } from 'quasar'
import LanguageSwitcher from '../components/LanguageSwitcher.vue'
import { useAuth } from '../composables/useAuth'
import { useAudioPlayer } from '../composables/useAudioPlayer'
import { useNotification } from '../composables/useNotification'

defineOptions({
  name: 'SettingsPage'
})

const { t, locale } = useI18n()
const $q = useQuasar()
const { proxy } = getCurrentInstance()
const { user, isLoggedIn, setShowLoginDialog, refreshUser } = useAuth()
const { setRewindSeekTime, setForwardSeekTime } = useAudioPlayer()
const { showSuccessNotification, showWarningNotification, showErrorNotification } = useNotification()

// Reactive data
const rewindTime = ref(5)
const forwardTime = ref(30)
const passwordForm = ref({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})
const emailForm = ref({
  email: ''
})
const unlinkForm = ref({
  password: ''
})
const showLinkEmail = ref(false)
const showChangeEmail = ref(false)
const showUnlinkEmail = ref(false)
const changingPassword = ref(false)
const linkingEmail = ref(false)
const changingEmail = ref(false)
const unlinkingEmail = ref(false)
const resendingVerification = ref(false)

// Computed properties
const authEnabled = computed(() => isLoggedIn.value)

const seekTimeOptions = computed(() => [
  { label: `5 ${t('settingsPage.seconds')}`, value: 5 },
  { label: `10 ${t('settingsPage.seconds')}`, value: 10 },
  { label: `15 ${t('settingsPage.seconds')}`, value: 15 },
  { label: `30 ${t('settingsPage.seconds')}`, value: 30 }
])

const loadSeekTimeSettings = () => {
  // Load settings from localStorage or use defaults
  if ($q?.localStorage) {
    const rewindTimeValue = $q.localStorage.getItem('rewindSeekTime')
    if (rewindTimeValue !== null) {
      const parsedRewindTime = parseInt(rewindTimeValue)
      rewindTime.value = parsedRewindTime
      // Apply to audio player store
      setRewindSeekTime(parsedRewindTime)
    }

    const forwardTimeValue = $q.localStorage.getItem('forwardSeekTime')
    if (forwardTimeValue !== null) {
      const parsedForwardTime = parseInt(forwardTimeValue)
      forwardTime.value = parsedForwardTime
      // Apply to audio player store
      setForwardSeekTime(parsedForwardTime)
    }
  }
}

const saveRewindTime = (value) => {
  if ($q?.localStorage) {
    $q.localStorage.set('rewindSeekTime', value)
  }
  setRewindSeekTime(value)
  showSuccessNotification(t('settingsPage.saved'))
}

const saveForwardTime = (value) => {
  if ($q?.localStorage) {
    $q.localStorage.set('forwardSeekTime', value)
  }
  setForwardSeekTime(value)
  showSuccessNotification(t('settingsPage.saved'))
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString(locale.value)
}

const showLoginDialog = () => {
  setShowLoginDialog(true)
}

const changePassword = async () => {
  if (passwordForm.value.newPassword !== passwordForm.value.confirmPassword) {
    showWarningNotification(t('validation.passwordMismatch'))
    return
  }

  changingPassword.value = true
  try {
    await proxy.$api.post('/api/v1/me/password/change', {
      old_password: passwordForm.value.oldPassword,
      new_password: passwordForm.value.newPassword
    })

    showSuccessNotification(t('notification.passwordChanged'))
    passwordForm.value.oldPassword = ''
    passwordForm.value.newPassword = ''
    passwordForm.value.confirmPassword = ''
  } catch (error) {
    showErrorNotification(error.response?.data?.error || t('failed'))
  } finally {
    changingPassword.value = false
  }
}

const resendVerification = async () => {
  if (!user.value.email) return

  resendingVerification.value = true
  try {
    await proxy.$api.post('/api/v1/me/email/link', {
      email: user.value.email
    })
    showSuccessNotification(t('notification.emailSent'))
  } catch (error) {
    showErrorNotification(error.response?.data?.error || t('failed'))
  } finally {
    resendingVerification.value = false
  }
}

const unlinkEmail = async () => {
  if (!unlinkForm.value.password) return

  unlinkingEmail.value = true
  try {
    await proxy.$api.post('/api/v1/me/email/unlink', {
      password: unlinkForm.value.password
    })
    showSuccessNotification(t('success'))
    cancelUnlinkEmail()
    await refreshUser()
  } catch (error) {
    showErrorNotification(error.response?.data?.error || t('failed'))
  } finally {
    unlinkingEmail.value = false
  }
}

const linkEmail = async () => {
  if (!emailForm.value.email) {
    showWarningNotification(t('validation.required'))
    return
  }

  linkingEmail.value = true
  try {
    await proxy.$api.post('/api/v1/me/email/link', {
      email: emailForm.value.email
    })
    showSuccessNotification(t('notification.emailSent'))
    cancelEmailDialog()
    await refreshUser()
  } catch (error) {
    showErrorNotification(error.response?.data?.error || t('failed'))
  } finally {
    linkingEmail.value = false
  }
}

const changeEmail = async () => {
  if (!emailForm.value.email) {
    showWarningNotification(t('validation.required'))
    return
  }

  changingEmail.value = true
  try {
    await proxy.$api.post('/api/v1/me/email/change', {
      email: emailForm.value.email
    })
    showSuccessNotification(t('notification.emailSent'))
    cancelEmailDialog()
    await refreshUser()
  } catch (error) {
    showErrorNotification(error.response?.data?.error || t('failed'))
  } finally {
    changingEmail.value = false
  }
}

const cancelEmailDialog = () => {
  showLinkEmail.value = false
  showChangeEmail.value = false
  emailForm.value.email = ''
}

const cancelUnlinkEmail = () => {
  showUnlinkEmail.value = false
  unlinkForm.value.password = ''
}

// Lifecycle
onMounted(() => {
  loadSeekTimeSettings()
})
</script>
