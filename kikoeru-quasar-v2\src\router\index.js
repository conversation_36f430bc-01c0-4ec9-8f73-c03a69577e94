import { defineRouter } from '#q-app/wrappers'
import { createRouter, createM<PERSON>oryHistory, createWebHistory, createWebHashHistory } from 'vue-router'
import { LocalStorage } from 'quasar'
import routes from './routes'

/*
 * If not building with SSR mode, you can
 * directly export the Router instantiation;
 *
 * The function below can be async too; either use
 * async/await or return a Promise which resolves
 * with the Router instance.
 */

export default defineRouter(function ({ store }) {
  const createHistory = process.env.SERVER
    ? createMemoryHistory
    : (process.env.VUE_ROUTER_MODE === 'history' ? createWebHistory : createWebHashHistory)

  const Router = createRouter({
    scrollBehavior: () => ({ left: 0, top: 0 }),
    routes,

    // Leave this as is and make changes in quasar.conf.js instead!
    // quasar.conf.js -> build -> vueRouterMode
    // quasar.conf.js -> build -> publicPath
    history: createHistory(process.env.VUE_ROUTER_BASE)
  })

  // Add navigation guards
  Router.beforeEach((to, from, next) => {
    // Safe check for store availability
    if (!store || !store.getters) {
      next()
      return
    }

    const isAuthenticated = store.getters['User/isLoggedIn']
    const token = LocalStorage.getItem('jwt-token')
    const isAdmin = store.getters['User/isAdmin']

    // Check if route requires authentication
    if (to.matched.some(record => record.meta.auth)) {
      if (!isAuthenticated || !token) {
        store.commit('User/SHOW_LOGIN_DIALOG')
        next(false) // Prevent navigation and stay on current page
        return
      }
    }

    // Check if route requires admin privileges
    if (to.matched.some(record => record.meta.requiresAdmin)) {
      if (!isAdmin) {
        next('/')
        return
      }
    }

    next()
  })

  return Router
})
