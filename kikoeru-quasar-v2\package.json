{"name": "kikoeru-quasar-v2", "version": "0.0.1", "description": "Ki<PERSON><PERSON><PERSON>", "productName": "Ki<PERSON><PERSON><PERSON>", "author": "<PERSON>-Byte <42319937+<PERSON>-<EMAIL>>", "type": "module", "private": true, "scripts": {"lint": "eslint -c ./eslint.config.js \"./src*/**/*.{js,cjs,mjs,vue}\"", "format": "prettier --write \"**/*.{js,vue,scss,html,md,json}\" --ignore-path .gitignore", "test": "echo \"No test specified\" && exit 0", "dev": "quasar dev", "build": "quasar build", "postinstall": "quasar prepare"}, "dependencies": {"@quasar/extras": "^1.16.4", "@vueform/slider": "^2.1.10", "axios": "^1.2.1", "lrc-file-parser": "^2.4.1", "mitt": "^3.0.1", "pinia": "^3.0.2", "quasar": "^2.16.0", "register-service-worker": "^1.7.2", "vue": "^3.4.18", "vue-i18n": "^11.0.0", "vue-router": "^4.0.0", "vuedraggable": "^4.1.0", "workbox-build": "^7.3.0", "workbox-cacheable-response": "^7.0.0", "workbox-core": "^7.0.0", "workbox-expiration": "^7.0.0", "workbox-precaching": "^7.0.0", "workbox-routing": "^7.0.0", "workbox-strategies": "^7.0.0"}, "devDependencies": {"@eslint/js": "^9.14.0", "@intlify/unplugin-vue-i18n": "^4.0.0", "@quasar/app-vite": "^2.1.0", "@vue/eslint-config-prettier": "^10.1.0", "autoprefixer": "^10.4.2", "eslint": "^9.14.0", "eslint-plugin-vue": "^9.30.0", "globals": "^15.12.0", "postcss": "^8.4.14", "prettier": "^3.3.3", "vite-plugin-checker": "^0.9.0"}, "engines": {"node": "^28 || ^26 || ^24 || ^22 || ^20 || ^18", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}}