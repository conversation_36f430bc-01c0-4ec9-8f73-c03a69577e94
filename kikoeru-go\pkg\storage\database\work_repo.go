package database

import (
	"context"
	"errors"
	"fmt"
	"strings"

	// "time" // Removed unused import

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
	common_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/common"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"gorm.io/gorm"
)

// ListWorksParams defines parameters for listing works.
type ListWorksParams struct {
	common_dto.PaginationParams
	Title               string   `form:"title"`
	OriginalID          string   `form:"original_id"`
	WorkType            string   `form:"work_type"`
	Language            string   `form:"language"`
	AgeRating           string   `form:"age_rating"`
	ExcludeAgeRating    string   `form:"exclude_age_rating"`
	LyricStatus         *string  `form:"lyric_status"`
	MinRateAverage2DP   *float64 `form:"min_rate_average_2dp"`
	MaxRateAverage2DP   *float64 `form:"max_rate_average_2dp"`
	MinReleaseDate      string   `form:"min_release_date"`
	MaxReleaseDate      string   `form:"max_release_date"`
	StorageID           *uint    `form:"storage_id"`
	PathInStorage       string   `form:"path_in_storage"`
	MinPrice            *int64   `form:"min_price"`
	MaxPrice            *int64   `form:"max_price"`
	MinSell             *int64   `form:"min_sell"`
	MaxSell             *int64   `form:"max_sell"`
	MinDuration         *int64   `form:"min_duration"`
	MaxDuration         *int64   `form:"max_duration"`
	ExcludeLanguage     string   `form:"exclude_language"`
	AdvancedSearchQuery string   `form:"advanced_search_query"`
	PlainTextTerms      []string // 存储纯文本搜索词，用于AND逻辑搜索

	// Frontend sends names, these are converted to IDs internally
	IncludeTagNames    []string `form:"include_tags"`
	ExcludeTagNames    []string `form:"exclude_tags"`
	IncludeVANames     []string `form:"include_vas"`
	ExcludeVANames     []string `form:"exclude_vas"`
	IncludeCircleNames []string `form:"include_circles"`
	ExcludeCircleNames []string `form:"exclude_circles"`

	// Internal fields used after name-to-ID conversion
	IncludeTagIDs    []string
	ExcludeTagIDs    []string
	IncludeVAIDs     []string
	ExcludeVAIDs     []string
	IncludeCircleIDs []string
	ExcludeCircleIDs []string
}

// WorkRepository defines the repository interface for works.
type WorkRepository interface {
	Create(ctx context.Context, work *models.Work) error
	GetByID(ctx context.Context, id uint) (*models.Work, error)
	GetByOriginalID(ctx context.Context, originalID string) (*models.Work, error)
	GetByPathInStorage(ctx context.Context, storageID uint, pathInStorage string) (*models.Work, error)
	List(ctx context.Context, params ListWorksParams) ([]*models.Work, int64, error)
	Update(ctx context.Context, work *models.Work) error
	Delete(ctx context.Context, id uint) error
	GetRandomWorks(ctx context.Context, limit int, includeTagIDs, excludeTagIDs []string, includeVAIDs, excludeVAIDs []string, includeCircleIDs, excludeCircleIDs []string, workType *string, minRating *float64) ([]*models.Work, error)
	AddTagToWork(ctx context.Context, workID uint, tagID string) error
	RemoveTagFromWork(ctx context.Context, workID uint, tagID string) error
	ReplaceWorkTags(ctx context.Context, workID uint, tagIDs []string) error
	AddVAToWork(ctx context.Context, workID uint, vaID string) error
	RemoveVAFromWork(ctx context.Context, workID uint, vaID string) error
	ReplaceWorkVAs(ctx context.Context, workID uint, vaIDs []string) error
	GetWorkTags(ctx context.Context, workID uint) ([]*models.Tag, error)
	GetWorkVAs(ctx context.Context, workID uint) ([]*models.VA, error)
	GetWorksByIDs(ctx context.Context, workIDs []uint) ([]*models.Work, error)
}

type workRepository struct {
	db *gorm.DB
}

func NewWorkRepository(db *gorm.DB) WorkRepository {
	return &workRepository{db: db}
}

func (r *workRepository) Create(ctx context.Context, work *models.Work) error {
	if work == nil {
		return errors.New("work cannot be nil")
	}
	// ID and timestamps are set by GORM hooks.
	// OriginalID and PathInStorage + StorageID should be unique.
	// Create will fail if these constraints are violated.
	result := r.db.WithContext(ctx).Create(work)
	if result.Error != nil {
		// TODO: Check for specific DB errors like unique constraint violation
		// and return a more specific error like service.ErrWorkAlreadyExists
		return fmt.Errorf("failed to create work in DB: %w", result.Error)
	}
	return nil
}

func (r *workRepository) GetByID(ctx context.Context, id uint) (*models.Work, error) {
	var work models.Work
	if id == 0 {
		return nil, errors.New("work ID cannot be 0")
	}
	result := r.db.WithContext(ctx).Preload("Circle").Preload("Tags").Preload("VAs").Where("id = ?", id).First(&work)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, apperrors.ErrWorkNotFound
		}
		return nil, result.Error
	}
	return &work, nil
}

func (r *workRepository) GetByOriginalID(ctx context.Context, originalID string) (*models.Work, error) {
	var work models.Work
	if originalID == "" {
		return nil, errors.New("original ID cannot be empty")
	}
	result := r.db.WithContext(ctx).Preload("Circle").Preload("Tags").Preload("VAs").Where("original_id = ?", originalID).First(&work)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, apperrors.ErrWorkNotFound
		}
		return nil, result.Error
	}
	return &work, nil
}

func (r *workRepository) GetByPathInStorage(ctx context.Context, storageID uint, pathInStorage string) (*models.Work, error) {
	var work models.Work
	if storageID == 0 || pathInStorage == "" {
		return nil, errors.New("storage ID and path in storage cannot be empty")
	}
	result := r.db.WithContext(ctx).Preload("Circle").Preload("Tags").Preload("VAs").Where("storage_id = ? AND path_in_storage = ?", storageID, pathInStorage).First(&work)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, apperrors.ErrWorkNotFound
		}
		return nil, result.Error
	}
	return &work, nil
}

func (r *workRepository) List(ctx context.Context, params ListWorksParams) ([]*models.Work, int64, error) {
	query := r.db.WithContext(ctx).Model(&models.Work{}).Preload("Circle").Preload("Tags").Preload("VAs")

	if params.Title != "" {
		query = query.Where("LOWER(title) LIKE LOWER(?)", "%"+params.Title+"%")
	}

	// 添加对PlainTextTerms的处理，使用AND逻辑
	if len(params.PlainTextTerms) > 0 {
		for _, term := range params.PlainTextTerms {
			query = query.Where("LOWER(title) LIKE LOWER(?)", "%"+term+"%")
		}
	}

	if params.OriginalID != "" {
		query = query.Where("LOWER(original_id) LIKE LOWER(?)", "%"+params.OriginalID+"%")
	}
	if params.WorkType != "" {
		query = query.Where("work_type = ?", params.WorkType)
	}
	if params.Language != "" {
		query = query.Where("language = ?", params.Language)
	}
	if params.ExcludeLanguage != "" {
		query = query.Where("language != ?", params.ExcludeLanguage)
	}
	if params.AgeRating != "" {
		query = query.Where("age_rating = ?", params.AgeRating)
	}
	if params.ExcludeAgeRating != "" {
		query = query.Where("age_rating != ?", params.ExcludeAgeRating)
	}
	if params.LyricStatus != nil {
		query = query.Where("lyric_status = ?", *params.LyricStatus)
	}
	if params.MinRateAverage2DP != nil {
		query = query.Where("rate_average_2dp >= ?", *params.MinRateAverage2DP)
	}
	if params.MaxRateAverage2DP != nil {
		query = query.Where("rate_average_2dp <= ?", *params.MaxRateAverage2DP)
	}
	if params.MinReleaseDate != "" {
		query = query.Where("release_date >= ?", params.MinReleaseDate)
	}
	if params.MaxReleaseDate != "" {
		query = query.Where("release_date <= ?", params.MaxReleaseDate)
	}
	if params.MinPrice != nil {
		query = query.Where("price >= ?", *params.MinPrice)
	}
	if params.MaxPrice != nil {
		query = query.Where("price <= ?", *params.MaxPrice)
	}
	if params.MinSell != nil {
		query = query.Where("dl_count >= ?", *params.MinSell)
	}
	if params.MaxSell != nil {
		query = query.Where("dl_count <= ?", *params.MaxSell)
	}
	if params.MinDuration != nil {
		query = query.Where("duration >= ?", *params.MinDuration)
	}
	if params.MaxDuration != nil {
		query = query.Where("duration <= ?", *params.MaxDuration)
	}
	if params.StorageID != nil {
		query = query.Where("storage_id = ?", *params.StorageID)
	}
	if params.PathInStorage != "" {
		query = query.Where("LOWER(path_in_storage) LIKE LOWER(?)", "%"+params.PathInStorage+"%")
	}

	// Tag filtering
	if len(params.IncludeTagIDs) > 0 {
		query = query.Joins("JOIN r_work_tags ON t_work.id = r_work_tags.work_id").
			Where("r_work_tags.tag_id IN (?)", params.IncludeTagIDs).
			Group("t_work.id").                                                         // Group by work ID to count distinct tags
			Having("COUNT(DISTINCT r_work_tags.tag_id) = ?", len(params.IncludeTagIDs)) // Ensure all included tags are present
	}
	if len(params.ExcludeTagIDs) > 0 {
		query = query.Where("t_work.id NOT IN (?)", r.db.Model(&models.Work{}).
			Select("t_work.id").
			Joins("JOIN r_work_tags ON t_work.id = r_work_tags.work_id").
			Where("r_work_tags.tag_id IN (?)", params.ExcludeTagIDs).
			Group("t_work.id"),
		)
	}

	// VA filtering
	if len(params.IncludeVAIDs) > 0 {
		query = query.Joins("JOIN r_work_vas ON t_work.id = r_work_vas.work_id").
			Where("r_work_vas.va_id IN (?)", params.IncludeVAIDs).
			Group("t_work.id").
			Having("COUNT(DISTINCT r_work_vas.va_id) = ?", len(params.IncludeVAIDs))
	}
	if len(params.ExcludeVAIDs) > 0 {
		query = query.Where("t_work.id NOT IN (?)", r.db.Model(&models.Work{}).
			Select("t_work.id").
			Joins("JOIN r_work_vas ON t_work.id = r_work_vas.work_id").
			Where("r_work_vas.va_id IN (?)", params.ExcludeVAIDs).
			Group("t_work.id"),
		)
	}

	// Circle filtering
	if len(params.IncludeCircleIDs) > 0 {
		query = query.Where("circle_id IN (?)", params.IncludeCircleIDs)
	}
	if len(params.ExcludeCircleIDs) > 0 {
		query = query.Where("circle_id NOT IN (?)", params.ExcludeCircleIDs)
	}

	var totalCount int64
	if errCount := query.Count(&totalCount).Error; errCount != nil {
		return nil, 0, fmt.Errorf("failed to count works: %w", errCount)
	}

	// Apply sorting
	if params.SortBy != "" {
		order := params.SortBy
		if params.SortOrder != "" && (strings.ToLower(params.SortOrder) == "asc" || strings.ToLower(params.SortOrder) == "desc") {
			order += " " + strings.ToLower(params.SortOrder)
		} else {
			order += " asc" // Default to ascending if SortOrder is invalid or empty
		}
		query = query.Order(order)
	} else {
		query = query.Order("id asc") // Default sort if SortBy is not provided
	}

	// Apply pagination
	if params.Page > 0 && params.PageSize > 0 {
		offset := (params.Page - 1) * params.PageSize
		query = query.Offset(offset).Limit(params.PageSize)
	}

	var works []*models.Work
	if errFind := query.Find(&works).Error; errFind != nil {
		return nil, 0, fmt.Errorf("failed to find works: %w", errFind)
	}

	return works, totalCount, nil
}

func (r *workRepository) Update(ctx context.Context, work *models.Work) error {
	if work == nil || work.ID == 0 {
		return errors.New("work for update must not be nil and must have an ID")
	}
	// Use Save for update, it handles both create and update based on primary key presence.
	// We expect the work object to have its ID set for updates.
	result := r.db.WithContext(ctx).Save(work)
	return result.Error
}

func (r *workRepository) Delete(ctx context.Context, id uint) error {
	if id == 0 {
		return errors.New("work ID cannot be 0 for deletion")
	}
	// GORM's Delete with a primary key value will only delete if the record exists.
	// We check RowsAffected to confirm deletion.
	result := r.db.WithContext(ctx).Where("id = ?", id).Delete(&models.Work{})
	if result.Error != nil {
		return fmt.Errorf("failed to delete work %d: %w", id, result.Error)
	}
	if result.RowsAffected == 0 {
		return apperrors.ErrWorkNotFound
	}
	return nil
}

func (r *workRepository) GetRandomWorks(ctx context.Context, limit int, includeTagIDs, excludeTagIDs []string, includeVAIDs, excludeVAIDs []string, includeCircleIDs, excludeCircleIDs []string, workType *string, minRating *float64) ([]*models.Work, error) {
	query := r.db.WithContext(ctx).Model(&models.Work{}).Preload("Circle").Preload("Tags").Preload("VAs")

	if workType != nil && *workType != "" {
		query = query.Where("work_type = ?", *workType)
	}
	if minRating != nil {
		query = query.Where("rate_average_2dp >= ?", *minRating)
	}

	// Tag filtering
	if len(includeTagIDs) > 0 {
		query = query.Joins("JOIN r_work_tags ON t_work.id = r_work_tags.work_id").
			Where("r_work_tags.tag_id IN (?)", includeTagIDs).
			Group("t_work.id").                                                  // Group by work ID to count distinct tags
			Having("COUNT(DISTINCT r_work_tags.tag_id) = ?", len(includeTagIDs)) // Ensure all included tags are present
	}
	if len(excludeTagIDs) > 0 {
		query = query.Where("t_work.id NOT IN (?)", r.db.Model(&models.Work{}).
			Select("t_work.id").
			Joins("JOIN r_work_tags ON t_work.id = r_work_tags.work_id").
			Where("r_work_tags.tag_id IN (?)", excludeTagIDs).
			Group("t_work.id"),
		)
	}

	// VA filtering
	if len(includeVAIDs) > 0 {
		query = query.Joins("JOIN r_work_vas ON t_work.id = r_work_vas.work_id").
			Where("r_work_vas.va_id IN (?)", includeVAIDs).
			Group("t_work.id").                                               // Group by work ID to count distinct VAs
			Having("COUNT(DISTINCT r_work_vas.va_id) = ?", len(includeVAIDs)) // Ensure all included VAs are present
	}
	if len(excludeVAIDs) > 0 {
		query = query.Where("t_work.id NOT IN (?)", r.db.Model(&models.Work{}).
			Select("t_work.id").
			Joins("JOIN r_work_vas ON t_work.id = r_work_vas.work_id").
			Where("r_work_vas.va_id IN (?)", excludeVAIDs).
			Group("t_work.id"),
		)
	}

	// Circle filtering
	if len(includeCircleIDs) > 0 {
		query = query.Where("circle_id IN (?)", includeCircleIDs)
	}
	if len(excludeCircleIDs) > 0 {
		query = query.Where("circle_id NOT IN (?)", excludeCircleIDs)
	}

	// Order by random
	// Check the dialect to determine which random function to use
	dialectName := r.db.Dialector.Name()
	if dialectName == "sqlite" {
		query = query.Order("RANDOM()")
	} else {
		// For MySQL and other databases
		query = query.Order("RAND()")
	}

	// Apply limit
	if limit > 0 {
		query = query.Limit(limit)
	}

	var works []*models.Work
	if err := query.Find(&works).Error; err != nil {
		return nil, fmt.Errorf("failed to get random works: %w", err)
	}

	return works, nil
}

func (r *workRepository) AddTagToWork(ctx context.Context, workID uint, tagID string) error {
	if workID == 0 || tagID == "" {
		return errors.New("work ID and tag ID cannot be empty")
	}
	// Check if association already exists
	var count int64
	// Corrected: Query the join table directly
	if err := r.db.WithContext(ctx).Model(&models.WorkTag{}).Where("work_id = ? AND tag_id = ?", workID, tagID).Count(&count).Error; err != nil {
		return fmt.Errorf("failed to count existing tag association: %w", err)
	}
	if count > 0 {
		return nil // Association already exists
	}

	// Add association
	work := models.Work{ID: workID}
	tag := models.Tag{ID: tagID}
	return r.db.WithContext(ctx).Model(&work).Association("Tags").Append(&tag)
}

func (r *workRepository) RemoveTagFromWork(ctx context.Context, workID uint, tagID string) error {
	if workID == 0 || tagID == "" {
		return errors.New("work ID and tag ID cannot be empty")
	}
	work := models.Work{ID: workID}
	tag := models.Tag{ID: tagID}
	return r.db.WithContext(ctx).Model(&work).Association("Tags").Delete(&tag)
}

func (r *workRepository) ReplaceWorkTags(ctx context.Context, workID uint, tagIDs []string) error {
	if workID == 0 {
		return errors.New("work ID cannot be 0")
	}

	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Delete existing associations
		if err := tx.Where("work_id = ?", workID).Delete(&models.WorkTag{}).Error; err != nil {
			return fmt.Errorf("failed to delete existing work tags: %w", err)
		}

		// Insert new associations if there are any
		if len(tagIDs) > 0 {
			var workTags []models.WorkTag
			for _, tagID := range tagIDs {
				workTags = append(workTags, models.WorkTag{
					WorkID: workID,
					TagID:  tagID,
				})
			}
			if err := tx.Create(&workTags).Error; err != nil {
				return fmt.Errorf("failed to create new work tag associations: %w", err)
			}
		}

		return nil
	})
}

func (r *workRepository) AddVAToWork(ctx context.Context, workID uint, vaID string) error {
	if workID == 0 || vaID == "" {
		return errors.New("work ID and VA ID cannot be empty")
	}
	// Check if association already exists
	var count int64
	// Corrected: Query the join table directly
	if err := r.db.WithContext(ctx).Model(&models.WorkVA{}).Where("work_id = ? AND va_id = ?", workID, vaID).Count(&count).Error; err != nil {
		return fmt.Errorf("failed to count existing VA association: %w", err)
	}
	if count > 0 {
		return nil // Association already exists
	}

	// Add association
	work := models.Work{ID: workID}
	va := models.VA{ID: vaID}
	return r.db.WithContext(ctx).Model(&work).Association("VAs").Append(&va)
}

func (r *workRepository) RemoveVAFromWork(ctx context.Context, workID uint, vaID string) error {
	if workID == 0 || vaID == "" {
		return errors.New("work ID and VA ID cannot be empty")
	}
	work := models.Work{ID: workID}
	va := models.VA{ID: vaID}
	return r.db.WithContext(ctx).Model(&work).Association("VAs").Delete(&va)
}

func (r *workRepository) ReplaceWorkVAs(ctx context.Context, workID uint, vaIDs []string) error {
	if workID == 0 {
		return errors.New("work ID cannot be 0")
	}

	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Delete existing associations
		if err := tx.Where("work_id = ?", workID).Delete(&models.WorkVA{}).Error; err != nil {
			return fmt.Errorf("failed to delete existing work VAs: %w", err)
		}

		// Insert new associations if there are any
		if len(vaIDs) > 0 {
			var workVAs []models.WorkVA
			for _, vaID := range vaIDs {
				workVAs = append(workVAs, models.WorkVA{
					WorkID: workID,
					VAID:   vaID,
				})
			}
			if err := tx.Create(&workVAs).Error; err != nil {
				return fmt.Errorf("failed to create new work VA associations: %w", err)
			}
		}

		return nil
	})
}

func (r *workRepository) GetWorkTags(ctx context.Context, workID uint) ([]*models.Tag, error) {
	var tags []*models.Tag
	if workID == 0 {
		return nil, errors.New("work ID cannot be 0 for getting tags")
	}
	work := models.Work{ID: workID}
	if err := r.db.WithContext(ctx).Model(&work).Association("Tags").Find(&tags); err != nil {
		return nil, fmt.Errorf("failed to get tags for work %d: %w", workID, err)
	}
	return tags, nil
}

func (r *workRepository) GetWorkVAs(ctx context.Context, workID uint) ([]*models.VA, error) {
	var vas []*models.VA
	if workID == 0 {
		return nil, errors.New("work ID cannot be 0 for getting VAs")
	}
	work := models.Work{ID: workID}
	if err := r.db.WithContext(ctx).Model(&work).Association("VAs").Find(&vas); err != nil {
		return nil, fmt.Errorf("failed to get VAs for work %d: %w", workID, err)
	}
	return vas, nil
}

func (r *workRepository) GetWorksByIDs(ctx context.Context, workIDs []uint) ([]*models.Work, error) {
	var works []*models.Work
	if len(workIDs) == 0 {
		return []*models.Work{}, nil
	}
	result := r.db.WithContext(ctx).Preload("Circle").Preload("Tags").Preload("VAs").Where("id IN (?)", workIDs).Find(&works)
	if result.Error != nil {
		return nil, fmt.Errorf("failed to get works by IDs: %w", result.Error)
	}
	return works, nil
}
