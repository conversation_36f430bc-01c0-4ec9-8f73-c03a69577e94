package handler

import (
	"errors"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
	dto_review "github.com/Sakura-Byte/kikoeru-go/pkg/dto/review" // Added DTO import
	"github.com/Sakura-Byte/kikoeru-go/pkg/http/middleware"
	"github.com/Sakura-Byte/kikoeru-go/pkg/http/rest/common"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"github.com/Sakura-Byte/kikoeru-go/pkg/ports" // Import ports package
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/database"
	"github.com/gin-gonic/gin"
	// "github.com/gin-gonic/gin/binding" // Removed unused import
)

type ReviewHandler struct {
	reviewService ports.ReviewService // Changed to ports.ReviewService
}

func NewReviewHandler(reviewService ports.ReviewService) *ReviewHandler { // Changed to ports.ReviewService
	return &ReviewHandler{reviewService: reviewService}
}

// PutReviewRequest is now defined in dto_review package

// PutReview godoc
// @Summary Create or update a review for a work
// @Description Creates a new review or updates an existing one for the specified work by the authenticated user.
// @Tags review
// @Accept json
// @Produce json
// @Param review_request body PutReviewRequest true "Review details"
// @Success 200 {object} models.Review "Successfully created or updated review" // TODO: Use DTO
// @Success 201 {object} models.Review "Successfully created review (if new)" // TODO: Use DTO
// @Failure 400 {object} common.ErrorResponse "Invalid request payload, work ID, rating, or progress"
// @Failure 401 {object} common.ErrorResponse "Unauthorized"
// @Failure 403 {object} common.ErrorResponse "Forbidden - Guest users cannot submit reviews"
// @Failure 404 {object} common.ErrorResponse "Work not found"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /review [put]
// @Security BearerAuth
func (h *ReviewHandler) PutReview(c *gin.Context) {
	userClaims := middleware.GetUserClaims(c)
	if userClaims == nil || userClaims.UserID == "" {
		common.SendErrorResponse(c, http.StatusUnauthorized, "Authentication required: User claims not found.")
		return
	}
	if userClaims.UserGroup == models.UserGroupGuest {
		common.SendErrorResponse(c, http.StatusForbidden, "Guest users cannot submit reviews.")
		return
	}
	userID := userClaims.UserID

	var req dto_review.PutReviewRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid request payload: "+err.Error())
		return
	}

	if strings.TrimSpace(req.WorkID) == "" {
		common.SendErrorResponse(c, http.StatusBadRequest, "work_id is required.")
		return
	}

	review, err := h.reviewService.PutReview(c.Request.Context(), userID, req)
	if err != nil {
		var valErr *apperrors.ValidationError
		if errors.As(err, &valErr) {
			common.SendErrorResponse(c, http.StatusBadRequest, valErr.Error())
		} else if errors.Is(err, apperrors.ErrWorkNotFound) {
			common.SendErrorResponse(c, http.StatusNotFound, err.Error())
		} else {
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to save review: "+err.Error())
		}
		return
	}

	if review.CreatedAt.Equal(review.UpdatedAt) || review.UpdatedAt.Sub(review.CreatedAt) < 2*time.Second {
		c.JSON(http.StatusCreated, review)
	} else {
		c.JSON(http.StatusOK, review)
	}
}

// GetReviews godoc
// @Summary List user's reviews
// @Description Retrieves a paginated list of reviews by the currently authenticated user, with sorting and filtering.
// @Tags review
// @Produce json
// @Param page query int false "Page number for pagination" default(1)
// @Param pageSize query int false "Number of items per page" default(20)
// @Param order query string false "Sort order field: updated_at, userRating, release, review_count, sell_count" default(updated_at)
// @Param sort query string false "Sort direction: asc, desc" default(desc)
// @Param filter query string false "Filter by progress: marked, listening, listened, replay, postponed"
// @Success 200 {object} common.PaginatedResponse{data=[]dto_review.ReviewResponseItem} "A list of reviews" // Changed to dto_review.ReviewResponseItem
// @Failure 400 {object} common.ErrorResponse "Invalid query parameters"
// @Failure 401 {object} common.ErrorResponse "Unauthorized"
// @Failure 403 {object} common.ErrorResponse "Forbidden - Guest users cannot list reviews"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /review [get]
// @Security BearerAuth
func (h *ReviewHandler) GetReviews(c *gin.Context) {
	userClaims := middleware.GetUserClaims(c)
	if userClaims == nil || userClaims.UserID == "" {
		common.SendErrorResponse(c, http.StatusUnauthorized, "Authentication required: User claims not found.")
		return
	}
	if userClaims.UserGroup == models.UserGroupGuest {
		common.SendErrorResponse(c, http.StatusForbidden, "Guest users cannot list reviews.")
		return
	}
	userID := userClaims.UserID

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "20"))
	order := c.DefaultQuery("order", "updated_at")
	sortDir := c.DefaultQuery("sort", "desc")
	filterProgress := c.DefaultQuery("filter", "")

	params := database.ListReviewParams{
		Page:           page,
		PageSize:       pageSize,
		Order:          order,
		Sort:           sortDir,
		FilterProgress: filterProgress,
		UserID:         userID,
	}

	reviews, totalCount, err := h.reviewService.GetUserReviews(c.Request.Context(), userID, params)
	if err != nil {
		var valErr *apperrors.ValidationError
		if errors.As(err, &valErr) {
			common.SendErrorResponse(c, http.StatusBadRequest, valErr.Error())
		} else {
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to list reviews: "+err.Error())
		}
		return
	}
	common.SendPaginatedResponse(c, http.StatusOK, reviews, totalCount, params.Page, params.PageSize)
}

// GetUserReviewForWork godoc
// @Summary Get user's review for a specific work
// @Description Retrieves the authenticated user's review for a specific work by work original ID
// @Tags review
// @Produce json
// @Param originalID path string true "Work original ID (RJID)"
// @Success 200 {object} models.Review "User's review for the work"
// @Failure 401 {object} common.ErrorResponse "Unauthorized"
// @Failure 403 {object} common.ErrorResponse "Forbidden - Guest users cannot access reviews"
// @Failure 404 {object} common.ErrorResponse "Review not found"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /review/work/{originalID} [get]
// @Security BearerAuth
func (h *ReviewHandler) GetUserReviewForWork(c *gin.Context) {
	userClaims := middleware.GetUserClaims(c)
	if userClaims == nil || userClaims.UserID == "" {
		common.SendErrorResponse(c, http.StatusUnauthorized, "Authentication required: User claims not found.")
		return
	}
	if userClaims.UserGroup == models.UserGroupGuest {
		common.SendErrorResponse(c, http.StatusForbidden, "Guest users cannot access reviews.")
		return
	}
	userID := userClaims.UserID

	originalID := c.Param("originalID")
	if originalID == "" {
		common.SendErrorResponse(c, http.StatusBadRequest, "Work original ID is required")
		return
	}

	review, err := h.reviewService.GetUserReviewForWork(c.Request.Context(), userID, originalID)
	if err != nil {
		if errors.Is(err, apperrors.ErrReviewNotFound) {
			common.SendErrorResponse(c, http.StatusNotFound, "Review not found")
		} else {
			var valErr *apperrors.ValidationError
			if errors.As(err, &valErr) {
				common.SendErrorResponse(c, http.StatusBadRequest, valErr.Error())
			} else {
				common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to get review: "+err.Error())
			}
		}
		return
	}

	c.JSON(http.StatusOK, review)
}

// DeleteReview godoc
// @Summary Delete a review
// @Description Deletes a review for a work by the currently authenticated user.
// @Tags review
// @Produce json
// @Param workID path string true "Work ID (RJID)"
// @Success 200 {object} common.SuccessResponse "Successfully deleted review"
// @Failure 400 {object} common.ErrorResponse "Invalid work ID format"
// @Failure 401 {object} common.ErrorResponse "Unauthorized"
// @Failure 403 {object} common.ErrorResponse "Forbidden - Guest users cannot delete reviews"
// @Failure 404 {object} common.ErrorResponse "Review not found"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /review/{workID} [delete]
// @Security BearerAuth
func (h *ReviewHandler) DeleteReview(c *gin.Context) {
	userClaims := middleware.GetUserClaims(c)
	if userClaims == nil || userClaims.UserID == "" {
		common.SendErrorResponse(c, http.StatusUnauthorized, "Authentication required: User claims not found.")
		return
	}
	if userClaims.UserGroup == models.UserGroupGuest {
		common.SendErrorResponse(c, http.StatusForbidden, "Guest users cannot delete reviews.")
		return
	}
	userID := userClaims.UserID

	workID := c.Param("workID")
	if workID == "" {
		common.SendErrorResponse(c, http.StatusBadRequest, "work_id is required.")
		return
	}

	err := h.reviewService.DeleteReview(c.Request.Context(), userID, workID)
	if err != nil {
		if errors.Is(err, apperrors.ErrReviewNotFound) {
			common.SendErrorResponse(c, http.StatusNotFound, err.Error())
		} else {
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to delete review: "+err.Error())
		}
		return
	}
	common.SendSuccessResponse(c, http.StatusOK, "Review deleted successfully")
}
