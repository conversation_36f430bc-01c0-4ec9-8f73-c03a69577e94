package handler

import (
	"io"
	"net/http"
	"strconv"

	"github.com/Sakura-Byte/kikoeru-go/pkg/http/middleware"
	"github.com/Sakura-Byte/kikoeru-go/pkg/http/rest/common"
	"github.com/Sakura-Byte/kikoeru-go/pkg/log"
	"github.com/Sakura-Byte/kikoeru-go/pkg/ports"
	"github.com/gin-gonic/gin"
)

// SubtitleHandler handles subtitle-related API requests
type SubtitleHandler struct {
	service ports.SubtitleService
}

// NewSubtitleHandler creates a new SubtitleHandler
func NewSubtitleHandler(service ports.SubtitleService) *SubtitleHandler {
	return &SubtitleHandler{
		service: service,
	}
}

// UploadSubtitle handles POST /api/v1/subtitles
func (h *SubtitleHandler) UploadSubtitle(c *gin.Context) {
	userClaims := middleware.GetUserClaims(c)
	if userClaims == nil {
		common.SendErrorResponse(c, http.StatusUnauthorized, "Unauthorized")
		return
	}

	// Check if user is a guest
	if userClaims.UserGroup == "guest" {
		common.SendErrorResponse(c, http.StatusForbidden, "Guest users are not allowed to upload subtitles")
		return
	}

	// Parse multipart form
	if err := c.Request.ParseMultipartForm(10 << 20); err != nil { // 10 MB limit
		log.Error(c.Request.Context(), "Failed to parse multipart form", "error", err)
		common.SendErrorResponse(c, http.StatusBadRequest, "Failed to parse form data")
		return
	}

	// Get subtitle file
	file, header, err := c.Request.FormFile("subtitle_file")
	if err != nil {
		log.Error(c.Request.Context(), "Failed to get subtitle file", "error", err)
		common.SendErrorResponse(c, http.StatusBadRequest, "Subtitle file is required")
		return
	}
	defer file.Close()

	// Parse other form fields
	var req ports.SubtitleUploadRequest
	if err := c.ShouldBind(&req); err != nil {
		log.Error(c.Request.Context(), "Failed to bind subtitle upload request", "error", err)
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid request data")
		return
	}

	// Create subtitle
	info, err := h.service.UploadSubtitle(c.Request.Context(), userClaims.UserID, header, req)
	if err != nil {
		log.Error(c.Request.Context(), "Failed to upload subtitle", "error", err)
		common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to upload subtitle: "+err.Error())
		return
	}

	common.SendSuccessResponse(c, http.StatusCreated, info)
}

// GetSubtitleContent handles GET /api/v1/subtitles/:id/content
func (h *SubtitleHandler) GetSubtitleContent(c *gin.Context) {
	subtitleIDStr := c.Param("id")
	subtitleID, err := strconv.ParseUint(subtitleIDStr, 10, 32)
	if err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid subtitle ID")
		return
	}

	content, contentType, filename, err := h.service.GetSubtitleContent(c.Request.Context(), uint(subtitleID))
	if err != nil {
		log.Error(c.Request.Context(), "Failed to get subtitle content", "subtitle_id", subtitleID, "error", err)
		common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to get subtitle content: "+err.Error())
		return
	}
	defer content.Close()

	c.Header("Content-Disposition", "attachment; filename="+filename)
	c.Header("Content-Type", contentType)

	_, copyErr := io.Copy(c.Writer, content)
	if copyErr != nil {
		log.Error(c.Request.Context(), "Error streaming subtitle to client", "subtitle_id", subtitleID, "error", copyErr)
	}
}

// FindSubtitlesForTrack handles GET /api/v1/subtitles/find
func (h *SubtitleHandler) FindSubtitlesForTrack(c *gin.Context) {
	userClaims := middleware.GetUserClaims(c)
	if userClaims == nil {
		common.SendErrorResponse(c, http.StatusUnauthorized, "Unauthorized")
		return
	}

	var req ports.SubtitleQueryRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error(c.Request.Context(), "Failed to bind subtitle query request", "error", err)
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid query parameters")
		return
	}

	// Set IsGuest flag based on the user's group
	req.IsGuest = (userClaims.UserGroup == "guest")

	subtitles, err := h.service.FindSubtitlesForTrack(c.Request.Context(), req, userClaims.UserID)
	if err != nil {
		log.Error(c.Request.Context(), "Failed to find subtitles", "error", err)
		common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to find subtitles: "+err.Error())
		return
	}

	common.SendSuccessResponse(c, http.StatusOK, subtitles)
}

// VoteOnSubtitle handles POST /api/v1/subtitles/vote
func (h *SubtitleHandler) VoteOnSubtitle(c *gin.Context) {
	userClaims := middleware.GetUserClaims(c)
	if userClaims == nil {
		common.SendErrorResponse(c, http.StatusUnauthorized, "Unauthorized")
		return
	}

	// Check if user is a guest
	if userClaims.UserGroup == "guest" {
		common.SendErrorResponse(c, http.StatusForbidden, "Guest users are not allowed to vote on subtitles")
		return
	}

	var req ports.SubtitleVoteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error(c.Request.Context(), "Failed to bind subtitle vote request", "error", err)
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid request data")
		return
	}

	// Validate vote value
	if req.Vote != 1 && req.Vote != -1 {
		common.SendErrorResponse(c, http.StatusBadRequest, "Vote must be either 1 (upvote) or -1 (downvote)")
		return
	}

	err := h.service.VoteOnSubtitle(c.Request.Context(), userClaims.UserID, req)
	if err != nil {
		log.Error(c.Request.Context(), "Failed to vote on subtitle", "error", err)
		common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to vote on subtitle: "+err.Error())
		return
	}

	common.SendSuccessResponse(c, http.StatusOK, gin.H{"message": "Vote recorded successfully"})
}

// DeleteSubtitle handles DELETE /api/v1/subtitles/:id
func (h *SubtitleHandler) DeleteSubtitle(c *gin.Context) {
	userClaims := middleware.GetUserClaims(c)
	if userClaims == nil {
		common.SendErrorResponse(c, http.StatusUnauthorized, "Unauthorized")
		return
	}

	// Check if user is a guest
	if userClaims.UserGroup == "guest" {
		common.SendErrorResponse(c, http.StatusForbidden, "Guest users are not allowed to delete subtitles")
		return
	}

	subtitleIDStr := c.Param("id")
	subtitleID, err := strconv.ParseUint(subtitleIDStr, 10, 32)
	if err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid subtitle ID")
		return
	}

	// Check if user is admin
	isAdmin := (userClaims.UserGroup == "admin")

	err = h.service.DeleteSubtitle(c.Request.Context(), userClaims.UserID, uint(subtitleID), isAdmin)
	if err != nil {
		log.Error(c.Request.Context(), "Failed to delete subtitle", "subtitle_id", subtitleID, "error", err)
		common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to delete subtitle: "+err.Error())
		return
	}

	common.SendSuccessResponse(c, http.StatusOK, gin.H{"message": "Subtitle deleted successfully"})
}

// UpdateSubtitleVisibility handles PUT /api/v1/admin/subtitles/:id/visibility
func (h *SubtitleHandler) UpdateSubtitleVisibility(c *gin.Context) {
	// Check if user is admin
	userClaims := middleware.GetUserClaims(c)
	if userClaims == nil || userClaims.UserGroup != "admin" {
		common.SendErrorResponse(c, http.StatusForbidden, "Admin privileges required")
		return
	}

	subtitleIDStr := c.Param("id")
	subtitleID, err := strconv.ParseUint(subtitleIDStr, 10, 32)
	if err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid subtitle ID")
		return
	}

	var req struct {
		IsPublic bool `json:"is_public" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error(c.Request.Context(), "Failed to bind visibility update request", "error", err)
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid request data")
		return
	}

	err = h.service.UpdateSubtitleVisibility(c.Request.Context(), uint(subtitleID), req.IsPublic)
	if err != nil {
		log.Error(c.Request.Context(), "Failed to update subtitle visibility", "subtitle_id", subtitleID, "error", err)
		common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to update subtitle visibility: "+err.Error())
		return
	}

	common.SendSuccessResponse(c, http.StatusOK, gin.H{"message": "Subtitle visibility updated successfully"})
}

// GetAllSubtitles handles GET /api/v1/admin/subtitles
func (h *SubtitleHandler) GetAllSubtitles(c *gin.Context) {
	// Check if user is admin
	userClaims := middleware.GetUserClaims(c)
	if userClaims == nil || userClaims.UserGroup != "admin" {
		common.SendErrorResponse(c, http.StatusForbidden, "Admin privileges required")
		return
	}

	// Parse pagination parameters
	page, err := strconv.Atoi(c.DefaultQuery("page", "1"))
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	if err != nil || pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	subtitles, total, err := h.service.GetAllSubtitles(c.Request.Context(), page, pageSize)
	if err != nil {
		log.Error(c.Request.Context(), "Failed to get all subtitles", "error", err)
		common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to get subtitles: "+err.Error())
		return
	}

	common.SendSuccessResponse(c, http.StatusOK, gin.H{
		"subtitles": subtitles,
		"pagination": gin.H{
			"total":      total,
			"page":       page,
			"page_size":  pageSize,
			"total_page": (total + pageSize - 1) / pageSize,
		},
	})
}
