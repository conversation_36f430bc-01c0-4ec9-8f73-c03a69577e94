-- migrations_temp/000003_create_storage_tables.up.sql
-- Defines the t_storage_sources table according to the refactoring plan.

CREATE TABLE IF NOT EXISTS t_storage_sources (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    -- mount_path VARCHAR(255) UNIQUE, -- Removed
    driver VARCHAR(255) NOT NULL,   -- Storage driver type (e.g., "alist", "local").
    addition TEXT,                  -- JSON string for driver-specific configurations (e.g., API keys, cookies, root_folder_path, root_folder_id).
    remark TEXT,                    -- Administrator's remarks for this storage source (used as display name).
    disabled BOOLEAN DEFAULT FALSE, -- Whether this storage source is disabled.
    
    -- Fields as per refactor.md (section 3.1)
    download_strategy VARCHAR(10) DEFAULT 'proxy' NOT NULL, -- Download/access strategy: 'proxy' or 'redirect'.
    -- default_root VARCHAR(1024) NOT NULL, -- Removed: Root configuration is now solely managed within the 'Addition' JSON.

    -- Added 'order' column as per user instruction for scan priority
    "order" INTEGER DEFAULT 0,
    cache_expiration INTEGER DEFAULT 0,

    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Indexes
-- CREATE INDEX IF NOT EXISTS idx_t_storage_sources_mount_path ON t_storage_sources (mount_path); -- Removed
CREATE INDEX IF NOT EXISTS idx_t_storage_sources_driver ON t_storage_sources (driver);
CREATE INDEX IF NOT EXISTS idx_t_storage_sources_disabled ON t_storage_sources (disabled);
CREATE INDEX IF NOT EXISTS idx_t_storage_sources_download_strategy ON t_storage_sources (download_strategy);
-- Optional: Add an index for 'order' if frequently sorted/queried
CREATE INDEX IF NOT EXISTS idx_t_storage_sources_order ON t_storage_sources ("order");

-- Trigger to update 'updated_at' timestamp on any row update
CREATE TRIGGER IF NOT EXISTS trigger_t_storage_sources_updated_at
AFTER UPDATE ON t_storage_sources
FOR EACH ROW
BEGIN
    UPDATE t_storage_sources SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
END;