package repository

import (
	"context"
	"time"

	"github.com/Sakura-Byte/kikoeru-go/pkg/domain/model"
)

// UserRepository defines the interface for user data access
type UserRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, user *model.User) error
	GetByID(ctx context.Context, id string) (*model.User, error)
	GetByUsername(ctx context.Context, username string) (*model.User, error)
	GetByEmail(ctx context.Context, email string) (*model.User, error)
	Update(ctx context.Context, user *model.User) error
	Delete(ctx context.Context, id string) error

	// Query operations
	ListAll(ctx context.Context) ([]model.User, error)
	ListWithPagination(ctx context.Context, page, pageSize int) ([]model.User, int, error)

	// Email verification and password reset
	UpdateVerificationToken(ctx context.Context, userID, token string, expires time.Time) error
	UpdatePasswordResetToken(ctx context.Context, userID, token string, expires time.Time) error
	GetByVerificationToken(ctx context.Context, token string) (*model.User, error)
	GetByPasswordResetToken(ctx context.Context, token string) (*model.User, error)
	ClearVerificationToken(ctx context.Context, userID string) error
	ClearPasswordResetToken(ctx context.Context, userID string) error
}
