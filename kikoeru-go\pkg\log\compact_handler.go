package log

import (
	"context"
	"fmt"
	"io"
	"log/slog"
	"runtime"
	"strings"
)

// CompactHandler 简洁版美观日志处理器
// 只显示级别、时间和消息，不显示详细参数
// 保留彩色和格式
type CompactHandler struct {
	out  io.Writer
	opts *slog.HandlerOptions
}

func NewCompactHandler(w io.Writer, opts *slog.HandlerOptions) slog.Handler {
	if opts == nil {
		opts = &slog.HandlerOptions{}
	}
	return &CompactHandler{out: w, opts: opts}
}

func (h *CompactHandler) Enabled(ctx context.Context, level slog.Level) bool {
	minLevel := slog.LevelInfo
	if h.opts != nil {
		minLevel = h.opts.Level.Level()
	}
	return level >= minLevel
}

func (h *CompactHandler) Handle(ctx context.Context, r slog.Record) error {
	var b strings.Builder

	useColor := isTerminal()

	// Level with emoji
	emoji := levelEmoji[r.Level]
	levelColor := levelColor(r.Level)

	// Level
	if useColor {
		b.WriteString(levelColor)
		if r.Level == slog.LevelError {
			b.WriteString(bgRed + colorBrightWhite)
		}
	}
	b.WriteString("[")
	b.WriteString(emoji)
	b.WriteString(" ")
	b.WriteString(strings.ToUpper(r.Level.String()))
	b.WriteString("]")
	if useColor {
		b.WriteString(colorReset)
	}

	b.WriteString(" ")

	// Time
	if useColor {
		b.WriteString(colorCyan)
	}
	b.WriteString(r.Time.Format("2006-01-02 15:04:05.000"))
	if useColor {
		b.WriteString(colorReset)
	}

	b.WriteString(" ")

	// Message (with level-based coloring)
	if useColor {
		b.WriteString(colorBold)
		switch r.Level {
		case slog.LevelError:
			b.WriteString(colorBrightRed)
		case slog.LevelWarn:
			b.WriteString(colorBrightYellow)
		case slog.LevelInfo:
			b.WriteString(colorBrightWhite)
		case slog.LevelDebug:
			b.WriteString(colorBrightGreen)
		}
	}
	b.WriteString(r.Message)
	if useColor {
		b.WriteString(colorReset)
	}

	// Optional source/caller info (simplified)
	if h.opts != nil && h.opts.AddSource && r.PC != 0 {
		frame, _ := runtime.CallersFrames([]uintptr{r.PC}).Next()
		if useColor {
			b.WriteString(" ")
			b.WriteString(colorGray)
			b.WriteString("(")
			b.WriteString(frame.File)
			b.WriteString(":")
			b.WriteString(fmt.Sprintf("%d", frame.Line))
			b.WriteString(")")
			b.WriteString(colorReset)
		} else {
			b.WriteString(fmt.Sprintf(" (%s:%d)", frame.File, frame.Line))
		}
	}

	b.WriteString("\n")
	_, err := h.out.Write([]byte(b.String()))
	return err
}

func (h *CompactHandler) WithAttrs(attrs []slog.Attr) slog.Handler {
	// 简洁模式不关心属性
	return h
}

func (h *CompactHandler) WithGroup(name string) slog.Handler {
	// 简洁模式不关心分组
	return h
}
