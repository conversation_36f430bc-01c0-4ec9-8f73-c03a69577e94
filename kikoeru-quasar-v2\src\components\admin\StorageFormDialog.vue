<template>
  <q-dialog :model-value="true" persistent @hide="emit('close')">
    <q-card style="min-width: 600px">
      <q-card-section>
        <div class="text-h6">{{ t('adminStorage.addStorage') }}</div>
      </q-card-section>

      <q-card-section class="q-pt-none">
        <q-form @submit="submitForm" class="q-gutter-md">
          <!-- 驱动选择 -->
          <q-select
            v-model="selectedDriverObject"
            :options="driverOptions"
            option-label="display_name"
            option-value="driver_name"
            :label="t('adminStorage.table.driver')"
            dense
            outlined
            :disable="isEdit"
            @update:model-value="onDriverObjectChange"
            :rules="[val => val || t('validation.required')]"
          />

          <!-- 基本信息 -->
          <q-input
            v-model="form.remark"
            :label="t('adminStorage.table.remark')"
            dense
            outlined

          />

          <q-input
            v-model.number="form.order"
            type="number"
            :label="t('adminStorage.table.order')"
            dense
            outlined
            :rules="[val => val >= 0 || t('validation.required')]"
          />

          <!-- 下载策略 -->
          <q-select
            v-if="selectedDriver"
            v-model="form.download_strategy"
            :options="selectedDriver.download_options"
            :label="t('adminStorage.table.downloadStrategy')"
            dense
            outlined
          />

          <!-- 高级选项 -->
          <div class="row q-gutter-md">
            <q-checkbox
              v-model="form.disabled"
              :label="t('adminStorage.statusDisabled')"
            />
          </div>

          <q-input
            v-model.number="form.cache_expiration"
            type="number"
            label="Cache Expiration"
            dense
            outlined
          />

          <!-- 驱动特定参数 -->
          <div v-if="selectedDriver" class="q-mt-md">
            <div class="text-subtitle1 q-mb-sm">
              {{ selectedDriver.display_name }} Configuration
            </div>

            <div v-for="param in selectedDriver.params" :key="param.name" class="q-mb-md">
              <q-input
                v-if="param.type === 'string' || param.type === 'password'"
                v-model="form.addition[param.name]"
                :label="param.label || param.name"
                :type="param.secret || param.type === 'password' ? 'password' : 'text'"
                dense
                outlined
                :hint="param.description"
                :rules="param.required ? [val => val && val.length > 0 || t('validation.required')] : []"
              />

              <q-input
                v-else-if="param.type === 'integer' || param.type === 'int'"
                v-model.number="form.addition[param.name]"
                type="number"
                :label="param.label || param.name"
                dense
                outlined
                :hint="param.description"
                :rules="param.required ? [val => val !== null && val !== undefined || t('validation.required')] : []"
              />

              <q-checkbox
                v-else-if="param.type === 'boolean' || param.type === 'bool'"
                v-model="form.addition[param.name]"
                :label="param.label || param.name"
                :hint="param.description"
              />

              <q-select
                v-else-if="param.options && param.options.length > 0"
                v-model="form.addition[param.name]"
                :options="param.options"
                :label="param.label || param.name"
                dense
                outlined
                :hint="param.description"
                :rules="param.required ? [val => val || t('validation.required')] : []"
              />

              <!-- fallback for unrecognized types -->
              <q-input
                v-else
                v-model="form.addition[param.name]"
                :label="param.label || param.name"
                dense
                outlined
                :hint="`${param.description || ''} (Type: ${param.type})`"
                :rules="param.required ? [val => val && val.length > 0 || t('validation.required')] : []"
              />
            </div>
          </div>
        </q-form>
      </q-card-section>

      <q-card-actions align="right">
        <q-btn flat :label="t('common.cancel')" @click="emit('close')" />
        <q-btn
          flat
          :label="isEdit ? t('common.save') : t('common.create')"
          color="primary"
          @click="submitForm"
          :loading="submitting"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted, getCurrentInstance } from 'vue'
import { useI18n } from 'vue-i18n'
import { useNotification } from '../../composables/useNotification'

defineOptions({
  name: 'StorageFormDialog'
})

const props = defineProps({
  drivers: {
    type: Array,
    default: () => []
  },
  storage: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['close', 'success'])

const { t } = useI18n()
const { proxy } = getCurrentInstance()
const { showSuccessNotification, showErrorNotification } = useNotification()

// Reactive data
const submitting = ref(false)
const form = ref({
  driver: null,
  remark: '',
  order: 0,
  disabled: false,
  download_strategy: '',
  cache_expiration: 0,
  addition: {}
})

// Computed properties
const isEdit = computed(() => !!props.storage)

const driverOptions = computed(() => props.drivers || [])

const selectedDriverObject = computed({
  get() {
    if (!form.value.driver) return null
    return props.drivers.find(d => d.driver_name === form.value.driver) || null
  },
  set(driverObj) {
    form.value.driver = driverObj ? driverObj.driver_name : null
  }
})

const selectedDriver = computed(() => {
  if (!form.value.driver) return null
  return props.drivers.find(d => d.driver_name === form.value.driver)
})

// Watchers
watch(selectedDriverObject, (newDriverObj, oldDriverObj) => {
  const newDriverName = newDriverObj ? newDriverObj.driver_name : null
  const oldDriverName = oldDriverObj ? oldDriverObj.driver_name : null

  // Only trigger driver change if not in edit mode or if this is the initial selection
  if (newDriverName !== oldDriverName && (!isEdit.value || !oldDriverObj)) {
    onDriverChange(newDriverName)
  }
}, { immediate: true })

const loadStorageData = () => {
  if (!props.storage) return

  form.value = {
    driver: props.storage.driver,
    remark: props.storage.remark || '',
    order: props.storage.order || 0,
    disabled: props.storage.disabled || false,
    download_strategy: props.storage.download_strategy || '',
    cache_expiration: props.storage.cache_expiration || 0,
    addition: parseAddition(props.storage.addition)
  }
}

const parseAddition = (addition) => {
  if (!addition) return {}
  try {
    return typeof addition === 'string' ? JSON.parse(addition) : addition
  } catch {
    return {}
  }
}

const onDriverObjectChange = (driverObj) => {
  // In edit mode, we don't want to reset the form values
  if (isEdit.value) return

  const driverName = driverObj ? driverObj.driver_name : null
  onDriverChange(driverName)
}

const onDriverChange = (driverName) => {
  if (!driverName) {
    form.value.addition = {}
    form.value.download_strategy = ''
    return
  }

  const driver = props.drivers.find(d => d.driver_name === driverName)
  if (driver) {
    form.value.download_strategy = driver.default_download

    // In edit mode, preserve existing values
    const newAddition = isEdit.value ? {...form.value.addition} : {}

    driver.params.forEach(param => {
      // Skip if we already have a value for this param in edit mode
      if (isEdit.value && newAddition[param.name] !== undefined) {
        return
      }

      if (param.default_value !== undefined && param.default_value !== null) {
        newAddition[param.name] = param.default_value
      } else {
        if (param.type === 'bool' || param.type === 'boolean') {
          newAddition[param.name] = false
        } else if (param.type === 'integer' || param.type === 'int') {
          newAddition[param.name] = 0
        } else {
          newAddition[param.name] = ''
        }
      }
    })

    form.value.addition = newAddition
  } else {
    form.value.addition = {}
    form.value.download_strategy = ''
  }
}

const submitForm = async () => {
  if (!form.value.driver) {
    showErrorNotification(t('validation.required'))
    return
  }

  submitting.value = true
  try {
    const payload = {
      ...form.value,
      addition: form.value.addition
    }

    if (isEdit.value) {
      await proxy.$api.put(`/api/v1/admin/storages/${props.storage.id}`, payload)
      showSuccessNotification(t('success'))
    } else {
      await proxy.$api.post('/api/v1/admin/storages', payload)
      showSuccessNotification(t('success'))
    }

    emit('success')
    emit('close')
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('failed'))
  } finally {
    submitting.value = false
  }
}

// Lifecycle
onMounted(() => {
  if (isEdit.value) {
    loadStorageData()
  }
})
</script>
