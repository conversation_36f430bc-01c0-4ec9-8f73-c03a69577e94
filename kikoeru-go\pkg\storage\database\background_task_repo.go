package database

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
	common_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/common" // Import common_dto
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"gorm.io/gorm"
)

// ListBackgroundTaskParams defines parameters for listing background tasks.
type ListBackgroundTaskParams struct {
	common_dto.PaginationParams                   // Embedded pagination and sorting
	TaskType                    string            // Filter by task type
	Status                      models.TaskStatus // Filter by status - changed from string to models.TaskStatus
	SubmittedByUserID           string            // Filter by user ID
}

// BackgroundTaskRepository defines the repository interface for background tasks.
type BackgroundTaskRepository interface {
	Create(ctx context.Context, task *models.BackgroundTask) error
	GetByID(ctx context.Context, taskID string) (*models.BackgroundTask, error)
	List(ctx context.Context, params ListBackgroundTaskParams) ([]*models.BackgroundTask, int64, error)
	UpdateStatus(ctx context.Context, taskID string, status models.TaskStatus, message string) error
	UpdateProgress(ctx context.Context, taskID string, progress float64, message string) error
	SetResult(ctx context.Context, taskID string, resultPayload []byte, status models.TaskStatus, errorMessage string) error
}

type backgroundTaskRepository struct {
	db *gorm.DB
}

func NewBackgroundTaskRepository(db *gorm.DB) BackgroundTaskRepository {
	return &backgroundTaskRepository{db: db}
}

func (r *backgroundTaskRepository) Create(ctx context.Context, task *models.BackgroundTask) error {
	if task == nil {
		return errors.New("task cannot be nil")
	}
	// ID and timestamps are set by GORM hooks
	result := r.db.WithContext(ctx).Create(task)
	if result.Error != nil {
		return fmt.Errorf("failed to create background task in DB: %w", result.Error)
	}
	return nil
}

func (r *backgroundTaskRepository) GetByID(ctx context.Context, taskID string) (*models.BackgroundTask, error) {
	var task models.BackgroundTask
	if taskID == "" {
		return nil, errors.New("task ID cannot be empty")
	}
	result := r.db.WithContext(ctx).Where("id = ?", taskID).First(&task)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, apperrors.ErrTaskNotFound
		}
		return nil, result.Error
	}
	return &task, nil
}

func (r *backgroundTaskRepository) List(ctx context.Context, params ListBackgroundTaskParams) ([]*models.BackgroundTask, int64, error) {
	query := r.db.WithContext(ctx).Model(&models.BackgroundTask{})

	if params.TaskType != "" {
		query = query.Where("task_type = ?", params.TaskType)
	}
	if params.Status != "" {
		query = query.Where("status = ?", params.Status)
	}
	if params.SubmittedByUserID != "" {
		query = query.Where("submitted_by_user_id = ?", params.SubmittedByUserID)
	}

	var totalCount int64
	if err := query.Count(&totalCount).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count background tasks: %w", err)
	}

	// Apply sorting
	orderBy := params.SortBy
	if orderBy == "" {
		orderBy = "created_at" // Default sort
	}
	if strings.ToLower(params.SortOrder) == "asc" {
		orderBy += " ASC"
	} else {
		orderBy += " DESC" // Default to descending
	}
	query = query.Order(orderBy)

	// Apply pagination
	if params.Page > 0 && params.PageSize > 0 {
		offset := (params.Page - 1) * params.PageSize
		query = query.Offset(offset).Limit(params.PageSize)
	}

	var tasks []*models.BackgroundTask
	if err := query.Find(&tasks).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list background tasks: %w", err)
	}

	return tasks, totalCount, nil
}

func (r *backgroundTaskRepository) UpdateStatus(ctx context.Context, taskID string, status models.TaskStatus, message string) error {
	if taskID == "" {
		return errors.New("task ID cannot be empty for status update")
	}
	updateData := map[string]interface{}{
		"status":     status,
		"message":    message,
		"updated_at": time.Now(),
	}
	result := r.db.WithContext(ctx).Model(&models.BackgroundTask{}).Where("id = ?", taskID).Updates(updateData)
	if result.Error != nil {
		return fmt.Errorf("failed to update task %s status: %w", taskID, result.Error)
	}
	// Note: We don't check RowsAffected == 0 here, as the task might not exist,
	// but the service layer should handle ErrTaskNotFound on GetByID before calling update.
	return nil
}

func (r *backgroundTaskRepository) UpdateProgress(ctx context.Context, taskID string, progress float64, message string) error {
	if taskID == "" {
		return errors.New("task ID cannot be empty for progress update")
	}
	updateData := map[string]interface{}{
		"progress":   progress,
		"message":    message,
		"updated_at": time.Now(),
	}
	result := r.db.WithContext(ctx).Model(&models.BackgroundTask{}).Where("id = ?", taskID).Updates(updateData)
	if result.Error != nil {
		return fmt.Errorf("failed to update task %s progress: %w", taskID, result.Error)
	}
	return nil
}

func (r *backgroundTaskRepository) SetResult(ctx context.Context, taskID string, resultPayload []byte, status models.TaskStatus, errorMessage string) error {
	if taskID == "" {
		return errors.New("task ID cannot be empty for setting result")
	}
	updateData := map[string]interface{}{
		"status":       status,
		"message":      errorMessage, // Use errorMessage for the message field
		"result":       resultPayload,
		"completed_at": time.Now(),
		"updated_at":   time.Now(),
	}
	result := r.db.WithContext(ctx).Model(&models.BackgroundTask{}).Where("id = ?", taskID).Updates(updateData)
	if result.Error != nil {
		return fmt.Errorf("failed to set task %s result: %w", taskID, result.Error)
	}
	return nil
}
