// This is just an example,
// so you can safely delete all default props below

export default {
  failed: 'Action failed',
  success: 'Action was successful',
  settings: 'Settings',

  // Admin profile page translations
  adminProfile: {
    title: 'Admin Profile',
    username: 'Userna<PERSON>',
    newPassword: 'New Password',
    confirmPassword: 'Confirm Password',
    passwordsMustMatch: 'Passwords must match',
    save: 'Save Changes',
    updateSuccess: 'Profile updated successfully',
    updateError: 'Failed to update profile',
    loadError: 'Failed to load profile',
    noChanges: 'No changes detected'
  },

  // Common text used across the application
  common: {
    confirm: 'Confirm',
    cancel: 'Cancel',
    delete: 'Delete',
    save: 'Save',
    edit: 'Edit',
    create: 'Create',
    add: 'Add',
    remove: 'Remove',
    file: 'File',
    next: 'Next',
    back: 'Back',
    ok: 'OK'
  },

  // Common validation messages
  validation: {
    required: 'This field is required',
    minLength: 'Must be at least {length} characters',
    usernameMinLength: 'Username must be at least {length} characters',
    passwordMinLength: 'Password must be at least {length} characters',
    invalidEmail: 'Please enter a valid email',
    passwordMismatch: 'Passwords do not match',
    playlistNameRequired: 'Please enter a playlist name'
  },

  // Language switcher
  languageSwitcher: {
    language: 'Language'
  },

  // Navigation items
  nav: {
    mediaLibrary: 'Media Library',
    myReviews: 'My Reviews',
    playlists: 'Playlists',
    history: 'History',
    circles: 'Circles',
    tags: 'Tags',
    voiceActors: 'Voice Actors',
    randomListen: 'Random Listen',
    sleepMode: 'Sleep Mode',
    adminPanel: 'Admin Panel',
    darkMode: 'Dark Mode',
    lightMode: 'Light Mode',
    logout: 'Logout',
    login: 'Login'
  },

  // Dialog messages
  dialog: {
    logout: 'Do you want to log out?',
    cancel: 'Cancel',
    confirm: 'Confirm',
    delete: 'Delete'
  },

  // Notifications
  notification: {
    darkModeEnabled: 'Dark mode enabled',
    lightModeEnabled: 'Light mode enabled',
    loggedOut: 'Logged out successfully',
    randomPlayStart: 'Randomly playing {count} audios',
    noPlayableAudio: 'No playable audio found',
    randomPlayError: 'Failed to get random work',
    loginSuccess: 'Login successful',
    loginFailed: 'Username or password is incorrect',
    networkError: 'Network error',
    registerSuccess: 'Registration successful',
    registerSuccessLogin: 'Registration successful! Please login.',
    registerFailed: 'Registration failed: {status}',
    passwordChanged: 'Password changed successfully',
    emailSent: 'Email has been sent',
    reviewSubmitted: 'Review submitted',
    reviewSubmitFailed: 'Failed to submit review',
    reviewDeleted: 'Review deleted',
    reviewDeleteFailed: 'Failed to delete review',
    historyDeleted: 'Play history record deleted',
    historyDeleteFailed: 'Failed to delete play history record',
    playlistUpdated: 'Playlist updated successfully',
    playlistUpdateFailed: 'Failed to update playlist',
    playlistDeleted: 'Playlist deleted successfully',
    playlistDeleteFailed: 'Failed to delete playlist',
    loadFolderFailed: 'Failed to load folder contents',
    downloadFailed: 'Failed to download file',
    openFileFailed: 'Failed to open file',
    loadListFailed: 'Failed to load {type}',
    loadHistoryFailed: 'Failed to load play history',
    loadPlaylistFailed: 'Failed to load playlist',
    playlistItemRemoved: 'Item removed from playlist',
    playlistItemRemoveFailed: 'Failed to remove item from playlist',
    playlistOrderUpdated: 'Playlist order updated',
    playlistOrderUpdateFailed: 'Failed to update playlist order',
    searchWorksFailed: 'Failed to search works',
    workAlreadyInPlaylist: 'Work is already in this playlist',
    playlistItemAdded: 'Item added to playlist',
    playlistItemAddFailed: 'Failed to add item to playlist',
    loadPlaylistsFailed: 'Failed to load playlists',
    playlistCreated: 'Playlist created successfully',
    playlistCreateFailed: 'Failed to create playlist',
    loadReviewsFailed: 'Failed to load reviews',
    loadWorksFailed: 'Failed to load works',
    loadWorkDetailsFailed: 'Failed to load work details',
    adminLoadStoragesFailed: 'Failed to load storage sources',
    adminLoadDriverDefinitionsFailed: 'Failed to load driver definitions',
    adminDriversReloaded: 'Drivers reloaded successfully',
    adminDriversReloadFailed: 'Failed to reload drivers',
    adminConnectionTestSuccess: 'Connection test successful',
    adminConnectionTestFailed: 'Connection test failed',
    adminStorageDeleted: 'Storage source deleted',
    adminStorageDeleteFailed: 'Failed to delete storage source',
    adminLoadTasksFailed: 'Failed to load tasks',
    adminLoadStoragesForTasksFailed: 'Failed to load storage sources for task form',
    adminScanTaskSubmitted: 'Scan task submitted',
    adminScanTaskSubmitFailed: 'Failed to submit scan task',
    adminScrapeAllTaskSubmitted: 'Scraping task submitted',
    adminScrapeAllTaskSubmitFailed: 'Failed to submit scraping task',
    adminTaskCancelled: 'Task cancelled',
    adminTaskCancelFailed: 'Failed to cancel task',
    adminSystemConfigSaved: 'System configuration saved',
    adminSystemConfigSaveFailed: 'Failed to save system configuration',
    adminLoadCacheInfoFailed: 'Failed to load cache information',
    adminCoverCacheCleared: 'Cover cache cleared',
    adminClearCoverCacheFailed: 'Failed to clear cover cache',
    adminFileCacheCleared: 'File cache cleared',
    adminClearFileCacheFailed: 'Failed to clear file cache',
    adminAllCacheCleared: 'All caches cleared',
    adminClearAllCacheFailed: 'Failed to clear all caches',
    adminWorksExported: 'Works data exported successfully',
    adminExportWorksFailed: 'Failed to export works data',
    adminUsersExported: 'Users data exported successfully',
    adminExportUsersFailed: 'Failed to export users data',
    adminDataImported: 'Data imported successfully',
    adminImportDataFailed: 'Failed to import data',
    adminLoadSystemInfoFailed: 'Failed to load system information',
    adminLoadScannerStatusFailed: 'Failed to load scanner status',
    adminScanSpecificStarted: 'Scan for selected storage started',
    adminScanAllStarted: 'Scan for all storages started',
    adminScanSpecificFailed: 'Failed to start scan for selected storage',
    adminScanAllFailed: 'Failed to start scan for all storages',
    adminLoadDashboardStatsFailed: 'Failed to load dashboard statistics',
    adminLoadRecentTasksFailed: 'Failed to load recent tasks',
    archiveLoadFailed: 'Failed to load archive',
    encodingChanged: 'Archive encoding changed successfully',
    loadFailed: 'Failed to load data',
    createSuccess: 'Created successfully',
    updateSuccess: 'Updated successfully',
    deleteSuccess: 'Deleted successfully',
    saveFailed: 'Failed to save',
    deleteFailed: 'Failed to delete',
    importFailed: 'Failed to import'
  },

  // Auth related translations
  auth: {
    login: 'Login',
    register: 'Register',
    username: 'Username',
    usernameOrEmail: 'Username or Email',
    password: 'Password',
    confirmPassword: 'Confirm Password',
    email: 'Email',
    forgotPassword: 'Forgot password?',
    resetPassword: 'Reset Password',
    sendResetEmail: 'Send Reset Email',
    createAccount: 'Create new account',
    adminLogin: 'Admin login successful! Admin panel is now visible in sidebar.',
    registerNewAccount: 'Register New Account',
    alreadyHaveAccount: 'Already have an account? Login'
  },

  // Settings page
  settingsPage: {
    personalInfo: 'Personal Information',
    playbackSettings: 'Playback Settings',
    interfaceSettings: 'Interface Settings',
    username: 'Username',
    userGroup: 'User Group',
    registrationDate: 'Registration Date',
    lastUpdate: 'Last Update',
    forwardJump: 'Forward Jump Time',
    rewindJump: 'Rewind Jump Time',
    seconds: 'seconds',
    guestBanner: 'You are currently browsing as a guest. Only player settings are displayed.',
    emailSettings: 'Email Settings',
    currentEmail: 'Current Email',
    notSet: 'Not set',
    verified: 'Verified',
    unverified: 'Unverified',
    changeEmail: 'Change Email',
    setEmail: 'Set Email',
    resendVerification: 'Resend Verification Email',
    unlinkEmail: 'Unlink Email',
    securitySettings: 'Security Settings',
    currentPassword: 'Current Password',
    newPassword: 'New Password',
    confirmNewPassword: 'Confirm New Password',
    changePassword: 'Change Password',
    passwordRequirements: 'Password must be at least 6 characters',
    saved: 'Setting saved'
  },

  // Work related translations
  work: {
    details: 'Details',
    tracks: 'Tracks',
    reviews: 'Reviews',
    releasedAt: 'Released',
    circle: 'Circle',
    tags: 'Tags',
    vas: 'Voice Actors',
    addToPlaylist: 'Add to Playlist',
    createPlaylist: 'Create Playlist',
    writeReview: 'Write Review',
    noReviews: 'No reviews yet',
    yourRating: 'Your Rating',
    rate: 'Rate',
    playAll: 'Play All',
    averageRating: 'Average',
    starRating: '{count} Star | {count} Stars',
    currency: 'JPY',
    soldCount: 'Sold',
    markProgress: 'Mark Progress',
    uploadSubtitle: 'Upload Subtitle'
  },

  // Player related translations
  player: {
    previous: 'Previous',
    play: 'Play',
    pause: 'Pause',
    rewind: 'Rewind',
    forward: 'Forward',
    next: 'Next',
    showQueue: 'Show Queue',
    playMode: 'Playback Mode',
    clearQueue: 'Clear Queue',
    removeFromQueue: 'Remove',
    reorder: 'Reorder',
    openWorkDetails: 'Open Work Details',
    hideCoverButtons: 'Hide Cover Buttons',
    swapSeekButtons: 'Swap Seek Buttons',
    subtitles: 'Subtitles',
    noSubtitlesAvailable: 'No subtitles available',
    uploadSubtitleFile: 'Upload subtitle file',
    upload: 'Upload',
    close: 'Close',
    uploadedBy: 'Uploaded by {name}',
    localSubtitle: 'Local subtitle',
    subtitleLoaded: 'Loaded subtitle: {name}',
    subtitleLoadFailed: 'Failed to load subtitle',
    subtitleUploaded: 'Subtitle uploaded successfully',
    subtitleUploadFailed: 'Failed to upload subtitle',
    desktopLyrics: 'Desktop Lyrics',
  },

  // Lyric dialog related translations
  lyric: {
    title: 'Lyrics',
    selectFile: 'Select Lyric File',
    noLyricsFound: 'No lyric files found',
    similarity: 'Similarity',
    selectLocalFile: 'Select Local File',
    noLyrics: 'No Lyrics Available',
    noLyricsDesc: 'Select a lyric file to display lyrics',
    adjustTiming: 'Adjust timing offset to sync lyrics with audio',
    localSelectedFile: 'Local Selected File'
  },

  // Review related translations
  review: {
    myReview: 'My Review',
    deleteTag: 'Delete Tag',
    confirmDeleteTag: 'Are you sure you want to delete this tag?',
    progress: {
      wantToListen: 'Want to Listen',
      listening: 'Listening',
      listened: 'Listened',
      relistening: 'Relistening',
      postponed: 'Postponed'
    },
    updatedAt: 'Updated At',
    editReview: 'Edit Review',
    deleteReview: 'Delete Review',
    confirmDelete: {
      title: 'Confirm Delete',
      message: 'Are you sure you want to delete this review?'
    }
  },

  // Playlist related translations
  playlist: {
    create: 'Create Playlist',
    edit: 'Edit Playlist',
    delete: 'Delete Playlist',
    name: 'Playlist Name',
    nameLabel: 'Playlist Name',
    description: 'Description',
    descriptionLabel: 'Description (Optional)',
    visibility: 'Visibility',
    visibilityLabel: 'Visibility',
    public: 'Public',
    private: 'Private',
    unlisted: 'Unlisted',
    itemCount: '{count} items | {count} item | {count} items',
    addTrack: 'Add Track',
    removeTrack: 'Remove Track',
    moveTracks: 'Move Tracks',
    moveUp: 'Move Up',
    moveDown: 'Move Down',
    track: 'Track',
    saveChanges: 'Save Changes',
    myPlaylists: 'My Playlists',
    createNew: 'Create New Playlist',
    emptyPlaylist: 'This playlist is empty',
    confirmDelete: 'Are you sure you want to delete this playlist?',
    saveInProgress: 'Save playlist feature is under development',
    confirmRemove: {
      title: 'Confirm Remove',
      message: 'Are you sure you want to remove this item from the playlist?'
    },
    confirmDeleteDialog: {
      title: 'Confirm Delete',
      message: 'Are you sure you want to delete the playlist "{name}"? This action cannot be undone.'
    },
    visibilityOptions: {
      private: 'Private',
      unlisted: 'Unlisted',
      public: 'Public'
    },
    updatedAt: 'Updated at {date}',
    playAll: 'Play All',
    shufflePlay: 'Shuffle Play',
    editInfo: 'Edit Info',
    content: 'Playlist Content',
    addItem: 'Add Item',
    addItemsToPlaylist: 'Add Items to Playlist',
    searchWorks: 'Search Works',
    searchPlaceholder: 'Enter work title or original ID',
    noResultsFound: 'No results found'
  },

  // Sleep mode related translations
  sleepMode: {
    title: 'Sleep Mode',
    setTime: 'Set Sleep Time',
    cancel: 'Cancel Sleep Mode',
    disabled: 'Sleep mode disabled',
    willStopAt: 'Will stop playing at {time}'
  },

  // History related translations
  history: {
    title: 'Play History',
    track: 'Track',
    finished: 'Finished',
    playedAt: 'Played At',
    deleteRecord: 'Delete Record',
    endOfList: 'END',
    confirmDelete: {
      title: 'Confirm Delete',
      message: 'Are you sure you want to delete this play history record?'
    },
    sortOptions: {
      updatedDesc: 'Played Date (Newest First)',
      updatedAsc: 'Played Date (Oldest First)',
      progressDesc: 'Progress (High to Low)',
      progressAsc: 'Progress (Low to High)'
    }
  },

  // Work Tree specific translations
  worktree: {
    root: 'Root',
    emptyFolder: 'This folder is empty',
    addToQueue: 'Add to Queue',
    playNext: 'Play Next',
    downloadFile: 'Download',
    passwordRequired: 'Archive Password Required',
    enterPassword: 'Enter password',
    selectEncoding: 'Select Text Encoding',
    selectEncodingTitle: 'Character Encoding',
    zipEncodingOnly: 'Encoding selection applies only to ZIP archives',
    encoding: 'Encoding',
    encodingHelp: 'Select the appropriate character encoding for non-Latin filenames in ZIP archives',
    encodingIssueDetected: 'Characters in this archive may be incorrectly displayed. Please select the appropriate encoding.',
    encodingUTF8: 'UTF-8 (Default)',
    encodingShiftJIS: 'Shift-JIS (Japanese)',
    encodingEUCJP: 'EUC-JP (Japanese)',
    encodingGBK: 'GBK (Chinese Simplified)',
    encodingBig5: 'Big5 (Chinese Traditional)',
    encodingEUCKR: 'EUC-KR (Korean)'
  },

  // Error page translations
  error: {
    notFound: 'Oops. Nothing here...',
    goHome: 'Go Home'
  },

  // List page translations
  list: {
    workCount: '{count} work | {count} works',
    noData: 'No data available',
    searchPlaceholder: 'Search for a {type}...',
    circle: 'circle',
    tag: 'tag',
    va: 'voice actor'
  },

  // Reviews Page specific translations
  reviewsPage: {
    sortBy: 'Sort by',
    gridView: 'Grid View',
    listView: 'List View',
    filterOptions: {
      all: 'All'
      // Other filter options like 'Want to Listen' etc. are already in review.progress
    },
    sortOptions: {
      updatedDesc: 'Updated Date (Newest First)',
      updatedAsc: 'Updated Date (Oldest First)',
      ratingDesc: 'Rating (High to Low)',
      ratingAsc: 'Rating (Low to High)',
      releaseDesc: 'Release Date (Newest First)',
      releaseAsc: 'Release Date (Oldest First)'
    }
  },

  // Works Page specific translations
  worksPage: {
    searchResultTitle: 'Search Results for "{keyword}"',
    circleWorksTitle: 'Works by {circle}',
    tagWorksTitle: 'Works tagged with "{tag}"',
    vaWorksTitle: 'Works by voice actor "{va}"',
    sortBy: 'Sort by',
    gridView: 'Grid View',
    listView: 'List View',
    showTags: 'Show Tags',
    hideTags: 'Hide Tags',
    detailView: 'Detail View',
    compactView: 'Compact View',
    sortOptions: {
      releaseDesc: 'Release Date (Newest First)',
      ratingDesc: 'Rating (High to Low)',
      releaseAsc: 'Release Date (Oldest First)',
      salesDesc: 'Sales (Most to Least)',
      priceAsc: 'Price (Low to High)',
      priceDesc: 'Price (High to Low)',
      reviewsDesc: 'Reviews (Most to Least)',
      createdDesc: 'Date Added (Newest First)'
    }
  },

  // Work Page specific translations
  workPage: {
    loading: 'Loading...'
  },

  // Admin Storage Management Page
  adminStorage: {
    title: 'Storage Management',
    addStorage: 'Add Storage Source',
    reloadDrivers: 'Reload Drivers',
    driverHelp: 'Driver Help',
    statusEnabled: 'Enabled',
    statusDisabled: 'Disabled',
    testConnection: 'Test Connection',
    driverHelpTitle: 'Storage Driver Help',
    driverInfo: {
      supportedStrategies: 'Supported Download Strategies',
      defaultStrategy: 'Default',
      accessMethod: 'Access Method',
      proxyOnly: 'Proxy access only',
      localOnly: 'Local access only',
      configParams: 'Configuration Parameters',
      required: 'Required',
      sensitive: 'Sensitive'
    },
    confirmDeleteDialog: {
      title: 'Confirm Delete',
      message: 'Are you sure you want to delete the storage source "{remark}"? This action cannot be undone.'
    },
    table: {
      order: 'Order',
      remark: 'Remark',
      driver: 'Driver',
      downloadStrategy: 'Download Strategy',
      status: 'Status',
      createdAt: 'Created At',
      actions: 'Actions'
    }
  },

  // Admin Task Management Page
  adminTasks: {
    title: 'Task Management',
    scanLibrary: 'Scan Library',
    scrapeAllWorks: 'Scrape All Works',
    refreshTaskList: 'Refresh Task List',
    viewDetails: 'View Details',
    cancelTask: 'Cancel Task',
    storageIdLabel: 'ID',
    scanLibraryDialog: {
      title: 'Scan Library',
      selectStorage: 'Select Storage Source',
      startScan: 'Start Scan'
    },
    scrapeAllDialog: {
      title: 'Scrape All Works',
      forceUpdate: 'Force update existing metadata',
      preferredLang: 'Preferred Language',
      startScraping: 'Start Scraping'
    },
    taskDetailDialog: {
      title: 'Task Details',
      taskId: 'Task ID',
      taskType: 'Task Type',
      status: 'Status',
      progress: 'Progress',
      message: 'Message',
      errorMessage: 'Error Message',
      createdAt: 'Created At',
      startedAt: 'Started At',
      completedAt: 'Completed At',
      payload: 'Payload',
      result: 'Result'
    },
    table: {
      taskType: 'Task Type',
      status: 'Status',
      progress: 'Progress',
      message: 'Message',
      createdAt: 'Created At',
      actions: 'Actions'
    },
    statusLabels: {
      pending: 'Pending',
      running: 'Running',
      completed: 'Completed',
      failed: 'Failed',
      cancelled: 'Cancelled'
    },
    languageOptions: {
      zh: 'Chinese',
      ja: 'Japanese',
      en: 'English'
    },
    validation: {
      selectStorage: 'Please select a storage source'
    }
  },

  // Dashboard - Advanced Settings Page
  dashboardAdvanced: {
    title: 'Advanced Settings',
    systemConfig: {
      title: 'System Configuration',
      siteName: 'Site Name',
      siteNameHint: 'Name displayed in the page title',
      allowRegistration: 'Allow User Registration',
      enableEmailFeatures: 'Enable Email Features',
      forceEmailVerification: 'Force email verification upon registration',
      saveButton: 'Save System Configuration'
    },
    cacheManagement: {
      title: 'Cache Management',
      coverCache: 'Cover Cache',
      fileCache: 'File Cache',
      allCache: 'All Cache',
      calculating: 'Calculating...',
      clearCoverCache: 'Clear Cover Cache',
      clearFileCache: 'Clear File Cache',
      clearAllCache: 'Clear All Cache',
      clearAllDescription: 'One-click to clear all caches',
      refreshCacheInfo: 'Refresh Cache Information'
    },
    dataManagement: {
      title: 'Data Management',
      exportData: 'Export Data',
      exportDescription: 'Export work data, user data, etc.',
      exportWorks: 'Export Works Data',
      exportUsers: 'Export Users Data',
      importData: 'Import Data',
      importDescription: 'Import data from a backup file',
      selectImportFile: 'Select import file',
      importButton: 'Import Data',
      noFileSelectedError: 'Please select a file to import'
    },
    systemInfo: {
      title: 'System Information',
      version: 'System Version',
      fetching: 'Fetching...',
      dbStatus: 'Database Status',
      dbStatusHealthy: 'Healthy',
      dbStatusError: 'Error',
      totalWorks: 'Total Works',
      totalWorksCount: '{count} works',
      totalUsers: 'Total Users',
      totalUsersCount: '{count} users',
      totalStorages: 'Total Storage Sources',
      totalStoragesCount: '{count} sources',
      refreshButton: 'Refresh System Information'
    }
  },

  // Dashboard - Scanner Page
  dashboardScanner: {
    title: 'Scanner Management',
    unnamedStorage: 'Unnamed Storage',
    neverScanned: 'Never Scanned',
    scannerStatus: {
      title: 'Scanner Status',
      currentStatus: 'Current Status',
      currentScanningStorage: 'Currently Scanning Storage',
      statusInfo: 'Status Information',
      lastError: 'Last Error',
      scanProgressLabel: 'Scan Progress',
      refreshStatus: 'Refresh Status'
    },
    manualScan: {
      title: 'Manual Scan',
      selectStorage: 'Select Storage Source',
      selectStorageHint: 'Select the storage source to scan',
      scanSelectedStorage: 'Scan Selected Storage',
      scanAllStorages: 'Scan All Storages'
    },
    storageList: {
      title: 'Storage Source List',
      statusEnabled: 'Enabled',
      statusDisabled: 'Disabled',
      scanThisStorage: 'Scan this storage source',
      manageStorages: 'Manage Storage Sources',
      table: {
        remark: 'Remark',
        driver: 'Driver',
        status: 'Status',
        lastScannedAt: 'Last Scanned At',
        actions: 'Actions'
      }
    },
    scanConfirmDialog: {
      title: 'Confirm Scan',
      message: 'Are you sure you want to start scanning? This process may take a long time.',
      startScan: 'Start Scan'
    },
    statusLabels: {
      idle: 'Idle',
      scanning: 'Scanning',
      scraping: 'Scraping',
      completed: 'Completed',
      failed: 'Failed',
      error: 'Error'
    }
  },

  // Dashboard - Home Page (Folders.vue is effectively the Dashboard Home)
  dashboardHome: {
    title: 'Dashboard',
    stats: {
      totalWorks: 'Total Works',
      totalUsers: 'Total Users',
      totalStorages: 'Storage Sources',
      runningTasks: 'Running Tasks'
    },
    quickActions: {
      title: 'Quick Actions',
      scanLibrary: 'Scan Library',
      manageStorages: 'Manage Storages',
      userManagement: 'User Management',
      feedbackManagement: 'Feedback Management'
    },
    recentTasks: {
      title: 'Recent Tasks',
      table: {
        taskType: 'Task Type',
        status: 'Status',
        createdAt: 'Created At'
      }
    }
  },

  // Dashboard - User Management Page
  dashboardUserManage: {
    title: 'User Management',
    addUser: 'Add User',
    searchPlaceholder: 'Search users...',
    emailVerified: 'Verified',
    emailUnverified: 'Unverified',
    noEmail: 'No Email',
    editUserTooltip: 'Edit User',
    deleteUserTooltip: 'Delete User',
    createUserDialog: {
      title: 'Add User',
      usernameLabel: 'Username',
      usernameRequired: 'Please enter a username',
      emailLabel: 'Email (Optional)',
      passwordLabel: 'Password',
      passwordMinLength: 'Password must be at least 6 characters',
      groupLabel: 'User Group'
    },
    editUserDialog: {
      title: 'Edit User',
      usernameLabel: 'Username',
      emailLabel: 'Email',
      groupLabel: 'User Group',
      newPasswordLabel: 'New Password (leave blank to keep current)'
    },
    deleteConfirmDialog: {
      title: 'Confirm Delete',
      message: 'Are you sure you want to delete user "{username}"? This action cannot be undone.'
    },
    table: {
      username: 'Username',
      email: 'Email',
      group: 'Group',
      emailVerified: 'Email Verified',
      createdAt: 'Created At',
      updatedAt: 'Updated At',
      actions: 'Actions'
    },
    userGroups: {
      admin: 'Administrator',
      user: 'User'
      // Add other groups if any, like 'guest'
    }
  },

  // Dashboard - Feedback Management Page
  dashboardFeedbackManage: {
    title: 'Feedback Management',
    filterStatusLabel: 'Filter by Status',
    filterTypeLabel: 'Filter by Type',
    anonymousUser: 'Anonymous User',
    viewDetailTooltip: 'View Details',
    updateStatusTooltip: 'Update Status',
    detailDialog: {
      title: 'Feedback Details',
      feedbackContentTitle: 'Feedback Content',
      resolutionNotesTitle: 'Resolution Notes',
      feedbackId: 'Feedback ID',
      type: 'Type',
      status: 'Status',
      submittedBy: 'Submitted By',
      submittedAt: 'Submitted At',
      updatedAt: 'Updated At',
      resolvedAt: 'Resolved At',
      resolvedBy: 'Resolved By'
    },
    editDialog: {
      title: 'Update Feedback Status',
      statusLabel: 'Status',
      resolutionNotesLabel: 'Resolution Notes (optional)'
    },
    table: {
      type: 'Type',
      status: 'Status',
      content: 'Content',
      user: 'User',
      createdAt: 'Submitted At',
      actions: 'Actions'
    },
    feedbackStatus: {
      new: 'New',
      open: 'Open',
      in_progress: 'In Progress',
      resolved: 'Resolved',
      closed: 'Closed',
      rejected: 'Rejected'
    },
    feedbackTypes: {
      bug: 'Bug Report',
      feature: 'Feature Request',
      improvement: 'Improvement',
      comment: 'Comment',
      other: 'Other'
    },
    statusFilters: {
      all: 'All Statuses'
      // Specific statuses are in feedbackStatus
    },
    typeFilters: {
      all: 'All Types'
      // Specific types are in feedbackTypes
    }
  },

  // Search related translations
  search: {
    placeholder: 'Search...',
    advancedSearch: 'Advanced Search',
    close: 'Close',
    ageCategory: 'Age Category',
    advancedFilters: 'Advanced Filters',
    options: {
      addToFilter: 'Filter',
      exclude: 'Exclude',
      alwaysExclude: 'Always Exclude',
      copyToClipboard: 'Copy Text',
      reverse: 'Reverse Search (Filter/Exclude)'
    },
    ageOptions: {
      onlyAllAges: '⚪ Only All Ages',
      onlyR15: '🟠 Only R-15',
      onlyAdult: '🔞 Only R-18',
      allAgesAndR15: '🟡 All Ages & R-15'
    },
    namespace: {
      tag: 'Search Tags',
      circle: 'Search Circles',
      va: 'Search Voice Actors',
      rate: 'Filter by Rating (greater than)',
      price: 'Filter by Price (greater than)',
      sell: 'Filter by Sales (greater than)',
      age: 'Filter by Age Category',
      duration: 'Filter by Duration (greater than)',
      lang: 'Filter by Language',
      '-tag': 'Exclude Tags',
      '-circle': 'Exclude Circles',
      '-va': 'Exclude Voice Actors',
      '-age': 'Exclude Age Category',
      '-duration': 'Filter by Duration (less than)',
      '-lang': 'Exclude Language'
    },
    plainText: {
      filter: 'Plain Text Search',
      exclude: 'Plain Text Search (Exclude)'
    },
    selectOrEnter: 'Select or enter {type}',
    apply: 'Apply',
    exclude: 'Exclude',
    cancel: 'Cancel'
  },

  // Work card component
  workCard: {
    soldCount: 'Sold count',
    ageRating: {
      adult: 'R-18',
      r15: 'R-15',
      general: 'All ages'
    },
    dlsiteLink: 'DLsite',
    rating: 'Average',
    commentCount: 'Comments'
  },

  // Subtitle upload related translations
  subtitle: {
    upload: {
      step1Title: 'Upload Subtitle File',
      dragDropText: 'Drag and drop subtitle files here',
      orText: 'or',
      selectFilesBtn: 'Select Files',
      selectedFiles: 'Selected Files',
      step2Title: 'Review & Edit Matches',
      overallThreshold: 'Overall Threshold',
      matchThreshold: 'Match Threshold',
      selectAll: 'SELECT ALL',
      clearAll: 'CLEAR ALL',
      applyToAllFiles: 'APPLY TO ALL FILES',
      loadingAudioFiles: 'Loading audio files...',
      noAudioFiles: 'No audio files found',
      noAudioFilesDesc: 'Unable to find audio files to match with this subtitle.',
      fetchAudioFiles: 'Fetch Audio Files',
      searchTracksPlaceholder: 'Search tracks...',
      step3Title: 'Confirm & Upload',
      uploadSummary: 'Upload Summary',
      subtitleFiles: 'Subtitle files',
      description: 'Description',
      descriptionPlaceholder: 'Optional description',
      visibility: 'Visibility',
      public: 'Public',
      selectedTracks: 'Selected tracks',
      showSelectedTracks: 'Show selected tracks',
      match: 'match',
      matches: 'matches',
      noMatch: 'No match',
      preview: 'Preview match',
      removeMatch: 'Remove match',
      forceMatch: 'Force match',
      matchPreviewTitle: 'Match Preview',
      settingsAppliedToAll: 'Settings applied to all files',
      thresholdApplied: 'Applied {threshold}% threshold to all files',
      uploadBtn: 'Upload',
      trackName: 'Track Name',
      matchPercentage: 'Match %',
      actions: 'Actions'
    },
    notification: {
      subtitleUploaded: 'Subtitle uploaded successfully',
      multipleTitlesUploaded: '{count} subtitles uploaded successfully',
      uploadFailed: 'Failed to upload subtitle',
      missingStorageInfo: 'Missing storage information',
      loadAudioFilesFailed: 'Failed to load audio files. Please try again.'
    }
  },

  // Admin Archive Password Management
  adminArchivePasswords: {
    title: 'Archive Password Management',
    addPassword: 'Add Password',
    editPassword: 'Edit Password',
    batchImport: 'Batch Import',
    password: 'Password',
    description: 'Description',
    descriptionHint: 'Optional description for this password',
    cachedPasswords: 'Cached Passwords',
    storageId: 'Storage ID',
    archivePath: 'Archive Path',
    lastUsed: 'Last Used',
    confirmDelete: 'Are you sure you want to delete this password?',
    confirmDeleteCached: 'Are you sure you want to delete the cached password for "{path}"?',
    passwordList: 'Password List',
    passwordListHint: 'Enter passwords separated by the selected separator',
    separator: 'Separator',
    separators: {
      newline: 'New Line',
      comma: 'Comma (,)',
      semicolon: 'Semicolon (;)',
      space: 'Space'
    },
    import: 'Import',
    importResult: 'Import completed: {added} added, {duplicates} duplicates, {invalid} invalid, {total} total',
    importErrors: 'Import errors'
  }
}
