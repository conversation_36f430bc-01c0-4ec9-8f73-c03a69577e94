<template>
  <q-layout view="hHh Lpr lFf" class="bg-grey-3 kikoeru-admin-layout">
    <q-header class="shadow-4" :class="{'bg-dark': $q.dark.isActive}">
      <q-toolbar class="row justify-between">
        <q-btn flat dense round @click="drawerOpen = !drawerOpen" icon="menu" aria-label="Menu" />

        <q-btn flat size="md" icon="arrow_back_ios" @click="back()" />

        <q-toolbar-title>
          <router-link :to="'/admin'" class="text-white text-decoration-none">
            Kikoeru {{ t('nav.adminPanel') }}
          </router-link>
        </q-toolbar-title>

        <q-btn flat icon="home" @click="router.push('/')" :label="t('common.back')" />
      </q-toolbar>
    </q-header>

    <q-drawer
      v-model="drawerOpen"
      show-if-above
      :mini="miniState"
      @mouseover="miniState = false"
      @mouseout="miniState = true"
      mini-to-overlay
      :width="250"
      :breakpoint="500"
      bordered
      content-class="bg-grey-1"
    >
      <q-scroll-area class="fit">
        <q-list>
          <q-item
            clickable
            v-ripple
            exact
            :to="link.path"
            active-class="text-deep-purple text-weight-medium"
            v-for="(link, index) in adminLinks"
            :key="index"
            @click="miniState = true"
          >
            <q-item-section avatar>
              <q-icon :name="link.icon" />
            </q-item-section>

            <q-item-section>
              <q-item-label class="text-subtitle1">
                {{link.title}}
              </q-item-label>
            </q-item-section>
          </q-item>
        </q-list>

        <q-separator />

        <q-list>
          <q-item
            clickable
            v-ripple
            exact
            active-class="text-deep-purple text-weight-medium"
            @click="logout"
          >
            <q-item-section avatar>
              <q-icon name="exit_to_app" />
            </q-item-section>

            <q-item-section>
              <q-item-label class="text-subtitle1">
                {{ t('nav.logout') }}
              </q-item-label>
              <q-item-label caption lines="2">{{ userName }}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </q-scroll-area>
    </q-drawer>

    <q-page-container>
      <router-view />
      <q-page-scroller position="bottom-right" :scroll-offset="150" :offset="[18, 18]">
        <q-btn fab icon="keyboard_arrow_up" color="accent" />
      </q-page-scroller>
    </q-page-container>
  </q-layout>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { useAuth } from '../composables/useAuth'
import { useNotification } from '../composables/useNotification'

defineOptions({
  name: 'DashboardLayout'
})

const { t } = useI18n()
const router = useRouter()
const { user, logout: authLogout } = useAuth()
const { showSuccessNotification } = useNotification()

// Reactive data
const drawerOpen = ref(false)
const miniState = ref(true)

// Computed properties
const userName = computed(() => user.value?.username || '')

const adminLinks = computed(() => [
  {
    title: t('dashboardHome.title'),
    icon: 'dashboard',
    path: '/admin'
  },
  {
    title: t('adminStorage.title'),
    icon: 'storage',
    path: '/admin/storages'
  },
  {
    title: t('adminTasks.title'),
    icon: 'task',
    path: '/admin/tasks'
  },
  {
    title: t('dashboardScanner.title'),
    icon: 'scanner',
    path: '/admin/scanner'
  },
  {
    title: t('dashboardUserManage.title'),
    icon: 'people',
    path: '/admin/usermanage'
  },
  {
    title: t('adminProfile.title'),
    icon: 'account_circle',
    path: '/admin/profile'
  },
  {
    title: t('dashboardFeedbackManage.title'),
    icon: 'feedback',
    path: '/admin/feedback'
  },
  {
    title: t('adminArchivePasswords.title'),
    icon: 'lock',
    path: '/admin/archive-passwords'
  },
  {
    title: t('dashboardAdvanced.title'),
    icon: 'settings',
    path: '/admin/advanced'
  }
])

// Methods
const logout = () => {
  authLogout()
  showSuccessNotification(t('notification.loggedOut'))
  router.push('/')
}

const back = () => {
  router.go(-1)
}
</script>

<!-- Styles moved to /src/css/components/DashboardLayout.scss -->
