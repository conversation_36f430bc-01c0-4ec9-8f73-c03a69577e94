<template>
  <div>
    <div class="text-h5 text-weight-regular q-ma-md">
      播放列表
      <span v-show="pagination.total_items">
        ({{pagination.total_items}})
      </span>
    </div>

    <div class="q-mx-md">
      <!-- 创建播放列表按钮 -->
      <div class="row justify-end q-mb-md">
        <q-btn
          color="primary"
          icon="add"
          label="创建播放列表"
          @click="showCreateDialog = true"
        />
      </div>

      <q-infinite-scroll @load="onLoad" :offset="250" :disable="stopLoad" class="col">
        <div class="row q-col-gutter-x-md q-col-gutter-y-lg">
          <div class="col-xs-12 col-sm-6 col-md-4 col-lg-3 col-xl-3" v-for="playlist in playlists" :key="playlist.id">
            <PlaylistCard :playlist="playlist" @refresh="reset" />
          </div>
        </div>

        <div v-show="stopLoad" class="q-mt-lg q-mb-xl text-h6 text-bold text-center">END</div>

        <template v-slot:loading>
          <div class="row justify-center q-my-md">
            <q-spinner-dots color="primary" size="40px" />
          </div>
        </template>
      </q-infinite-scroll>
    </div>

    <!-- 创建播放列表对话框 -->
    <q-dialog v-model="showCreateDialog" persistent>
      <q-card style="min-width: 400px">
        <q-card-section>
          <div class="text-h6">创建播放列表</div>
        </q-card-section>

        <q-card-section class="q-pt-none">
          <q-input
            dense
            v-model="newPlaylist.name"
            label="播放列表名称"
            maxlength="100"
            counter
            :rules="[val => val && val.length > 0 || '请输入播放列表名称']"
          />

          <q-input
            dense
            v-model="newPlaylist.description"
            label="描述 (可选)"
            type="textarea"
            rows="3"
            maxlength="500"
            counter
            class="q-mt-md"
          />

          <q-select
            dense
            v-model="newPlaylist.visibility"
            :options="visibilityOptions"
            label="可见性"
            class="q-mt-md"
          />
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="取消" @click="cancelCreate" />
          <q-btn flat label="创建" color="primary" @click="createPlaylist" :loading="creating" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onActivated, onDeactivated, getCurrentInstance } from 'vue'
import PlaylistCard from '../components/PlaylistCard.vue'
import { useNotification } from '../composables/useNotification'

defineOptions({
  name: 'PlaylistsPage'
})

const { proxy } = getCurrentInstance()
const { showSuccessNotification, showErrorNotification } = useNotification()

// Reactive data
const stopLoad = ref(false)
const playlists = ref([])
const pagination = ref({ current_page: 0, per_page: 20, total_items: 0 })
const showCreateDialog = ref(false)
const creating = ref(false)
const newPlaylist = ref({
  name: '',
  description: '',
  visibility: 'private'
})
const visibilityOptions = ref([
  { label: '私有', value: 'private' },
  { label: '不公开', value: 'unlisted' },
  { label: '公开', value: 'public' }
])

// Computed properties
const requestParams = computed(() => ({
  page: pagination.value.current_page + 1 || 1,
  page_size: 20
}))

// Methods
const onLoad = (index, done) => {
  requestPlaylists()
    .then(() => done())
}

const requestPlaylists = async () => {
  try {
    const response = await proxy.$api.get('/api/v1/me/playlists', {
      params: requestParams.value
    })

    const data = response.data
    const playlistsData = data.items || []

    playlists.value = (requestParams.value.page === 1) ? playlistsData.concat() : playlists.value.concat(playlistsData)
    pagination.value = data.pagination || { current_page: 0, per_page: 20, total_items: 0 }

    if (playlists.value.length >= pagination.value.total_items) {
      stopLoad.value = true
    }
  } catch (error) {
    if (error.response?.status !== 401) {
      showErrorNotification(error.response?.data?.error || error.message || 'Failed to load playlists')
    }
    stopLoad.value = true
  }
}

const createPlaylist = async () => {
  if (!newPlaylist.value.name.trim()) {
    showErrorNotification('请输入播放列表名称')
    return
  }

  creating.value = true
  try {
    await proxy.$api.post('/api/v1/me/playlists', newPlaylist.value)
    showSuccessNotification('播放列表创建成功')
    cancelCreate()
    reset()
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || '创建播放列表失败')
  } finally {
    creating.value = false
  }
}

const cancelCreate = () => {
  showCreateDialog.value = false
  newPlaylist.value = {
    name: '',
    description: '',
    visibility: 'private'
  }
}

const reset = () => {
  stopLoad.value = true
  pagination.value = { current_page: 0, per_page: 20, total_items: 0 }
  requestPlaylists()
    .then(() => {
      stopLoad.value = false
    })
}

// Lifecycle hooks
onActivated(() => {
  stopLoad.value = false
})

onDeactivated(() => {
  stopLoad.value = true
})
</script>
