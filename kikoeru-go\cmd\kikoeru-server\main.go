package main

import (
	"context"
	"crypto/rand"
	"crypto/rsa"
	"crypto/tls"
	"crypto/x509"
	"crypto/x509/pkix"
	"errors"
	"flag"
	"fmt"
	"math/big"
	"net"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/Sakura-Byte/kikoeru-go/pkg/admin"
	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
	"github.com/Sakura-Byte/kikoeru-go/pkg/auth"
	"github.com/Sakura-Byte/kikoeru-go/pkg/config"
	"github.com/Sakura-Byte/kikoeru-go/pkg/email"
	"github.com/Sakura-Byte/kikoeru-go/pkg/http/rest"
	"github.com/Sakura-Byte/kikoeru-go/pkg/log"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"github.com/Sakura-Byte/kikoeru-go/pkg/scanner"
	"github.com/Sakura-Byte/kikoeru-go/pkg/service"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/cachedriver"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/database"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/driver"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/driver/all_drivers"
	"github.com/gin-gonic/gin"

	// Import all archive tools to ensure they're registered
	_ "github.com/Sakura-Byte/kikoeru-go/pkg/storage/archive/all_tools"
)

const (
	defaultAdminUsername = "admin"
	randomPasswordLength = 12
)

var (
	version = "dev"
	commit  = "none"
	date    = "unknown"
)

// 生成自签名SSL证书
func generateSelfSignedCert() (*tls.Certificate, error) {
	// 生成一个新的私钥
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		return nil, err
	}

	// 准备证书模板
	serialNumberLimit := new(big.Int).Lsh(big.NewInt(1), 128)
	serialNumber, err := rand.Int(rand.Reader, serialNumberLimit)
	if err != nil {
		return nil, err
	}

	notBefore := time.Now()
	notAfter := notBefore.Add(365 * 24 * time.Hour) // 1年有效期

	template := x509.Certificate{
		SerialNumber: serialNumber,
		Subject: pkix.Name{
			Organization: []string{"Kikoeru Local Development"},
			CommonName:   "localhost",
		},
		NotBefore:             notBefore,
		NotAfter:              notAfter,
		KeyUsage:              x509.KeyUsageKeyEncipherment | x509.KeyUsageDigitalSignature,
		ExtKeyUsage:           []x509.ExtKeyUsage{x509.ExtKeyUsageServerAuth},
		BasicConstraintsValid: true,
		IsCA:                  true,
		DNSNames:              []string{"localhost"},
		IPAddresses:           []net.IP{net.ParseIP("127.0.0.1")},
	}

	// 创建自签名证书
	derBytes, err := x509.CreateCertificate(rand.Reader, &template, &template, &privateKey.PublicKey, privateKey)
	if err != nil {
		return nil, err
	}

	// 将DER编码的证书转换为PEM编码
	cert := &tls.Certificate{
		Certificate: [][]byte{derBytes},
		PrivateKey:  privateKey,
	}

	return cert, nil
}

func main() {
	ctx := context.Background()
	var configPath string
	flag.StringVar(&configPath, "config", "", "Path to the configuration file (e.g., configs/config.yaml)")
	showVersion := flag.Bool("version", false, "Print version information and exit")

	// Parse initial flags
	flag.Parse()

	if *showVersion {
		fmt.Printf("Kikoeru-Go Server\n")
		fmt.Printf("Version: %s\n", version)
		fmt.Printf("Commit: %s\n", commit)
		fmt.Printf("Built: %s\n", date)
		os.Exit(0)
	}
	gin.SetMode(gin.ReleaseMode)

	// Check if this is an admin command
	if admin.HandleCommands(ctx, os.Args, configPath) {
		return
	}

	// Normal server startup
	appConfig, err := config.LoadConfig(configPath)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error loading application configuration: %v\n", err)
		os.Exit(1)
	}

	log.InitGlobalLogger(appConfig.Log)
	logger := log.L(ctx)
	logger.Debug("Logger initialized successfully", "level", appConfig.Log.Level, "format", appConfig.Log.Format)
	logger.Debug("Configuration loaded successfully")
	if configPath != "" {
		logger.Debug("Using configuration file", "path", configPath)
	} else {
		logger.Info("Using default configuration search paths")
	}
	logger.Info("Kikoeru-Go server starting...", "version", version)
	logger.Debug("Full configuration loaded", "config_details_omitted_for_brevity", true)

	logger.Info("Initializing database connection...")
	db, err := database.ConnectDB(ctx, appConfig.Database, appConfig.Paths)
	if err != nil {
		logger.Error("Failed to connect to database", "error", err)
		os.Exit(1)
	}
	logger.Info("Database connection successful")

	logger.Info("Running database migrations...")
	if err := database.RunMigrations(ctx, appConfig.Database, appConfig.Paths, "migrations"); err != nil {
		logger.Error("Failed to run database migrations", "error", err)
		os.Exit(1)
	}
	logger.Info("Database migrations completed")

	userRepo := database.NewUserRepository(db)

	_, err = userRepo.GetByUsername(ctx, defaultAdminUsername)
	if err != nil {
		if errors.Is(err, apperrors.ErrUserNotFound) {
			logger.Info("Default admin user not found, creating one...")
			const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
			password := make([]byte, randomPasswordLength)
			for i := range password {
				num, randErr := rand.Int(rand.Reader, big.NewInt(int64(len(charset))))
				if randErr != nil {
					logger.Error("Failed to generate random number for password", "error", randErr)
					os.Exit(1)
				}
				password[i] = charset[num.Int64()]
			}
			randomPassword := string(password)
			hashedPassword, hashErr := auth.HashPassword(randomPassword, appConfig.Auth.BcryptCost)
			if hashErr != nil {
				logger.Error("Failed to hash default admin password", "error", hashErr)
				os.Exit(1)
			}
			adminUser := &models.User{
				Username:      defaultAdminUsername,
				Password:      hashedPassword,
				Group:         models.UserGroupAdmin,
				EmailVerified: true,
			}
			if createErr := userRepo.Create(ctx, adminUser); createErr != nil {
				logger.Error("Failed to create default admin user", "error", createErr)
				os.Exit(1)
			}
			logger.Info("************************************************************")
			logger.Info("Default admin user 'admin' created successfully.")
			logger.Info("IMPORTANT: Your randomly generated password for 'admin' is:")
			logger.Info(randomPassword)
			logger.Info("Please store this password securely.")
			logger.Info("************************************************************")
		} else {
			logger.Error("Failed to check for default admin user", "error", err)
		}
	} else {
		logger.Info("Default admin user 'admin' already exists.")
	}

	logger.Info("Initializing other repositories...")
	workRepo := database.NewWorkRepository(db)
	workRepoAdapter := database.NewWorkRepositoryAdapter(workRepo)
	userRepoAdapter := database.NewUserRepositoryAdapter(userRepo)
	playlistRepo := database.NewPlaylistRepository(db)
	feedbackRepo := database.NewFeedbackRepository(db)
	storageSourceRepo := database.NewStorageSourceRepository(db)
	tagRepo := database.NewTagRepository(db)
	vaRepo := database.NewVARepository(db)
	circleRepo := database.NewCircleRepository(db)
	playHistoryRepo := database.NewPlayHistoryRepository(db)
	backgroundTaskRepo := database.NewBackgroundTaskRepository(db)
	// favoriteRepository := database.NewGormFavoriteRepository(db) // Removed
	reviewRepository := database.NewReviewRepository(db)             // Added
	subtitleRepo := database.NewSubtitleRepository(db)               // Added subtitle repository
	archivePasswordRepo := database.NewArchivePasswordRepository(db) // Added archive password repository

	logger.Info("Registering driver wrapper...")
	driver.RegisterDriverWrapper(cachedriver.NewCachedStorageDriver) // Register the cached driver wrapper
	logger.Info("Driver wrapper registered.")

	logger.Info("Initializing storage driver manager...")
	globalAppDriverCommonConfig := driver.AlistDriverCommonConfig{
		RequestTimeout: appConfig.Server.ReadTimeout,
	}
	driverManager := driver.NewStorageDriverManager(storageSourceRepo, appConfig, globalAppDriverCommonConfig)

	all_drivers.RegisterAllDrivers(driverManager)

	if err := driverManager.InitDrivers(ctx); err != nil {
		logger.Error("Failed to initialize storage drivers", "error", err)
		os.Exit(1)
	}
	logger.Info("Storage drivers initialized successfully")

	logger.Info("Initializing email sender...")
	var emailSender email.EmailSender
	if appConfig.Auth.EnableEmailFeatures && appConfig.SMTP.Host != "" {
		emailSender = email.NewSMTPEmailSender(appConfig.SMTP, appConfig.Auth)
		logger.Info("SMTP email sender initialized.")
	} else {
		emailSender = email.NewDummyEmailSender()
		logger.Info("Dummy email sender initialized (email features disabled or SMTP not configured).")
	}

	logger.Info("Initializing core services...")
	userService := service.NewUserService(userRepo, appConfig, emailSender)
	scraperService := service.NewScraperService(appConfig, driverManager)
	archivePasswordService := service.NewArchivePasswordService(archivePasswordRepo)

	logger.Info("Initializing FileSystemService...")
	fileSystemService := service.NewFileSystemServiceImpl(
		driverManager,
		storageSourceRepo,
		appConfig,
		archivePasswordService,
	)
	logger.Info("FileSystemService initialized.")

	mediaService := service.NewMediaService(
		workRepo,
		storageSourceRepo,
		scraperService,
		fileSystemService,
		appConfig,
	)

	workService := service.NewWorkService(
		workRepo,
		tagRepo,
		vaRepo,
		circleRepo,
		playlistRepo,
		// favoriteRepository, // Removed
		reviewRepository, // Added, or handle review logic separately if WorkService doesn't need it directly
		scraperService,
		mediaService,
		appConfig,
	)
	summaryService := service.NewSummaryService(circleRepo, tagRepo, vaRepo)

	logger.Info("Initializing BackgroundTaskService...")
	bgTaskServicePort := service.NewBackgroundTaskService(backgroundTaskRepo)
	// Type assert to the concrete type to call setters, as SetScannerService and SetWorkService are not on the interface
	backgroundTaskServiceImpl, ok := bgTaskServicePort.(*service.BackgroundTaskService)
	if !ok {
		logger.Error("Failed to assert BackgroundTaskService to its concrete type *service.BackgroundTaskService")
		os.Exit(1) // or handle error appropriately
	}

	logger.Info("Initializing remaining services (Scanner, Playlist, Feedback, StorageAdmin)...")
	scannerService := scanner.NewScannerService(appConfig, driverManager, workRepo, workService, storageSourceRepo)

	backgroundTaskServiceImpl.SetScannerService(scannerService)
	backgroundTaskServiceImpl.SetWorkService(workService)
	// backgroundTaskService (the interface) will be passed to SetupRouter

	playlistService := service.NewPlaylistService(playlistRepo, workRepo, mediaService, appConfig)
	feedbackService := service.NewFeedbackService(feedbackRepo, userRepo, appConfig)
	storageAdminService := service.NewStorageAdminService(storageSourceRepo, userRepo, driverManager, appConfig, workRepo, tagRepo, vaRepo, circleRepo)
	playHistoryService := service.NewPlayHistoryService(playHistoryRepo, workRepo)
	// favoriteService := service.NewFavoriteService(favoriteRepository, workRepo, logger.With("service", "Favorite")) // Removed
	reviewService := service.NewReviewService(reviewRepository, workRepo, workService)                       // Added workService
	subtitleService := service.NewSubtitleService(subtitleRepo, workRepoAdapter, userRepoAdapter, appConfig) // Added subtitle service

	logger.Debug("About to call ScanAllLibraries...")
	if errScan := scannerService.ScanAllLibraries(context.Background()); errScan != nil {
		logger.Error("Error during initial library scan", "error", errScan)
		logger.Debug("ScanAllLibraries returned an error.")
	} else {
		logger.Debug("ScanAllLibraries completed successfully (returned nil).")
	}
	logger.Debug("Initial library scan attempt finished. Proceeding to router setup.")

	logger.Debug("Setting up HTTP router...")
	router := rest.SetupRouter(
		appConfig,
		userService, workService, mediaService,
		fileSystemService,
		playHistoryService, summaryService,
		scannerService,
		playlistService, feedbackService, storageAdminService,
		bgTaskServicePort,      // Pass the interface type
		reviewService,          // Added
		subtitleService,        // Added subtitle service
		archivePasswordService, // Added archive password service
	)

	// 设置HTTP服务器
	logger.Debug("Starting HTTP server...", "address", appConfig.Server.ListenPort)
	srv := &http.Server{
		Addr: appConfig.Server.ListenPort, Handler: router,
		ReadTimeout: appConfig.Server.ReadTimeout, WriteTimeout: appConfig.Server.WriteTimeout,
		IdleTimeout: appConfig.Server.IdleTimeout,
	}
	go func() {
		if err := srv.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			logger.Error("HTTP server ListenAndServe error", "error", err)
		}
	}()

	// 启动HTTPS服务器（自动支持HTTP/2）
	var httpsSrv *http.Server
	if appConfig.Server.HttpsEnabled {
		logger.Info("Starting HTTPS server (with HTTP/2 support)...", "address", appConfig.Server.HttpsPort)
		httpsSrv = &http.Server{
			Addr: appConfig.Server.HttpsPort, Handler: router,
			ReadTimeout:  appConfig.Server.ReadTimeout,
			WriteTimeout: appConfig.Server.WriteTimeout,
			IdleTimeout:  appConfig.Server.IdleTimeout,
		}
		go func() {
			// 设置TLS配置
			httpsSrv.TLSConfig = &tls.Config{
				MinVersion: tls.VersionTLS12,
				NextProtos: []string{"h2", "http/1.1"}, // 启用HTTP/2协议
			}

			// 判断是否使用自签名证书
			if appConfig.Server.UseSelfSignedCert {
				logger.Info("Using self-signed certificate as configured")
				// 生成自签名证书
				cert, err := generateSelfSignedCert()
				if err != nil {
					logger.Error("Failed to generate self-signed certificate", "error", err)
					return
				}

				// 设置生成的证书
				httpsSrv.TLSConfig.Certificates = []tls.Certificate{*cert}

				// 启动HTTPS服务器
				if err := httpsSrv.ListenAndServeTLS("", ""); err != nil && !errors.Is(err, http.ErrServerClosed) {
					logger.Error("HTTPS server ListenAndServeTLS error", "error", err)
				}
			} else {
				// 检查证书文件是否存在
				_, certErr := os.Stat(appConfig.Server.HttpsCertFile)
				_, keyErr := os.Stat(appConfig.Server.HttpsKeyFile)

				if os.IsNotExist(certErr) || os.IsNotExist(keyErr) {
					logger.Warn("Certificate or key file not found, generating self-signed certificate...")

					// 生成自签名证书
					cert, err := generateSelfSignedCert()
					if err != nil {
						logger.Error("Failed to generate self-signed certificate", "error", err)
						return
					}

					// 使用生成的证书
					httpsSrv.TLSConfig.Certificates = []tls.Certificate{*cert}

					// 启动HTTPS服务器
					if err := httpsSrv.ListenAndServeTLS("", ""); err != nil && !errors.Is(err, http.ErrServerClosed) {
						logger.Error("HTTPS server ListenAndServeTLS error", "error", err)
					}
				} else {
					// 使用配置文件中指定的证书
					if err := httpsSrv.ListenAndServeTLS(
						appConfig.Server.HttpsCertFile,
						appConfig.Server.HttpsKeyFile,
					); err != nil && !errors.Is(err, http.ErrServerClosed) {
						logger.Error("HTTPS server ListenAndServeTLS error", "error", err)
					}
				}
			}
		}()
	}

	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	logger.Info("Shutting down server...")
	shutdownCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 关闭HTTP服务器
	if err := srv.Shutdown(shutdownCtx); err != nil {
		logger.Error("HTTP server forced to shutdown", "error", err)
	}

	// 关闭HTTPS服务器（如果启用）
	if httpsSrv != nil {
		if err := httpsSrv.Shutdown(shutdownCtx); err != nil {
			logger.Error("HTTPS server forced to shutdown", "error", err)
		}
	}

	logger.Info("Server exiting")
}
