import { defineBoot } from '#q-app/wrappers'
import { LocalStorage } from 'quasar'
import { useUserStore } from '../stores/user.js'

export default defineBoot(async ({ app }) => {
  // Pinia is already registered by Quasar automatically from stores/index.js
  // No need to register it again here to avoid "Plugin has already been applied" warning

  // Get user store instance (Pinia is already available)
  const userStore = useUserStore()

  // Try to restore user session from localStorage
  const token = LocalStorage.getItem('jwt-token')

  if (token) {
    try {
      // Fetch user info if token exists
      await userStore.fetchCurrentUser(app.config.globalProperties.$api)
    } catch (error) {
      console.warn('Failed to restore user session:', error)
      // Token is likely invalid, clear it
      LocalStorage.remove('jwt-token')
    }
  }

  // Fetch site configuration
  try {
    const response = await app.config.globalProperties.$api.get('/api/v1/site/info')
    // Store site config in a global property for components to access
    app.config.globalProperties.$siteConfig = response.data
  } catch (error) {
    console.warn('Failed to fetch site configuration:', error)
    // Set default config if fetch fails
    app.config.globalProperties.$siteConfig = {
      enable_email_features: false,
      ensure_email: false,
      allow_registration: true
    }
  }
})
