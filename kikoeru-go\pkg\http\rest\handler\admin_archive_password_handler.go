package handler

import (
	"net/http"
	"strconv"

	"github.com/Sakura-Byte/kikoeru-go/pkg/http/rest/common"
	"github.com/Sakura-Byte/kikoeru-go/pkg/log"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"github.com/Sakura-Byte/kikoeru-go/pkg/ports"
	"github.com/gin-gonic/gin"
)

// AdminArchivePasswordHandler handles admin archive password management endpoints
type AdminArchivePasswordHandler struct {
	service ports.ArchivePasswordService
}

// NewAdminArchivePasswordHandler creates a new admin archive password handler
func NewAdminArchivePasswordHandler(service ports.ArchivePasswordService) *AdminArchivePasswordHandler {
	return &AdminArchivePasswordHandler{
		service: service,
	}
}

// GetAllPasswords handles GET /api/v1/admin/archive-passwords
func (h *AdminArchivePasswordHandler) GetAllPasswords(c *gin.Context) {
	passwords, err := h.service.GetAllPasswords(c.Request.Context())
	if err != nil {
		log.Error(c.Request.Context(), "Failed to get all archive passwords", "error", err)
		common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to retrieve archive passwords")
		return
	}

	common.SendSuccessResponse(c, http.StatusOK, passwords)
}

// CreatePassword handles POST /api/v1/admin/archive-passwords
func (h *AdminArchivePasswordHandler) CreatePassword(c *gin.Context) {
	var req models.ArchivePasswordInput
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Warn(c.Request.Context(), "Failed to bind JSON for create password", "error", err)
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid request payload")
		return
	}

	password, err := h.service.CreatePassword(c.Request.Context(), &req)
	if err != nil {
		log.Error(c.Request.Context(), "Failed to create archive password", "error", err)

		statusCode := http.StatusInternalServerError
		message := "Failed to create archive password"

		// Handle specific error types
		switch err.Error() {
		case "archive password already exists":
			statusCode = http.StatusConflict
			message = "Password already exists in the list"
		case "validation failed":
			statusCode = http.StatusBadRequest
			message = err.Error()
		}

		common.SendErrorResponse(c, statusCode, message)
		return
	}

	common.SendSuccessResponse(c, http.StatusCreated, password)
}

// UpdatePassword handles PUT /api/v1/admin/archive-passwords/:id
func (h *AdminArchivePasswordHandler) UpdatePassword(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid password ID")
		return
	}

	var req models.ArchivePasswordUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Warn(c.Request.Context(), "Failed to bind JSON for update password", "error", err)
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid request payload")
		return
	}

	password, err := h.service.UpdatePassword(c.Request.Context(), uint(id), &req)
	if err != nil {
		log.Error(c.Request.Context(), "Failed to update archive password", "id", id, "error", err)

		statusCode := http.StatusInternalServerError
		message := "Failed to update archive password"

		// Handle specific error types
		switch err.Error() {
		case "archive password not found":
			statusCode = http.StatusNotFound
			message = "Password not found"
		case "archive password already exists":
			statusCode = http.StatusConflict
			message = "Password already exists in the list"
		case "validation failed":
			statusCode = http.StatusBadRequest
			message = err.Error()
		}

		common.SendErrorResponse(c, statusCode, message)
		return
	}

	common.SendSuccessResponse(c, http.StatusOK, password)
}

// DeletePassword handles DELETE /api/v1/admin/archive-passwords/:id
func (h *AdminArchivePasswordHandler) DeletePassword(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid password ID")
		return
	}

	if err := h.service.DeletePassword(c.Request.Context(), uint(id)); err != nil {
		log.Error(c.Request.Context(), "Failed to delete archive password", "id", id, "error", err)

		statusCode := http.StatusInternalServerError
		message := "Failed to delete archive password"

		if err.Error() == "archive password not found" {
			statusCode = http.StatusNotFound
			message = "Password not found"
		}

		common.SendErrorResponse(c, statusCode, message)
		return
	}

	common.SendSuccessResponse(c, http.StatusOK, gin.H{"message": "Password deleted successfully"})
}

// BatchImportPasswords handles POST /api/v1/admin/archive-passwords/batch-import
func (h *AdminArchivePasswordHandler) BatchImportPasswords(c *gin.Context) {
	var req models.ArchivePasswordBatchImportRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Warn(c.Request.Context(), "Failed to bind JSON for batch import", "error", err)
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid request payload")
		return
	}

	result, err := h.service.BatchImportPasswords(c.Request.Context(), &req)
	if err != nil {
		log.Error(c.Request.Context(), "Failed to batch import passwords", "error", err)
		common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to import passwords")
		return
	}

	common.SendSuccessResponse(c, http.StatusOK, result)
}

// GetAllCachedPasswords handles GET /api/v1/admin/archive-passwords/cache
func (h *AdminArchivePasswordHandler) GetAllCachedPasswords(c *gin.Context) {
	caches, err := h.service.GetAllCachedPasswords(c.Request.Context())
	if err != nil {
		log.Error(c.Request.Context(), "Failed to get all cached passwords", "error", err)
		common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to retrieve cached passwords")
		return
	}

	common.SendSuccessResponse(c, http.StatusOK, caches)
}

// DeleteCachedPassword handles DELETE /api/v1/admin/archive-passwords/cache
func (h *AdminArchivePasswordHandler) DeleteCachedPassword(c *gin.Context) {
	storageIDStr := c.Query("storage_id")
	archivePath := c.Query("archive_path")

	if storageIDStr == "" || archivePath == "" {
		common.SendErrorResponse(c, http.StatusBadRequest, "storage_id and archive_path query parameters are required")
		return
	}

	storageID, err := strconv.ParseUint(storageIDStr, 10, 32)
	if err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid storage_id format")
		return
	}

	if err := h.service.DeleteCachedPassword(c.Request.Context(), uint(storageID), archivePath); err != nil {
		log.Error(c.Request.Context(), "Failed to delete cached password", "storageID", storageID, "archivePath", archivePath, "error", err)

		statusCode := http.StatusInternalServerError
		message := "Failed to delete cached password"

		if err.Error() == "archive password not found" {
			statusCode = http.StatusNotFound
			message = "Cached password not found"
		}

		common.SendErrorResponse(c, statusCode, message)
		return
	}

	common.SendSuccessResponse(c, http.StatusOK, gin.H{"message": "Cached password deleted successfully"})
}
