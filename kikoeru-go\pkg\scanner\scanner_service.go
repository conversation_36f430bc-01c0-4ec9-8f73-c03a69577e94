package scanner

import (
	"context"
	"errors"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"sync"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
	"github.com/Sakura-Byte/kikoeru-go/pkg/config"
	"github.com/Sakura-Byte/kikoeru-go/pkg/log"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"github.com/Sakura-Byte/kikoeru-go/pkg/ports"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/database"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/driver"
)

// ScanStatus and ScannerStatusResponse types have been moved to github.com/Sakura-Byte/kikoeru-go/pkg/ports

// ScannerService interface has been moved to github.com/Sakura-Byte/kikoeru-go/pkg/ports

var (
	rjCodePattern  = regexp.MustCompile(`(?i)(RJ|VJ|BJ)(\d{6,8})`)
	rjRangePattern = regexp.MustCompile(`(?i)(RJ|VJ|BJ)\d{6,8}~(RJ|VJ|BJ)\d{6,8}`)
)

// ProgressUpdater type has been moved to github.com/Sakura-Byte/kikoeru-go/pkg/ports

type ScannerService struct {
	appConfig                *config.AppConfig
	driverManager            driver.DriverManagerGetter
	workRepo                 database.WorkRepository
	workService              ports.WorkService
	storageRepo              database.StorageSourceRepository
	statusMutex              sync.RWMutex
	currentStatus            ports.ScanStatus
	currentStorageIdentifier string
	processedWorks           int
	totalWorksInLib          int
	lastError                error
	currentScanCtx           context.Context
	currentScanCancel        context.CancelFunc
}

// Ensure ScannerService implements ports.ScannerService
var _ ports.ScannerService = (*ScannerService)(nil)

// NewScannerService creates a new ScannerService instance
func NewScannerService(appConfig *config.AppConfig, driverManager driver.DriverManagerGetter, workRepo database.WorkRepository, workService ports.WorkService, storageRepo database.StorageSourceRepository) *ScannerService {
	return &ScannerService{
		appConfig:     appConfig,
		driverManager: driverManager,
		workRepo:      workRepo,
		workService:   workService,
		storageRepo:   storageRepo,
		currentStatus: ports.StatusIdle,
	}
}
func (s *ScannerService) GetStatus() ports.ScannerStatusResponse {
	s.statusMutex.RLock()
	defer s.statusMutex.RUnlock()
	errMsg := ""
	if s.lastError != nil {
		errMsg = s.lastError.Error()
	}
	return ports.ScannerStatusResponse{
		Status:                   s.currentStatus,
		CurrentStorageIdentifier: s.currentStorageIdentifier,
		ProcessedWorks:           s.processedWorks,
		TotalWorksInLib:          s.totalWorksInLib,
		LastError:                errMsg,
		Message:                  fmt.Sprintf("Scanner is %s.", s.currentStatus),
	}
}
func (s *ScannerService) StopScan() {
	s.statusMutex.Lock()
	defer s.statusMutex.Unlock()
	if s.currentScanCancel != nil {
		log.Info(context.Background(),"StopScan called, attempting to cancel current scan.")
		s.currentScanCancel()
	} else {
		log.Info(context.Background(),"StopScan called, but no scan is currently active or cancellable.")
	}
}

func (s *ScannerService) _setScanStatusInternal(status ports.ScanStatus, storageIdentifier string, message string, err error, currentProcessed int, currentTotal int) {
	s.currentStatus = status
	s.currentStorageIdentifier = storageIdentifier
	s.lastError = err
	if status == ports.StatusScanningStorageSource || status == ports.StatusIdle || status == ports.StatusError || status == ports.StatusScanningAllStorageSources {
		s.processedWorks = 0
		if status != ports.StatusScanningStorageSource {
			s.totalWorksInLib = 0
		}
	}
}

func (s *ScannerService) setScanStatus(status ports.ScanStatus, storageIdentifier string, message string, err error) {
	s.statusMutex.Lock()
	processed := s.processedWorks
	total := s.totalWorksInLib
	if status == ports.StatusScanningStorageSource || status == ports.StatusIdle || status == ports.StatusError || status == ports.StatusScanningAllStorageSources {
		processed = 0
		if status != ports.StatusScanningStorageSource {
			total = 0
		}
	}
	s._setScanStatusInternal(status, storageIdentifier, message, err, processed, total)
	s.statusMutex.Unlock()
}

func (s *ScannerService) incrementProcessedWorks(storageIdentifier string, taskIDForProgress string, updater ports.ProgressUpdater) {
	s.statusMutex.Lock()
	s.processedWorks++
	processed := s.processedWorks
	total := s.totalWorksInLib
	s.statusMutex.Unlock()

	if updater != nil && taskIDForProgress != "" && total > 0 {
		progress := float64(processed) / float64(total)
		message := fmt.Sprintf("Processed %d/%d items for storage '%s'", processed, total, storageIdentifier)
		if err := updater(taskIDForProgress, progress, message); err != nil {
			log.Error(context.Background(), "Failed to update task progress via callback", "task_id", taskIDForProgress, "error", err)
		}
	}
}

func (s *ScannerService) _processWorkDirectory(ctx context.Context, storageDriver driver.StorageDriver, storageSourceID uint, storageIdentifier string, workStorageAlias string, workRelPathFromConfigRoot string, workFolderName string, taskIDForProgress string, updater ports.ProgressUpdater, worksInDbMutex *sync.Mutex, worksInDb map[string]*models.Work) error {
	select {
	case <-ctx.Done():
		log.Info(ctx, "_processWorkDirectory cancelled before start", "work_folder_name", workFolderName)
		return ctx.Err()
	default:
	}

	// Mark this path as found on disk by removing it from the to-be-deleted map.
	worksInDbMutex.Lock()
	delete(worksInDb, workRelPathFromConfigRoot)
	worksInDbMutex.Unlock()

	log.Info(ctx, "Processing work directory", "folder_name", workFolderName, "path_in_storage", workRelPathFromConfigRoot, "storage_alias", workStorageAlias, "storage_identifier", storageIdentifier)

	var originalID, workType string
	matches := rjCodePattern.FindStringSubmatch(workFolderName)
	if len(matches) > 1 {
		originalID = matches[0]
		if len(matches) > 2 {
			workType = strings.ToUpper(matches[1])
		} else if len(originalID) > 2 {
			workType = strings.ToUpper(originalID[0:2])
		}
	}
	if workType != "RJ" && workType != "VJ" && workType != "BJ" {
		workType = ""
	}

	if originalID == "" {
		log.Warn(ctx, "Folder name does not match expected OriginalID pattern, skipping direct work processing for this folder.", "folder_name", workFolderName, "path", workRelPathFromConfigRoot)
		return nil
	}

	currentStorageSource, err := s.storageRepo.GetByID(ctx, storageSourceID)
	if err != nil {
		log.Error(ctx, "Failed to get current storage source details for order comparison", "storage_id", storageSourceID, "error", err)
		return err
	}
	currentOrder := currentStorageSource.Order

	existingWork, err := s.workRepo.GetByOriginalID(ctx, originalID)
	if err != nil && !errors.Is(err, apperrors.ErrWorkNotFound) { // Use apperrors
		log.Error(ctx, "Failed to query existing work by OriginalID", "original_id", originalID, "error", err)
		return err
	}

	select {
	case <-ctx.Done():
		log.Info(ctx, "_processWorkDirectory cancelled before DB operation", "work_folder_name", workFolderName)
		return ctx.Err()
	default:
	}

	if existingWork != nil {
		existingWorkStorageSource, errExistingSS := s.storageRepo.GetByID(ctx, existingWork.StorageID)
		if errExistingSS != nil {
			log.Error(ctx, "Failed to get storage source of existing work for order comparison", "existing_work_id", existingWork.ID, "existing_storage_id", existingWork.StorageID, "error", errExistingSS)
			s.incrementProcessedWorks(storageIdentifier, taskIDForProgress, updater)
			return nil
		}
		existingOrder := existingWorkStorageSource.Order

		if currentOrder < existingOrder {
			log.Info(ctx, "Current storage source has higher priority (lower order value), updating existing work.",
				"original_id", originalID,
				"existing_work_id", existingWork.ID, "existing_order", existingOrder, "existing_storage_id", existingWork.StorageID,
				"current_storage_id", storageSourceID, "current_order", currentOrder,
			)
			existingWork.StorageID = storageSourceID
			existingWork.PathInStorage = workRelPathFromConfigRoot
			if workType != "" {
				if existingWork.WorkType == nil || *existingWork.WorkType != workType {
					existingWork.WorkType = &workType
				}
			} else {
				if existingWork.WorkType != nil {
					existingWork.WorkType = nil
				}
			}

			if err_update := s.workRepo.Update(ctx, existingWork); err_update != nil {
				log.Error(ctx, "Failed to update existing work with higher priority source", "work_id", existingWork.ID, "error", err_update)
				return err_update
			}
			log.Info(ctx, "Successfully updated existing work with new higher priority storage source.", "work_id", existingWork.ID)
			if s.workService != nil && existingWork.ID > 0 {
				s.statusMutex.Lock()
				currentScanCtxIdentifier := s.currentStorageIdentifier
				s.statusMutex.Unlock()
				if currentScanCtxIdentifier == storageIdentifier {
					s.setScanStatus(ports.StatusScanningStorageSource, storageIdentifier, fmt.Sprintf("Scraping metadata for updated %s", existingWork.Title), nil)
				}
				log.Info(ctx, "Triggering metadata scraping for updated work via WorkService", "work_id", existingWork.ID, "title", existingWork.Title)
				scrapeOpts := ports.ScrapeOptions{ForceUpdate: s.appConfig.Scanner.ForceScrapeOnScan, PreferredLang: s.appConfig.Scanner.DefaultScraperLang, ScrapeMetadata: true, ScrapeCover: true, DownloadCover: true}
				if errScrape := s.workService.TriggerScrapeForWorkByAdmin(ctx, nil, existingWork.ID, scrapeOpts); errScrape != nil {
					errMsg := fmt.Sprintf("Scrape via WorkService failed for %s (ID: %d): %s", existingWork.Title, existingWork.ID, errScrape.Error())
					log.Error(ctx, "Failed to trigger scrape for updated work", "work_id", existingWork.ID, "error", errScrape)
					s.statusMutex.Lock()
					s.lastError = errors.New(errMsg)
					s.statusMutex.Unlock()
				} else {
					log.Info(ctx, "Successfully triggered scrape for updated work", "work_id", existingWork.ID)
				}
				if currentScanCtxIdentifier == storageIdentifier {
					s.setScanStatus(ports.StatusScanningStorageSource, storageIdentifier, fmt.Sprintf("Continuing scan after scrape for updated %s", existingWork.Title), s.lastError)
				}
			}
		} else {
			log.Info(ctx, "Work with OriginalID already exists in a higher or equal priority storage source, skipping.",
				"original_id", originalID,
				"existing_work_id", existingWork.ID, "existing_order", existingOrder, "existing_storage_id", existingWork.StorageID,
				"current_storage_id", storageSourceID, "current_order", currentOrder,
			)
		}
		s.incrementProcessedWorks(storageIdentifier, taskIDForProgress, updater)
		return nil
	}

	title := workFolderName
	newWork := &models.Work{
		Title:         title,
		StorageID:     storageSourceID,
		PathInStorage: workRelPathFromConfigRoot,
		OriginalID:    &originalID,
		WorkType:      nil,
	}
	if workType != "" {
		newWork.WorkType = &workType
	}

	if err_create := s.workRepo.Create(ctx, newWork); err_create != nil {
		if errors.Is(err_create, apperrors.ErrWorkAlreadyExists) { // Use apperrors
			log.Warn(ctx, "Work with this OriginalID was created by another process concurrently, skipping.", "original_id", originalID)
			s.incrementProcessedWorks(storageIdentifier, taskIDForProgress, updater)
			return nil
		}
		log.Error(ctx, "Failed to create new work", "title", title, "original_id", originalID, "error", err_create)
		return err_create
	}
	originalIDLogValue := "N/A"
	if newWork.OriginalID != nil {
		originalIDLogValue = *newWork.OriginalID
	}
	log.Info(ctx, "Created new work entry", "id", newWork.ID, "title", newWork.Title, "original_id", originalIDLogValue, "storage_id", newWork.StorageID, "path_in_storage", newWork.PathInStorage)
	s.incrementProcessedWorks(storageIdentifier, taskIDForProgress, updater)

	if s.workService != nil && newWork.ID > 0 {
		s.statusMutex.Lock()
		currentScanCtxIdentifier := s.currentStorageIdentifier
		s.statusMutex.Unlock()
		if currentScanCtxIdentifier == storageIdentifier {
			s.setScanStatus(ports.StatusScanningStorageSource, storageIdentifier, fmt.Sprintf("Scraping metadata for %s", newWork.Title), nil)
		}

		log.Info(ctx, "Triggering metadata scraping for newly added work via WorkService", "work_id", newWork.ID, "title", newWork.Title)
		scrapeOpts := ports.ScrapeOptions{
			ForceUpdate:    s.appConfig.Scanner.ForceScrapeOnScan,
			PreferredLang:  s.appConfig.Scanner.DefaultScraperLang,
			ScrapeMetadata: true,
			ScrapeCover:    true,
			DownloadCover:  true,
		}
		if errScrape := s.workService.TriggerScrapeForWorkByAdmin(ctx, nil, newWork.ID, scrapeOpts); errScrape != nil {
			errMsg := fmt.Sprintf("Scrape via WorkService failed for %s (ID: %d): %s", newWork.Title, newWork.ID, errScrape.Error())
			log.Error(ctx, "Failed to trigger scrape via WorkService after scan", "work_id", newWork.ID, "title", newWork.Title, "error", errScrape)
			s.statusMutex.Lock()
			s.lastError = errors.New(errMsg)
			s.statusMutex.Unlock()
		} else {
			log.Info(ctx, "Successfully triggered scrape via WorkService after scan", "work_id", newWork.ID, "title", newWork.Title)
		}
		if currentScanCtxIdentifier == storageIdentifier {
			s.setScanStatus(ports.StatusScanningStorageSource, storageIdentifier, fmt.Sprintf("Continuing scan in %s after attempting scrape for %s", storageIdentifier, newWork.Title), s.lastError)
		}
	}
	return nil
}

func (s *ScannerService) ScanAllLibraries(ctx context.Context) error {
	s.statusMutex.Lock()
	if s.currentStatus != ports.StatusIdle && s.currentStatus != ports.StatusError {
		s.statusMutex.Unlock()
		log.Warn(ctx, "ScanAllStorageSources called, but scan already in progress.", "status", s.currentStatus)
		return errors.New("scan already in progress")
	}
	scanCtx, cancel := context.WithCancel(ctx)
	s.currentScanCtx = scanCtx
	s.currentScanCancel = cancel
	s.statusMutex.Unlock()
	defer func() {
		s.statusMutex.Lock()
		s.currentScanCtx = nil
		s.currentScanCancel = nil
		processed := s.processedWorks
		total := s.totalWorksInLib
		if s.currentStatus != ports.StatusError {
			s._setScanStatusInternal(ports.StatusIdle, "", "Scan for all storage sources finished or stopped.", s.lastError, processed, total)
		}
		s.statusMutex.Unlock()
	}()
	s.setScanStatus(ports.StatusScanningAllStorageSources, "", "Scan for all storage sources started.", nil)

	allStorageSources, _, err := s.storageRepo.ListAll(ctx, false, 1, 0)
	if err != nil {
		log.Error(scanCtx, "Failed to list storage sources from DB for ScanAllStorageSources", "error", err)
		s.setScanStatus(ports.StatusError, "", "Failed to list storage sources.", err)
		return err
	}

	if len(allStorageSources) == 0 {
		log.Info(scanCtx, "No enabled storage sources found in database to scan.")
		s.setScanStatus(ports.StatusIdle, "", "No enabled storage sources configured.", nil)
		return nil
	}
	log.Info(scanCtx, "Starting scan for all enabled storage sources", "count", len(allStorageSources))
	var firstError error
	var errMutex sync.Mutex
	setError := func(err error) {
		errMutex.Lock()
		if firstError == nil {
			firstError = err
		}
		errMutex.Unlock()
	}
	var wg sync.WaitGroup
	for _, storageSource := range allStorageSources {
		select {
		case <-scanCtx.Done():
			logIdentifier := storageSource.Remark
			if logIdentifier == "" {
				logIdentifier = fmt.Sprintf("ID: %d", storageSource.ID)
			}
			log.Info(scanCtx, "ScanAllStorageSources cancelled before starting scan for source", "source_identifier", logIdentifier)
			setError(scanCtx.Err())
			goto endScanAll
		default:
		}
		wg.Add(1)
		go func(ss models.StorageSource) {
			defer wg.Done()
			identifierToScan := fmt.Sprintf("%d", ss.ID)
			if err := s.ScanLibrary(scanCtx, identifierToScan, "", nil); err != nil {
				if !errors.Is(err, context.Canceled) || errors.Is(err, context.DeadlineExceeded) {
					log.Error(scanCtx, "Failed to scan storage source", "identifier", identifierToScan, "error", err)
				}
				setError(err)
			}
		}(*storageSource)
	}
endScanAll:
	wg.Wait()
	log.Info(scanCtx, "Finished scanning all storage sources.")
	s.statusMutex.Lock()
	s.lastError = firstError
	finalStatus := ports.StatusIdle
	finalMessage := "Scan for all storage sources completed."
	if firstError != nil {
		if errors.Is(firstError, context.Canceled) || errors.Is(firstError, context.DeadlineExceeded) {
			finalMessage = "Scan for all storage sources was cancelled."
		} else {
			finalStatus = ports.StatusError
			finalMessage = fmt.Sprintf("Scan for all storage sources completed with errors: %v", firstError)
		}
	}
	s.statusMutex.Unlock()
	s.setScanStatus(finalStatus, "", finalMessage, firstError)
	return firstError
}

func (s *ScannerService) ScanLibrary(ctx context.Context, storageSourceIdentifier string, taskIDForProgress string, updater ports.ProgressUpdater) error {
	select {
	case <-ctx.Done():
		log.Info(ctx, "ScanStorageSource cancelled before start", "identifier", storageSourceIdentifier)
		return ctx.Err()
	default:
	}
	s.setScanStatus(ports.StatusScanningStorageSource, storageSourceIdentifier, fmt.Sprintf("Scan for storage source '%s' started.", storageSourceIdentifier), nil)
	log.Info(ctx, "Starting scan for storage source", "identifier", storageSourceIdentifier)

	if s.storageRepo == nil {
		err := errors.New("StorageSourceRepository is not initialized in ScannerService")
		log.Error(ctx, err.Error())
		s.setScanStatus(ports.StatusError, storageSourceIdentifier, err.Error(), err)
		return err
	}

	var storageSource *models.StorageSource
	var err error

	parsedID, parseErr := strconv.ParseUint(storageSourceIdentifier, 10, 32)
	if parseErr == nil {
		storageSource, err = s.storageRepo.GetByID(ctx, uint(parsedID))
		if err != nil && !errors.Is(err, apperrors.ErrStorageNotFound) { // Use apperrors
			errMsg := fmt.Sprintf("Failed to retrieve storage source by ID '%s': %v", storageSourceIdentifier, err)
			log.Error(ctx, errMsg, "error", err)
			s.setScanStatus(ports.StatusError, storageSourceIdentifier, errMsg, errors.New(errMsg))
			return errors.New(errMsg)
		}
	}

	if storageSource == nil || errors.Is(err, apperrors.ErrStorageNotFound) { // Use apperrors
		log.Info(ctx, "Storage source not found by ID (or identifier was not numeric), trying by remark", "identifier", storageSourceIdentifier)
		storageSource, err = s.storageRepo.GetByRemark(ctx, storageSourceIdentifier)
		if err != nil {
			errMsg := fmt.Sprintf("Storage source '%s' not found by ID or remark", storageSourceIdentifier)
			if !errors.Is(err, apperrors.ErrStorageNotFound) { // Use apperrors
				errMsg = fmt.Sprintf("Failed to retrieve storage source '%s': %v", storageSourceIdentifier, err)
			}
			log.Error(ctx, errMsg, "error", err)
			s.setScanStatus(ports.StatusError, storageSourceIdentifier, errMsg, errors.New(errMsg))
			return errors.New(errMsg)
		}
	}

	if storageSource == nil {
		errMsg := fmt.Sprintf("Storage source '%s' could not be resolved", storageSourceIdentifier)
		log.Error(ctx, errMsg)
		s.setScanStatus(ports.StatusError, storageSourceIdentifier, errMsg, errors.New(errMsg))
		return errors.New(errMsg)
	}

	if storageSource.Disabled {
		errMsg := fmt.Sprintf("Storage source '%s' (ID: %d, Remark: '%s') is disabled.", storageSourceIdentifier, storageSource.ID, storageSource.Remark)
		log.Warn(ctx, errMsg)
		s.setScanStatus(ports.StatusError, storageSourceIdentifier, errMsg, errors.New(errMsg))
		return errors.New(errMsg)
	}

	storageDriver, ok := s.driverManager.GetDriver(storageSource.ID)
	if !ok {
		err := fmt.Errorf("storage driver not found for StorageID: %d", storageSource.ID)
		s.setScanStatus(ports.StatusError, storageSourceIdentifier, err.Error(), err)
		return err
	}

	log.Debug(ctx, "Scanning storage source details", "storage_id", storageSource.ID, "remark", storageSource.Remark, "driver", storageSource.Driver)

	// Get all works for this storage source to find deleted ones later
	log.Debug(ctx, "Fetching existing works for storage source to detect deletions", "storage_id", storageSource.ID)
	allDbWorks, _, errDb := s.workRepo.List(ctx, database.ListWorksParams{StorageID: &storageSource.ID})
	if errDb != nil {
		errMsg := fmt.Sprintf("Failed to list existing works from DB for storage source '%s': %v", storageSourceIdentifier, errDb)
		log.Error(ctx, errMsg, "error", errDb)
		s.setScanStatus(ports.StatusError, storageSourceIdentifier, errMsg, errDb)
		return errDb
	}

	worksInDb := make(map[string]*models.Work)
	for _, w := range allDbWorks {
		if w.PathInStorage != "" {
			worksInDb[w.PathInStorage] = w
		}
	}
	var worksInDbMutex sync.Mutex

	log.Debug(ctx, "Found existing works in DB", "count", len(worksInDb))

	s.statusMutex.Lock()
	s.processedWorks = 0
	s.totalWorksInLib = 0
	s.statusMutex.Unlock()

	storageAlias := storageSource.Remark
	if storageAlias == "" {
		storageAlias = fmt.Sprintf("storage_id_%d", storageSource.ID)
	}

	errScan := s._discoverAndProcessWorks(ctx, storageDriver, storageSource.ID, storageAlias, "", storageSourceIdentifier, 0, taskIDForProgress, updater, &worksInDbMutex, worksInDb)

	// After scan, delete works that are still in the map (i.e., not found on disk)
	if errScan == nil && len(worksInDb) > 0 {
		log.Info(ctx, "Scan finished, deleting works not found on disk", "count", len(worksInDb), "storage_identifier", storageSourceIdentifier)
		for path, workToDelete := range worksInDb {
			log.Info(ctx, "Deleting work not found on disk", "work_id", workToDelete.ID, "title", workToDelete.Title, "path", path)
			if errDelete := s.workRepo.Delete(ctx, workToDelete.ID); errDelete != nil {
				log.Error(ctx, "Failed to delete work from database", "work_id", workToDelete.ID, "path", path, "error", errDelete)
				// Don't let a single deletion failure stop others, but record the first error.
				if errScan == nil {
					errScan = errDelete
				}
			}
		}
	}

	finalStatus := ports.StatusIdle
	finalMessage := fmt.Sprintf("Finished scan for storage source '%s'.", storageSourceIdentifier)
	if errScan != nil {
		if errors.Is(errScan, context.Canceled) || errors.Is(errScan, context.DeadlineExceeded) {
			log.Info(ctx, "Scan for storage source cancelled", "identifier", storageSourceIdentifier, "error", errScan)
			finalMessage = fmt.Sprintf("Scan for storage source '%s' was cancelled.", storageSourceIdentifier)
		} else {
			log.Error(ctx, "Error during work discovery for storage source", "identifier", storageSourceIdentifier, "error", errScan)
			finalStatus = ports.StatusError
			finalMessage = fmt.Sprintf("Error scanning storage source '%s': %v", storageSourceIdentifier, errScan)
		}
	}

	s.statusMutex.Lock()
	s.lastError = errScan
	s.statusMutex.Unlock()

	s.setScanStatus(finalStatus, "", finalMessage, errScan)

	log.Info(ctx, finalMessage)
	return errScan
}

func (s *ScannerService) _discoverAndProcessWorks(ctx context.Context, storage driver.StorageDriver, storageSourceID uint, storageAlias string, currentPathWithinStorage string, storageIdentifier string, depth int, taskIDForProgress string, updater ports.ProgressUpdater, worksInDbMutex *sync.Mutex, worksInDb map[string]*models.Work) error {
	select {
	case <-ctx.Done():
		log.Info(ctx, "_discoverAndProcessWorks cancelled before start", "path", currentPathWithinStorage)
		return ctx.Err()
	default:
	}
	if depth >= s.appConfig.Scanner.MaxRecursionDepth {
		log.Warn(ctx, "Reached max scan depth", "path", currentPathWithinStorage)
		return nil
	}

	pathToListForDriver := currentPathWithinStorage
	// 对于根路径，保持为空字符串，不同的驱动程序对根路径的处理方式不同
	// local driver期望空字符串，而其他驱动程序可能期望"/"

	log.Debug(ctx, "Discovering in path", "path_to_list_for_driver", pathToListForDriver, "storage_alias", storageAlias)
	dirObj := driver.NewPathAlistObj(pathToListForDriver)

	items, err := storage.List(ctx, dirObj, driver.AlistListArgs{Refresh: true})
	if err != nil {
		log.Error(ctx, "Failed to list items", "path", dirObj.GetPath(), "storage_alias", storageAlias, "error", err)
		return nil
	}

	if depth == 0 {
		var potentialWorks int
		for _, item := range items {
			if item.IsDir() && rjCodePattern.MatchString(item.GetName()) && !rjRangePattern.MatchString(item.GetName()) {
				potentialWorks++
			}
		}
		s.statusMutex.Lock()
		s.totalWorksInLib = potentialWorks
		s.statusMutex.Unlock()
	}

	// Create a list of all work directories to process
	var workDirs []struct {
		path         string
		folderName   string
		isWorkFolder bool
	}

	for _, item := range items {
		select {
		case <-ctx.Done():
			log.Info(ctx, "_discoverAndProcessWorks loop cancelled", "path", currentPathWithinStorage)
			return ctx.Err()
		default:
		}

		if item.IsDir() {
			isRangeDir := rjRangePattern.MatchString(item.GetName())
			isWorkDir := rjCodePattern.MatchString(item.GetName()) && !isRangeDir

			workDirs = append(workDirs, struct {
				path         string
				folderName   string
				isWorkFolder bool
			}{
				path:         item.GetPath(),
				folderName:   item.GetName(),
				isWorkFolder: isWorkDir,
			})
		}
	}

	// Process work directories in parallel with a worker pool
	var wg sync.WaitGroup
	maxParallelism := s.appConfig.Scanner.MaxParallelism
	if maxParallelism < 1 {
		maxParallelism = 1 // Ensure at least 1 worker
	}

	// Create a semaphore channel to limit concurrency
	semaphore := make(chan struct{}, maxParallelism)
	errChan := make(chan error, len(workDirs))

	log.Debug(ctx, "Processing work directories with parallelism", "max_parallelism", maxParallelism, "directories_count", len(workDirs))

	for _, dir := range workDirs {
		if dir.isWorkFolder {
			wg.Add(1)
			// Acquire semaphore slot
			semaphore <- struct{}{}

			go func(itemPath, folderName string) {
				defer wg.Done()
				defer func() { <-semaphore }() // Release semaphore slot

				if err := s._processWorkDirectory(ctx, storage, storageSourceID, storageIdentifier, storageAlias, itemPath, folderName, taskIDForProgress, updater, worksInDbMutex, worksInDb); err != nil {
					if errors.Is(err, context.Canceled) || errors.Is(err, context.DeadlineExceeded) {
						errChan <- err
					} else {
						log.Error(ctx, "Failed to process work dir", "path", itemPath, "error", err)
					}
				}
			}(dir.path, dir.folderName)
		} else {
			// For non-work directories, continue recursive scanning (not parallelized to prevent explosion of goroutines)
			if err := s._discoverAndProcessWorks(ctx, storage, storageSourceID, storageAlias, dir.path, storageIdentifier, depth+1, taskIDForProgress, updater, worksInDbMutex, worksInDb); err != nil {
				if errors.Is(err, context.Canceled) || errors.Is(err, context.DeadlineExceeded) {
					return err
				}
				log.Error(ctx, "Error in recursive discovery", "path", dir.path, "error", err)
			}
		}
	}

	// Wait for all goroutines to complete
	wg.Wait()
	close(errChan)

	// Check for errors
	for err := range errChan {
		if err != nil {
			return err
		}
	}

	return nil
}
