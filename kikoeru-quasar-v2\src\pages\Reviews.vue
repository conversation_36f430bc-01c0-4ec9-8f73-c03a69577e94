<template>
  <div>
    <div class="text-h5 text-weight-regular q-ma-md">
      {{ t('nav.myReviews') }}
      <span v-show="pagination.total_items">
        ({{pagination.total_items}})
      </span>
    </div>

    <div class="q-mx-md">
      <!-- 过滤按钮 -->
      <div class="row justify-start q-mb-md q-gutter-sm">
        <q-btn
          v-for="(filterOption, index) in filterOptions"
          :key="index"
          :color="currentFilter === filterOption.value ? 'primary' : 'grey-5'"
          :text-color="currentFilter === filterOption.value ? 'white' : 'grey-8'"
          :unelevated="currentFilter === filterOption.value"
          :outline="currentFilter !== filterOption.value"
          dense
          no-caps
          @click="setFilter(filterOption.value)"
        >
          {{ filterOption.label }}
        </q-btn>
      </div>

      <!-- 排序选择框 -->
      <div class="row justify-between q-mb-md">
        <q-select
          dense
          rounded
          outlined
          bg-color="white"
          transition-show="scale"
          transition-hide="scale"
          v-model="sortOption"
          :options="sortOptions"
          :label="t('reviewsPage.sortBy')"
          option-label="label"
          option-value="value"
          emit-value
          map-options
          class="col-auto"
          style="min-width: 200px;"
        />

        <!-- 切换显示模式按钮 -->
        <q-btn-toggle
          dense
          spread
          rounded
          v-model="listMode"
          toggle-color="primary"
          color="white"
          text-color="primary"
          :options="[
            { icon: 'apps', value: false, slot: 'grid-view' }, // slot for tooltip
            { icon: 'list', value: true, slot: 'list-view' }   // slot for tooltip
          ]"
          style="width: 85px;"
          class="col-auto"
        >
          <template v-slot:grid-view>
            <q-tooltip>{{ t('reviewsPage.gridView') }}</q-tooltip>
          </template>
          <template v-slot:list-view>
            <q-tooltip>{{ t('reviewsPage.listView') }}</q-tooltip>
          </template>
        </q-btn-toggle>
      </div>

      <q-infinite-scroll @load="onLoad" :offset="250" :disable="stopLoad" class="col">
        <q-list v-if="listMode" bordered separator class="shadow-2">
          <ReviewListItem v-for="review_item in reviews" :key="`${review_item.work_id}-${review_item.id}`" :review="review_item" @refresh="reset" />
        </q-list>

        <div v-else class="row q-col-gutter-x-md q-col-gutter-y-lg">
          <div class="col-xs-12 col-sm-6 col-md-4 col-lg-3 col-xl-3" v-for="review_item in reviews" :key="`${review_item.work_id}-${review_item.id}`">
            <WorkCard v-if="review_item.work" :metadata="review_item.work" class="fit"/>
          </div>
        </div>

        <div v-show="stopLoad" class="q-mt-lg q-mb-xl text-h6 text-bold text-center">{{ t('history.endOfList') }}</div>

        <template v-slot:loading>
          <div class="row justify-center q-my-md">
            <q-spinner-dots color="primary" size="40px" />
          </div>
        </template>
      </q-infinite-scroll>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onActivated, onDeactivated, getCurrentInstance } from 'vue'
import { useI18n } from 'vue-i18n'
import WorkCard from '../components/WorkCard.vue'
import ReviewListItem from '../components/ReviewListItem.vue'
import { useNotification } from '../composables/useNotification'

defineOptions({
  name: 'ReviewsPage'
})

const { t } = useI18n()
const { proxy } = getCurrentInstance()
const { showErrorNotification } = useNotification()

// Reactive data
const listMode = ref(true)
const stopLoad = ref(false)
const reviews = ref([])
const pagination = ref({ current_page: 0, per_page: 20, total_items: 0 })
const currentFilter = ref('')

// Computed properties
const filterOptions = computed(() => [
  { label: t('reviewsPage.filterOptions.all'), value: '' },
  { label: t('review.progress.wantToListen'), value: 'marked' },
  { label: t('review.progress.listening'), value: 'listening' },
  { label: t('review.progress.listened'), value: 'listened' },
  { label: t('review.progress.relistening'), value: 'replay' },
  { label: t('review.progress.postponed'), value: 'postponed' }
])

const sortOption = ref('updated_at_desc')

const sortOptions = computed(() => [
  {
    label: t('reviewsPage.sortOptions.updatedDesc'),
    value: 'updated_at_desc',
    order: 'updated_at',
    sort: 'desc'
  },
  {
    label: t('reviewsPage.sortOptions.updatedAsc'),
    value: 'updated_at_asc',
    order: 'updated_at',
    sort: 'asc'
  },
  {
    label: t('reviewsPage.sortOptions.ratingDesc'),
    value: 'userRating_desc',
    order: 'userRating',
    sort: 'desc'
  },
  {
    label: t('reviewsPage.sortOptions.ratingAsc'),
    value: 'userRating_asc',
    order: 'userRating',
    sort: 'asc'
  },
  {
    label: t('reviewsPage.sortOptions.releaseDesc'),
    value: 'release_desc',
    order: 'release',
    sort: 'desc'
  },
  {
    label: t('reviewsPage.sortOptions.releaseAsc'),
    value: 'release_asc',
    order: 'release',
    sort: 'asc'
  }
])

const currentSortConfig = computed(() => {
  return sortOptions.value.find(option => option.value === sortOption.value) || sortOptions.value[0]
})

const requestParams = computed(() => {
  const params = {
    order: currentSortConfig.value.order,
    sort: currentSortConfig.value.sort,
    page: pagination.value.current_page + 1 || 1,
    pageSize: 20
  }

  if (currentFilter.value) {
    params.filter = currentFilter.value
  }

  return params
})

// Methods
const onLoad = (index, done) => {
  requestReviews()
    .then(() => done())
}

const requestReviews = async () => {
  try {
    const response = await proxy.$api.get('/api/v1/review', {
      params: requestParams.value
    })

    const data = response.data
    const reviewsData = data.items || []

    reviews.value = (requestParams.value.page === 1) ? reviewsData.concat() : reviews.value.concat(reviewsData)
    pagination.value = data.pagination || { current_page: 0, per_page: 20, total_items: 0 }

    if (reviews.value.length >= pagination.value.total_items) {
      stopLoad.value = true
    }
  } catch (error) {
    if (error.response?.status !== 401) {
      showErrorNotification(error.response?.data?.error || error.message || t('notification.loadReviewsFailed'))
    }
    stopLoad.value = true
  }
}

const setFilter = (filterValue) => {
  currentFilter.value = filterValue
}

const reset = () => {
  stopLoad.value = true
  reviews.value = []
  pagination.value = { current_page: 0, per_page: 20, total_items: 0 }
  requestReviews()
    .then(() => {
      stopLoad.value = false
    })
}

// Watchers
watch(listMode, (newListMode) => {
  localStorage.reviewListMode = newListMode
})

watch(currentFilter, () => {
  reset()
})

watch(sortOption, () => {
  reset()
})

// Lifecycle hooks
onMounted(() => {
  // Load preferences from localStorage
  if (localStorage.reviewListMode) {
    listMode.value = (localStorage.reviewListMode === 'true')
  }
})

onActivated(() => {
  stopLoad.value = false
})

onDeactivated(() => {
  stopLoad.value = true
})


</script>
