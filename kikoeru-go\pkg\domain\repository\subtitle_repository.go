package repository

import (
	"context"

	"github.com/Sakura-Byte/kikoeru-go/pkg/domain/model"
)

// SubtitleRepository defines the interface for subtitle data access
type SubtitleRepository interface {
	// CRUD operations
	CreateSubtitle(ctx context.Context, subtitle *model.Subtitle) error
	GetSubtitleByID(ctx context.Context, id uint) (*model.Subtitle, error)
	GetSubtitleByUUID(ctx context.Context, uuid string) (*model.Subtitle, error)
	UpdateSubtitle(ctx context.Context, subtitle *model.Subtitle) error
	DeleteSubtitle(ctx context.Context, id uint) error

	// Find operations
	FindSubtitlesForTrack(ctx context.Context, originalID, trackPath string, isInArchive bool, archivePath string) ([]model.Subtitle, error)
	GetAllSubtitles(ctx context.Context, page, pageSize int) ([]model.Subtitle, int, error)

	// Vote operations
	GetUserVote(ctx context.Context, subtitleID uint, userID string) (*model.SubtitleVote, error)
	SaveVote(ctx context.Context, vote *model.SubtitleVote) error
	DeleteVote(ctx context.Context, subtitleID uint, userID string) error

	// Additional operations
	GetSubtitleCountForWork(ctx context.Context, workID uint) (int, error)
	GetSubtitleCountForUser(ctx context.Context, userID string) (int, error)
	IsSubtitleOwner(ctx context.Context, subtitleID uint, userID string) (bool, error)

	// Subtitle Track operations
	AddTrackToSubtitle(ctx context.Context, subtitleID uint, trackPath string, isInArchive bool, archivePath string) error
	GetTracksForSubtitle(ctx context.Context, subtitleID uint) ([]model.SubtitleTrack, error)
	RemoveTrackFromSubtitle(ctx context.Context, subtitleID uint, trackPath string) error
}
