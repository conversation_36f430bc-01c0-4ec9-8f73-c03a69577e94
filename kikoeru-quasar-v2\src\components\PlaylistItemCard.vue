<template>
  <q-item style="padding: 5px;">
    <!-- 拖拽手柄 -->
    <q-item-section avatar class="drag-handle" style="min-width: 20px; cursor: grab;">
      <div class="text-grey-6">{{ index }}</div>
    </q-item-section>

    <!-- 封面 -->
    <q-item-section avatar style="padding: 0px 5px 0px 0px;">
      <router-link v-if="item.work" :to="`/work/${item.work.original_id}`">
        <q-img transition="fade" :src="samCoverUrl" style="height: 60px; width: 60px;" />
      </router-link>
    </q-item-section>

    <!-- 内容 -->
    <q-item-section>
      <q-item-label v-if="item.work" lines="2" class="text">
        <router-link :to="`/work/${item.work.original_id}`" class="text-black">
          {{ item.work.title }}
        </router-link>
      </q-item-label>

      <q-item-label v-if="item.track_path" caption>
        {{ t('playlist.track') }}: {{ getTrackName(item.track_path) }}
      </q-item-label>

      <q-item-label v-if="item.work">
        <div class="row q-gutter-x-sm q-gutter-y-xs items-center">
          <!-- 社团名 -->
          <router-link v-if="item.work.circle" :to="`/works?circle=${item.work.circle.name}`" class="col-auto text-grey">
            {{ item.work.circle.name }}
          </router-link>

          <!-- 评分 -->
          <div v-if="item.work.rate_average_2dp" class="col-auto">
            <q-rating
              :model-value="item.work.rate_average_2dp"
              readonly
              size="sm"
              color="amber"
              icon="star"
            />
            <span class="text-caption text-grey-6 q-ml-xs">{{ item.work.rate_average_2dp }}</span>
          </div>
        </div>
      </q-item-label>
    </q-item-section>

    <!-- 操作按钮 -->
    <q-item-section side>
      <div class="row q-gutter-xs items-center">
        <!-- 播放按钮 -->
        <q-btn
          dense
          round
          color="primary"
          icon="play_arrow"
          size="sm"
          @click="emit('play', item)"
        >
          <q-tooltip>{{ t('player.play') }}</q-tooltip>
        </q-btn>

        <!-- 上移按钮 -->
        <q-btn
          dense
          round
          color="grey"
          icon="keyboard_arrow_up"
          size="sm"
          @click="emit('move-up', index - 1)"
          :disable="index === 1"
        >
          <q-tooltip>{{ t('playlist.moveUp') }}</q-tooltip>
        </q-btn>

        <!-- 下移按钮 -->
        <q-btn
          dense
          round
          color="grey"
          icon="keyboard_arrow_down"
          size="sm"
          @click="emit('move-down', index - 1)"
        >
          <q-tooltip>{{ t('playlist.moveDown') }}</q-tooltip>
        </q-btn>

        <!-- 删除按钮 -->
        <q-btn
          dense
          round
          color="negative"
          icon="delete"
          size="sm"
          @click="confirmRemove"
        >
          <q-tooltip>{{ t('common.remove') }}</q-tooltip>
        </q-btn>
      </div>
    </q-item-section>

    <!-- 删除确认对话框 -->
    <q-dialog v-model="showRemoveDialog" persistent>
      <q-card>
        <q-card-section>
          <div class="text-h6">{{ t('playlist.confirmRemove.title') }}</div>
          <div class="text-body2">{{ t('playlist.confirmRemove.message') }}</div>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="t('common.cancel')" v-close-popup />
          <q-btn flat :label="t('common.remove')" color="negative" @click="removeItem" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-item>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'

defineOptions({
  name: 'PlaylistItemCard'
})

const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  index: {
    type: Number,
    required: true
  }
})

const emit = defineEmits(['play', 'move-up', 'move-down', 'remove'])

const { t } = useI18n()

// Reactive data
const showRemoveDialog = ref(false)

// Computed properties
const samCoverUrl = computed(() => {
  return props.item.work?.original_id ? `/api/v1/cover/${props.item.work.original_id}?type=sam` : ""
})

// Methods
const getTrackName = (trackPath) => {
  if (!trackPath) return ''
  const parts = trackPath.split('/')
  return parts[parts.length - 1] // Get filename
}

const confirmRemove = () => {
  showRemoveDialog.value = true
}

const removeItem = () => {
  showRemoveDialog.value = false
  emit('remove', props.item)
}


</script>

<style scoped>
.drag-handle:active {
  cursor: grabbing;
}
</style>
