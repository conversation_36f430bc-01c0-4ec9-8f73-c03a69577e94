package service

import (
	"context"
	"fmt"

	"github.com/Sakura-Byte/kikoeru-go/pkg/ports" // Import ports package
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/database"

	metadata_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/metadata"
	tag_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/tag" // Added tag_dto import
	va_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/va"   // Added va_dto import
)

// SummaryService interface has been moved to github.com/Sakura-Byte/kikoeru-go/pkg/ports

type summaryService struct {
	circleRepo database.CircleRepository
	tagRepo    database.TagRepository
	vaRepo     database.VARepository
}

// Ensure summaryService implements ports.SummaryService
var _ ports.SummaryService = (*summaryService)(nil)

// NewSummaryService creates a new SummaryService.
func NewSummaryService(cr database.CircleRepository, tr database.TagRepository, vr database.VARepository) ports.SummaryService { // Changed return type to ports.SummaryService
	return &summaryService{
		circleRepo: cr,
		tagRepo:    tr,
		vaRepo:     vr,
	}
}

func (s *summaryService) ListAllCirclesWithWorkCount(ctx context.Context) ([]*metadata_dto.CircleWithWorkCount, int64, error) {
	circles, total, err := s.circleRepo.ListAllWithWorkCount(ctx)
	if err != nil {
		return nil, 0, fmt.Errorf("summaryService.ListAllCirclesWithWorkCount: %w", err)
	}
	return circles, total, nil
}

func (s *summaryService) ListAllTagsWithWorkCount(ctx context.Context) ([]*metadata_dto.TagWithWorkCount, error) {
	tagsModels, err := s.tagRepo.ListAllWithWorkCount(ctx)
	if err != nil {
		return nil, fmt.Errorf("summaryService.ListAllTagsWithWorkCount: %w", err)
	}
	// Convert []*models.Tag (which should have WorkCount populated by the repo) to []*metadata_dto.TagWithWorkCount
	// This assumes models.Tag has a WorkCount field that GORM populates.
	// And metadata_dto.TagWithWorkCount is compatible or requires manual mapping.
	// For now, let's assume a direct conversion is possible or the DTO matches the model structure for these fields.
	// If metadata_dto.TagWithWorkCount is different, a mapping loop is needed here.
	// Based on the repo query, models.Tag itself will have the WorkCount.
	// We need to ensure metadata_dto.TagWithWorkCount can represent this.
	// Let's assume metadata_dto.TagWithWorkCount is similar to models.Tag + WorkCount
	// or that the service layer is responsible for this transformation if they differ significantly.
	// For now, to fix the compile error, we focus on the return signature.
	// The actual transformation logic might need review.
	// If models.Tag already has WorkCount, and metadata_dto.TagWithWorkCount is just a DTO version:
	dtos := make([]*metadata_dto.TagWithWorkCount, len(tagsModels))
	for i, tm := range tagsModels {
		dtos[i] = &metadata_dto.TagWithWorkCount{
			TagDTO: tag_dto.TagDTO{
				ID:        tm.ID,
				Name:      tm.Name,
				CreatedAt: tm.CreatedAt,
				UpdatedAt: tm.UpdatedAt,
			},
			WorkCount: tm.WorkCount,
			// I18n:      i18nMapForTag, // Placeholder for actual I18n data if available
		}
	}
	return dtos, nil
}

func (s *summaryService) ListAllVAsWithWorkCount(ctx context.Context) ([]*metadata_dto.VAWithWorkCount, error) {
	vasModels, err := s.vaRepo.ListAllWithWorkCount(ctx)
	if err != nil {
		return nil, fmt.Errorf("summaryService.ListAllVAsWithWorkCount: %w", err)
	}
	// Similar to tags, convert []*models.VA to []*metadata_dto.VAWithWorkCount
	dtos := make([]*metadata_dto.VAWithWorkCount, len(vasModels))
	for i, vm := range vasModels {
		dtos[i] = &metadata_dto.VAWithWorkCount{
			VADTO: va_dto.VADTO{
				ID:        vm.ID,
				Name:      vm.Name,
				CreatedAt: vm.CreatedAt,
				UpdatedAt: vm.UpdatedAt,
			},
			WorkCount: vm.WorkCount,
			// I18n:      i18nMapForVA, // Placeholder for actual I18n data if available
		}
	}
	return dtos, nil
}
