package storage_dto

import "time" // Added time import

// CreateStorageSourceRequest defines the payload for creating a new storage source.
type CreateStorageSourceRequest struct {
	Driver           string                 `json:"driver" binding:"required"`
	Remark           string                 `json:"remark"`
	RootPath         string                 `json:"root_path,omitempty"` // Kept for backward compatibility / specific drivers
	RootID           string                 `json:"root_id,omitempty"`   // Kept for backward compatibility / specific drivers
	Order            int32                  `json:"order"`
	Disabled         bool                   `json:"disabled"`
	DownloadStrategy string                 `json:"download_strategy,omitempty"`
	CacheExpiration  *int                   `json:"cache_expiration,omitempty"` // Cache expiration in minutes
	Addition         map[string]interface{} `json:"addition"`                   // Driver-specific configurations
}

// UpdateStorageSourceRequest defines the payload for updating an existing storage source.
type UpdateStorageSourceRequest struct {
	Remark           *string                `json:"remark"`
	RootPath         *string                `json:"root_path,omitempty"`
	RootID           *string                `json:"root_id,omitempty"`
	Order            *int32                 `json:"order"`
	Disabled         *bool                  `json:"disabled"`
	DownloadStrategy *string                `json:"download_strategy,omitempty"`
	CacheExpiration  *int                   `json:"cache_expiration,omitempty"` // Cache expiration in minutes
	Addition         map[string]interface{} `json:"addition,omitempty"`
}

// StorageSourceResponseDTO defines the DTO for StorageSource responses.
type StorageSourceResponseDTO struct {
	ID               uint      `json:"id"`
	Order            int       `json:"order"`
	Driver           string    `json:"driver"`
	Addition         string    `json:"addition"` // Mirrors models.StorageSource.Addition (JSON string)
	Remark           string    `json:"remark"`
	Disabled         bool      `json:"disabled"`
	DownloadStrategy string    `json:"download_strategy,omitempty"`
	CacheExpiration  int       `json:"cache_expiration,omitempty"`
	CreatedAt        time.Time `json:"created_at"`
	UpdatedAt        time.Time `json:"updated_at"`
}
