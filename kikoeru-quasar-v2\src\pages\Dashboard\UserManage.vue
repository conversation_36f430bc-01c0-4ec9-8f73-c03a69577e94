<template>
  <div class="q-pa-md">
    <div class="text-h5 text-weight-regular q-mb-lg">{{ t('dashboardUserManage.title') }}</div>

    <div class="row justify-between q-mb-md">
      <q-btn
        color="primary"
        icon="person_add"
        :label="t('dashboardUserManage.addUser')"
        @click="showCreateDialog = true"
      />

      <div class="row q-gutter-sm">
        <q-input
          dense
          v-model="searchQuery"
          :placeholder="t('dashboardUserManage.searchPlaceholder')"
          @keyup.enter="loadUsers"
        >
          <template v-slot:append>
            <q-btn flat round dense icon="search" @click="loadUsers" />
          </template>
        </q-input>

        <q-btn
          color="secondary"
          icon="refresh"
          @click="loadUsers"
          :loading="loading"
        />
      </div>
    </div>

    <!-- 用户列表 -->
    <q-table
      :rows="users"
      :columns="columnsComputed"
      row-key="id"
      :loading="loading"
      flat
      bordered
      class="shadow-2"
      :pagination="pagination"
      @request="onRequest"
    >
      <template v-slot:body-cell-avatar="props">
        <q-td :props="props">
          <q-avatar color="primary" text-color="white">
            {{ props.row.username.charAt(0).toUpperCase() }}
          </q-avatar>
        </q-td>
      </template>

      <template v-slot:body-cell-group="props">
        <q-td :props="props">
          <q-chip
            :color="getGroupColor(props.row.group)"
            text-color="white"
            dense
          >
            {{ getGroupLabel(props.row.group) }}
          </q-chip>
        </q-td>
      </template>

      <template v-slot:body-cell-email_verified="props">
        <q-td :props="props">
          <q-chip
            v-if="props.row.email"
            :color="props.row.email_verified ? 'green' : 'orange'"
            text-color="white"
            dense
            size="sm"
          >
            {{ props.row.email_verified ? t('dashboardUserManage.emailVerified') : t('dashboardUserManage.emailUnverified') }}
          </q-chip>
          <span v-else class="text-grey-6">{{ t('dashboardUserManage.noEmail') }}</span>
        </q-td>
      </template>

      <template v-slot:body-cell-actions="props">
        <q-td :props="props">
          <div class="row q-gutter-xs">
            <q-btn
              dense
              round
              color="primary"
              icon="edit"
              size="sm"
              @click="editUser(props.row)"
            >
              <q-tooltip>{{ t('dashboardUserManage.editUserTooltip') }}</q-tooltip>
            </q-btn>

            <q-btn
              dense
              round
              color="negative"
              icon="delete"
              size="sm"
              @click="confirmDelete(props.row)"
              :disable="props.row.id === currentUserId"
            >
              <q-tooltip>{{ t('dashboardUserManage.deleteUserTooltip') }}</q-tooltip>
            </q-btn>
          </div>
        </q-td>
      </template>
    </q-table>

    <!-- 创建用户对话框 -->
    <q-dialog v-model="showCreateDialog" persistent>
      <q-card style="min-width: 500px">
        <q-card-section>
          <div class="text-h6">{{ t('dashboardUserManage.createUserDialog.title') }}</div>
        </q-card-section>

        <q-card-section class="q-pt-none">
          <q-form @submit="createUser" class="q-gutter-md">
            <q-input
              v-model="createForm.username"
              :label="t('dashboardUserManage.createUserDialog.usernameLabel')"
              dense
              outlined
              :rules="[val => val && val.length > 0 || t('dashboardUserManage.createUserDialog.usernameRequired')]"
            />

            <q-input
              v-model="createForm.email"
              :label="t('dashboardUserManage.createUserDialog.emailLabel')"
              type="email"
              dense
              outlined
            />

            <q-input
              v-model="createForm.password"
              :label="t('dashboardUserManage.createUserDialog.passwordLabel')"
              type="password"
              dense
              outlined
              :rules="[val => val && val.length >= 6 || t('dashboardUserManage.createUserDialog.passwordMinLength')]"
            />

            <q-select
              v-model="createForm.group"
              :options="groupOptionsComputed"
              option-label="label"
              option-value="value"
              emit-value
              map-options
              :label="t('dashboardUserManage.createUserDialog.groupLabel')"
              dense
              outlined
            />
          </q-form>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="t('common.cancel')" @click="cancelCreate" />
          <q-btn flat :label="t('common.create')" color="primary" @click="createUser" :loading="creating" />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 编辑用户对话框 -->
    <q-dialog v-model="showEditDialog" persistent>
      <q-card style="min-width: 500px">
        <q-card-section>
          <div class="text-h6">{{ t('dashboardUserManage.editUserDialog.title') }}</div>
        </q-card-section>

        <q-card-section class="q-pt-none">
          <q-form @submit="updateUser" class="q-gutter-md">
            <q-input
              v-model="editForm.username"
              :label="t('dashboardUserManage.editUserDialog.usernameLabel')"
              dense
              outlined
              readonly
            />

            <q-input
              v-model="editForm.email"
              :label="t('dashboardUserManage.editUserDialog.emailLabel')"
              type="email"
              dense
              outlined
            />

            <q-select
              v-model="editForm.group"
              :options="groupOptionsComputed"
              option-label="label"
              option-value="value"
              emit-value
              map-options
              :label="t('dashboardUserManage.editUserDialog.groupLabel')"
              dense
              outlined
            />

            <q-input
              v-model="editForm.newPassword"
              :label="t('dashboardUserManage.editUserDialog.newPasswordLabel')"
              type="password"
              dense
              outlined
            />
          </q-form>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="t('common.cancel')" @click="cancelEdit" />
          <q-btn flat :label="t('common.save')" color="primary" @click="updateUser" :loading="updating" />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 删除确认对话框 -->
    <q-dialog v-model="showDeleteDialog" persistent>
      <q-card>
        <q-card-section>
          <div class="text-h6">{{ t('dashboardUserManage.deleteConfirmDialog.title') }}</div>
          <div class="text-body2">{{ t('dashboardUserManage.deleteConfirmDialog.message', { username: deletingUser?.username }) }}</div>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="t('common.cancel')" v-close-popup />
          <q-btn flat :label="t('common.delete')" color="negative" @click="deleteUser" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, getCurrentInstance } from 'vue'
import { useI18n } from 'vue-i18n'
import { useAuth } from '../../composables/useAuth'
import { useNotification } from '../../composables/useNotification'

defineOptions({
  name: 'UserManagePage'
})

const { t, locale } = useI18n()
const { proxy } = getCurrentInstance()
const { user } = useAuth()
const { showSuccessNotification, showErrorNotification } = useNotification()

// Reactive data
const users = ref([])
const loading = ref(false)
const creating = ref(false)
const updating = ref(false)
const searchQuery = ref('')
const showCreateDialog = ref(false)
const showEditDialog = ref(false)
const showDeleteDialog = ref(false)
const deletingUser = ref(null)
const createForm = ref({
  username: '',
  email: '',
  password: '',
  group: 'user'
})
const editForm = ref({
  id: null,
  username: '',
  email: '',
  group: 'user',
  newPassword: ''
})
const pagination = ref({
  page: 1,
  rowsPerPage: 10,
  rowsNumber: 0
})

// Computed properties
const currentUserId = computed(() => user.value?.id)

const columnsComputed = computed(() => [
  { name: 'avatar', label: '', field: 'avatar', align: 'center', style: 'width: 60px' },
  { name: 'username', label: t('dashboardUserManage.table.username'), field: 'username', sortable: true, align: 'left' },
  { name: 'email', label: t('dashboardUserManage.table.email'), field: 'email', sortable: true, align: 'left' },
  { name: 'group', label: t('dashboardUserManage.table.group'), field: 'group', sortable: true, align: 'center' },
  { name: 'email_verified', label: t('dashboardUserManage.table.emailVerified'), field: 'email_verified', sortable: true, align: 'center' },
  {
    name: 'created_at',
    label: t('dashboardUserManage.table.createdAt'),
    field: 'created_at',
    sortable: true,
    align: 'center',
    format: val => new Date(val).toLocaleDateString(locale.value)
  },
  {
    name: 'updated_at',
    label: t('dashboardUserManage.table.updatedAt'),
    field: 'updated_at',
    sortable: true,
    align: 'center',
    format: val => new Date(val).toLocaleDateString(locale.value)
  },
  { name: 'actions', label: t('dashboardUserManage.table.actions'), field: 'actions', align: 'center' }
])

const groupOptionsComputed = computed(() => [
  { label: t('dashboardUserManage.userGroups.admin'), value: 'admin' },
  { label: t('dashboardUserManage.userGroups.user'), value: 'user' }
])

const loadUsers = async () => {
  loading.value = true
  try {
    const response = await proxy.$api.get('/api/v1/admin/users', {
      params: {
        page: pagination.value.page,
        page_size: pagination.value.rowsPerPage,
        search: searchQuery.value
      }
    })
    users.value = response.data.items || []
    pagination.value.rowsNumber = response.data.pagination.total_items
  } catch (error) {
    users.value = []
    showErrorNotification(error.response?.data?.error || error.message || t('notification.adminLoadUsersFailed'))
  } finally {
    loading.value = false
  }
}

const onRequest = (props) => {
  pagination.value = props.pagination
  loadUsers()
}

const getGroupColor = (group) => {
  if (group === 'admin') return 'purple'
  return 'primary'
}

const getGroupLabel = (group) => {
  const option = groupOptionsComputed.value.find(opt => opt.value === group)
  return option ? option.label : group
}

const cancelCreate = () => {
  showCreateDialog.value = false
  createForm.value = { username: '', email: '', password: '', group: 'user' }
}

const createUser = async () => {
  creating.value = true
  try {
    await proxy.$api.post('/api/v1/admin/users', createForm.value)
    showSuccessNotification(t('notification.adminUserCreated'))
    cancelCreate()
    loadUsers()
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('notification.adminUserCreateFailed'))
  } finally {
    creating.value = false
  }
}

const editUser = (user) => {
  editForm.value = { ...user, newPassword: '' }
  showEditDialog.value = true
}

const cancelEdit = () => {
  showEditDialog.value = false
  editForm.value = { id: null, username: '', email: '', group: 'user', newPassword: '' }
}

const updateUser = async () => {
  updating.value = true
  const payload = { ...editForm.value }
  if (!payload.newPassword) {
    delete payload.newPassword // Don't send empty password
  }
  try {
    await proxy.$api.put(`/api/v1/admin/users/${editForm.value.id}`, payload)
    showSuccessNotification(t('notification.adminUserUpdated'))
    cancelEdit()
    loadUsers()
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('notification.adminUserUpdateFailed'))
  } finally {
    updating.value = false
  }
}

const confirmDelete = (user) => {
  deletingUser.value = user
  showDeleteDialog.value = true
}

const deleteUser = async () => {
  try {
    await proxy.$api.delete(`/api/v1/admin/users/${deletingUser.value.id}`)
    showSuccessNotification(t('notification.adminUserDeleted'))
    showDeleteDialog.value = false
    deletingUser.value = null
    loadUsers()
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('notification.adminUserDeleteFailed'))
  }
}

// Lifecycle
onMounted(() => {
  loadUsers()
})
</script>
