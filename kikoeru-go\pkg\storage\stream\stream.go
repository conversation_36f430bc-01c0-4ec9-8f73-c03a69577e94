package stream

import (
	"bytes"
	"errors"
	"fmt"
	"io"
	"os"
	"sync"
	"time"

	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
)

// Link represents a link to a file
type Link struct {
	URL        string
	MFile      io.ReadCloser
	Header     map[string]string
	Size       int64
	Expiration *time.Time
}

// SeekableStream is a stream that can be seeked
type SeekableStream struct {
	io.ReadCloser
	io.Seeker
	Link     *Link
	Size     int64
	tmpFile  *os.File      // Temporary file for caching content
	peekBuff *bytes.Reader // Buffer for peeking into content
	mu       sync.Mutex    // Mutex for thread safety
	closers  []io.Closer   // Slice of closers to be closed when stream is closed
}

// GetSize returns the size of the stream
func (s *SeekableStream) GetSize() int64 {
	if s.tmpFile != nil {
		info, err := s.tmpFile.Stat()
		if err == nil {
			return info.Size()
		}
	}
	return s.Size
}

// ReadSeekCloser combines io.Reader, io.Seeker and io.Closer
type ReadSeekCloser interface {
	io.Reader
	io.Seeker
	io.Closer
}

// Add adds a closer to the list of closers
func (s *SeekableStream) Add(closer io.Closer) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.closers = append(s.closers, closer)
}

// Close closes the stream and all registered closers
func (s *SeekableStream) Close() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	var errs []error

	// Close all registered closers
	for _, closer := range s.closers {
		if err := closer.Close(); err != nil {
			errs = append(errs, err)
		}
	}
	s.closers = nil

	// Close the main reader/closer
	if s.ReadCloser != nil {
		if err := s.ReadCloser.Close(); err != nil {
			errs = append(errs, err)
		}
	}

	// Clean up temporary file if it exists
	if s.tmpFile != nil {
		if err := s.tmpFile.Close(); err != nil {
			errs = append(errs, err)
		}
		// Remove the temporary file
		if err := os.Remove(s.tmpFile.Name()); err != nil {
			errs = append(errs, fmt.Errorf("failed to remove temporary file %s: %w", s.tmpFile.Name(), err))
		}
		s.tmpFile = nil
	}

	if len(errs) > 0 {
		return fmt.Errorf("multiple errors during close: %v", errs)
	}
	return nil
}

// HttpRange specifies the byte range to be sent to the client
type HttpRange struct {
	Start  int64
	Length int64
}

// NewSeekableStream creates a new SeekableStream from a file stream and link
func NewSeekableStream(file models.FileStream, link *Link) (*SeekableStream, error) {
	if link == nil {
		return nil, errors.New("link is nil")
	}

	var r ReadSeekCloser
	if link.MFile != nil {
		// If we already have a ReadCloser, try to convert it to a ReadSeekCloser
		if rsc, ok := link.MFile.(ReadSeekCloser); ok {
			r = rsc
		} else {
			// We can't seek in this file, return an error
			return nil, errors.New("file does not support seeking")
		}
	} else {
		// We need to create a ReadSeekCloser from the URL
		// This would typically involve making an HTTP request with range support
		return nil, errors.New("creating seekable stream from URL not implemented")
	}

	size := link.Size
	if size == 0 && file.Obj != nil {
		size = file.Obj.GetSize()
	}

	ss := &SeekableStream{
		ReadCloser: r,
		Seeker:     r,
		Link:       link,
		Size:       size,
		closers:    make([]io.Closer, 0),
	}

	return ss, nil
}

const InMemoryBufMaxSize = 10 * 1024 * 1024 // 10MB max buffer size for in-memory caching

// RangeRead reads a specific range from the stream
// If the range is small enough, it might be served from memory
// Otherwise, it might require caching the entire file first
func (s *SeekableStream) RangeRead(httpRange HttpRange) (io.ReadCloser, error) {
	s.mu.Lock()
	defer s.mu.Unlock()

	// Adjust length if not specified
	if httpRange.Length == -1 {
		httpRange.Length = s.GetSize() - httpRange.Start
	}

	// Check if range exceeds file size
	if httpRange.Start >= s.GetSize() {
		return nil, fmt.Errorf("range start (%d) exceeds file size (%d)", httpRange.Start, s.GetSize())
	}

	// Check if we can serve this from our peek buffer
	if s.peekBuff != nil && httpRange.Start < int64(s.peekBuff.Size()) &&
		httpRange.Start+httpRange.Length <= int64(s.peekBuff.Size()) {
		// Create a section reader from our peek buffer
		s.peekBuff.Seek(httpRange.Start, io.SeekStart)
		return io.NopCloser(io.LimitReader(s.peekBuff, httpRange.Length)), nil
	}

	// If we have a temporary file, serve from it
	if s.tmpFile != nil {
		// Create a section reader from the temporary file
		return &limitedReadCloser{
			Reader: io.NewSectionReader(s.tmpFile, httpRange.Start, httpRange.Length),
			Closer: io.NopCloser(nil), // Nothing to close, tmpFile will be closed by SeekableStream
		}, nil
	}

	// If we're dealing with the first part of the file and it's small enough,
	// we might cache it in memory for faster subsequent requests
	if httpRange.Start == 0 && httpRange.Length <= InMemoryBufMaxSize && s.peekBuff == nil {
		bufSize := min(httpRange.Length, s.GetSize())

		// Temporarily store current position
		currentPos, err := s.Seeker.Seek(0, io.SeekCurrent)
		if err != nil {
			return nil, fmt.Errorf("failed to get current position: %w", err)
		}

		// Seek to start
		_, err = s.Seeker.Seek(0, io.SeekStart)
		if err != nil {
			return nil, fmt.Errorf("failed to seek to start: %w", err)
		}

		// Read data into buffer
		buf := make([]byte, bufSize)
		n, err := io.ReadFull(s.ReadCloser, buf)
		if err != nil && err != io.EOF && err != io.ErrUnexpectedEOF {
			// Seek back to original position on error
			s.Seeker.Seek(currentPos, io.SeekStart)
			return nil, fmt.Errorf("failed to read data for peek buffer: %w", err)
		}

		// Create peek buffer with actual data read
		s.peekBuff = bytes.NewReader(buf[:n])

		// Seek back to original position
		_, err = s.Seeker.Seek(currentPos, io.SeekStart)
		if err != nil {
			return nil, fmt.Errorf("failed to seek back to original position: %w", err)
		}

		// If we now have the data in our peek buffer, serve from it
		if int64(n) >= httpRange.Start+httpRange.Length {
			s.peekBuff.Seek(httpRange.Start, io.SeekStart)
			return io.NopCloser(io.LimitReader(s.peekBuff, httpRange.Length)), nil
		}
	}

	// Otherwise, we need to seek and create a limited reader
	_, err := s.Seeker.Seek(httpRange.Start, io.SeekStart)
	if err != nil {
		return nil, fmt.Errorf("failed to seek to range start: %w", err)
	}

	return &limitedReadCloser{
		Reader: io.LimitReader(s.ReadCloser, httpRange.Length),
		Closer: io.NopCloser(nil), // The actual closing is handled by SeekableStream
	}, nil
}

// limitedReadCloser combines a limited reader with a closer
type limitedReadCloser struct {
	Reader io.Reader
	Closer io.Closer
}

func (lrc *limitedReadCloser) Read(p []byte) (n int, err error) {
	return lrc.Reader.Read(p)
}

func (lrc *limitedReadCloser) Close() error {
	return lrc.Closer.Close()
}

// CacheFullInTempFile caches the entire file in a temporary file
// This is useful for operations that need random access to the file
func (s *SeekableStream) CacheFullInTempFile() (*os.File, error) {
	s.mu.Lock()
	defer s.mu.Unlock()

	// If we already have a temporary file, return it
	if s.tmpFile != nil {
		return s.tmpFile, nil
	}

	// Create a temporary file
	tmpFile, err := os.CreateTemp("", "kikoeru-stream-*.tmp")
	if err != nil {
		return nil, fmt.Errorf("failed to create temporary file: %w", err)
	}

	// Save current position
	currentPos, err := s.Seeker.Seek(0, io.SeekCurrent)
	if err != nil {
		tmpFile.Close()
		os.Remove(tmpFile.Name())
		return nil, fmt.Errorf("failed to get current position: %w", err)
	}

	// Seek to beginning
	_, err = s.Seeker.Seek(0, io.SeekStart)
	if err != nil {
		tmpFile.Close()
		os.Remove(tmpFile.Name())
		return nil, fmt.Errorf("failed to seek to beginning: %w", err)
	}

	// Copy all data to temporary file
	_, err = io.Copy(tmpFile, s.ReadCloser)
	if err != nil {
		tmpFile.Close()
		os.Remove(tmpFile.Name())
		// Restore original position
		s.Seeker.Seek(currentPos, io.SeekStart)
		return nil, fmt.Errorf("failed to copy data to temporary file: %w", err)
	}

	// Seek back to original position in the source
	_, err = s.Seeker.Seek(currentPos, io.SeekStart)
	if err != nil {
		tmpFile.Close()
		os.Remove(tmpFile.Name())
		return nil, fmt.Errorf("failed to restore original position: %w", err)
	}

	// Seek temporary file to beginning
	_, err = tmpFile.Seek(0, io.SeekStart)
	if err != nil {
		tmpFile.Close()
		os.Remove(tmpFile.Name())
		return nil, fmt.Errorf("failed to seek temporary file to beginning: %w", err)
	}

	s.tmpFile = tmpFile
	return tmpFile, nil
}

// ConvertToStreamSeekable converts a models.StreamWithSeek to a SeekableStream
func ConvertToStreamSeekable(ss *models.StreamWithSeek) *SeekableStream {
	return &SeekableStream{
		ReadCloser: ss.ReadCloser,
		Seeker:     ss.Seeker,
		Size:       ss.Size,
		closers:    make([]io.Closer, 0),
	}
}

// ConvertToModelsSeekable converts a SeekableStream to a models.StreamWithSeek
func ConvertToModelsSeekable(ss *SeekableStream) *models.StreamWithSeek {
	return &models.StreamWithSeek{
		ReadCloser: ss.ReadCloser,
		Seeker:     ss.Seeker,
		Size:       ss.Size,
	}
}

// min returns the smaller of two int64 values
func min(a, b int64) int64 {
	if a < b {
		return a
	}
	return b
}

// readerSeeker combines io.ReadCloser and io.Seeker
type readerSeeker struct {
	io.ReadCloser
	io.Seeker
}

// ConvertToReaderSeeker converts models.StreamWithSeek to io.ReadSeeker
func ConvertToReaderSeeker(ss *models.StreamWithSeek) io.ReadSeeker {
	return &readerSeeker{
		ReadCloser: ss.ReadCloser,
		Seeker:     ss.Seeker,
	}
}

// ConvertStreamWithSeekToReaderSeeker converts models.StreamWithSeek to io.ReadSeeker
func ConvertStreamWithSeekToReaderSeeker(ss *models.StreamWithSeek) io.ReadSeeker {
	return &readerSeeker{
		ReadCloser: ss.ReadCloser,
		Seeker:     ss.Seeker,
	}
}

// NewReadAtSeeker creates a ReadAtSeeker from either a models.StreamWithSeek or a StreamWithSeekAdapter
func NewReadAtSeeker(ss interface{}, offset int64) (ReadAtSeeker, error) {
	var readSeeker io.ReadSeeker
	var size int64

	switch s := ss.(type) {
	case *models.StreamWithSeek:
		readSeeker = s
		size = s.Size
	case *StreamWithSeekAdapter:
		readSeeker = s.StreamWithSeek
		size = s.Size
	default:
		return nil, fmt.Errorf("unsupported stream type: %T", ss)
	}

	if offset > 0 {
		_, err := readSeeker.Seek(offset, io.SeekStart)
		if err != nil {
			return nil, err
		}
	}

	return &ReadAtSeekerWrapper{
		ReadSeeker: readSeeker,
		size:       size,
	}, nil
}

// ReadAtSeeker is an interface that combines io.ReaderAt and io.ReadSeeker
type ReadAtSeeker interface {
	io.ReaderAt
	io.ReadSeeker
	Size() int64
}

// ReadAtSeekerWrapper adapts an io.ReadSeeker to implement ReadAtSeeker
type ReadAtSeekerWrapper struct {
	io.ReadSeeker
	size int64
}

// ReadAt implements io.ReaderAt
func (r *ReadAtSeekerWrapper) ReadAt(p []byte, off int64) (n int, err error) {
	// Save the current position
	currentPos, err := r.ReadSeeker.Seek(0, io.SeekCurrent)
	if err != nil {
		return 0, err
	}

	// Seek to the requested offset
	_, err = r.ReadSeeker.Seek(off, io.SeekStart)
	if err != nil {
		return 0, err
	}

	// Read the data
	n, err = r.ReadSeeker.Read(p)

	// Restore the original position
	_, seekErr := r.ReadSeeker.Seek(currentPos, io.SeekStart)
	if seekErr != nil && err == nil {
		err = seekErr
	}

	return n, err
}

// Size returns the size of the underlying stream
func (r *ReadAtSeekerWrapper) Size() int64 {
	return r.size
}

// CacheFullInTempFile caches the entire stream content in a temporary file for better random access
// This is useful for archive operations which may need random access to file content
func (r *ReadAtSeekerWrapper) CacheFullInTempFile() (*os.File, error) {
	// Create a temporary file
	tmpFile, err := os.CreateTemp("", "kikoeru-stream-*.tmp")
	if err != nil {
		return nil, err
	}

	// Reset the stream to the beginning
	_, err = r.ReadSeeker.Seek(0, io.SeekStart)
	if err != nil {
		tmpFile.Close()
		os.Remove(tmpFile.Name())
		return nil, err
	}

	// Copy all data to the temporary file
	_, err = io.Copy(tmpFile, r.ReadSeeker)
	if err != nil {
		tmpFile.Close()
		os.Remove(tmpFile.Name())
		return nil, err
	}

	// Seek back to the beginning of the temporary file
	_, err = tmpFile.Seek(0, io.SeekStart)
	if err != nil {
		tmpFile.Close()
		os.Remove(tmpFile.Name())
		return nil, err
	}

	// Set the file to be deleted automatically when closed
	// This only works on some platforms, but it's a best effort
	tmpFile.Chdir()

	return tmpFile, nil
}
