package database

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
	common_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/common"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"gorm.io/gorm"
)

// ListTagsParams defines parameters for listing tags.
type ListTagsParams struct {
	common_dto.PaginationParams
	Name string
}

// TagRepository defines the repository interface for tags.
type TagRepository interface {
	Create(ctx context.Context, tag *models.Tag) error
	GetByID(ctx context.Context, id string) (*models.Tag, error)
	GetByName(ctx context.Context, name string) (*models.Tag, error)
	List(ctx context.Context, params ListTagsParams) ([]*models.Tag, int64, error)
	Update(ctx context.Context, tag *models.Tag) error
	DeleteByID(ctx context.Context, tagID string) error
	GetOrCreateTag(ctx context.Context, name string) (*models.Tag, error)
	ListAllWithWorkCount(ctx context.Context) ([]*models.Tag, error)
}

type tagRepository struct {
	db *gorm.DB
}

func NewTagRepository(db *gorm.DB) TagRepository {
	return &tagRepository{db: db}
}

func (r *tagRepository) Create(ctx context.Context, tag *models.Tag) error {
	if tag == nil {
		return errors.New("tag cannot be nil")
	}
	// Name is unique. ID is primary key and auto-incremented.
	// Create will fail if name is not unique due to DB constraints.
	result := r.db.WithContext(ctx).Create(tag)
	if result.Error != nil {
		// TODO: Check for specific DB errors like unique constraint violation
		// and return a more specific error like service.ErrTagAlreadyExists
		return fmt.Errorf("failed to create tag in DB: %w", result.Error)
	}
	return nil
}

func (r *tagRepository) GetByID(ctx context.Context, id string) (*models.Tag, error) {
	var tag models.Tag
	if id == "" {
		return nil, errors.New("tag ID cannot be empty")
	}
	result := r.db.WithContext(ctx).Where("id = ?", id).First(&tag)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, apperrors.ErrTagNotFound
		}
		return nil, result.Error
	}
	return &tag, nil
}

func (r *tagRepository) GetByName(ctx context.Context, name string) (*models.Tag, error) {
	var tag models.Tag
	if name == "" {
		return nil, errors.New("tag name cannot be empty")
	}
	result := r.db.WithContext(ctx).Where("name = ?", name).First(&tag)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, apperrors.ErrTagNotFound
		}
		return nil, result.Error
	}
	return &tag, nil
}

func (r *tagRepository) List(ctx context.Context, params ListTagsParams) ([]*models.Tag, int64, error) {
	query := r.db.WithContext(ctx).Model(&models.Tag{})

	if params.Name != "" {
		// Case-insensitive search for name
		query = query.Where("LOWER(name) LIKE LOWER(?)", "%"+params.Name+"%")
	}

	var totalCount int64
	if err := query.Count(&totalCount).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count tags: %w", err)
	}

	// Apply pagination
	if params.Page > 0 && params.PageSize > 0 {
		offset := (params.Page - 1) * params.PageSize
		query = query.Offset(offset).Limit(params.PageSize)
	}

	// Apply sorting
	if params.SortBy == "" {
		params.SortBy = "name" // Default sort
	}
	order := params.SortBy
	if params.SortOrder != "" && (strings.ToLower(params.SortOrder) == "asc" || strings.ToLower(params.SortOrder) == "desc") {
		order += " " + strings.ToLower(params.SortOrder)
	} else {
		order += " asc" // Default to ascending
	}
	query = query.Order(order)

	var tags []*models.Tag
	if err := query.Find(&tags).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list tags: %w", err)
	}

	return tags, totalCount, nil
}

func (r *tagRepository) Update(ctx context.Context, tag *models.Tag) error {
	if tag == nil || tag.ID == "" {
		return errors.New("tag for update must not be nil and must have a non-empty ID")
	}
	result := r.db.WithContext(ctx).Save(tag)
	return result.Error
}

func (r *tagRepository) DeleteByID(ctx context.Context, tagID string) error {
	if tagID == "" {
		return errors.New("tag ID cannot be empty for deletion")
	}

	// GORM's Delete with a primary key value will only delete if the record exists.
	// We check RowsAffected to confirm deletion.
	result := r.db.WithContext(ctx).Where("id = ?", tagID).Delete(&models.Tag{})
	if result.Error != nil {
		return fmt.Errorf("failed to delete tag %s: %w", tagID, result.Error)
	}
	if result.RowsAffected == 0 {
		return apperrors.ErrTagNotFound
	}
	return nil
}

func (r *tagRepository) GetOrCreateTag(ctx context.Context, name string) (*models.Tag, error) {
	if name == "" {
		return nil, errors.New("tag name cannot be empty for GetOrCreateTag")
	}
	var tag models.Tag
	// Try to find the tag first
	result := r.db.WithContext(ctx).Where("name = ?", name).First(&tag)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			// Tag not found, create it
			newTag := &models.Tag{Name: name}
			if createErr := r.Create(ctx, newTag); createErr != nil {
				// Check for unique constraint violation in case of concurrent creation
				// TODO: More robust check for unique constraint violation error type
				if strings.Contains(createErr.Error(), "Duplicate entry") || strings.Contains(createErr.Error(), "UNIQUE constraint failed") {
					// If it's a unique constraint error, try to get the existing tag again
					return r.GetByName(ctx, name)
				}
				return nil, fmt.Errorf("failed to create new tag '%s': %w", name, createErr)
			}
			return newTag, nil
		}
		// Other database error
		return nil, fmt.Errorf("failed to get tag by name '%s': %w", name, result.Error)
	}
	// Tag found
	return &tag, nil
}

// ListAllWithWorkCount retrieves all tags and their associated work count.
func (r *tagRepository) ListAllWithWorkCount(ctx context.Context) ([]*models.Tag, error) {
	var tags []*models.Tag
	// Join with r_work_tags and t_work to count associated works
	// Select tags and count of works, grouping by tag ID
	// Note: This query might be slow for a very large number of tags or works.
	// Consider pagination if needed in the future.

	type TagWithCount struct {
		models.Tag
		WorkCount int64 `json:"work_count"`
	}

	var results []*TagWithCount

	result := r.db.WithContext(ctx).
		Model(&models.Tag{}).
		Select("t_tag.*, COUNT(r_work_tags.work_id) as work_count").
		Joins("LEFT JOIN r_work_tags ON t_tag.id = r_work_tags.tag_id").
		Group("t_tag.id").
		Order("t_tag.name asc").
		Scan(&results)

	if result.Error != nil {
		return nil, fmt.Errorf("failed to list tags with work count: %w", result.Error)
	}

	// Convert to []*models.Tag with WorkCount populated
	tags = make([]*models.Tag, len(results))
	for i, r := range results {
		tags[i] = &models.Tag{
			ID:        r.ID,
			Name:      r.Name,
			CreatedAt: r.CreatedAt,
			UpdatedAt: r.UpdatedAt,
			WorkCount: r.WorkCount,
		}
	}

	return tags, nil
}
