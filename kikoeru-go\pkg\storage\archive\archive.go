package archive

import (
	"context"
	"io"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/archive/tool"
)

// ExtractFile extracts a file from an archive
func ExtractFile(ctx context.Context, archiveStream io.ReadSeeker, archiveSize int64, archiveName string, innerPath string, password string) (io.ReadCloser, int64, error) {
	// Create a seekable stream from the archive
	ss := &models.StreamWithSeek{
		ReadCloser: io.NopCloser(archiveStream),
		Seeker:     archiveStream,
		Size:       archiveSize,
	}

	// Get the archive tool based on file extension
	t, _, err := tool.GetArchiveTool(archiveName)
	if err != nil {
		return nil, 0, apperrors.ErrUnsupportedArchiveFormat
	}

	// Extract the file
	args := models.ArchiveInnerArgs{
		ArchiveArgs: models.ArchiveArgs{
			Password: password,
		},
		InnerPath: innerPath,
	}

	rc, size, err := t.Extract(ss, args)
	if err != nil {
		return nil, 0, err
	}

	return rc, size, nil
}

// ListArchive lists files in an archive
func ListArchive(archiveStream io.ReadSeeker, archiveSize int64, archiveName string, innerPath string, password string) ([]models.Obj, error) {
	// Create a seekable stream from the archive
	ss := &models.StreamWithSeek{
		ReadCloser: io.NopCloser(archiveStream),
		Seeker:     archiveStream,
		Size:       archiveSize,
	}

	// Get the archive tool based on file extension
	t, _, err := tool.GetArchiveTool(archiveName)
	if err != nil {
		return nil, apperrors.ErrUnsupportedArchiveFormat
	}

	// List files
	args := models.ArchiveInnerArgs{
		ArchiveArgs: models.ArchiveArgs{
			Password: password,
		},
		InnerPath: innerPath,
	}

	return t.List(ss, args)
}

// GetArchiveMeta gets metadata about an archive
func GetArchiveMeta(archiveStream io.ReadSeeker, archiveSize int64, archiveName string, password string) (models.ArchiveMeta, error) {
	// Create a seekable stream from the archive
	ss := &models.StreamWithSeek{
		ReadCloser: io.NopCloser(archiveStream),
		Seeker:     archiveStream,
		Size:       archiveSize,
	}

	// Get the archive tool based on file extension
	t, _, err := tool.GetArchiveTool(archiveName)
	if err != nil {
		return nil, apperrors.ErrUnsupportedArchiveFormat
	}

	// Get metadata
	args := models.ArchiveArgs{
		Password: password,
	}

	return t.GetMeta(ss, args)
}

// DecompressArchive decompresses an archive to a directory
func DecompressArchive(archiveStream io.ReadSeeker, archiveSize int64, archiveName string, outputPath string, innerPath string, password string, progressFn func(float64)) error {
	// Create a seekable stream from the archive
	ss := &models.StreamWithSeek{
		ReadCloser: io.NopCloser(archiveStream),
		Seeker:     archiveStream,
		Size:       archiveSize,
	}

	// Get the archive tool based on file extension
	t, _, err := tool.GetArchiveTool(archiveName)
	if err != nil {
		return apperrors.ErrUnsupportedArchiveFormat
	}

	// Decompress
	args := models.ArchiveInnerArgs{
		ArchiveArgs: models.ArchiveArgs{
			Password: password,
		},
		InnerPath: innerPath,
	}

	return t.Decompress(ss, outputPath, args, progressFn)
}
