package models

import (
	"time"

	"github.com/Sakura-Byte/kikoeru-go/pkg/utils"
	"gorm.io/gorm"
)

// Tag 对应数据库中的 t_tag 表
type Tag struct {
	ID        string    `gorm:"primaryKey;type:varchar(36)" json:"id"`              // UUID
	Name      string    `gorm:"type:varchar(255);not null;uniqueIndex" json:"name"` // 标签名称通常应该是唯一的
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	WorkCount int64     `gorm:"-" json:"work_count,omitempty"` // Used to store count of associated works

	// 反向关联 (可选, GORM 可以处理)
	Works []*Work `gorm:"many2many:r_work_tags;" json:"works,omitempty"`
}

// TableName 指定 GORM 使用的表名
func (Tag) TableName() string {
	return "t_tag"
}

// BeforeCreate hook to generate UUID v5 based on tag name
func (t *Tag) BeforeCreate(tx *gorm.DB) (err error) {
	if t.ID == "" && t.Name != "" {
		t.ID, err = utils.GenerateUUIDv5ForName(t.Name)
		if err != nil {
			return err
		}
	}
	return
}

// r_work_tags 是 Work 和 Tag 之间的连接表。
// GORM 会自动创建和管理这个表，如果它不存在并且你在模型中定义了 many2many 关系。
// 如果需要为连接表添加额外字段，则需要显式定义这个连接表模型。
// 在当前 kikoeru-express 的 schema.js 中，r_tag_work 只有 tag_id 和 work_id。
// 因此，我们通常不需要为 GORM 显式定义 RWorkTag 结构体，除非要对其进行直接操作。
// GORM 标签中的 "r_work_tags" 已经指定了连接表的名称。
