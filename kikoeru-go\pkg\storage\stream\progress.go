package stream

import (
	"io"

	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
)

// SimpleReaderWithSize is a reader with a size
type SimpleReaderWithSize struct {
	Reader io.Reader
	Size   int64
}

// Read implements the io.Reader interface
func (r *SimpleReaderWithSize) Read(p []byte) (n int, err error) {
	return r.Reader.Read(p)
}

// Close implements the io.Closer interface
func (r *SimpleReaderWithSize) Close() error {
	if c, ok := r.Reader.(io.Closer); ok {
		return c.Close()
	}
	return nil
}

// GetSize returns the size of the reader
func (r *SimpleReaderWithSize) GetSize() int64 {
	return r.Size
}

// ReaderUpdatingProgress is a reader that updates progress
type ReaderUpdatingProgress struct {
	Reader         io.Reader
	UpdateProgress models.UpdateProgress
	read           int64
	size           int64
}

// Read implements the io.Reader interface
func (r *ReaderUpdatingProgress) Read(p []byte) (n int, err error) {
	n, err = r.Reader.Read(p)
	if n > 0 {
		r.read += int64(n)
		if r.UpdateProgress != nil {
			size := r.size
			if size <= 0 {
				if sized, ok := r.Reader.(interface{ GetSize() int64 }); ok {
					size = sized.GetSize()
				}
			}
			if size > 0 {
				r.UpdateProgress(float64(r.read) / float64(size) * 100)
			}
		}
	}
	return
}

// Close implements the io.Closer interface
func (r *ReaderUpdatingProgress) Close() error {
	if c, ok := r.Reader.(io.Closer); ok {
		return c.Close()
	}
	return nil
}
