package handler

import (
	"net/http"

	common_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/common"
	"github.com/Sakura-Byte/kikoeru-go/pkg/http/middleware"
	"github.com/Sakura-Byte/kikoeru-go/pkg/http/rest/common"
	"github.com/Sakura-Byte/kikoeru-go/pkg/ports"
	"github.com/gin-gonic/gin"
)

// AdminProfileHandler handles admin profile-related endpoints
type AdminProfileHandler struct {
	userService ports.UserService
}

// NewAdminProfileHandler creates a new AdminProfileHandler instance
func NewAdminProfileHandler(userService ports.UserService) *AdminProfileHandler {
	return &AdminProfileHandler{
		userService: userService,
	}
}

// AdminProfileUpdateRequest represents the request body for admin profile update
type AdminProfileUpdateRequest struct {
	NewUsername *string `json:"newUsername"`
	NewPassword *string `json:"newPassword"`
}

// UpdateAdminProfile godoc
// @Summary Update admin profile
// @Description Update the current admin's username and/or password without requiring current password
// @Tags admin
// @Accept json
// @Produce json
// @Param profile_update body AdminProfileUpdateRequest true "Admin profile update details"
// @Success 200 {object} common_dto.SuccessResponse "Profile updated successfully"
// @Failure 400 {object} common.ErrorResponse "Invalid request payload or validation error"
// @Failure 401 {object} common.ErrorResponse "Unauthorized"
// @Failure 403 {object} common.ErrorResponse "Forbidden - Not an admin"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /admin/profile/update [post]
// @Security BearerAuth
func (h *AdminProfileHandler) UpdateAdminProfile(c *gin.Context) {
	userClaims := middleware.GetUserClaims(c)
	if userClaims == nil || userClaims.UserID == "" {
		common.SendErrorResponse(c, http.StatusUnauthorized, "Authentication required")
		return
	}

	// Verify user is an admin before proceeding
	user, err := h.userService.GetUserByID(c.Request.Context(), userClaims.UserID)
	if err != nil {
		common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to retrieve user details")
		return
	}

	if user.Group != "admin" {
		common.SendErrorResponse(c, http.StatusForbidden, "Only admin users can access this endpoint")
		return
	}

	var req AdminProfileUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid request payload: "+err.Error())
		return
	}

	// No changes requested
	if req.NewUsername == nil && req.NewPassword == nil {
		response := common_dto.SuccessResponse{
			Message: "No changes requested",
		}
		c.JSON(http.StatusOK, response)
		return
	}

	// Using the AdminUpdateOwnProfile method from UserService
	_, err = h.userService.AdminUpdateOwnProfile(c.Request.Context(), userClaims.Username, req.NewUsername, req.NewPassword)
	if err != nil {
		common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to update admin profile: "+err.Error())
		return
	}

	response := common_dto.SuccessResponse{
		Message: "Admin profile updated successfully",
	}
	c.JSON(http.StatusOK, response)
}
