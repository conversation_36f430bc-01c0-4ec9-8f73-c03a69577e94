package models

import "time"

// ProgressStatus 定义了作品的收听进度状态
type ProgressStatus string

const (
	ProgressMarked    ProgressStatus = "marked"    // 想听
	ProgressListening ProgressStatus = "listening" // 在听
	ProgressListened  ProgressStatus = "listened"  // 听过
	ProgressReplay    ProgressStatus = "replay"    // 重听
	ProgressPostponed ProgressStatus = "postponed" // 搁置
	ProgressNone      ProgressStatus = ""          // 无标记 (或 null)
)

// Review 对应数据库中的 t_reviews 表
type Review struct {
	ID     uint   `gorm:"primaryKey;autoIncrement" json:"id"`
	UserID string `gorm:"type:varchar(36);not null;index:idx_review_user_work_unique,unique" json:"user_id"`  // Changed to string for User UUID
	WorkID string `gorm:"type:varchar(255);not null;index:idx_review_user_work_unique,unique" json:"work_id"` // Changed to string for Work OriginalID (RJID)
	Rating *int   `gorm:"type:smallint" json:"rating,omitempty"`                                              // 用户评分, 使用指针以允许 NULL
	// Ensure ReviewText can be null in DB if json omitempty is to truly represent null.
	// GORM default for string is not null. For nullable text, use *string or implement custom scanner/valuer.
	// For simplicity, keeping as string, omitempty will omit if empty string. If actual NULL is needed, use *string.
	ReviewText *string        `gorm:"type:text" json:"review_text,omitempty"` // Changed to *string to allow null
	Progress   ProgressStatus `gorm:"type:varchar(50);index" json:"progress,omitempty"`
	CreatedAt  time.Time      `gorm:"default:CURRENT_TIMESTAMP;not null" json:"created_at"`
	UpdatedAt  time.Time      `gorm:"default:CURRENT_TIMESTAMP;not null" json:"updated_at"`

	// GORM 关联关系
	User *User `gorm:"foreignKey:UserID" json:"user,omitempty"`
	// Work *Work `gorm:"foreignKey:WorkID" json:"work,omitempty"` // This FK won't work directly if WorkID is OriginalID and Work.ID is the PK.
	// Work association will be handled by service layer if needed.
	Work *Work `gorm:"-" json:"work,omitempty"` // Keep for API response, but GORM won't manage it via FK.
}

// TableName 指定 GORM 使用的表名
func (Review) TableName() string {
	return "t_reviews"
}
