package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
	playhistory_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/playhistory" // Added DTO import
	"github.com/Sakura-Byte/kikoeru-go/pkg/log"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"github.com/Sakura-Byte/kikoeru-go/pkg/ports" // Import ports package
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/database"
)

// ErrInvalidPlayHistoryInput has been moved to apperrors package

// PlayHistoryService interface has been moved to github.com/Sakura-Byte/kikoeru-go/pkg/ports

type playHistoryService struct {
	historyRepo database.PlayHistoryRepository
	workRepo    database.WorkRepository
}

// Ensure playHistoryService implements ports.PlayHistoryService
var _ ports.PlayHistoryService = (*playHistoryService)(nil)

// NewPlayHistoryService creates a new PlayHistoryService.
func NewPlayHistoryService(
	historyRepo database.PlayHistoryRepository,
	workRepo database.WorkRepository,
) ports.PlayHistoryService { // Changed return type to ports.PlayHistoryService
	return &playHistoryService{
		historyRepo: historyRepo,
		workRepo:    workRepo,
	}
}

func (s *playHistoryService) RecordPlayPosition(ctx context.Context, userID string, req playhistory_dto.RecordPlayPositionRequest) error {
	if userID == "" {
		return fmt.Errorf("%w: user ID cannot be empty", apperrors.ErrInvalidPlayHistoryInput)
	}
	if req.WorkID == 0 {
		return fmt.Errorf("%w: work ID cannot be zero", apperrors.ErrInvalidPlayHistoryInput)
	}
	if req.PlaybackPositionSeconds < 0 {
		return fmt.Errorf("%w: playback position cannot be negative", apperrors.ErrInvalidPlayHistoryInput)
	}
	if req.ProgressPercentage < 0.0 || req.ProgressPercentage > 1.0 {
		return fmt.Errorf("%w: progress percentage must be between 0.0 and 1.0", apperrors.ErrInvalidPlayHistoryInput)
	}

	// Validate if work exists
	_, err := s.workRepo.GetByID(ctx, req.WorkID)
	if err != nil {
		if errors.Is(err, apperrors.ErrWorkNotFound) {
			return fmt.Errorf("%w: work with ID %d not found", apperrors.ErrWorkNotFound, req.WorkID)
		}
		log.Error(ctx, "Failed to validate work for play history", "work_id", req.WorkID, "error", err)
		return fmt.Errorf("failed to validate work: %w", err)
	}

	history := &models.PlayHistory{
		UserID:                  userID,
		WorkID:                  req.WorkID,
		TrackPath:               req.TrackPath,
		PlaybackPositionSeconds: req.PlaybackPositionSeconds,
		ProgressPercentage:      req.ProgressPercentage,
		IsFinished:              req.IsFinished,
		UpdatedAt:               time.Now(),
	}

	log.Debug(ctx, "Recording play position", "user_id", userID, "work_id", req.WorkID, "track", req.TrackPath, "pos_sec", req.PlaybackPositionSeconds, "progress", req.ProgressPercentage)
	if err := s.historyRepo.Upsert(ctx, history); err != nil {
		log.Error(ctx, "Failed to upsert play history", "user_id", userID, "work_id", req.WorkID, "error", err)
		return fmt.Errorf("could not record play position: %w", err)
	}
	return nil
}

func (s *playHistoryService) GetUserPlayHistory(
	ctx context.Context, userID string, page int, pageSize int, orderBy string, sortDirection string,
) ([]*models.PlayHistory, int64, error) {
	if userID == "" {
		return nil, 0, fmt.Errorf("%w: user ID cannot be empty", apperrors.ErrInvalidPlayHistoryInput)
	}
	log.Debug(ctx, "Fetching play history for user",
		"user_id", userID,
		"page", page,
		"page_size", pageSize,
		"order_by", orderBy,
		"sort_direction", sortDirection)

	records, total, err := s.historyRepo.ListByUser(ctx, userID, page, pageSize, orderBy, sortDirection)
	if err != nil {
		log.Error(ctx, "Failed to get user play history from repository", "user_id", userID, "error", err)
		return nil, 0, fmt.Errorf("could not retrieve play history: %w", err)
	}
	return records, total, nil
}

func (s *playHistoryService) DeletePlayHistory(ctx context.Context, userID string, historyID uint) error {
	if userID == "" {
		return fmt.Errorf("%w: user ID cannot be empty", apperrors.ErrInvalidPlayHistoryInput)
	}
	if historyID == 0 {
		return fmt.Errorf("%w: history ID cannot be zero", apperrors.ErrInvalidPlayHistoryInput)
	}

	log.Debug(ctx, "Deleting play history record", "user_id", userID, "history_id", historyID)
	err := s.historyRepo.Delete(ctx, historyID)
	if err != nil {
		if errors.Is(err, apperrors.ErrPlayHistoryNotFound) {
			return fmt.Errorf("%w: history record ID %d not found", apperrors.ErrPlayHistoryNotFound, historyID)
		}
		log.Error(ctx, "Failed to delete play history", "history_id", historyID, "user_id", userID, "error", err)
		return fmt.Errorf("could not delete play history record: %w", err)
	}
	return nil
}
