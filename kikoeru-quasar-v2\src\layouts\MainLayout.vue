<template>
  <q-layout view="hHh Lpr lFf" class="bg-grey-3 kikoeru-layout">
    <q-header class="shadow-4" :class="{'bg-dark': $q.dark.isActive}">
      <q-toolbar class="row justify-between">
        <q-btn flat dense round @click="drawerOpen = !drawerOpen" icon="menu" aria-label="Menu" />

        <q-btn flat size="md" icon="arrow_back_ios" @click="back()" v-if="isNotAtHomePage"/>

        <q-toolbar-title class="gt-xs">
          <router-link :to="'/'" class="text-white text-decoration-none">
            Kikoeru
          </router-link>
        </q-toolbar-title>

        <div class="search-container q-mr-sm">
          <q-input
            ref="searchRef"
            dark
            dense
            rounded
            standout
            v-model="searchInput"
            :placeholder="searchInput || searchTerms.length > 0 ? '' : t('search.placeholder')"
            @keydown.enter="addSearchTerm"
            @keydown.space="addSearchTerm"
            :aria-expanded="showSearchMenu"
            @click.stop="showSearchMenu = true"
            class="search-input"
          >
            <template v-slot:prepend v-if="searchTerms.length > 0">
              <div class="search-chips-container">
                <q-chip
                  v-for="(term, index) in searchTerms"
                  :key="index"
                  removable
                  @remove="removeSearchTerm(index)"
                  @click.stop="editSearchTerm(term, index)"
                  dense
                  color="primary"
                  text-color="white"
                  class="search-chip"
                  clickable
                >
                  {{ term }}
                </q-chip>
              </div>
            </template>
            <template v-slot:append>
              <q-icon name="search" class="cursor-pointer" @click.stop="onSearchIconClick" />
            </template>
          </q-input>

          <!-- Initial search menu -->
          <q-menu
            v-model="showSearchMenu"
            fit
            anchor="bottom left"
            self="top left"
            :offset="[0, 8]"
            no-focus
          >
            <q-list style="min-width: 200px">
              <q-item clickable @click="openAdvancedSearch">
                <q-item-section avatar>
                  <q-icon name="search" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>{{ t('search.advancedSearch') }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </q-menu>
        </div>

      </q-toolbar>

      <AudioPlayer />
    </q-header>

    <q-drawer
      v-model="drawerOpen"
      :show-if-above="!$q.platform.is.mobile"
      :mini="miniState"
      @mouseover="miniState = false"
      @mouseout="miniState = true"
      mini-to-overlay
      class="drawer-transition"
      behavior="desktop"
      :width="230"
      :breakpoint="500"
      bordered
      content-class="bg-grey-1"
    >
      <div class="drawer-container">
        <q-scroll-area class="main-scroll">
          <q-list class="main-links">
            <q-item
              clickable
              v-ripple
              exact
              :to="link.path"
              active-class="text-deep-purple text-weight-medium"
              v-for="(link, index) in mainLinks"
              :key="index"
              @click="miniState = true"
            >
              <q-item-section avatar>
                <q-icon :name="link.icon" />
              </q-item-section>

              <q-item-section>
                <q-item-label class="text-subtitle1" lines="1">
                  {{link.title}}
                </q-item-label>
              </q-item-section>
            </q-item>

            <q-item
              clickable
              v-ripple
              exact
              active-class="text-deep-purple text-weight-medium"
              @click="randomPlay"
            >
              <q-item-section avatar>
                <q-icon name="shuffle" />
              </q-item-section>

              <q-item-section>
                <q-item-label class="text-subtitle1" lines="1">
                  {{ t('nav.randomListen') }}
                </q-item-label>
              </q-item-section>
            </q-item>

            <q-item
              clickable
              v-ripple
              exact
              active-class="text-deep-purple text-weight-medium"
              @click="showTimer = true"
            >
              <q-item-section avatar>
                <q-icon name="bedtime" />
              </q-item-section>

              <q-item-section>
                <q-item-label class="text-subtitle1" lines="1">
                  {{ t('nav.sleepMode') }}
                </q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </q-scroll-area>

        <q-separator />

        <q-list class="bottom-links">
          <q-item
            clickable
            v-ripple
            exact
            :to="'/settings'"
            active-class="text-deep-purple text-weight-medium"
            @click="miniState = true"
          >
            <q-item-section avatar>
              <q-icon name="settings" />
            </q-item-section>

            <q-item-section>
              <q-item-label class="text-subtitle1" lines="1">
                {{ t('settings') }}
              </q-item-label>
            </q-item-section>
          </q-item>

          <q-item
            clickable
            v-ripple
            exact
            active-class="text-deep-purple text-weight-medium"
            @click="miniState = true"
            v-if="isAdmin"
            :to="'/admin'"
          >
            <q-item-section avatar>
              <q-icon name="admin_panel_settings" />
            </q-item-section>

            <q-item-section>
              <q-item-label class="text-subtitle1" lines="1">
                {{ t('nav.adminPanel') }}
              </q-item-label>
            </q-item-section>
          </q-item>

          <q-item>
            <q-item-section avatar>
              <q-icon :name="darkModeIcon" />
            </q-item-section>

            <q-item-section>
              <q-item-label class="text-subtitle1" lines="1">
                {{ t('nav.darkMode') }}
              </q-item-label>
              <q-item-label caption lines="1">{{ darkModeCaption }}</q-item-label>
            </q-item-section>

            <q-item-section side>
              <q-toggle
                v-model="darkMode"
                color="primary"
                @update:model-value="toggleDarkMode"
                icon="dark_mode"
              />
            </q-item-section>
          </q-item>

          <q-item
            clickable
            v-ripple
            exact
            active-class="text-deep-purple text-weight-medium"
            @click="confirm = true"
            v-if="authEnabled"
          >
            <q-item-section avatar>
              <q-icon name="exit_to_app" />
            </q-item-section>

            <q-item-section>
              <q-item-label class="text-subtitle1" lines="1">
                {{ t('nav.logout') }}
              </q-item-label>
              <q-item-label caption lines="2">{{ userName }}</q-item-label>
            </q-item-section>
          </q-item>

          <q-item
            clickable
            v-ripple
            exact
            active-class="text-deep-purple text-weight-medium"
            @click="showLoginDialog = true"
            v-if="!authEnabled"
          >
            <q-item-section avatar>
              <q-icon name="login" />
            </q-item-section>

            <q-item-section>
              <q-item-label class="text-subtitle1" lines="1">
                {{ t('nav.login') }}
              </q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </div>
    </q-drawer>

    <q-dialog v-model="confirm" persistent>
      <q-card>
        <q-card-section class="row items-center">
          <q-avatar icon="power_settings_new" color="primary" text-color="white" />
          <span class="q-ml-sm">{{ t('dialog.logout') }}</span>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="t('dialog.cancel')" color="primary" v-close-popup />
          <q-btn flat :label="t('dialog.confirm')" color="primary" @click="logout()" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <SleepMode v-model="showTimer" />

    <LoginDialog
      v-model="showLoginDialog"
      @switch-to-register="switchToRegister"
    />

    <RegisterDialog
      v-model="showRegisterDialog"
      @switch-to-login="switchToLogin"
    />

    <q-page-container>
      <router-view v-slot="{ Component }">
        <keep-alive include="Works">
          <component :is="Component" />
        </keep-alive>
      </router-view>
      <q-page-scroller position="bottom-right" :scroll-offset="150" :offset="[18, 18]">
        <q-btn fab icon="keyboard_arrow_up" color="accent" />
      </q-page-scroller>
    </q-page-container>

    <q-footer class="q-pa-none">
      <LyricsBar />
      <PlayerBar />
    </q-footer>

    <!-- Add dialogs for tag/circle/va search -->
    <q-dialog v-model="showTagSearchDialog" persistent>
      <q-card style="min-width: 350px">
        <q-card-section>
          <div class="text-h6">{{ t('search.namespace.tag') }}</div>
        </q-card-section>

        <q-card-section>
          <q-select
            v-model="selectedTag"
            :options="tagOptions"
            @filter="filterTags"
            use-input
            hide-selected
            fill-input
            input-debounce="300"
            :label="t('search.selectOrEnter', { type: t('search.namespace.tag').toLowerCase() })"
          />
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="t('search.cancel')" color="primary" v-close-popup @click="cancelTagSearch" />
          <q-btn flat :label="t('search.apply')" color="primary" @click="applyTagSearch" v-close-popup />
          <q-btn flat :label="t('search.exclude')" color="negative" @click="applyTagExclusion" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <q-dialog v-model="showCircleSearchDialog" persistent>
      <q-card style="min-width: 350px">
        <q-card-section>
          <div class="text-h6">{{ t('search.namespace.circle') }}</div>
        </q-card-section>

        <q-card-section>
          <q-select
            v-model="selectedCircle"
            :options="circleOptions"
            @filter="filterCircles"
            use-input
            hide-selected
            fill-input
            input-debounce="300"
            :label="t('search.selectOrEnter', { type: t('search.namespace.circle').toLowerCase() })"
          />
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="t('search.cancel')" color="primary" v-close-popup @click="cancelCircleSearch" />
          <q-btn flat :label="t('search.apply')" color="primary" @click="applyCircleSearch" v-close-popup />
          <q-btn flat :label="t('search.exclude')" color="negative" @click="applyCircleExclusion" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <q-dialog v-model="showVaSearchDialog" persistent>
      <q-card style="min-width: 350px">
        <q-card-section>
          <div class="text-h6">{{ t('search.namespace.va') }}</div>
        </q-card-section>

        <q-card-section>
          <q-select
            v-model="selectedVa"
            :options="vaOptions"
            @filter="filterVas"
            use-input
            hide-selected
            fill-input
            input-debounce="300"
            :label="t('search.selectOrEnter', { type: t('search.namespace.va').toLowerCase() })"
          />
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="t('search.cancel')" color="primary" v-close-popup @click="cancelVaSearch" />
          <q-btn flat :label="t('search.apply')" color="primary" @click="applyVaSearch" v-close-popup />
          <q-btn flat :label="t('search.exclude')" color="negative" @click="applyVaExclusion" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <q-dialog v-model="showRateSearchDialog" persistent>
      <q-card style="min-width: 350px">
        <q-card-section>
          <div class="text-h6">{{ t('search.namespace.rate') }}</div>
        </q-card-section>

        <q-card-section>
          <q-slider
            v-model="selectedRate"
            :min="0"
            :max="5"
            :step="0.1"
            label
            label-always
            color="primary"
          />
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="t('search.cancel')" color="primary" v-close-popup @click="cancelRateSearch" />
          <q-btn flat :label="t('search.apply')" color="primary" @click="applyRateSearch" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <q-dialog v-model="showPriceSearchDialog" persistent>
      <q-card style="min-width: 350px">
        <q-card-section>
          <div class="text-h6">{{ t('search.namespace.price') }}</div>
        </q-card-section>

        <q-card-section>
          <q-input
            v-model.number="selectedPrice"
            type="number"
            :label="t('search.namespace.price')"
            :suffix="t('work.currency')"
          />
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="t('search.cancel')" color="primary" v-close-popup @click="cancelPriceSearch" />
          <q-btn flat :label="t('search.apply')" color="primary" @click="applyPriceSearch" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <q-dialog v-model="showDurationSearchDialog" persistent>
      <q-card style="min-width: 350px">
        <q-card-section>
          <div class="text-h6">{{ t('search.namespace.duration') }}</div>
        </q-card-section>

        <q-card-section>
          <q-input
            v-model.number="selectedDuration"
            type="number"
            :label="t('search.namespace.duration')"
            suffix="min"
          />
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="t('search.cancel')" color="primary" v-close-popup @click="cancelDurationSearch" />
          <q-btn flat :label="t('search.apply')" color="primary" @click="applyDurationSearch" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- Add a separate dialog for advanced search options -->
    <q-dialog
      v-model="showAdvancedSearch"
      @show="onAdvancedSearchShow"
    >
      <q-card class="advanced-search-dialog">
        <q-card-section>
          <div class="text-h6">{{ t('search.advancedSearch') }}</div>
        </q-card-section>

        <q-card-section class="q-pa-none">
          <q-list separator>
            <!-- Age category options -->
            <q-item-label header class="q-pa-md">{{ t('search.ageCategory') }}</q-item-label>
            <q-item
              v-for="(caption, value) in advancedSearchOptions.optionsCaptionByValue"
              :key="value"
              clickable
              @click="applyAdvancedSearchOption(value)"
            >
              <q-item-section>
                <q-item-label lines="1">{{ caption }}</q-item-label>
              </q-item-section>
            </q-item>

            <!-- Advanced search by category -->
            <q-item-label header class="q-pa-md">{{ t('search.advancedFilters') }}</q-item-label>
            <q-item clickable @click="openTagSearch">
              <q-item-section avatar>
                <q-icon name="label" />
              </q-item-section>
              <q-item-section>
                <q-item-label lines="1">{{ t('search.namespace.tag') }}</q-item-label>
              </q-item-section>
            </q-item>

            <q-item clickable @click="openCircleSearch">
              <q-item-section avatar>
                <q-icon name="group" />
              </q-item-section>
              <q-item-section>
                <q-item-label lines="1">{{ t('search.namespace.circle') }}</q-item-label>
              </q-item-section>
            </q-item>

            <q-item clickable @click="openVaSearch">
              <q-item-section avatar>
                <q-icon name="record_voice_over" />
              </q-item-section>
              <q-item-section>
                <q-item-label lines="1">{{ t('search.namespace.va') }}</q-item-label>
              </q-item-section>
            </q-item>

            <q-item clickable @click="openRateSearch">
              <q-item-section avatar>
                <q-icon name="star" />
              </q-item-section>
              <q-item-section>
                <q-item-label lines="1">{{ t('search.namespace.rate') }}</q-item-label>
              </q-item-section>
            </q-item>

            <q-item clickable @click="openPriceSearch">
              <q-item-section avatar>
                <q-icon name="attach_money" />
              </q-item-section>
              <q-item-section>
                <q-item-label lines="1">{{ t('search.namespace.price') }}</q-item-label>
              </q-item-section>
            </q-item>

            <q-item clickable @click="openDurationSearch">
              <q-item-section avatar>
                <q-icon name="schedule" />
              </q-item-section>
              <q-item-section>
                <q-item-label lines="1">{{ t('search.namespace.duration') }}</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="t('search.close')" color="primary" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-layout>
</template>

<script setup>
import { ref, computed, watch, onMounted, getCurrentInstance } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import { useQuasar } from 'quasar'
import AudioPlayer from '../components/AudioPlayer.vue'
import LyricsBar from '../components/LyricsBar.vue'
import PlayerBar from '../components/PlayerBar.vue'
import SleepMode from '../components/SleepMode.vue'
import LoginDialog from '../components/LoginDialog.vue'
import RegisterDialog from '../components/RegisterDialog.vue'
import { useAuth } from '../composables/useAuth'
import { useNotification } from '../composables/useNotification'

// Composables
const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const $q = useQuasar()
const { proxy } = getCurrentInstance()
const { user, isLoggedIn, isAdmin, logout: authLogout } = useAuth()
const { showSuccessNotification, showErrorNotification } = useNotification()

const drawerOpen = ref(false)
const miniState = ref(true)
const confirm = ref(false)
const showTimer = ref(false)
const showLoginDialog = ref(false)
const showRegisterDialog = ref(false)
const darkMode = ref(false)
const searchInput = ref('')
const searchTerms = ref([])
const showSearchMenu = ref(false)
const showAdvancedSearch = ref(false)
const showTagSearchDialog = ref(false)
const showCircleSearchDialog = ref(false)
const showVaSearchDialog = ref(false)
const showRateSearchDialog = ref(false)
const showPriceSearchDialog = ref(false)
const showDurationSearchDialog = ref(false)
const selectedTag = ref(null)
const selectedCircle = ref(null)
const selectedVa = ref(null)
const selectedRate = ref(0)
const selectedPrice = ref(0)
const selectedDuration = ref(0)
const tagOptions = ref([])
const circleOptions = ref([])
const vaOptions = ref([])
const allTags = ref([])
const allCircles = ref([])
const allVas = ref([])

// Computed properties
const authEnabled = computed(() => isLoggedIn.value)

const userName = computed(() => user.value?.username || '')

const isNotAtHomePage = computed(() => route.path !== '/')

const darkModeIcon = computed(() => darkMode.value ? 'dark_mode' : 'light_mode')

const darkModeCaption = computed(() => darkMode.value ? t('nav.darkMode') : t('nav.lightMode'))

const mainLinks = computed(() => {
  const links = [
    {
      title: t('nav.mediaLibrary'),
      icon: 'library_music',
      path: '/works'
    },
    {
      title: t('nav.circles'),
      icon: 'group',
      path: '/circles'
    },
    {
      title: t('nav.tags'),
      icon: 'label',
      path: '/tags'
    },
    {
      title: t('nav.voiceActors'),
      icon: 'record_voice_over',
      path: '/vas'
    }
  ]

  if (authEnabled.value) {
    links.push(
      {
        title: t('nav.myReviews'),
        icon: 'rate_review',
        path: '/reviews'
      },
      {
        title: t('nav.history'),
        icon: 'history',
        path: '/history'
      },
      {
        title: t('nav.playlists'),
        icon: 'playlist_play',
        path: '/playlists'
      }
    )
  }

  return links
})

const advancedSearchOptions = computed(() => ({
  optionsCaptionByValue: {
    'general': t('search.ageOptions.onlyAllAges'),
    'r15': t('search.ageOptions.onlyR15'),
    'adult': t('search.ageOptions.onlyAdult'),
    'general+r15': t('search.ageOptions.allAgesAndR15')
  }
}))

// Methods
const back = () => {
  router.go(-1)
}

const loadSearchData = async () => {
  try {
    const [tagsResponse, circlesResponse, vasResponse] = await Promise.all([
      proxy.$api.get('/api/v1/tags'),
      proxy.$api.get('/api/v1/circles'),
      proxy.$api.get('/api/v1/vas')
    ])

    allTags.value = tagsResponse.data || []
    allCircles.value = circlesResponse.data || []
    allVas.value = vasResponse.data || []
  } catch (error) {
    console.error('Failed to load search data:', error)
  }
}

const toggleDarkMode = (val) => {
  $q.dark.set(val)
  try {
    $q.localStorage.set('darkMode', val)
  } catch {
    console.log('Web Storage API error')
  }
}

const logout = () => {
  authLogout()
  showSuccessNotification(t('notification.loggedOut'))
  if (route.meta?.requiresAuth) {
    router.push('/')
  }
}

const switchToRegister = () => {
  showLoginDialog.value = false
  showRegisterDialog.value = true
}

const switchToLogin = () => {
  showRegisterDialog.value = false
  showLoginDialog.value = true
}

const randomPlay = async () => {
  try {
    const response = await proxy.$api.get('/api/v1/works', {
      params: {
        page: 1,
        page_size: 1,
        order: 'random'
      }
    })

    const works = response.data.items || []
    if (works.length > 0) {
      const work = works[0]
      router.push(`/work/${work.original_id}`)
    } else {
      showErrorNotification(t('notification.noWorksFound'))
    }
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('notification.randomPlayError'))
  }
}

// Essential search methods (simplified for now)
const addSearchTerm = () => {
  if (searchInput.value.trim()) {
    searchTerms.value.push(searchInput.value.trim())
    searchInput.value = ''
    showSearchMenu.value = false
  }
}

const removeSearchTerm = (index) => {
  searchTerms.value.splice(index, 1)
}

const performSearch = () => {
  const allTerms = [...searchTerms.value]
  if (searchInput.value.trim()) {
    allTerms.push(searchInput.value.trim())
  }

  if (allTerms.length > 0) {
    const keyword = allTerms.join(' ')
    router.push({
      name: 'works',
      query: { keyword }
    })
    searchInput.value = ''
    searchTerms.value = []
    showSearchMenu.value = false
  }
}

const onSearchIconClick = () => {
  if (searchInput.value.trim() || searchTerms.value.length > 0) {
    performSearch()
  } else {
    showSearchMenu.value = !showSearchMenu.value
  }
}

// Advanced search functions
const openAdvancedSearch = () => {
  showAdvancedSearch.value = true
  showSearchMenu.value = false
}

const onAdvancedSearchShow = () => {
  // This function is called when the advanced search dialog is shown
  // Can be used for initialization if needed
}

const openTagSearch = () => {
  showAdvancedSearch.value = false
  showTagSearchDialog.value = true
}

const openCircleSearch = () => {
  showAdvancedSearch.value = false
  showCircleSearchDialog.value = true
}

const openVaSearch = () => {
  showAdvancedSearch.value = false
  showVaSearchDialog.value = true
}

const openRateSearch = () => {
  showAdvancedSearch.value = false
  showRateSearchDialog.value = true
}

const openPriceSearch = () => {
  showAdvancedSearch.value = false
  showPriceSearchDialog.value = true
}

const openDurationSearch = () => {
  showAdvancedSearch.value = false
  showDurationSearchDialog.value = true
}

const applyAdvancedSearchOption = (value) => {
  showAdvancedSearch.value = false
  router.push({
    name: 'works',
    query: { keyword: `$age:${value}$` }
  })
}

// Filter functions for search dialogs
const filterTags = (val, update) => {
  update(() => {
    if (val === '') {
      tagOptions.value = allTags.value.slice(0, 50)
    } else {
      const needle = val.toLowerCase()
      tagOptions.value = allTags.value.filter(tag =>
        tag.name.toLowerCase().includes(needle)
      ).slice(0, 50)
    }
  })
}

const filterCircles = (val, update) => {
  update(() => {
    if (val === '') {
      circleOptions.value = allCircles.value.slice(0, 50)
    } else {
      const needle = val.toLowerCase()
      circleOptions.value = allCircles.value.filter(circle =>
        circle.name.toLowerCase().includes(needle)
      ).slice(0, 50)
    }
  })
}

const filterVas = (val, update) => {
  update(() => {
    if (val === '') {
      vaOptions.value = allVas.value.slice(0, 50)
    } else {
      const needle = val.toLowerCase()
      vaOptions.value = allVas.value.filter(va =>
        va.name.toLowerCase().includes(needle)
      ).slice(0, 50)
    }
  })
}

// Apply search functions
const applyTagSearch = () => {
  if (selectedTag.value) {
    const tagName = typeof selectedTag.value === 'string' ? selectedTag.value : selectedTag.value.name
    router.push({
      name: 'works',
      query: { keyword: `$tag:${tagName}$` }
    })
  }
  cancelTagSearch()
}

const applyTagExclusion = () => {
  if (selectedTag.value) {
    const tagName = typeof selectedTag.value === 'string' ? selectedTag.value : selectedTag.value.name
    router.push({
      name: 'works',
      query: { keyword: `$-tag:${tagName}$` }
    })
  }
  cancelTagSearch()
}

const applyCircleSearch = () => {
  if (selectedCircle.value) {
    const circleName = typeof selectedCircle.value === 'string' ? selectedCircle.value : selectedCircle.value.name
    router.push({
      name: 'works',
      query: { keyword: `$circle:${circleName}$` }
    })
  }
  cancelCircleSearch()
}

const applyCircleExclusion = () => {
  if (selectedCircle.value) {
    const circleName = typeof selectedCircle.value === 'string' ? selectedCircle.value : selectedCircle.value.name
    router.push({
      name: 'works',
      query: { keyword: `$-circle:${circleName}$` }
    })
  }
  cancelCircleSearch()
}

const applyVaSearch = () => {
  if (selectedVa.value) {
    const vaName = typeof selectedVa.value === 'string' ? selectedVa.value : selectedVa.value.name
    router.push({
      name: 'works',
      query: { keyword: `$va:${vaName}$` }
    })
  }
  cancelVaSearch()
}

const applyVaExclusion = () => {
  if (selectedVa.value) {
    const vaName = typeof selectedVa.value === 'string' ? selectedVa.value : selectedVa.value.name
    router.push({
      name: 'works',
      query: { keyword: `$-va:${vaName}$` }
    })
  }
  cancelVaSearch()
}

const applyRateSearch = () => {
  if (selectedRate.value > 0) {
    router.push({
      name: 'works',
      query: { keyword: `$rate:${selectedRate.value}$` }
    })
  }
  cancelRateSearch()
}

const applyPriceSearch = () => {
  if (selectedPrice.value > 0) {
    router.push({
      name: 'works',
      query: { keyword: `$price:${selectedPrice.value}$` }
    })
  }
  cancelPriceSearch()
}

const applyDurationSearch = () => {
  if (selectedDuration.value > 0) {
    router.push({
      name: 'works',
      query: { keyword: `$duration:${selectedDuration.value}$` }
    })
  }
  cancelDurationSearch()
}

// Cancel search functions
const cancelTagSearch = () => {
  selectedTag.value = null
  showTagSearchDialog.value = false
}

const cancelCircleSearch = () => {
  selectedCircle.value = null
  showCircleSearchDialog.value = false
}

const cancelVaSearch = () => {
  selectedVa.value = null
  showVaSearchDialog.value = false
}

const cancelRateSearch = () => {
  selectedRate.value = 0
  showRateSearchDialog.value = false
}

const cancelPriceSearch = () => {
  selectedPrice.value = 0
  showPriceSearchDialog.value = false
}

const cancelDurationSearch = () => {
  selectedDuration.value = 0
  showDurationSearchDialog.value = false
}

// Watchers
watch(() => $q.dark.isActive, (val) => {
  darkMode.value = val
})

// Lifecycle
onMounted(() => {
  darkMode.value = $q.dark.isActive
  loadSearchData()
})
</script>
