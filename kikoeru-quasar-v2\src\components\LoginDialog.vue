<template>
  <q-dialog v-model="showDialog" persistent>
    <q-card style="width: 400px; max-width: 90vw;">
      <q-card-section class="text-center">
        <div class="text-h5 q-mb-md">{{ $t('auth.login') }}</div>

        <q-form @submit="onSubmit" class="q-gutter-md">
          <q-input
            filled
            v-model="identifier"
            :label="$t('auth.usernameOrEmail')"
            lazy-rules
            :rules="[val => !!val && val.length >= 1 || $t('validation.required')]"
          />

          <q-input
            filled
            type="password"
            v-model="password"
            :label="$t('auth.password')"
            lazy-rules
            :rules="[val => !!val && val.length >= 1 || $t('validation.required')]"
          />

          <div class="text-center">
            <q-btn
              :label="$t('auth.login')"
              type="submit"
              color="primary"
              class="full-width q-mb-none"
              :loading="loading"
            />
          </div>
        </q-form>
      </q-card-section>

      <q-card-section
        class="text-center q-pt-sm q-pb-none"
        v-if="$siteConfig && $siteConfig.enable_email_features"
      >
        <q-btn
          flat
          color="grey"
          :label="$t('auth.forgotPassword')"
          @click="showPasswordReset = true"
        />
      </q-card-section>

      <q-card-actions
        class="q-px-md q-pb-md"
        :class="{'q-pt-md': !($siteConfig && $siteConfig.enable_email_features)}"
      >
        <q-btn
          flat
          color="primary"
          :label="$t('auth.createAccount')"
          @click="goToRegister"
        />
        <q-space />
        <q-btn flat :label="$t('dialog.cancel')" color="primary" v-close-popup />
      </q-card-actions>
    </q-card>

    <q-dialog v-model="showPasswordReset">
      <q-card style="min-width: 350px">
        <q-card-section>
          <div class="text-h6">{{ $t('auth.resetPassword') }}</div>
        </q-card-section>

        <q-card-section class="q-pt-none">
          <q-input
            filled
            v-model="resetEmail"
            :label="$t('auth.email')"
            type="email"
            lazy-rules
          />
        </q-card-section>

        <q-card-actions align="right" class="text-primary">
          <q-btn flat :label="$t('dialog.cancel')" v-close-popup />
          <q-btn
            flat
            :label="$t('auth.sendResetEmail')"
            @click="requestPasswordReset"
            :loading="resetLoading"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-dialog>
</template>

<script setup>
import { ref, computed, nextTick, getCurrentInstance } from 'vue'
import { useI18n } from 'vue-i18n'
import { useAuth } from '../composables/useAuth'
import { useNotification } from '../composables/useNotification'

defineOptions({
  name: 'LoginDialog'
})

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'switch-to-register'])

const { t } = useI18n()
const { proxy } = getCurrentInstance()
const { login, fetchCurrentUser, isAdmin, hideLoginDialog } = useAuth()
const { showSuccessNotification, showWarningNotification, showErrorNotification } = useNotification()

// Reactive data
const identifier = ref('')
const password = ref('')
const loading = ref(false)
const showPasswordReset = ref(false)
const resetEmail = ref('')
const resetLoading = ref(false)

// Computed properties
const showDialog = computed({
  get() {
    return props.modelValue
  },
  set(val) {
    emit('update:modelValue', val)
  }
})

// Methods
const onSubmit = async () => {
  loading.value = true
  try {
    await login({
      identifier: identifier.value,
      password: password.value
    })

    await fetchCurrentUser()

    showSuccessNotification(t('notification.loginSuccess'))
    hideLoginDialog()
    showDialog.value = false

    identifier.value = ''
    password.value = ''

    await nextTick()
    if (isAdmin.value) {
      showSuccessNotification(t('auth.adminLogin'))
    }
  } catch (error) {
    if (error.response) {
      if (error.response.status === 401) {
        showWarningNotification(error.response.data.error || t('notification.loginFailed'))
      } else {
        showErrorNotification(error.response.data.error || `${error.response.status} ${error.response.statusText}`)
      }
    } else {
      showErrorNotification(error.message || t('notification.networkError'))
    }
  } finally {
    loading.value = false
  }
}

const goToRegister = () => {
  showDialog.value = false
  emit('switch-to-register')
}

const requestPasswordReset = async () => {
  if (!resetEmail.value) {
    showWarningNotification(t('validation.required'))
    return
  }

  resetLoading.value = true
  try {
    await proxy.$api.post('/api/v1/auth/email/request-password-reset', {
      email: resetEmail.value
    })

    showSuccessNotification(t('notification.emailSent'))
    showPasswordReset.value = false
    resetEmail.value = ''
  } catch (error) {
    if (error.response) {
      showErrorNotification(error.response.data.error || t('failed'))
    } else {
      showErrorNotification(t('notification.networkError'))
    }
  } finally {
    resetLoading.value = false
  }
}


</script>

<style>
.q-dialog__inner--minimized > div {
  max-width: 90vw;
  margin: 0 auto;
}
</style>
