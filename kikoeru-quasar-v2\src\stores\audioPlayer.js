import { defineStore } from 'pinia'

export const useAudioPlayerStore = defineStore('audioPlayer', {
  state: () => ({
    hide: false,
    playing: false, // 播放状态 (true/false)
    currentTime: 0, // 单位: 秒
    duration: 0,
    source: "",
    queue: [
      // list of tracks. object format:
      /*
        workId: null, // work database ID (integer)
        originalId: '', // work original ID (string, e.g. RJ number)
        trackPath: '', // track path within work (string)
        title: null, // title to show in UI
        workTitle: null, // work title to show in UI
        duration: null, // track duration in seconds
        isInArchive: false, // whether track is in archive
        archivePath: '' // archive path if in archive
       */
    ],
    queueIndex: 0, // which track in the queue is currently selected
    playMode: {
      id: 0,
      name: "order"
    }, // 顺序播放("order"), 循环播放("all repeat"), 单曲循环("repeat once") or 随机播放("shuffle")
    muted: false,
    volume: 0.8, // 音量 (0.0-1.0)
    currentLyric: '',
    sleepTime: null,
    sleepMode: false,
    rewindSeekTime: 5,
    forwardSeekTime: 30,
    rewindSeekMode: false,
    forwardSeekMode: false,
    seekToTime: null, // For lyric dialog seek functionality
    pendingSeekTime: null, // For seeking when audio becomes ready
    // Lyric information
    availableLyrics: [],
    currentLyricFile: null,
    lyricContent: '',
    // PIP Lyrics
    enablePIPLyrics: false
  }),

  getters: {
    currentPlayingFile: (state) => {
      return state.queue[state.queueIndex] || {
        workId: null,
        originalId: '',
        trackPath: '',
        title: '',
        workTitle: '',
        duration: null,
        isInArchive: false,
        archivePath: ''
      }
    },

    isQueueEmpty: (state) => {
      return state.queue.length === 0
    }
  },

  actions: {
    toggleHide() {
      this.hide = !this.hide
    },

    play() {
      this.playing = true
    },

    pause() {
      this.playing = false
    },

    togglePlaying() {
      this.playing = !this.playing
    },

    // Play a specific file from the queue.
    setTrack(index) {
      if (index >= this.queue.length || index < 0) {
        return // Invalid index, bail.
      }

      this.queueIndex = index
      this.source = this.queue[index]
    },

    setQueue({ queue, index, resetPlaying = true }) {
      this.queue = queue
      this.queueIndex = index || 0

      if (resetPlaying) {
        this.playing = false
      }

      if (this.queue.length) {
        this.source = this.queue[this.queueIndex]
      } else {
        this.source = ""
      }
    },

    addToQueue(track) {
      this.queue.push(track)
    },

    removeFromQueue(index) {
      if (index < 0 || index >= this.queue.length) {
        return
      }

      this.queue.splice(index, 1)

      // Adjust queueIndex if necessary
      if (index < this.queueIndex) {
        this.queueIndex--
      } else if (index === this.queueIndex) {
        // If we removed the current track, adjust to stay in bounds
        if (this.queueIndex >= this.queue.length) {
          this.queueIndex = Math.max(0, this.queue.length - 1)
        }
        // Update source to the new current track
        if (this.queue.length > 0) {
          this.source = this.queue[this.queueIndex]
        } else {
          this.source = ""
        }
      }
    },

    emptyQueue() {
      this.queue = []
      this.queueIndex = 0
      this.source = ""
      this.playing = false
    },

    nextTrack() {
      if (this.queue.length === 0) return

      switch (this.playMode.name) {
        case 'shuffle':
          this.queueIndex = Math.floor(Math.random() * this.queue.length)
          break
        case 'repeat once':
          // Don't change track in repeat once mode
          break
        case 'all repeat':
          this.queueIndex = (this.queueIndex + 1) % this.queue.length
          break
        default: // 'order'
          if (this.queueIndex < this.queue.length - 1) {
            this.queueIndex++
          } else {
            this.playing = false
            return
          }
      }

      this.source = this.queue[this.queueIndex]
    },

    previousTrack() {
      if (this.queue.length === 0) return

      switch (this.playMode.name) {
        case 'shuffle':
          this.queueIndex = Math.floor(Math.random() * this.queue.length)
          break
        case 'repeat once':
          // Don't change track in repeat once mode
          break
        case 'all repeat':
          this.queueIndex = this.queueIndex === 0 ? this.queue.length - 1 : this.queueIndex - 1
          break
        default: // 'order'
          if (this.queueIndex > 0) {
            this.queueIndex--
          } else {
            return
          }
      }

      this.source = this.queue[this.queueIndex]
    },

    changePlayMode() {
      const modes = [
        { id: 0, name: "order" },
        { id: 1, name: "all repeat" },
        { id: 2, name: "repeat once" },
        { id: 3, name: "shuffle" }
      ]

      const currentIndex = modes.findIndex(mode => mode.id === this.playMode.id)
      const nextIndex = (currentIndex + 1) % modes.length
      this.playMode = modes[nextIndex]
    },

    setVolume(volume) {
      this.volume = Math.max(0, Math.min(1, volume))
    },

    setMuted(muted) {
      this.muted = muted
    },

    setCurrentTime(time) {
      this.currentTime = time
    },

    setDuration(duration) {
      this.duration = duration
    },

    setRewindSeekTime(time) {
      this.rewindSeekTime = time
    },

    setForwardSeekTime(time) {
      this.forwardSeekTime = time
    },

    setRewindSeekMode(mode) {
      this.rewindSeekMode = mode
    },

    setForwardSeekMode(mode) {
      this.forwardSeekMode = mode
    },

    setSleepTime(time) {
      this.sleepTime = time
    },

    setSleepMode(mode) {
      this.sleepMode = mode
    },

    setSeekToTime(time) {
      this.seekToTime = time
    },

    setPendingSeekTime(time) {
      this.pendingSeekTime = time
    },

    setAvailableLyrics(lyrics) {
      this.availableLyrics = lyrics
    },

    setCurrentLyricFile(file) {
      this.currentLyricFile = file
    },

    setLyricContent(content) {
      this.lyricContent = content
    },

    setCurrentLyric(lyric) {
      this.currentLyric = lyric
    },

    setEnablePIPLyrics(enable) {
      this.enablePIPLyrics = enable
    }
  }
})
