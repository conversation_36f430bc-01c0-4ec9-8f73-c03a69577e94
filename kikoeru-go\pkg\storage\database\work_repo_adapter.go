package database

import (
	"context"

	"github.com/Sakura-Byte/kikoeru-go/pkg/domain/model"
	"github.com/Sakura-Byte/kikoeru-go/pkg/domain/repository"
	common_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/common"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
)

// workRepositoryAdapter adapts the database WorkRepository to the domain repository.WorkRepository interface
type workRepositoryAdapter struct {
	repo WorkRepository
}

// NewWorkRepositoryAdapter creates a new adapter that implements repository.WorkRepository
func NewWorkRepositoryAdapter(repo WorkRepository) repository.WorkRepository {
	return &workRepositoryAdapter{repo: repo}
}

// convertToModelWork converts models.Work to model.Work
func convertToModelWork(work *models.Work) *model.Work {
	if work == nil {
		return nil
	}

	var originalID, circleID string
	if work.OriginalID != nil {
		originalID = *work.OriginalID
	}
	if work.CircleID != nil {
		circleID = *work.CircleID
	}

	modelWork := &model.Work{
		ID:              work.ID,
		Title:           work.Title,
		OriginalID:      originalID,
		CircleID:        circleID,
		StorageID:       work.StorageID,
		WorkType:        work.WorkType,
		AgeRating:       work.AgeRating,
		NSFW:            work.NSFW,
		ReleaseDate:     work.ReleaseDate,
		RateCount:       work.RateCount,
		RateAverage2DP:  work.RateAverage2DP,
		LyricStatus:     work.LyricStatus,
		Language:        work.Language,
		Duration:        work.Duration,
		DlCount:         work.DlCount,
		Price:           work.Price,
		ReviewCount:     work.ReviewCount,
		RateCountDetail: work.RateCountDetail,
		PathInStorage:   work.PathInStorage,
		Circle:          work.Circle,
		Tags:            work.Tags,
		VAs:             work.VAs,
		CreatedAt:       work.CreatedAt,
		UpdatedAt:       work.UpdatedAt,
	}

	// Convert RankList items individually
	if len(work.Rank) > 0 {
		rankItems := make([]model.RankItem, len(work.Rank))
		for i, item := range work.Rank {
			rankItems[i] = model.RankItem{
				Term:     item.Term,
				Category: item.Category,
				Rank:     item.Rank,
				RankDate: item.RankDate,
			}
		}
		modelWork.Rank = rankItems
	}

	return modelWork
}

// convertFromModelWork converts model.Work to models.Work
func convertFromModelWork(work *model.Work) *models.Work {
	if work == nil {
		return nil
	}

	originalID := &work.OriginalID
	circleID := &work.CircleID

	// Handle empty strings
	if work.OriginalID == "" {
		originalID = nil
	}
	if work.CircleID == "" {
		circleID = nil
	}

	modelWork := &models.Work{
		ID:              work.ID,
		Title:           work.Title,
		OriginalID:      originalID,
		CircleID:        circleID,
		StorageID:       work.StorageID,
		WorkType:        work.WorkType,
		AgeRating:       work.AgeRating,
		NSFW:            work.NSFW,
		ReleaseDate:     work.ReleaseDate,
		RateCount:       work.RateCount,
		RateAverage2DP:  work.RateAverage2DP,
		LyricStatus:     work.LyricStatus,
		Language:        work.Language,
		Duration:        work.Duration,
		DlCount:         work.DlCount,
		Price:           work.Price,
		ReviewCount:     work.ReviewCount,
		RateCountDetail: work.RateCountDetail,
		PathInStorage:   work.PathInStorage,
		Circle:          work.Circle,
		Tags:            work.Tags,
		VAs:             work.VAs,
		CreatedAt:       work.CreatedAt,
		UpdatedAt:       work.UpdatedAt,
	}

	// Convert RankList items individually
	if len(work.Rank) > 0 {
		rankItems := make([]models.RankItem, len(work.Rank))
		for i, item := range work.Rank {
			rankItems[i] = models.RankItem{
				Term:     item.Term,
				Category: item.Category,
				Rank:     item.Rank,
				RankDate: item.RankDate,
			}
		}
		modelWork.Rank = rankItems
	}

	return modelWork
}

// Create implements repository.WorkRepository
func (a *workRepositoryAdapter) Create(ctx context.Context, work *model.Work) error {
	return a.repo.Create(ctx, convertFromModelWork(work))
}

// GetByID implements repository.WorkRepository
func (a *workRepositoryAdapter) GetByID(ctx context.Context, id uint) (*model.Work, error) {
	work, err := a.repo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}
	return convertToModelWork(work), nil
}

// Update implements repository.WorkRepository
func (a *workRepositoryAdapter) Update(ctx context.Context, work *model.Work) error {
	return a.repo.Update(ctx, convertFromModelWork(work))
}

// Delete implements repository.WorkRepository
func (a *workRepositoryAdapter) Delete(ctx context.Context, id uint) error {
	return a.repo.Delete(ctx, id)
}

// FindWorkByOriginalID implements repository.WorkRepository
func (a *workRepositoryAdapter) FindWorkByOriginalID(ctx context.Context, originalID string) (*model.Work, error) {
	work, err := a.repo.GetByOriginalID(ctx, originalID)
	if err != nil {
		return nil, err
	}
	return convertToModelWork(work), nil
}

// ListWorks implements repository.WorkRepository
func (a *workRepositoryAdapter) ListWorks(ctx context.Context, page, pageSize int, sortBy, sortOrder string, filters map[string]interface{}) ([]model.Work, int, error) {
	params := ListWorksParams{
		PaginationParams: common_dto.PaginationParams{
			Page:      page,
			PageSize:  pageSize,
			SortBy:    sortBy,
			SortOrder: sortOrder,
		},
	}
	// Convert filters to appropriate fields in ListWorksParams
	if filters != nil {
		if title, ok := filters["title"].(string); ok {
			params.Title = title
		}
		if originalID, ok := filters["original_id"].(string); ok {
			params.OriginalID = originalID
		}
		// Add other filter conversions as needed
	}

	works, total, err := a.repo.List(ctx, params)
	if err != nil {
		return nil, 0, err
	}

	result := make([]model.Work, len(works))
	for i, work := range works {
		if converted := convertToModelWork(work); converted != nil {
			result[i] = *converted
		}
	}
	return result, int(total), nil
}

// SearchWorks implements repository.WorkRepository
func (a *workRepositoryAdapter) SearchWorks(ctx context.Context, query string, page, pageSize int) ([]model.Work, int, error) {
	// Use ListWorks with a title filter for basic search functionality
	return a.ListWorks(ctx, page, pageSize, "title", "asc", map[string]interface{}{
		"title": query,
	})
}

// GetRandomWorks implements repository.WorkRepository
func (a *workRepositoryAdapter) GetRandomWorks(ctx context.Context, limit int, filters map[string]interface{}) ([]model.Work, error) {
	// Convert filters to appropriate parameters
	var workType *string
	var minRating *float64
	if filters != nil {
		if wt, ok := filters["work_type"].(string); ok {
			workType = &wt
		}
		if mr, ok := filters["min_rating"].(float64); ok {
			minRating = &mr
		}
	}

	works, err := a.repo.GetRandomWorks(ctx, limit, nil, nil, nil, nil, nil, nil, workType, minRating)
	if err != nil {
		return nil, err
	}

	result := make([]model.Work, len(works))
	for i, work := range works {
		if converted := convertToModelWork(work); converted != nil {
			result[i] = *converted
		}
	}
	return result, nil
}

// AddTagToWork implements repository.WorkRepository
func (a *workRepositoryAdapter) AddTagToWork(ctx context.Context, workID uint, tagID string) error {
	return a.repo.AddTagToWork(ctx, workID, tagID)
}

// RemoveTagFromWork implements repository.WorkRepository
func (a *workRepositoryAdapter) RemoveTagFromWork(ctx context.Context, workID uint, tagID string) error {
	return a.repo.RemoveTagFromWork(ctx, workID, tagID)
}

// ReplaceWorkTags implements repository.WorkRepository
func (a *workRepositoryAdapter) ReplaceWorkTags(ctx context.Context, workID uint, tagIDs []string) error {
	return a.repo.ReplaceWorkTags(ctx, workID, tagIDs)
}

// AddVAToWork implements repository.WorkRepository
func (a *workRepositoryAdapter) AddVAToWork(ctx context.Context, workID uint, vaID string) error {
	return a.repo.AddVAToWork(ctx, workID, vaID)
}

// RemoveVAFromWork implements repository.WorkRepository
func (a *workRepositoryAdapter) RemoveVAFromWork(ctx context.Context, workID uint, vaID string) error {
	return a.repo.RemoveVAFromWork(ctx, workID, vaID)
}

// ReplaceWorkVAs implements repository.WorkRepository
func (a *workRepositoryAdapter) ReplaceWorkVAs(ctx context.Context, workID uint, vaIDs []string) error {
	return a.repo.ReplaceWorkVAs(ctx, workID, vaIDs)
}

// GetWorkCount implements repository.WorkRepository
func (a *workRepositoryAdapter) GetWorkCount(ctx context.Context) (int, error) {
	_, total, err := a.repo.List(ctx, ListWorksParams{})
	if err != nil {
		return 0, err
	}
	return int(total), nil
}

// GetTracksCount implements repository.WorkRepository
func (a *workRepositoryAdapter) GetTracksCount(ctx context.Context) (int, error) {
	// This is a placeholder implementation. You'll need to implement the actual track counting logic
	return 0, nil
}
