-- migrations/000020_create_subtitle_table.up.sql
-- Create subtitle table for user-generated and admin-uploaded subtitles

CREATE TABLE IF NOT EXISTS t_subtitle (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    uuid VARCHAR(36) NOT NULL UNIQUE,
    original_id VARCHAR(255) NOT NULL, -- The originalID of the work
    work_id INTEGER NOT NULL,
    format VARCHAR(10) NOT NULL, -- srt, lrc, ass, vtt, etc.
    uploader_id VARCHAR(36) NOT NULL, -- User ID of the uploader
    subtitle_type VARCHAR(20) NOT NULL, -- user_submitted, ai_generated, admin_uploaded
    description TEXT, -- Optional description for the subtitle
    is_public BOOLEAN NOT NULL DEFAULT TRUE,
    up_votes INTEGER NOT NULL DEFAULT 0,
    down_votes INTEGER NOT NULL DEFAULT 0,
    is_in_archive BOOLEAN NOT NULL DEFAULT FALSE, -- Whether this is for a file in an archive
    archive_path VARCHAR(1024), -- Path to the archive file, if applicable
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (work_id) REFERENCES t_work(id) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (uploader_id) REFERENCES t_user(id) ON DELETE CASCADE ON UPDATE CASCADE
);

-- Create indexes for efficient lookups
CREATE INDEX IF NOT EXISTS idx_subtitle_original_id ON t_subtitle(original_id);
CREATE INDEX IF NOT EXISTS idx_subtitle_work_id ON t_subtitle(work_id);
CREATE INDEX IF NOT EXISTS idx_subtitle_uploader_id ON t_subtitle(uploader_id);
CREATE INDEX IF NOT EXISTS idx_subtitle_subtitle_type ON t_subtitle(subtitle_type);
CREATE INDEX IF NOT EXISTS idx_subtitle_is_public ON t_subtitle(is_public);

-- Create a table for user votes on subtitles
CREATE TABLE IF NOT EXISTS t_subtitle_vote (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    subtitle_id INTEGER NOT NULL,
    user_id VARCHAR(36) NOT NULL,
    vote INTEGER NOT NULL, -- +1 for upvote, -1 for downvote
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (subtitle_id) REFERENCES t_subtitle(id) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (user_id) REFERENCES t_user(id) ON DELETE CASCADE ON UPDATE CASCADE,
    UNIQUE (subtitle_id, user_id) -- Each user can only vote once per subtitle
);

-- Create indexes for the vote table
CREATE INDEX IF NOT EXISTS idx_subtitle_vote_subtitle_id ON t_subtitle_vote(subtitle_id);
CREATE INDEX IF NOT EXISTS idx_subtitle_vote_user_id ON t_subtitle_vote(user_id);

-- Create triggers to update the updated_at timestamp
CREATE TRIGGER IF NOT EXISTS trigger_t_subtitle_updated_at
AFTER UPDATE ON t_subtitle
FOR EACH ROW
BEGIN
    UPDATE t_subtitle SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
END;

CREATE TRIGGER IF NOT EXISTS trigger_t_subtitle_vote_updated_at
AFTER UPDATE ON t_subtitle_vote
FOR EACH ROW
BEGIN
    UPDATE t_subtitle_vote SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
END;

-- Create join table for subtitle-track associations
CREATE TABLE IF NOT EXISTS t_subtitle_track (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    subtitle_id INTEGER NOT NULL,
    track_path VARCHAR(1024) NOT NULL,
    is_in_archive BOOLEAN NOT NULL DEFAULT FALSE,
    archive_path VARCHAR(1024),
    FOREIGN KEY (subtitle_id) REFERENCES t_subtitle(id) ON DELETE CASCADE ON UPDATE CASCADE
);
CREATE INDEX IF NOT EXISTS idx_subtitle_track_subtitle_id ON t_subtitle_track(subtitle_id);
CREATE INDEX IF NOT EXISTS idx_subtitle_track_track_path ON t_subtitle_track(track_path); 