-- Reversing the changes from 000004_add_priority_to_storage_sources.up.sql
-- SQLite does not directly support DROP COLUMN easily in older versions.
-- A common workaround is to recreate the table without the column.
-- However, for simplicity and if using a modern SQLite or other DB that supports it:
-- ALTER TABLE t_storage_sources DROP COLUMN priority;
-- For SQLite, a safer approach (though more complex) involves:
-- 1. Create a new table without the column.
-- 2. Copy data from the old table to the new table.
-- 3. Drop the old table.
-- 4. Rename the new table to the original name.

-- Given the constraints and typical migration patterns,
-- and assuming the DB might not be SQLite or is a version that supports DROP COLUMN,
-- or accepting that for SQLite this might be a manual data migration step if rollback is truly needed.
-- For now, providing the standard SQL DROP COLUMN.
-- If this project specifically targets an older SQLite that doesn't support this,
-- the down migration would be more complex or might be omitted/marked as manual.

-- Attempt to drop the index first
DROP INDEX IF EXISTS idx_storage_sources_priority;

-- Attempt to drop the column
-- This syntax might vary slightly depending on the specific SQL database being used.
-- For SQLite, this requires version 3.35.0+
-- For PostgreSQL and MySQL, this is standard.
ALTER TABLE t_storage_sources DROP COLUMN priority;

-- If the above ALTER TABLE DROP COLUMN fails on older SQLite,
-- the alternative is a more complex table recreation sequence:
/*
PRAGMA foreign_keys=off;

CREATE TABLE t_storage_sources_new (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    driver VARCHAR(255) NOT NULL,
    mount_path VARCHAR(255) UNIQUE,
    addition TEXT,
    remark TEXT,
    disabled BOOLEAN DEFAULT FALSE,
    web_dav_url TEXT,
    web_dav_username TEXT,
    web_dav_password TEXT,
    web_dav_root TEXT,
    s3_endpoint TEXT,
    s3_region TEXT,
    s3_access_key_id TEXT,
    s3_secret_access_key TEXT,
    s3_bucket_name TEXT,
    s3_root_folder TEXT,
    s3_url_style VARCHAR(10) DEFAULT 'path',
    alist_url TEXT,
    alist_token TEXT,
    alist_root_folder_path TEXT,
    download_strategy VARCHAR(10) DEFAULT 'proxy' NOT NULL,
    default_root VARCHAR(1024) NOT NULL DEFAULT '/' -- Ensure this matches the up migration's default if any
);

INSERT INTO t_storage_sources_new (
    id, driver, mount_path, addition, remark, disabled,
    web_dav_url, web_dav_username, web_dav_password, web_dav_root,
    s3_endpoint, s3_region, s3_access_key_id, s3_secret_access_key, s3_bucket_name, s3_root_folder, s3_url_style,
    alist_url, alist_token, alist_root_folder_path,
    download_strategy, default_root
)
SELECT
    id, driver, mount_path, addition, remark, disabled,
    web_dav_url, web_dav_username, web_dav_password, web_dav_root,
    s3_endpoint, s3_region, s3_access_key_id, s_secret_access_key, s3_bucket_name, s3_root_folder, s3_url_style,
    alist_url, alist_token, alist_root_folder_path,
    download_strategy, default_root
FROM t_storage_sources;

DROP TABLE t_storage_sources;

ALTER TABLE t_storage_sources_new RENAME TO t_storage_sources;

PRAGMA foreign_keys=on;
*/