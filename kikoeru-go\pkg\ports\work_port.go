package ports

import (
	"context"
	// "time" // Removed unused import

	"github.com/Sakura-Byte/kikoeru-go/pkg/auth" // Import auth
	work_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/work"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/database"
)

// ListWorksParams contains parameters for listing works
type ListWorksParams struct {
	Page          int
	PageSize      int
	SortBy        string
	SortDirection string
	Filter        map[string]interface{}
}

// WorkService defines the application service interface for work-related operations.
// This interface is a "driven" port, implemented by the service layer and used by handlers.
// It also implicitly includes methods from ports.WorkScrapeTriggerService.
type WorkService interface {
	GetWorkByID(ctx context.Context, id uint) (*work_dto.WorkDTO, error)                   // This might be deprecated or internally used
	GetWorkByOriginalID(ctx context.Context, originalID string) (*work_dto.WorkDTO, error) // Primary method for fetching by originalID
	GetWorkInfoByOriginalID(ctx context.Context, originalID string) (*work_dto.WorkDTO, error)
	ListWorks(ctx context.Context, params database.ListWorksParams) ([]*work_dto.WorkDTO, int64, error)
	CountWorks() (int64, error)                                                                                                         // New method to count total works
	UpdateWorkByAdmin(ctx context.Context, claims *auth.Claims, workID uint, req work_dto.UpdateWorkRequest) (*work_dto.WorkDTO, error) // Changed req type to work_dto.UpdateWorkRequest
	DeleteWorkByAdmin(ctx context.Context, claims *auth.Claims, workID uint) error
	GetRandomWorkQueue(ctx context.Context, claims *auth.Claims, params work_dto.RandomQueueParams) ([]*work_dto.WorkDTO, error)
	AddTagToWorkByName(ctx context.Context, claims *auth.Claims, workID uint, tagName string) error
	RemoveTagFromWorkByName(ctx context.Context, claims *auth.Claims, workID uint, tagName string) error
	ReplaceWorkTagsByNames(ctx context.Context, claims *auth.Claims, workID uint, tagNames []string) error
	AddVAToWorkByName(ctx context.Context, claims *auth.Claims, workID uint, vaName string) error

	// Methods for Voice Actor (VA) management / fulfilling ports.WorkScrapeRemoveVAService implicitly
	RemoveVAFromWorkByName(ctx context.Context, claims *auth.Claims, workID uint, vaName string) error
	ReplaceWorkVAsByNames(ctx context.Context, claims *auth.Claims, workID uint, vaNames []string) error

	// Methods from ports.WorkScrapeTriggerService
	TriggerScrapeForWorkByAdmin(ctx context.Context, claims *auth.Claims, workID uint, req ScrapeOptions) error
	TriggerScrapeForAllWorksByAdmin(ctx context.Context, claims *auth.Claims, req ScrapeOptions) error
}

// UpdateWorkRequest DTO has been moved to pkg/dto/work.
// RandomQueueParams DTO has been moved to pkg/dto/work.
// WorkDTO (formerly WorkInfoResponse) is defined in pkg/dto/work.
