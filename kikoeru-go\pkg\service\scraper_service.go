package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net"
	"net/http"
	"regexp"
	"strconv"
	"strings"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors" // Added for apperrors
	"github.com/Sakura-Byte/kikoeru-go/pkg/config"
	circle_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/circle" // Added circle_dto import
	scraper_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/scraper"
	tag_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/tag" // Added tag_dto import
	va_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/va"   // Added va_dto import
	"github.com/Sakura-Byte/kikoeru-go/pkg/log"
	"github.com/Sakura-Byte/kikoeru-go/pkg/ports"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/driver" // Kept for DriverManagerGetter if used for cover path generation, though likely not
	"github.com/go-resty/resty/v2"
)

// Error definitions moved to kikoeru-go/pkg/apperrors/errors.go

type scraperService struct {
	restyClient    *resty.Client
	cfg            *config.AppConfig
	asmrOneApiBase string
}

func NewScraperService(
	appCfg *config.AppConfig,
	_ driver.DriverManagerGetter,
) ports.ScraperService {
	client := resty.New()
	client.SetTimeout(appCfg.Scraper.Timeout)
	client.SetHeader("User-Agent", "Mozilla/5.0 Kikoeru Scraper (Golang Bot; github.com/Sakura-Byte/kikoeru-go)")
	if appCfg.Scraper.HttpProxy != "" {
		proxyURL := appCfg.Scraper.HttpProxy
		if strings.HasPrefix(proxyURL, "http://") || strings.HasPrefix(proxyURL, "https://") || strings.HasPrefix(proxyURL, "socks5://") {
			client.SetProxy(proxyURL)
		} else {
			log.Warn(context.Background(), "Invalid scraper proxy URL format. Proxy not set.", "url", proxyURL)
		}
	}
	if appCfg.Scraper.RetryAttempts > 0 {
		client.SetRetryCount(appCfg.Scraper.RetryAttempts)
		if appCfg.Scraper.RetryDelay > 0 {
			client.SetRetryWaitTime(appCfg.Scraper.RetryDelay)
		}
		client.AddRetryCondition(func(r *resty.Response, err error) bool {
			if err != nil {
				if errors.Is(err, context.DeadlineExceeded) {
					return true
				}
				var netErr net.Error
				if errors.As(err, &netErr) && netErr.Timeout() {
					return true
				}
				if r == nil || r.RawResponse == nil {
					return true
				}
				return false
			}
			if r != nil {
				switch r.StatusCode() {
				case http.StatusTooManyRequests, http.StatusServiceUnavailable, http.StatusBadGateway, http.StatusGatewayTimeout:
					log.Debug(context.Background(), "Retrying due to HTTP status code", "status_code", r.StatusCode(), "url", r.Request.URL)
					return true
				}
			}
			return false
		})
	}
	return &scraperService{
		restyClient: client,
		cfg:         appCfg,
	}
}

func (s *scraperService) ScrapeWorkDataAndCover(ctx context.Context, originalID string, options ports.ScrapeOptions) (*scraper_dto.ScrapedWorkData, []byte, []byte, error) {
	log.Info(ctx, "Starting ScrapeWorkDataAndCover", "original_id", originalID, "options", fmt.Sprintf("%+v", options))

	if originalID == "" {
		log.Error(ctx, "Cannot scrape work, originalID is empty")
		return nil, nil, nil, errors.New("originalID cannot be empty for scraping") // This specific error can remain local
	}

	var scrapedData *scraper_dto.ScrapedWorkData
	var scrapeErr error
	var mainCoverBytes []byte
	var samCoverBytes []byte

	log.Info(ctx, "Attempting to scrape from DLSite first", "rjid", originalID)
	scrapedData, scrapeErr = s.ScrapeDLsiteWorkByRJID(ctx, originalID, options.PreferredLang)

	if scrapeErr != nil {
		log.Warn(ctx, "Failed to scrape from DLSite, attempting ASMR.ONE as fallback", "rjid", originalID, "dlsite_error", scrapeErr)
		var asmrErr error
		scrapedData, asmrErr = s.ScrapeASMRONEWorkByID(ctx, originalID, options.PreferredLang)
		if asmrErr != nil {
			log.Error(ctx, "Failed to scrape from ASMR.ONE after DLSite failure", "rjid", originalID, "asmr_error", asmrErr)
			return nil, nil, nil, fmt.Errorf("failed to scrape from DLSite (error: %v) and ASMR.ONE (error: %v) for %s", scrapeErr, asmrErr, originalID)
		}
		log.Info(ctx, "Successfully scraped data from ASMR.ONE as fallback", "rjid", originalID)
		scrapeErr = nil
	} else {
		log.Info(ctx, "Successfully scraped data from DLSite", "rjid", originalID)
	}

	if scrapedData == nil {
		log.Error(ctx, "Scraped data is nil after attempting all sources", "rjid", originalID)
		return nil, nil, nil, fmt.Errorf("failed to obtain any scraped data for %s", originalID)
	}

	if options.DownloadCover {
		if scrapedData.CoverURL != "" {
			log.Info(ctx, "Attempting to download main cover image bytes", "cover_url", scrapedData.CoverURL)
			mainCoverResp, errDownloadMain := s.restyClient.R().SetContext(ctx).Get(scrapedData.CoverURL)
			if errDownloadMain != nil {
				log.Error(ctx, "Failed to download main cover image", "url", scrapedData.CoverURL, "error", errDownloadMain)
			} else if mainCoverResp.IsError() {
				log.Error(ctx, "HTTP error downloading main cover", "url", scrapedData.CoverURL, "status", mainCoverResp.Status())
			} else {
				mainCoverBytes = mainCoverResp.Body()
				log.Info(ctx, "Main cover image bytes downloaded successfully", "url", scrapedData.CoverURL, "size_bytes", len(mainCoverBytes))
			}
		} else {
			log.Info(ctx, "No main cover URL found in scraped data for work", "rjid", originalID)
		}

		if scrapedData.ThumbnailURL != "" {
			log.Info(ctx, "Attempting to download SAM cover image bytes", "sam_cover_url", scrapedData.ThumbnailURL)
			samCoverResp, errDownloadSam := s.restyClient.R().SetContext(ctx).Get(scrapedData.ThumbnailURL)
			if errDownloadSam != nil {
				log.Error(ctx, "Failed to download SAM cover image", "url", scrapedData.ThumbnailURL, "error", errDownloadSam)
			} else if samCoverResp.IsError() {
				log.Error(ctx, "HTTP error downloading SAM cover", "url", scrapedData.ThumbnailURL, "status", samCoverResp.Status())
			} else {
				samCoverBytes = samCoverResp.Body()
				log.Info(ctx, "SAM cover image bytes downloaded successfully", "url", scrapedData.ThumbnailURL, "size_bytes", len(samCoverBytes))
			}
		} else {
			log.Info(ctx, "No SAM cover URL found in scraped data for work", "rjid", originalID)
		}
	}

	log.Info(ctx, "ScrapeWorkDataAndCover finished", "original_id", originalID, "main_cover_downloaded", len(mainCoverBytes) > 0, "sam_cover_downloaded", len(samCoverBytes) > 0)
	return scrapedData, mainCoverBytes, samCoverBytes, scrapeErr
}

type dlsiteProductGenre struct {
	ID   json.Number `json:"id"`
	Name string      `json:"name"`
}
type dlsiteProductCreatorVoiceBy struct {
	Name string `json:"name"`
}
type dlsiteProductCreators struct {
	VoiceBy []dlsiteProductCreatorVoiceBy `json:"voice_by"`
}
type DlsiteImageThumb struct {
	URL      string `json:"url"`
	FileName string `json:"file_name"`
}
type DlsiteImageInfo struct {
	URL      string `json:"url"`
	FileName string `json:"file_name"`
}
type dlsiteProductData struct {
	ProductName string                `json:"product_name"`
	MakerID     string                `json:"maker_id"`
	MakerName   string                `json:"maker_name"`
	AgeCategory json.Number           `json:"age_category"`
	RegistDate  string                `json:"regist_date"`
	Genres      []dlsiteProductGenre  `json:"genres"`
	Creaters    dlsiteProductCreators `json:"creaters"`
	WorkType    string                `json:"work_type"`
	Description string                `json:"introduction"`
	SeriesName  string                `json:"series_name"`
	ImageMain   DlsiteImageInfo       `json:"image_main"`
	ImageThum   DlsiteImageThumb      `json:"image_thum"`
}
type dlsiteDynamicProductData struct {
	SiteID          string          `json:"site_id"`
	SiteIDTouch     string          `json:"site_id_touch"`
	MakerID         string          `json:"maker_id"`
	AgeCategory     json.Number     `json:"age_category"`
	AffiliateDeny   json.Number     `json:"affiliate_deny"`
	DlCount         json.Number     `json:"dl_count"`
	WishlistCount   json.Number     `json:"wishlist_count"`
	DlFormat        json.Number     `json:"dl_format"`
	Rank            json.RawMessage `json:"rank"`
	RateAverage     json.Number     `json:"rate_average"`
	RateAverage2DP  json.Number     `json:"rate_average_2dp"`
	RateAverageStar json.Number     `json:"rate_average_star"`
	RateCount       json.Number     `json:"rate_count"`
	RateCountDetail json.RawMessage `json:"rate_count_detail"`
	ReviewCount     json.Number     `json:"review_count"`
	Price           json.Number     `json:"price"`
	PriceWithoutTax json.Number     `json:"price_without_tax"`
	PriceStr        string          `json:"price_str"`
	WorkName        string          `json:"work_name"`
	WorkNameMasked  string          `json:"work_name_masked"`
	WorkImage       string          `json:"work_image"`
	WorkType        string          `json:"work_type"`
}

var rjCodePatternInternal_scraper = regexp.MustCompile(`(?i)(RJ|VJ|BJ)(\d{6,8})`)

func formatRJIDNumberInternal_scraper(numericIDStr string) (string, error) {
	idNum, err := strconv.ParseInt(numericIDStr, 10, 64)
	if err != nil {
		return "", fmt.Errorf("invalid numeric ID: %s", numericIDStr)
	}
	if idNum < 0 {
		return "", fmt.Errorf("numeric ID negative: %d", idNum)
	}
	if idNum >= 1000000 {
		return fmt.Sprintf("RJ%08d", idNum), nil
	}
	if idNum > 0 {
		return fmt.Sprintf("RJ%06d", idNum), nil
	}
	return "", fmt.Errorf("numeric ID must be positive: %d", idNum)
}
func extractNumericRJIDInternal_scraper(text string) string {
	matches := rjCodePatternInternal_scraper.FindStringSubmatch(text)
	if len(matches) > 2 {
		return matches[2]
	}
	if len(matches) > 1 {
		return matches[1]
	}
	return ""
}

func (s *scraperService) ScrapeDLsiteWorkByRJID(ctx context.Context, rjID string, language string) (*scraper_dto.ScrapedWorkData, error) {
	log.Info(ctx, "Scraping DLSite work using built-in logic.", "rjid", rjID, "requested_language", language)
	finalLanguage := language
	if finalLanguage == "" {
		finalLanguage = s.cfg.Scraper.TagLanguage
	}
	if finalLanguage == "" {
		finalLanguage = "ja-jp"
	}
	if rjID == "" {
		return nil, errors.New("rjID cannot be empty")
	}
	originalNumericRJIDStr := strings.TrimPrefix(strings.ToUpper(rjID), "RJ")
	originalNumericRJIDStr = strings.TrimPrefix(originalNumericRJIDStr, "VJ")
	originalNumericRJIDStr = strings.TrimPrefix(originalNumericRJIDStr, "BJ")
	formattedOriginalRJID, err := formatRJIDNumberInternal_scraper(originalNumericRJIDStr)
	if err != nil {
		return nil, fmt.Errorf("invalid rjID format for ScrapeDLsiteWorkByRJID: %w", err)
	}

	scrapedData := &scraper_dto.ScrapedWorkData{OriginalID: formattedOriginalRJID, Language: finalLanguage}
	productJsonURL := fmt.Sprintf("https://www.dlsite.com/maniax/api/=/product.json?workno=%s", formattedOriginalRJID)
	respStatic, err := s.restyClient.R().SetContext(ctx).SetHeader("Cookie", fmt.Sprintf("locale=%s; adultchecked=1;", finalLanguage)).Get(productJsonURL)
	if err != nil {
		return nil, fmt.Errorf("%w: product.json %s: %v", apperrors.ErrScrapingSourceOffline, formattedOriginalRJID, err) // Changed to apperrors
	}
	if respStatic.IsError() {
		return nil, fmt.Errorf("%w: product.json status %s for %s", apperrors.ErrScrapingFailed, respStatic.Status(), formattedOriginalRJID) // Changed to apperrors
	}

	var dlsiteProducts []dlsiteProductData
	if err := json.Unmarshal(respStatic.Body(), &dlsiteProducts); err != nil {
		return nil, fmt.Errorf("%w: unmarshal product.json %s: %v. Body: %s", apperrors.ErrScrapingInvalidData, formattedOriginalRJID, err, string(respStatic.Body())) // Changed to apperrors
	}
	if len(dlsiteProducts) == 0 {
		return nil, fmt.Errorf("%w for %s from product.json", apperrors.ErrScrapingDataNotFound, formattedOriginalRJID) // Changed to apperrors
	}

	productData := dlsiteProducts[0]
	scrapedData.Title = productData.ProductName
	scrapedData.Synopsis = productData.Description
	scrapedData.WorkType = productData.WorkType
	if productData.MakerName != "" {
		scrapedData.Circle = &circle_dto.CircleNameDTO{Name: productData.MakerName} // Changed to circle_dto
	}
	if productData.RegistDate != "" {
		parts := strings.Split(productData.RegistDate, " ")
		if len(parts) > 0 {
			scrapedData.ReleaseDate = parts[0]
		}
	}
	ageCatInt, _ := productData.AgeCategory.Int64()
	switch ageCatInt {
	case 1:
		scrapedData.AgeRating = "general"
	case 2:
		scrapedData.AgeRating = "r15"
	case 3:
		scrapedData.AgeRating = "adult"
	default:
		scrapedData.AgeRating = "unknown"
	}

	for _, genre := range productData.Genres {
		scrapedData.Tags = append(scrapedData.Tags, tag_dto.TagNameDTO{Name: genre.Name}) // Changed to tag_dto
	}
	for _, va := range productData.Creaters.VoiceBy {
		scrapedData.VAS = append(scrapedData.VAS, va_dto.VANameDTO{Name: va.Name}) // Changed to va_dto
	}

	dynamicDataURL := fmt.Sprintf("https://www.dlsite.com/maniax/product/info/ajax?product_id=%s", formattedOriginalRJID)
	respDynamic, err := s.restyClient.R().SetContext(ctx).SetHeader("Cookie", "adultchecked=1;").Get(dynamicDataURL)
	if err != nil {
		log.Warn(ctx,"Failed to get dynamic data (request error)", "rjid", formattedOriginalRJID, "error", err)
	} else if respDynamic.IsError() {
		log.Warn(ctx,"Received error status for dynamic data", "rjid", formattedOriginalRJID, "status", respDynamic.Status())
	} else {
		var dynamicDataMap map[string]dlsiteDynamicProductData
		if errUnmarshal := json.Unmarshal(respDynamic.Body(), &dynamicDataMap); errUnmarshal == nil {
			if data, ok := dynamicDataMap[formattedOriginalRJID]; ok {
				if dlCount, e := data.DlCount.Int64(); e == nil {
					scrapedData.DLCount = dlCount
				}
				if rateAvg, e := data.RateAverage2DP.Float64(); e == nil {
					scrapedData.RatingAverage = rateAvg
				}
				if rateCount, e := data.RateCount.Int64(); e == nil {
					scrapedData.RatingCount = rateCount
				}
				if reviewCount, e := data.ReviewCount.Int64(); e == nil {
					scrapedData.ReviewCount = reviewCount
				}
				if price, e := data.Price.Int64(); e == nil {
					scrapedData.Price = price
				}
				if len(data.Rank) > 0 {
					var rankItems []scraper_dto.ScrapedRankItem
					if errJson := json.Unmarshal(data.Rank, &rankItems); errJson == nil {
						scrapedData.Rank = rankItems
					} else {
						log.Warn(ctx,"Failed to unmarshal Rank from DLSite dynamic data", "rjid", formattedOriginalRJID, "error", errJson)
					}
				}
				if len(data.RateCountDetail) > 0 {
					var rateDetailItems []scraper_dto.ScrapedRateCountDetailItem
					if errJson := json.Unmarshal(data.RateCountDetail, &rateDetailItems); errJson == nil {
						scrapedData.RateCountDetail = rateDetailItems
					} else {
						log.Warn(ctx,"Failed to unmarshal RateCountDetail from DLSite dynamic data", "rjid", formattedOriginalRJID, "error", errJson)
					}
				}
			}
		} else {
			log.Warn(ctx,"Failed to unmarshal dynamic data", "rjid", formattedOriginalRJID, "error", errUnmarshal, "body_preview", string(respDynamic.Body())[:min(200, len(respDynamic.Body()))])
		}
	}

	if productData.ImageMain.URL != "" {
		coverURL := productData.ImageMain.URL
		if strings.HasPrefix(coverURL, "//") {
			coverURL = "https:" + coverURL
		}
		scrapedData.CoverURL = coverURL
	} else {
		log.Warn(ctx,"MainCoverImageURL not found directly in product.json's image_main.url", "rjid", formattedOriginalRJID)
	}

	if productData.ImageThum.URL != "" {
		thumbnailURL := productData.ImageThum.URL
		if strings.HasPrefix(thumbnailURL, "//") {
			thumbnailURL = "https:" + thumbnailURL
		}
		scrapedData.ThumbnailURL = thumbnailURL
	} else {
		log.Warn(ctx,"ThumbnailURL not found in product.json's image_thum.url", "rjid", formattedOriginalRJID)
	}

	return scrapedData, nil
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// func (s *scraperService) _ensureAsmrOneApiUrl(ctx context.Context) error {
// 	if s.asmrOneApiBase != "" {
// 		return nil
// 	}
// 	log.Info(ctx, "ASMR.ONE API base URL not cached, attempting to fetch.")
// 	indexURL := "https://asmr.one/index.html"
// 	req := s.restyClient.R().SetContext(ctx)
// 	req.SetHeader("Cookie", "locale=zh-cn")
// 	resp, err := req.Get(indexURL)
// 	if err != nil {
// 		log.Error(ctx, "Failed to fetch asmr.one index.html", "error", err)
// 		return fmt.Errorf("failed to fetch asmr.one index.html: %w", err)
// 	}
// 	if resp.IsError() {
// 		log.Error(ctx, "Error status when fetching asmr.one index.html", "status", resp.Status())
// 		return fmt.Errorf("error status %s when fetching asmr.one index.html", resp.Status())
// 	}
// 	doc, err := goquery.NewDocumentFromReader(strings.NewReader(string(resp.Body())))
// 	if err != nil {
// 		log.Error(ctx, "Failed to parse asmr.one index.html", "error", err)
// 		return fmt.Errorf("failed to parse asmr.one index.html: %w", err)
// 	}
// 	apiUrl, exists := doc.Find(`link[rel="preconnect"][as="fetch"]`).Attr("href")
// 	if !exists || apiUrl == "" {
// 		log.Warn(ctx, "Could not find ASMR.ONE API preconnect link in index.html, using fallback.")
// 		s.asmrOneApiBase = "https://api.asmr-200.com"
// 		log.Info(ctx, "Using fallback ASMR.ONE API base URL.", "url", s.asmrOneApiBase)
// 		return nil
// 	}
// 	s.asmrOneApiBase = strings.TrimSuffix(apiUrl, "/")
// 	log.Info(ctx, "Successfully fetched and cached ASMR.ONE API base URL.", "url", s.asmrOneApiBase)
// 	return nil
// }

func (s *scraperService) ScrapeASMRONEWorkByID(ctx context.Context, workID string, language string) (*scraper_dto.ScrapedWorkData, error) {
	// if err := s._ensureAsmrOneApiUrl(ctx); err != nil {
	// 	return nil, fmt.Errorf("could not determine ASMR.ONE API base URL: %w", err)
	// }
	// if s.asmrOneApiBase == "" {
	// 	return nil, errors.New("ASMR.ONE API base URL is not available") // Specific error, can remain local
	// }
	s.asmrOneApiBase = "https://api.asmr-200.com"
	formattedID := workID
	numericIDStr := extractNumericRJIDInternal_scraper(formattedID)
	if numericIDStr != "" {
		formattedID, _ = formatRJIDNumberInternal_scraper(numericIDStr)
	} else if !strings.HasPrefix(strings.ToUpper(formattedID), "RJ") {
		log.Warn(ctx, "ASMR.ONE: workID does not appear to be a valid RJ code format", "work_id", workID)
	}

	apiURL := fmt.Sprintf("%s/api/workInfo/%s", s.asmrOneApiBase, formattedID)
	log.Info(ctx, "Scraping ASMR.ONE work", "url", apiURL)
	resp, err := s.restyClient.R().SetContext(ctx).SetHeader("Cookie", "locale=zh-cn").Get(apiURL)
	if err != nil {
		return nil, fmt.Errorf("%w: ASMR.ONE API %s: %v", apperrors.ErrScrapingSourceOffline, formattedID, err) // Changed to apperrors
	}
	if resp.IsError() {
		return nil, fmt.Errorf("%w: ASMR.ONE API status %s for %s", apperrors.ErrScrapingFailed, resp.Status(), formattedID) // Changed to apperrors
	}

	type AsmrOneAPIVA struct {
		Name string `json:"name"`
	}
	type AsmrOneAPITag struct {
		Name string `json:"name"`
	}
	type AsmrOneWorkResponse struct {
		ID                json.Number                              `json:"id"`
		Title             string                                   `json:"title"`
		CircleName        string                                   `json:"name"`
		ReleaseDate       string                                   `json:"release"`
		DLCount           json.Number                              `json:"dl_count"`
		Price             json.Number                              `json:"price"`
		ReviewCount       json.Number                              `json:"review_count"`
		RateCount         json.Number                              `json:"rate_count"`
		RatingAverage     json.Number                              `json:"rate_average_2dp"`
		RateCountDetail   []scraper_dto.ScrapedRateCountDetailItem `json:"rate_count_detail"`
		Rank              []scraper_dto.ScrapedRankItem            `json:"rank"`
		VAs               []AsmrOneAPIVA                           `json:"vas"`
		Tags              []AsmrOneAPITag                          `json:"tags"`
		AgeCategoryString string                                   `json:"age_category_string"`
		Duration          json.Number                              `json:"duration"`
		SourceID          string                                   `json:"source_id"`
		MainCoverURL      string                                   `json:"mainCoverUrl"`
		SamCoverUrl       string                                   `json:"samCoverUrl"`
		Worktype          string                                   `json:"worktype,omitempty"`
		Introduction      string                                   `json:"introduction,omitempty"`
		SeriesName        string                                   `json:"seriesName,omitempty"`
	}
	var asmrData AsmrOneWorkResponse
	if err := json.Unmarshal(resp.Body(), &asmrData); err != nil {
		return nil, fmt.Errorf("%w: unmarshal ASMR.ONE JSON for %s: %v. Body: %s", apperrors.ErrScrapingInvalidData, formattedID, err, string(resp.Body())) // Changed to apperrors
	}

	scrapedData := &scraper_dto.ScrapedWorkData{
		OriginalID:      asmrData.SourceID,
		Title:           asmrData.Title,
		Language:        language,
		Synopsis:        asmrData.Introduction,
		WorkType:        asmrData.Worktype,
		CoverURL:        asmrData.MainCoverURL,
		ThumbnailURL:    asmrData.SamCoverUrl,
		Rank:            asmrData.Rank,
		RateCountDetail: asmrData.RateCountDetail,
	}
	if priceInt, errPrice := asmrData.Price.Int64(); errPrice == nil {
		scrapedData.Price = priceInt
	}
	if dlCountInt, errDL := asmrData.DLCount.Int64(); errDL == nil {
		scrapedData.DLCount = dlCountInt
	}
	if ratingAvgFloat, errRatingAvg := asmrData.RatingAverage.Float64(); errRatingAvg == nil {
		scrapedData.RatingAverage = ratingAvgFloat
	}
	if ratingCountInt, errRatingCount := asmrData.RateCount.Int64(); errRatingCount == nil {
		scrapedData.RatingCount = ratingCountInt
	}
	if reviewCountInt, errReviewCount := asmrData.ReviewCount.Int64(); errReviewCount == nil {
		scrapedData.ReviewCount = reviewCountInt
	}

	if durationInt, errDuration := asmrData.Duration.Int64(); errDuration == nil {
		scrapedData.Duration = durationInt
	}
	if asmrData.CircleName != "" {
		scrapedData.Circle = &circle_dto.CircleNameDTO{Name: asmrData.CircleName} // Changed to circle_dto
	}
	if asmrData.ReleaseDate != "" {
		scrapedData.ReleaseDate = asmrData.ReleaseDate
	}

	switch strings.ToLower(asmrData.AgeCategoryString) {
	case "adult":
		scrapedData.AgeRating = "adult"
	case "r15":
		scrapedData.AgeRating = "r15"
	case "general", "all":
		scrapedData.AgeRating = "general"
	default:
		scrapedData.AgeRating = "unknown"
		if asmrData.AgeCategoryString != "" {
			log.Warn(ctx, "Unknown age category string from ASMR.ONE, defaulting to Unknown", "value", asmrData.AgeCategoryString)
		}
	}

	for _, tag := range asmrData.Tags {
		scrapedData.Tags = append(scrapedData.Tags, tag_dto.TagNameDTO{Name: tag.Name}) // Changed to tag_dto
	}
	for _, va := range asmrData.VAs {
		scrapedData.VAS = append(scrapedData.VAS, va_dto.VANameDTO{Name: va.Name}) // Changed to va_dto
	}
	log.Info(ctx, "Successfully scraped data from ASMR.ONE", "work_id", formattedID)
	return scrapedData, nil
}
