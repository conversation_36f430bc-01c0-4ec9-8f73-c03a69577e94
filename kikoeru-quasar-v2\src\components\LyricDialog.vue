<template>
  <q-dialog
    v-model="showDialog"
    transition-show="scale"
    transition-hide="scale"
    @hide="onDialogHide"
  >
    <q-card class="lyric-dialog-card" style="width: 500px; max-width: 90vw; height: 800px; max-height: 90vh;">
      <!-- Header with close button -->
      <q-card-section class="row items-center q-pb-none">
        <div class="text-h6">{{ t('lyric.title') }}</div>
        <q-space />
        <q-btn icon="close" flat round dense v-close-popup />
      </q-card-section>

      <!-- Top Section - Lyric File Selection -->
      <q-card-section class="q-pt-none">
        <q-select
          v-model="selectedLyric"
          :options="localAvailableLyrics"
          option-label="displayName"
          option-value="id"
          :label="t('lyric.selectFile')"
          outlined
          dense
          :loading="loadingLyrics"
          @update:model-value="onLyricSelect"
        >
          <template v-slot:no-option>
            <q-item>
              <q-item-section class="text-grey">
                {{ t('lyric.noLyricsFound') }}
              </q-item-section>
            </q-item>
          </template>

          <template v-slot:option="scope">
            <q-item v-bind="scope.itemProps">
              <q-item-section>
                <q-item-label>{{ scope.opt.name }}</q-item-label>
                <q-item-label caption>
                  {{ t('lyric.similarity') }}: {{ scope.opt.similarity }}%
                  <q-chip
                    v-if="scope.opt.type === 'ugc'"
                    size="sm"
                    color="orange"
                    text-color="white"
                    dense
                  >
                    UGC
                  </q-chip>
                </q-item-label>
              </q-item-section>
            </q-item>
          </template>
        </q-select>

        <!-- Manual file selection button -->
        <div class="q-mt-sm">
          <q-btn
            flat
            dense
            icon="folder_open"
            :label="t('lyric.selectLocalFile')"
            @click="selectLocalFile"
            :loading="loadingLocalFile"
          />
        </div>
      </q-card-section>

      <!-- Middle Section - Lyric Display -->
      <q-card-section class="lyric-content-section">
        <div
          ref="lyricContainer"
          class="lyric-container"
          :class="{ 'no-lyrics': !hasLyrics }"
        >
          <div v-if="!hasLyrics" class="no-lyrics-message">
            <q-icon name="music_note" size="4rem" color="grey-5" />
            <div class="text-h6 text-grey-5 q-mt-md">{{ t('lyric.noLyrics') }}</div>
            <div class="text-body2 text-grey-6">{{ t('lyric.noLyricsDesc') }}</div>
          </div>

          <div v-else class="lyric-lines">
            <div
              v-for="(line, index) in lyricLines"
              :key="index"
              class="lyric-line"
              :class="{
                'current-line': index === currentLineIndex,
                'clickable': line.time !== undefined
              }"
              @click="seekToLine(line)"
            >
              <span class="line-time" v-if="line.time !== undefined">
                [{{ formatTime(line.time) }}]
              </span>
              <span class="line-text">{{ line.text }}</span>
            </div>
          </div>
        </div>
      </q-card-section>

      <!-- Bottom Section - Timing Controls -->
      <q-card-section class="timing-controls">
        <div class="row items-center justify-center q-gutter-md">
          <q-btn
            flat
            dense
            icon="remove"
            label="-0.3s"
            @click="adjustTiming(-0.3)"
            :disable="!hasLyrics"
          />

          <div class="timing-display">
            <q-input
              v-model.number="timingOffset"
              type="number"
              step="0.1"
              suffix="s"
              dense
              outlined
              style="width: 80px"
              @update:model-value="onTimingChange"
              :disable="!hasLyrics"
            />
          </div>

          <q-btn
            flat
            dense
            icon="add"
            label="+0.3s"
            @click="adjustTiming(0.3)"
            :disable="!hasLyrics"
          />
        </div>

        <div class="text-center text-caption text-grey-6 q-mt-sm">
          {{ t('lyric.adjustTiming') }}
        </div>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, computed, nextTick, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useQuasar } from 'quasar'
import Lyric from 'lrc-file-parser'
import LyricDiscoveryService from 'src/services/lyricDiscoveryService'
import { useAudioPlayer } from '../composables/useAudioPlayer'
import { useWorkInfo } from '../composables/useWorkInfo'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  treeData: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

const { t } = useI18n()
const $q = useQuasar()
const {
  currentTime,
  playing,
  availableLyrics,
  currentLyricFile,
  lyricContent,
  currentPlayingFile,
  setSeekToTime
} = useAudioPlayer()
const { getTreeData } = useWorkInfo()

// Reactive data
const localAvailableLyrics = ref([])
const selectedLyric = ref(null)
const lyricLines = ref([])
const currentLineIndex = ref(-1)
const timingOffset = ref(0)
const loadingLyrics = ref(false)
const loadingLocalFile = ref(false)
const lrcObj = ref(null)
const lyricContainer = ref(null)

// Computed properties
const showDialog = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  }
})

const hasLyrics = computed(() => lyricLines.value.length > 0)

const loadAvailableLyrics = async () => {
  if (!currentPlayingFile.value) {
    console.log('LyricDialog: No current playing file')
    return
  }

  loadingLyrics.value = true
  try {
    console.log('LyricDialog: Loading available lyrics...')
    console.log('LyricDialog: availableLyrics from store:', availableLyrics.value)
    console.log('LyricDialog: currentLyricFile from store:', currentLyricFile.value)
    console.log('LyricDialog: lyricContent from store:', lyricContent.value ? 'Content available' : 'No content')

    // Check if lyrics are already loaded by AudioPlayer
    if (availableLyrics.value && availableLyrics.value.length > 0) {
      console.log('LyricDialog: Reusing lyrics already loaded by AudioPlayer:', availableLyrics.value.length, 'lyrics found')

      // Copy store lyrics to local array
      localAvailableLyrics.value = [...availableLyrics.value]

      // If there's a current lyric file and content, use it
      if (currentLyricFile.value && lyricContent.value) {
        console.log('LyricDialog: Using current lyric file and content from store')
        selectedLyric.value = currentLyricFile.value
        initializeLyric(lyricContent.value)
        // Ensure we scroll to current position after initialization
        nextTick(() => {
          updateCurrentLine()
        })
      } else if (localAvailableLyrics.value.length > 0) {
        // Auto-select the first lyric if no current one
        console.log('LyricDialog: Auto-selecting first available lyric')
        selectedLyric.value = localAvailableLyrics.value[0]
        await loadSelectedLyric()
      }
    } else {
      // Fallback: discover lyrics if not already loaded
      console.log('LyricDialog: No lyrics loaded by AudioPlayer, discovering lyrics')
      const lyrics = await LyricDiscoveryService.discoverLyrics(
        currentPlayingFile.value,
        props.treeData
      )

      console.log('LyricDialog: Discovered lyrics:', lyrics.length, 'lyrics found')
      localAvailableLyrics.value = lyrics

      // Auto-select the first lyric if available
      if (lyrics.length > 0) {
        selectedLyric.value = lyrics[0]
        await loadSelectedLyric()
      }
    }
  } catch (error) {
    console.error('LyricDialog: Failed to load available lyrics:', error)
    $q.notify({
      color: 'negative',
      message: t('player.subtitleLoadFailed')
    })
  } finally {
    loadingLyrics.value = false
  }
}

const onLyricSelect = async () => {
  if (selectedLyric.value) {
    await loadSelectedLyric()
  }
}

const loadSelectedLyric = async () => {
  if (!selectedLyric.value) return

  try {
    // Get storage ID and path_in_storage from cached tree data
    let storageId = null
    let workPathInStorage = ''

    if (currentPlayingFile.value?.originalId) {
      const cachedData = getTreeData(currentPlayingFile.value.originalId)
      storageId = cachedData?.storageId
      workPathInStorage = cachedData?.pathInStorage || ''
    }

    if (!storageId) {
      throw new Error('Storage ID not available - work info may not be loaded')
    }

    const content = await LyricDiscoveryService.loadLyricContent(
      selectedLyric.value,
      storageId,
      workPathInStorage
    )

    initializeLyric(content)

    // Ensure we scroll to current position after loading
    nextTick(() => {
      updateCurrentLine()
    })
  } catch (error) {
    console.error('Failed to load lyric content:', error)
    $q.notify({
      color: 'negative',
      message: t('player.subtitleLoadFailed')
    })
  }
}

const selectLocalFile = async () => {
  loadingLocalFile.value = true
  try {
    const content = await LyricDiscoveryService.selectLocalFile()
    initializeLyric(content)

    // Ensure we scroll to current position after loading
    nextTick(() => {
      updateCurrentLine()
    })

    // Add to available lyrics list
    const localFile = {
      id: 'local-selected',
      name: t('lyric.localSelectedFile'),
      type: 'local',
      similarity: 100,
      displayName: `${t('lyric.localSelectedFile')} (${t('lyric.similarity')} 100%)`
    }

    localAvailableLyrics.value.unshift(localFile)
    selectedLyric.value = localFile
  } catch (error) {
    console.error('Failed to load local file:', error)

    // Don't show error notification for user cancellation
    if (error.message !== 'File selection cancelled') {
      $q.notify({
        color: 'negative',
        message: t('player.subtitleLoadFailed')
      })
    }
  } finally {
    loadingLocalFile.value = false
  }
}

const initializeLyric = (content) => {
  try {
    console.log('Initializing lyric with content length:', content.length)
    console.log('First 200 chars:', content.substring(0, 200))

    lrcObj.value = new Lyric({
      onPlay: (_lineTime, text) => {
        console.log('lrc-file-parser onPlay:', _lineTime, text)
        // Find the current line index
        currentLineIndex.value = lyricLines.value.findIndex(l => l.text === text)
        scrollToCurrentLine()
      }
    })

    console.log('Setting lyric content to lrc-file-parser')
    lrcObj.value.setLyric(content)
    console.log('lrc-file-parser lines:', lrcObj.value.lines)
    lyricLines.value = parseLyricLines(content)

    console.log('Parsed lyric lines:', lyricLines.value.length)
    console.log('First few lines:', lyricLines.value.slice(0, 5))

    // Always sync to current time, regardless of playing state
    lrcObj.value.play((currentTime.value + timingOffset.value) * 1000)

    // If not playing, pause the lrc object but keep the position
    if (!playing.value) {
      lrcObj.value.pause()
    }

    // Update current line and scroll to position
    updateCurrentLine()
  } catch (error) {
    console.error('Failed to initialize lyric:', error)
    $q.notify({
      color: 'negative',
      message: t('player.subtitleLoadFailed')
    })
  }
}

const parseLyricLines = (content) => {
  // Enhanced LRC parser for display
  const lines = content.split('\n')
  const lyricLines = []

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim()
    if (!line) continue

    // Try multiple LRC timestamp formats
    // [mm:ss.xx] or [mm:ss.xxx] or [mm:ss:xx] or [mm:ss]
    const timeMatch = line.match(/\[(\d{1,2}):(\d{2})(?:[.:])(\d{2,3})\](.*)/) ||
                     line.match(/\[(\d{1,2}):(\d{2})\](.*)/)

    if (timeMatch) {
      const minutes = parseInt(timeMatch[1])
      const seconds = parseInt(timeMatch[2])
      const milliseconds = timeMatch[3] ? parseInt(timeMatch[3].padEnd(3, '0')) : 0
      const time = minutes * 60 + seconds + milliseconds / 1000
      const text = (timeMatch[4] || timeMatch[3] || '').trim()

      if (text) {
        lyricLines.push({ time, text })
      }
    } else if (line.startsWith('[') && line.includes(']')) {
      // Metadata lines like [ar:Artist], [ti:Title], etc.
      // Skip metadata for now
    } else if (line && !line.startsWith('[')) {
      // Plain text line without timestamp
      lyricLines.push({ text: line })
    }
  }

  return lyricLines
}

const updateCurrentLine = () => {
  if (!hasLyrics.value) return

  const currentTimeValue = currentTime.value + timingOffset.value
  let newIndex = -1

  for (let i = 0; i < lyricLines.value.length; i++) {
    const line = lyricLines.value[i]
    if (line.time !== undefined && line.time <= currentTimeValue) {
      newIndex = i
    } else {
      break
    }
  }

  if (newIndex !== currentLineIndex.value) {
    currentLineIndex.value = newIndex
    scrollToCurrentLine()
  }
}

const scrollToCurrentLine = () => {
  nextTick(() => {
    const container = lyricContainer.value
    const currentLine = container?.querySelector('.current-line')

    if (currentLine && container) {
      const containerHeight = container.clientHeight
      const lineTop = currentLine.offsetTop
      const lineHeight = currentLine.clientHeight

      const scrollTop = lineTop - containerHeight / 2 + lineHeight / 2
      container.scrollTo({
        top: Math.max(0, scrollTop),
        behavior: 'smooth'
      })
    }
  })
}

const seekToLine = (line) => {
  if (line.time !== undefined) {
    const seekTime = line.time - timingOffset.value
    setSeekToTime(seekTime)
  }
}

const adjustTiming = (delta) => {
  timingOffset.value += delta
  onTimingChange()
}

const onTimingChange = () => {
  if (lrcObj.value && playing.value) {
    lrcObj.value.play((currentTime.value + timingOffset.value) * 1000)
  }
  updateCurrentLine()
}

const formatTime = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

const onDialogHide = () => {
  // Clean up when dialog is hidden
  if (lrcObj.value) {
    lrcObj.value.pause()
  }
}

// Watchers
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    loadAvailableLyrics()
  }
})

watch(currentTime, () => {
  updateCurrentLine()
})

watch(playing, (isPlaying) => {
  if (lrcObj.value) {
    if (isPlaying) {
      lrcObj.value.play((currentTime.value + timingOffset.value) * 1000)
    } else {
      lrcObj.value.pause()
    }
  }
})
</script>

<style lang="scss" scoped>
.lyric-dialog-card {
  display: flex;
  flex-direction: column;
}

.lyric-content-section {
  flex: 1;
  overflow: hidden;
  padding: 0;
  height: 600px; /* Fixed height for dialog mode */
}

.lyric-container {
  height: 100%;
  overflow-y: auto;
  padding: 1rem;

  &.no-lyrics {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  }
}

.no-lyrics-message {
  text-align: center;
}

.lyric-lines {
  max-width: 100%;
  margin: 0 auto;
}

.lyric-line {
  padding: 0.4rem 0.8rem;
  margin: 0.2rem 0;
  border-radius: 6px;
  transition: all 0.3s ease;
  line-height: 1.5;

  &.clickable {
    cursor: pointer;

    &:hover {
      background-color: rgba(0, 0, 0, 0.1);
      color: var(--q-primary);

      .line-time {
        color: var(--q-primary-dark);
      }
    }
  }

  &.current-line {
    background-color: var(--q-primary);
    color: white;
    font-weight: 500;
    transform: scale(1.02);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

    .line-time {
      color: rgba(255, 255, 255, 0.9);
    }
  }
}

.line-time {
  color: rgba(0, 0, 0, 0.7); /* Dark color for day mode */
  font-size: 0.85em;
  margin-right: 0.5rem;
  font-family: monospace;
}

.line-text {
  font-size: 1.1em;
}

.timing-controls {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  background-color: rgba(0, 0, 0, 0.02);
}

.timing-display {
  display: flex;
  align-items: center;
}

/* Dark mode styles */
.body--dark {
  .lyric-line {
    &.clickable:hover {
      background-color: rgba(255, 255, 255, 0.1);
      color: var(--q-primary);

      .line-time {
        color: var(--q-primary-light);
      }
    }
  }

  .timing-controls {
    border-top-color: rgba(255, 255, 255, 0.1);
    background-color: rgba(255, 255, 255, 0.02);
  }

  .line-time {
    color: rgba(255, 255, 255, 0.7); /* Light color for dark mode */
  }
}

/* Responsive design */
@media (max-width: 600px) {
  .lyric-dialog-card {
    width: 95vw !important;
    height: 80vh !important;
  }

  .lyric-content-section {
    height: 60vh !important;
  }

  .lyric-line {
    padding: 0.3rem 0.6rem;
    font-size: 0.9em;
  }

  .line-text {
    font-size: 0.95em;
  }

  .timing-controls .row {
    flex-direction: column;
    gap: 0.5rem;
  }
}
</style>
