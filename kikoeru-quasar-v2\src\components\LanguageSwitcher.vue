<template>
  <q-item>
    <q-item-section avatar>
      <q-icon name="language" />
    </q-item-section>

    <q-item-section>
      <q-item-label class="text-subtitle1">
        {{ $t('languageSwitcher.language') }}
      </q-item-label>
      <q-item-label caption lines="1">{{ currentLanguageName }}</q-item-label>
    </q-item-section>

    <q-item-section side>
      <q-select
        v-model="currentLocale"
        :options="languageOptions"
        dense
        borderless
        emit-value
        map-options
        options-dense
        style="min-width: 100px"
      />
    </q-item-section>
  </q-item>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useQuasar, LocalStorage } from 'quasar'

defineOptions({
  name: 'LanguageSwitcher'
})

// Map our locale codes to Quasar language pack codes
const quasarLangMap = {
  'en-US': 'en-US',
  'zh-CN': 'zh-CN',
  'ja-JP': 'ja'
}

// Use Vite's import.meta.glob to load language modules
const langModules = import.meta.glob('../../node_modules/quasar/lang/*.mjs')

const { locale } = useI18n({ useScope: 'global' })
const $q = useQuasar()

const currentLocale = ref(locale.value)

// Available languages
const languageOptions = [
  { label: 'English', value: 'en-US' },
  { label: '中文', value: 'zh-CN' },
  { label: '日本語', value: 'ja-JP' }
]

// Compute current language name
const currentLanguageName = computed(() => {
  const langOption = languageOptions.find(opt => opt.value === currentLocale.value)
  return langOption ? langOption.label : 'English'
})

// Set Quasar language pack
const setQuasarLanguage = async (localeCode) => {
  try {
    const quasarLangCode = quasarLangMap[localeCode] || 'en-US'
    const langPath = `../../node_modules/quasar/lang/${quasarLangCode}.mjs`

    if (langModules[langPath]) {
      const langModule = await langModules[langPath]()
      $q.lang.set(langModule.default)
    } else {
      console.warn(`Quasar language pack for ${quasarLangCode} not found, using default`)
    }
  } catch (error) {
    console.error('Failed to load Quasar language pack:', error)
  }
}

// Watch for changes to the locale
const setLocale = async (newLocale) => {
  // Update the i18n locale
  locale.value = newLocale

  // Update Quasar language pack
  await setQuasarLanguage(newLocale)

  // Save the locale in localStorage
  LocalStorage.set('locale', newLocale)
}

// Watch for changes to the currentLocale ref
const handleLocaleChange = (value) => {
  setLocale(value)
}

// Watchers
watch(currentLocale, (newValue) => {
  handleLocaleChange(newValue)
})
</script>
