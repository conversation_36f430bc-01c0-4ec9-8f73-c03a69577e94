-- migrations_temp/000001_create_user_related_tables.up.sql
-- Defines tables related to users, feedback, play history, favorites, reviews,
-- user uploaded subtitles, and tag votes.
-- This consolidates initial creation and subsequent alterations from various migration files.

-- User Table (t_user)
-- Based on 000001_create_initial_tables.up.sql and 000002_add_user_email_fields.up.sql
CREATE TABLE IF NOT EXISTS t_user (
    id VARCHAR(36) PRIMARY KEY, -- UUID
    username VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    user_group VARCHAR(50) NOT NULL, -- e.g., 'admin', 'user'
    
    email VARCHAR(255) UNIQUE, -- From 000002, UNIQUE constraint handled by index
    email_verified BOOLEAN DEFAULT FALSE, -- From 000002
    verification_token VARCHAR(100), -- From 000002
    verification_token_expires_at DATETIME, -- From 000002
    reset_password_token VARCHAR(100), -- From 000002
    reset_password_token_expires_at DATETIME, -- From 000002
    
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);
-- Indexes for t_user
CREATE INDEX IF NOT EXISTS idx_user_username ON t_user(username);
CREATE INDEX IF NOT EXISTS idx_user_user_group ON t_user(user_group);
-- CREATE UNIQUE INDEX IF NOT EXISTS uix_user_email ON t_user(email); -- This is created by `UNIQUE` on column def for most DBs, or by GORM. For SQLite, explicit index is good.
CREATE INDEX IF NOT EXISTS idx_user_verification_token ON t_user(verification_token);
CREATE INDEX IF NOT EXISTS idx_user_verification_token_expires_at ON t_user(verification_token_expires_at);
CREATE INDEX IF NOT EXISTS idx_user_reset_password_token ON t_user(reset_password_token);
CREATE INDEX IF NOT EXISTS idx_user_reset_password_token_expires_at ON t_user(reset_password_token_expires_at);


-- Feedback Table (t_feedback)
-- Based on 000001_create_initial_tables.up.sql and 000006_create_feedback_table.up.sql
CREATE TABLE IF NOT EXISTS t_feedback (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id VARCHAR(36), -- Nullable for anonymous feedback
    email VARCHAR(255),    -- Nullable, for anonymous contact (from 000006)
    content TEXT NOT NULL,
    category VARCHAR(50) NOT NULL DEFAULT 'general_comment', -- Renamed from 'type'
    status VARCHAR(50) NOT NULL DEFAULT 'new',
    user_agent VARCHAR(512),
    referrer VARCHAR(512), -- Was 1024 in 000001, 512 in 000006. Standardizing to 512.
    resolution_notes TEXT, -- From 000006
    resolved_by VARCHAR(36), -- Changed to user_id (VARCHAR(36)) from VARCHAR(100) username
    resolved_at DATETIME, -- From 000006
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at DATETIME, -- From 000006 (for soft deletes)
    FOREIGN KEY (user_id) REFERENCES t_user(id) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (resolved_by) REFERENCES t_user(id) ON DELETE SET NULL ON UPDATE CASCADE -- If resolved_by is a user
);
-- Indexes for t_feedback
CREATE INDEX IF NOT EXISTS idx_t_feedback_user_id ON t_feedback (user_id);
CREATE INDEX IF NOT EXISTS idx_t_feedback_category ON t_feedback (category);
CREATE INDEX IF NOT EXISTS idx_t_feedback_status ON t_feedback (status);
CREATE INDEX IF NOT EXISTS idx_t_feedback_deleted_at ON t_feedback (deleted_at);


-- Play History Table (t_play_history)
-- Based on 000001_create_initial_tables.up.sql and 000011_create_play_history_table.up.sql
CREATE TABLE IF NOT EXISTS t_play_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT, -- Added primary key from 000011
    user_id VARCHAR(36) NOT NULL,
    work_id INTEGER NOT NULL,
    track_path VARCHAR(1024), -- Can be NULL if history is for the whole work (from 000011)
    playback_position_seconds INTEGER DEFAULT 0, -- From 000011
    progress_percentage REAL DEFAULT 0.0, -- From 000011
    is_finished BOOLEAN DEFAULT FALSE, -- From 000011
    -- state JSON NOT NULL, -- Removed from 000001, superseded by specific fields in 000011
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Will be updated on each playback record
    FOREIGN KEY (user_id) REFERENCES t_user(id) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (work_id) REFERENCES t_work(id) ON DELETE CASCADE ON UPDATE CASCADE,
    UNIQUE (user_id, work_id, track_path) -- From 000011
);
-- Indexes for t_play_history
CREATE INDEX IF NOT EXISTS idx_play_history_user_last_played ON t_play_history (user_id, updated_at DESC); -- From 000011
CREATE INDEX IF NOT EXISTS idx_play_history_work ON t_play_history (work_id); -- From 000011


-- User Favorites Table (t_user_favorites)
-- Based on 000014_create_user_favorites_table.up.sql
-- Note: 000014 used user_id INTEGER, but t_user.id is VARCHAR(36). Correcting to VARCHAR(36).
CREATE TABLE IF NOT EXISTS t_user_favorites (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id VARCHAR(36) NOT NULL, -- Corrected type to VARCHAR(36)
    work_id INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, -- Changed from TIMESTAMP
    FOREIGN KEY (user_id) REFERENCES t_user(id) ON DELETE CASCADE,
    FOREIGN KEY (work_id) REFERENCES t_work(id) ON DELETE CASCADE,
    UNIQUE (user_id, work_id)
);
-- Indexes for t_user_favorites
CREATE INDEX IF NOT EXISTS idx_user_favorites_user_id ON t_user_favorites(user_id);
CREATE INDEX IF NOT EXISTS idx_user_favorites_work_id ON t_user_favorites(work_id);


-- Reviews Table (t_reviews)
-- Based on 000001_create_initial_tables.up.sql and 000015_create_reviews_table.up.sql
-- Note: 000015 used user_id INTEGER, but t_user.id is VARCHAR(36). Correcting to VARCHAR(36).
CREATE TABLE IF NOT EXISTS t_reviews (
    id INTEGER PRIMARY KEY AUTOINCREMENT, -- Added from 000015
    user_id VARCHAR(36) NOT NULL, -- Corrected type to VARCHAR(36)
    work_id INTEGER NOT NULL,
    rating INTEGER, 
    review_text TEXT,
    progress VARCHAR(50), -- 'progress' from 000001, 'progress TEXT' from 000015. Using VARCHAR(50) as in 000001.
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, -- Changed from TIMESTAMP
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, -- Changed from TIMESTAMP
    FOREIGN KEY (user_id) REFERENCES t_user(id) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (work_id) REFERENCES t_work(id) ON DELETE CASCADE ON UPDATE CASCADE,
    UNIQUE (user_id, work_id) -- From 000015
);
-- Indexes for t_reviews
CREATE INDEX IF NOT EXISTS idx_reviews_user_id ON t_reviews(user_id); -- From 000015
CREATE INDEX IF NOT EXISTS idx_reviews_work_id ON t_reviews(work_id); -- From 000015
CREATE INDEX IF NOT EXISTS idx_review_progress ON t_reviews(progress); -- From 000001 (idx_review_progress)

-- TagVote Table (t_tag_vote)
-- Based on 000001_create_initial_tables.up.sql
CREATE TABLE IF NOT EXISTS t_tag_vote (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id VARCHAR(36) NOT NULL,
    work_id INTEGER NOT NULL,
    tag_id INTEGER NOT NULL,
    vote INTEGER NOT NULL, -- e.g., +1 or -1
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES t_user(id) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (work_id) REFERENCES t_work(id) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES t_tag(id) ON DELETE CASCADE ON UPDATE CASCADE,
    UNIQUE (user_id, work_id, tag_id)
);

-- Triggers to update 'updated_at' timestamps
CREATE TRIGGER IF NOT EXISTS trigger_t_user_updated_at
AFTER UPDATE ON t_user
FOR EACH ROW
BEGIN
    UPDATE t_user SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
END;

CREATE TRIGGER IF NOT EXISTS trigger_t_feedback_updated_at
AFTER UPDATE ON t_feedback
FOR EACH ROW
BEGIN
    UPDATE t_feedback SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
END;

CREATE TRIGGER IF NOT EXISTS trigger_t_play_history_updated_at
AFTER UPDATE ON t_play_history
FOR EACH ROW
BEGIN
    UPDATE t_play_history SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
END;

-- No updated_at for t_user_favorites as it's typically an immutable record once created.

CREATE TRIGGER IF NOT EXISTS trigger_t_reviews_updated_at
AFTER UPDATE ON t_reviews
FOR EACH ROW
BEGIN
    UPDATE t_reviews SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
END;

CREATE TRIGGER IF NOT EXISTS trigger_t_tag_vote_updated_at
AFTER UPDATE ON t_tag_vote
FOR EACH ROW
BEGIN
    UPDATE t_tag_vote SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
END;