package service

import (
	"context"
	"encoding/base64"
	"errors"
	"fmt"
	"io"
	"mime"
	"net/http"
	"path/filepath" // Added for parseRangeInternal
	"strings"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
	"github.com/Sakura-Byte/kikoeru-go/pkg/config"
	"github.com/Sakura-Byte/kikoeru-go/pkg/http_range"
	"github.com/Sakura-Byte/kikoeru-go/pkg/log"
	"github.com/Sakura-Byte/kikoeru-go/pkg/ports"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/database"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/driver"
	"github.com/Sakura-Byte/kikoeru-go/pkg/utils" // Import the new utils package
)

// FileSystemEntry, ListRequest, LinkResponse, and FileSystemService interface have been moved to github.com/Sakura-Byte/kikoeru-go/pkg/ports

// NewFileSystemServiceImpl is implemented in file_system_service_impl.go

// Tree related code removed in favor of using List's built-in caching

// Added a custom type for context keys to avoid collisions
type contextKey string

// Define constant for the visitedPaths key
const visitedPathsKey contextKey = "visitedPaths"

type fileSystemServiceImpl struct {
	driverManager     driver.DriverManagerGetter
	storageSourceRepo database.StorageSourceRepository
	cfg               *config.AppConfig
	archiveService    *ArchiveService
}

// Ensure fileSystemServiceImpl implements ports.FileSystemService
var _ ports.FileSystemService = (*fileSystemServiceImpl)(nil)

// NewFileSystemServiceImpl creates a new FileSystemService instance.
// This function is implemented here.
func NewFileSystemServiceImpl(
	dm driver.DriverManagerGetter,
	ssr database.StorageSourceRepository,
	appCfg *config.AppConfig,
	archivePasswordService *ArchivePasswordService,
) ports.FileSystemService {
	fsService := &fileSystemServiceImpl{
		driverManager:     dm,
		storageSourceRepo: ssr,
		cfg:               appCfg,
	}

	// Create a storage service adapter for the archive service
	storageAdapter := &storageServiceAdapter{
		fileSystemService: fsService,
	}

	// Initialize the archive service with password service
	fsService.archiveService = NewArchiveService(storageAdapter, archivePasswordService)

	return fsService
}

// storageServiceAdapter adapts FileSystemService to the StorageService interface required by ArchiveService
type storageServiceAdapter struct {
	fileSystemService *fileSystemServiceImpl
}

func (a *storageServiceAdapter) List(ctx context.Context, req ports.ListRequest) ([]ports.FileSystemEntry, error) {
	return a.fileSystemService.List(ctx, req)
}

func (a *storageServiceAdapter) GetStorageSource(ctx context.Context, id uint) (*ports.StorageSource, error) {
	storageSource, err := a.fileSystemService.storageSourceRepo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, apperrors.ErrStorageNotFound) {
			return nil, apperrors.ErrStorageNotFound
		}
		return nil, err
	}

	return &ports.StorageSource{
		ID:      storageSource.ID,
		Enabled: true, // Assuming all storage sources from the repo are enabled
	}, nil
}

func (a *storageServiceAdapter) GetFileStream(ctx context.Context, storageID uint, path string) (io.ReadCloser, string, error) {
	stream, contentType, _, _, _, _, err := a.fileSystemService.GetFileStream(ctx, storageID, path, "")
	return stream, contentType, err
}

func (s *fileSystemServiceImpl) Get(ctx context.Context, storageID uint, path string) (*ports.FileSystemEntry, error) {
	log.Debug(ctx, "FileSystemService Get called", "storage_id", storageID, "path", path)

	storageSource, err := s.storageSourceRepo.GetByID(ctx, storageID)
	if err != nil {
		log.Error(ctx, "Get: Failed to get storage source by ID", "storage_id", storageID, "error", err)
		if errors.Is(err, apperrors.ErrStorageNotFound) {
			return nil, apperrors.ErrStorageNotFound
		}
		return nil, fmt.Errorf("Get: failed to get storage source %d: %w", storageID, err)
	}

	storageDriver, ok := s.driverManager.GetDriver(storageSource.ID)
	if !ok {
		log.Error(ctx, "Get: Storage driver not found", "storage_id", storageID)
		return nil, fmt.Errorf("Get: storage driver for ID %d not found", storageID)
	}

	// First resolve the path to an ID if dealing with an ID-based driver
	resolvedPathOrID, err := s.driverManager.ResolvePath(ctx, storageID, path)
	if err != nil {
		log.Error(ctx, "Get: Failed to resolve path", "storage_id", storageID, "path", path, "error", err)
		if errors.Is(err, driver.ErrFileNotFound) {
			return nil, apperrors.ErrPathNotFound
		}
		return nil, fmt.Errorf("Get: failed to resolve path '%s': %w", path, err)
	}

	alistObj, err := driver.GetItemRecursive(ctx, storageDriver, resolvedPathOrID)
	if err != nil {
		log.Error(ctx, "Get: Failed to get AlistObj via GetItemRecursive",
			"storage_id", storageID,
			"path", path,
			"resolved_path_or_id", resolvedPathOrID,
			"error", err)
		if errors.Is(err, driver.ErrFileNotFound) {
			return nil, apperrors.ErrPathNotFound
		}
		return nil, fmt.Errorf("Get: failed to retrieve object for path '%s': %w", path, err)
	}

	entry := ports.FileSystemEntry{
		Name:         alistObj.GetName(),
		IsDir:        alistObj.IsDir(),
		Path:         alistObj.GetPath(),
		Size:         alistObj.GetSize(),
		ModifiedTime: alistObj.ModTime(),
		Type:         determineFileType(alistObj.GetName(), alistObj.IsDir()),
	}
	log.Debug(ctx, "Get: Successfully retrieved entry", "entry_name", entry.Name, "entry_path", entry.Path)
	return &entry, nil
}

func (s *fileSystemServiceImpl) List(ctx context.Context, req ports.ListRequest) ([]ports.FileSystemEntry, error) {
	log.Debug(ctx, "FileSystemService List called", "storage_id", req.StorageID, "path", req.Path, "refresh", req.Refresh)

	storageSource, err := s.storageSourceRepo.GetByID(ctx, req.StorageID)
	if err != nil {
		log.Error(ctx, "List: Failed to get storage source by ID", "storage_id", req.StorageID, "error", err)
		if errors.Is(err, apperrors.ErrStorageNotFound) {
			return nil, apperrors.ErrStorageNotFound
		}
		return nil, fmt.Errorf("List: failed to get storage source %d: %w", req.StorageID, err)
	}

	// Get the driver type first to determine how to handle path resolution
	isIDBased, isPathBased, err := s.driverManager.GetDriverType(req.StorageID)
	if err != nil {
		log.Error(ctx, "List: Failed to determine driver type", "storage_id", req.StorageID, "error", err)
		return nil, fmt.Errorf("List: failed to determine driver type for storage %d: %w", req.StorageID, err)
	}

	log.Debug(ctx, "Driver type info", "storage_id", req.StorageID, "is_id_based", isIDBased, "is_path_based", isPathBased)

	// First resolve the path to an ID if dealing with an ID-based driver
	resolvedPathOrID, err := s.driverManager.ResolvePath(ctx, req.StorageID, req.Path)
	if err != nil {
		log.Error(ctx, "List: Failed to resolve path",
			"storage_id", req.StorageID,
			"path", req.Path,
			"error", err)
		if errors.Is(err, driver.ErrFileNotFound) {
			return nil, apperrors.ErrPathNotFound
		}
		return nil, fmt.Errorf("List: failed to resolve path '%s': %w", req.Path, err)
	}

	log.Debug(ctx, "List: Successfully resolved path/ID",
		"input_path", req.Path,
		"resolved_path_or_id", resolvedPathOrID,
		"is_id_based", isIDBased)

	storageDriver, ok := s.driverManager.GetDriver(storageSource.ID)
	if !ok {
		log.Error(ctx, "List: Storage driver not found for ID", "storage_id", req.StorageID)
		return nil, fmt.Errorf("List: storage driver for ID %d not found", req.StorageID)
	}

	// Get the directory object
	var dirObj driver.AlistObj

	// For ID-based drivers, use Get with the resolved ID directly when possible
	// Also make sure the resolved path is different from the input path (indicating id resolution)
	// AND ensure it doesn't contain path separators, which would indicate failed resolution
	if isIDBased && !strings.Contains(resolvedPathOrID, "/") && !strings.Contains(resolvedPathOrID, "\\") {
		if getter, ok := storageDriver.(driver.Getter); ok {
			log.Debug(ctx, "List: Using driver.Get with resolved ID",
				"resolved_id", resolvedPathOrID)
			obj, err := getter.Get(ctx, resolvedPathOrID)
			if err == nil && obj.IsDir() {
				dirObj = obj
				log.Debug(ctx, "List: Successfully got directory using Get",
					"resolved_id", resolvedPathOrID)
			} else if err != nil {
				log.Warn(ctx, "List: Failed to get directory with Get, falling back to GetItemRecursive",
					"resolved_id", resolvedPathOrID,
					"error", err)
			} else if !obj.IsDir() {
				log.Warn(ctx, "List: Path resolves to a file, not a directory",
					"resolved_id", resolvedPathOrID)
				return nil, apperrors.ErrPathIsNotDir
			}
		}
	}

	// If not set by the Get method above, use GetItemRecursive as fallback
	if dirObj == nil {
		dirObj, err = driver.GetItemRecursive(ctx, storageDriver, resolvedPathOrID)
		if err != nil {
			log.Error(ctx, "List: Failed to get directory object using GetItemRecursive",
				"resolved_path_or_id", resolvedPathOrID,
				"error", err)
			if errors.Is(err, driver.ErrFileNotFound) {
				return nil, apperrors.ErrPathNotFound
			}
			return nil, fmt.Errorf("List: error getting directory object for '%s': %w", resolvedPathOrID, err)
		}
	}

	if !dirObj.IsDir() {
		log.Error(ctx, "List: Path is not a directory",
			"resolved_path_or_id", resolvedPathOrID,
			"object_name", dirObj.GetName())
		return nil, apperrors.ErrPathIsNotDir
	}

	listArgs := driver.AlistListArgs{
		Refresh: req.Refresh,
	}

	log.Debug(ctx, "List: Attempting to list directory contents with driver",
		"dir_obj_to_list_name", dirObj.GetName(),
		"dir_obj_to_list_path", dirObj.GetPath(),
		"dir_obj_to_list_id", dirObj.GetID())
	rawFiles, err := storageDriver.List(ctx, dirObj, listArgs)
	if err != nil {
		log.Error(ctx, "List: Driver failed to list path",
			"listed_path_name", dirObj.GetName(),
			"error", err)
		return nil, fmt.Errorf("driver error listing path '%s': %w", resolvedPathOrID, err)
	}

	log.Debug(ctx, "Driver List call successful",
		"storage_id", storageSource.ID,
		"resolved_path_or_id", resolvedPathOrID,
		"raw_file_count", len(rawFiles))

	entries := make([]ports.FileSystemEntry, 0, len(rawFiles))
	for i, rawFile := range rawFiles {
		entryPath := rawFile.GetPath()
		log.Debug(ctx, "Processing raw file entry",
			"index", i,
			"raw_file_name", rawFile.GetName(),
			"raw_file_path", rawFile.GetPath(),
			"raw_file_id", rawFile.GetID(),
			"calculated_entry_path", entryPath,
			"is_dir", rawFile.IsDir(),
			"size", rawFile.GetSize(),
			"mod_time", rawFile.ModTime())

		entry := ports.FileSystemEntry{
			Name:         rawFile.GetName(),
			IsDir:        rawFile.IsDir(),
			Path:         entryPath,
			Size:         rawFile.GetSize(),
			ModifiedTime: rawFile.ModTime(),
			Type:         utils.DetermineFileType(rawFile.GetName(), rawFile.IsDir()),
		}
		entries = append(entries, entry)
	}

	return entries, nil
}

func determineFileType(name string, isDir bool) string {
	if isDir {
		return "folder"
	}
	ext := strings.ToLower(filepath.Ext(name))
	switch ext {
	case ".mp3", ".wav", ".flac", ".aac", ".ogg", ".opus", ".m4a":
		return "audio"
	case ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp":
		return "image"
	case ".txt", ".lrc", ".md":
		return "text"
	case ".srt", ".ass", ".vtt", ".sub", ".sbv", ".ssa":
		return "subtitle"
	case ".mp4", ".mkv", ".avi", ".mov", ".webm":
		return "video"
	case ".zip", ".7z", ".rar", ".z01", ".001":
		return "archive"
	default:
		return "unknown"
	}
}

func (s *fileSystemServiceImpl) GetLink(ctx context.Context, storageID uint, path string) (*ports.LinkResponse, error) {
	log.Debug(ctx, "FileSystemService GetLink called", "storage_id", storageID, "path", path)

	storageSource, err := s.storageSourceRepo.GetByID(ctx, storageID)
	if err != nil {
		log.Error(ctx, "Failed to get storage source by ID for GetLink", "storage_id", storageID, "error", err)
		if errors.Is(err, apperrors.ErrStorageNotFound) {
			return nil, apperrors.ErrStorageNotFound
		}
		return nil, fmt.Errorf("storage source %d not found: %w", storageID, err)
	}

	storageDriver, ok := s.driverManager.GetDriver(storageSource.ID)
	if !ok {
		log.Error(ctx, "Storage driver not found for GetLink", "storage_id", storageID)
		return nil, fmt.Errorf("storage driver for ID %d not found", storageID)
	}

	// First resolve the path to an ID if dealing with an ID-based driver
	resolvedPathOrID, err := s.driverManager.ResolvePath(ctx, storageID, path)
	if err != nil {
		log.Error(ctx, "GetLink: Failed to resolve path",
			"storage_id", storageID,
			"path", path,
			"error", err)
		if errors.Is(err, driver.ErrFileNotFound) {
			return nil, apperrors.ErrPathNotFound
		}
		return nil, fmt.Errorf("GetLink: failed to resolve path '%s': %w", path, err)
	}

	log.Debug(ctx, "Resolved path for GetLink",
		"original_path_input", path,
		"resolved_path_or_id", resolvedPathOrID)

	fileObj, err := driver.GetItemRecursive(ctx, storageDriver, resolvedPathOrID)
	if err != nil {
		log.Error(ctx, "GetLink: Failed to get file object",
			"resolved_path_or_id", resolvedPathOrID,
			"error", err)
		if errors.Is(err, driver.ErrFileNotFound) {
			return nil, apperrors.ErrFileNotFound
		}
		return nil, fmt.Errorf("driver error getting file object for '%s': %w", resolvedPathOrID, err)
	}

	if fileObj.IsDir() {
		log.Error(ctx, "GetLink: Cannot get link for a directory",
			"resolved_path_or_id", resolvedPathOrID,
			"object_name", fileObj.GetName())
		return nil, apperrors.ErrPathIsDir
	}

	strategy := storageSource.DownloadStrategy
	if strategy == "" {
		strategy = driver.DownloadStrategyProxy
	}

	drvCfg := storageDriver.Config()
	downloadOptions := drvCfg.DownloadOptions

	// Check if the requested download strategy is available for this driver
	strategyAvailable := false
	for _, option := range downloadOptions {
		if option == strategy {
			strategyAvailable = true
			break
		}
	}

	// If the requested strategy isn't available, use the default
	if !strategyAvailable {
		// Determine default download strategy
		defaultStrategy := drvCfg.DefaultDownload
		if defaultStrategy == "" {
			// Logic similar to manager.go
			hasRedirect := false
			hasProxy := false
			for _, opt := range downloadOptions {
				if opt == driver.DownloadStrategyRedirect {
					hasRedirect = true
				} else if opt == driver.DownloadStrategyProxy {
					hasProxy = true
				}
			}

			if hasRedirect && hasProxy {
				defaultStrategy = driver.DownloadStrategyRedirect
			} else if hasProxy {
				defaultStrategy = driver.DownloadStrategyProxy
			} else if len(downloadOptions) > 0 {
				// If neither redirect nor proxy, use the first available option
				defaultStrategy = downloadOptions[0]
			} else {
				// Fallback if no options are available (should not happen)
				defaultStrategy = driver.DownloadStrategyProxy
				log.Warn(ctx, "Driver has no download options, using proxy as fallback",
					"storage_id", storageID, "driver", drvCfg.Name)
			}
		}

		log.Info(ctx, "Download strategy not available for this driver, using default",
			"requested_strategy", strategy,
			"available_options", downloadOptions,
			"default", defaultStrategy)
		strategy = defaultStrategy
	}

	if strategy == driver.DownloadStrategyProxy {
		if s.cfg == nil || s.cfg.Server.PublicURL == "" {
			log.Error(ctx, "Server PublicURL not configured, cannot generate proxy URL", "storage_id", storageID)
			return nil, errors.New("server public URL not configured for proxy link generation")
		}
		encodedPath := base64.URLEncoding.EncodeToString([]byte(path))
		proxyBase := strings.TrimSuffix(s.cfg.Server.PublicURL, "/")
		finalURL := fmt.Sprintf("%s/api/v1/proxy/%d/%s", proxyBase, storageID, encodedPath)
		log.Debug(ctx, "Generated proxy URL for GetLink", "proxy_url", finalURL, "original_path_for_proxy", path)
		return &ports.LinkResponse{URL: finalURL}, nil
	}

	linkArgs := driver.AlistLinkArgs{}
	alistLink, errLink := storageDriver.Link(ctx, fileObj, linkArgs)

	if errLink != nil {
		if alistLink != nil && strings.HasPrefix(alistLink.URL, "file://") && storageSource.Driver == "local" {
			localFilePath := strings.TrimPrefix(alistLink.URL, "file://")
			log.Info(ctx, "Local driver Link returned file URI; attempting direct file open", "path", path, "local_file_path", localFilePath)
			log.Warn(ctx, "Direct file serving logic found in GetLink, this is likely incorrect and will be ignored.", "path", path)

		}
		log.Error(ctx, "Driver failed to generate link for redirect strategy", "path_for_driver", path, "error", errLink)
		return nil, fmt.Errorf("driver error generating link for '%s': %w", path, errLink)
	}

	if alistLink == nil || alistLink.URL == "" {
		log.Error(ctx, "Driver generated a nil or empty URL for redirect strategy", "path_for_driver", path)
		return nil, fmt.Errorf("driver generated a nil or empty URL for '%s'", path)
	}

	log.Debug(ctx, "Using direct URL (redirect strategy)", "url", alistLink.URL)
	return &ports.LinkResponse{URL: alistLink.URL}, nil
}

func (s *fileSystemServiceImpl) GetFileStream(ctx context.Context, storageID uint, decodedPath string, rangeHeader string) (stream io.ReadCloser, contentType string, originalContentLength int64, actualContentLength int64, filename string, httpStatus int, err error) {
	log.Debug(ctx, "FileSystemService GetFileStream called", "storage_id", storageID, "decoded_path", decodedPath, "range_header", rangeHeader)

	httpStatus = http.StatusInternalServerError

	storageSource, err := s.storageSourceRepo.GetByID(ctx, storageID)
	if err != nil {
		log.Error(ctx, "Failed to get storage source by ID for GetFileStream", "storage_id", storageID, "error", err)
		if errors.Is(err, apperrors.ErrStorageNotFound) {
			return nil, "", 0, 0, "", http.StatusNotFound, apperrors.ErrStorageNotFound
		}
		return nil, "", 0, 0, "", http.StatusInternalServerError, fmt.Errorf("storage source %d not found: %w", storageID, err)
	}

	storageDriver, ok := s.driverManager.GetDriver(storageSource.ID)
	if !ok {
		log.Error(ctx, "Storage driver not found for GetFileStream", "storage_id", storageID)
		return nil, "", 0, 0, "", http.StatusInternalServerError, fmt.Errorf("storage driver for ID %d not found", storageID)
	}

	// First resolve the path to an ID if dealing with an ID-based driver
	resolvedPathOrID, err := s.driverManager.ResolvePath(ctx, storageID, decodedPath)
	if err != nil {
		log.Error(ctx, "GetFileStream: Failed to resolve path",
			"storage_id", storageID,
			"path", decodedPath,
			"error", err)
		if errors.Is(err, driver.ErrFileNotFound) {
			return nil, "", 0, 0, "", http.StatusNotFound, apperrors.ErrPathNotFound
		}
		return nil, "", 0, 0, "", http.StatusInternalServerError, fmt.Errorf("GetFileStream: failed to resolve path '%s': %w", decodedPath, err)
	}

	fileObj, err := driver.GetItemRecursive(ctx, storageDriver, resolvedPathOrID)
	if err != nil {
		log.Error(ctx, "GetFileStream: Failed to get file object",
			"resolved_path_or_id", resolvedPathOrID,
			"error", err)
		if errors.Is(err, driver.ErrFileNotFound) {
			return nil, "", 0, 0, "", http.StatusNotFound, apperrors.ErrFileNotFound
		}
		return nil, "", 0, 0, "", http.StatusInternalServerError, fmt.Errorf("driver error getting file object for '%s': %w", resolvedPathOrID, err)
	}

	if fileObj.IsDir() {
		log.Warn(ctx, "Attempted to get file stream for a directory via proxy", "path", decodedPath)
		return nil, "", 0, 0, "", http.StatusBadRequest, apperrors.ErrPathIsDir
	}

	originalContentLength = fileObj.GetSize()
	actualContentLength = originalContentLength
	httpStatus = http.StatusOK
	var rangeStart int64 = 0
	// var rangeEnd int64 = originalContentLength - 1 // Not directly used with AlistDriver.Proxy

	if rangeHeader != "" && originalContentLength > 0 {
		log.Debug(ctx, "Processing Range header", "range_header", rangeHeader, "original_size", originalContentLength)
		ranges, parseErr := http_range.ParseRange(rangeHeader, originalContentLength)
		if parseErr != nil {
			log.Warn(ctx, "Malformed Range header, ignoring and serving full content", "range_header", rangeHeader, "error", parseErr)
		} else if len(ranges) > 0 {
			hr := ranges[0] // Use the first range
			rangeStart = hr.Start
			actualContentLength = hr.Length
			// rangeEnd = hr.start + hr.length - 1 // Not directly used with AlistDriver.Proxy
			httpStatus = http.StatusPartialContent
			log.Info(ctx, "Serving partial content", "range_start", rangeStart, "range_length", actualContentLength)
		} else if len(ranges) == 0 { // Should not happen if parseErr is nil and rangeHeader is not empty
			log.Warn(ctx, "Range header present but no satisfiable ranges parsed, serving full content", "range_header", rangeHeader)
		}
	}

	contentType = mime.TypeByExtension(filepath.Ext(fileObj.GetName()))
	if contentType == "" {
		contentType = "application/octet-stream"
	}

	// Get filename for Content-Disposition header
	filename = fileObj.GetName()

	// Check if the driver implements the Proxy method (assuming it's on a separate interface)
	streamer, ok := storageDriver.(interface {
		Proxy(ctx context.Context, file driver.AlistObj, httpRange string) (io.ReadCloser, string, int64, int64, int, error)
	})
	if !ok {
		log.Error(ctx, "Storage driver does not implement Proxy method", "storage_id", storageID, "driver_type", fmt.Sprintf("%T", storageDriver))
		return nil, "", 0, 0, "", http.StatusInternalServerError, apperrors.ErrOperationNotSupported
	}

	// Pass the original rangeHeader to the driver's Proxy method
	stream, contentTypeFromDriver, _, actualLengthFromDriver, statusFromDriver, err := streamer.Proxy(ctx, fileObj, rangeHeader)
	if err != nil {
		log.Error(ctx, "Failed to get file stream from storage driver", "storage_id", storageID, "path", decodedPath, "error", err)
		if errors.Is(err, driver.ErrFileNotFound) {
			return nil, "", 0, 0, "", http.StatusNotFound, apperrors.ErrFileNotFound
		}
		if errors.Is(err, driver.ErrNotAFile) {
			return nil, "", 0, 0, "", http.StatusBadRequest, apperrors.ErrPathIsNotFile
		}
		if statusFromDriver >= 400 { // Use statusFromDriver if available
			return nil, "", 0, 0, "", statusFromDriver, fmt.Errorf("driver returned status %d for path '%s': %w", statusFromDriver, decodedPath, err)
		}
		return nil, "", 0, 0, "", http.StatusInternalServerError, fmt.Errorf("failed to get file stream from storage %d at path '%s': %w", storageID, decodedPath, err)
	}
	// Trust the values returned by the driver's Proxy method
	contentType = contentTypeFromDriver
	actualContentLength = actualLengthFromDriver
	httpStatus = statusFromDriver

	log.Debug(ctx, "Successfully got file stream", "storage_id", storageID, "path", decodedPath, "content_type", contentType, "original_len", originalContentLength, "actual_len", actualContentLength, "filename", filename, "status", httpStatus)
	return stream, contentType, originalContentLength, actualContentLength, filename, httpStatus, nil
}

// ListArchive delegates to the archive service
func (s *fileSystemServiceImpl) ListArchive(ctx context.Context, req ports.ArchiveRequest) ([]ports.ArchiveEntry, error) {
	log.Debug(ctx, "FileSystemService ListArchive called", "storage_id", req.StorageID, "path_in_storage", req.PathInStorage, "path_in_archive", req.PathInArchive)
	// Delegate to ArchiveService, assuming ArchiveService also uses/returns ports types or can be adapted.
	// This might require changes in ArchiveService.
	archiveEntries, err := s.archiveService.ListArchive(ctx, req) // req is already ports.ArchiveRequest
	if err != nil {
		return nil, err
	}
	// If archiveService.ListArchive returns a different type, conversion is needed here.
	// For now, assume it returns []ports.ArchiveEntry or a compatible type.
	return archiveEntries, nil
}

// Tree builds a recursive directory tree starting from the specified path
func (s *fileSystemServiceImpl) Tree(ctx context.Context, req ports.TreeRequest) (*ports.TreeNode, error) {
	log.Debug(ctx, "FileSystemService Tree called", "storage_id", req.StorageID, "path", req.Path, "refresh", req.Refresh)

	// Get the root entry first
	rootEntry, err := s.Get(ctx, req.StorageID, req.Path)
	if err != nil {
		log.Error(ctx, "Tree: Failed to get root entry", "storage_id", req.StorageID, "path", req.Path, "error", err)
		return nil, fmt.Errorf("failed to get root entry: %w", err)
	}

	// Create the root node
	rootNode := &ports.TreeNode{
		Entry: *rootEntry,
	}

	// Only build the tree if the root is a directory
	if rootEntry.IsDir {
		// Use a context with value to track visited paths and avoid cycles
		treeCtx := context.WithValue(ctx, visitedPathsKey, make(map[string]bool))
		err = s.buildTree(treeCtx, rootNode, req.StorageID, req.Refresh)
		if err != nil {
			log.Error(ctx, "Tree: Failed to build tree", "storage_id", req.StorageID, "path", req.Path, "error", err)
			return nil, fmt.Errorf("failed to build tree: %w", err)
		}
	}

	return rootNode, nil
}

// buildTree recursively builds the directory tree
func (s *fileSystemServiceImpl) buildTree(ctx context.Context, node *ports.TreeNode, storageID uint, refresh bool) error {
	// Skip if not a directory
	if !node.Entry.IsDir {
		return nil
	}

	// Get visited paths map from context
	visitedPaths, ok := ctx.Value(visitedPathsKey).(map[string]bool)
	if !ok {
		return fmt.Errorf("invalid context: missing visitedPaths")
	}

	// Check if this path has already been visited (to prevent cycles)
	if visitedPaths[node.Entry.Path] {
		return nil
	}
	visitedPaths[node.Entry.Path] = true

	// List files/directories in the current path
	entries, err := s.List(ctx, ports.ListRequest{
		StorageID: storageID,
		Path:      node.Entry.Path,
		Refresh:   refresh,
	})
	if err != nil {
		return fmt.Errorf("failed to list entries for path '%s': %w", node.Entry.Path, err)
	}

	// Create child nodes
	for _, entry := range entries {
		childNode := ports.TreeNode{
			Entry: entry,
		}

		// Recursively build tree for directories
		if entry.IsDir {
			if err := s.buildTree(ctx, &childNode, storageID, refresh); err != nil {
				log.Warn(ctx, "Failed to build subtree", "path", entry.Path, "error", err)
				// Continue with other entries even if one fails
				continue
			}
		}

		// Add child to parent node
		node.Children = append(node.Children, childNode)
	}

	return nil
}

// GetArchiveFileStream delegates to the archive service
func (s *fileSystemServiceImpl) GetArchiveFileStream(ctx context.Context, storageID uint, archivePath string, filePathInArchive string, password string, encoding string, rangeHeader string) (stream io.ReadCloser, contentType string, originalContentLength int64, actualContentLength int64, filename string, httpStatus int, err error) {
	return s.archiveService.GetArchiveFileStream(ctx, storageID, archivePath, filePathInArchive, password, encoding, rangeHeader)
}

// TreeArchive delegates to the archive service
func (s *fileSystemServiceImpl) TreeArchive(ctx context.Context, req ports.ArchiveTreeRequest) (*ports.ArchiveTreeNode, error) {
	log.Debug(ctx, "FileSystemService TreeArchive called", "storage_id", req.StorageID, "path_in_storage", req.PathInStorage, "path_in_archive", req.PathInArchive)
	return s.archiveService.TreeArchive(ctx, req)
}
