package http_range

import (
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"strings"
)

// Range represents an HTTP range request
type Range struct {
	Start  int64
	Length int64
}

// DefaultMaxRange is the default maximum range length
const DefaultMaxRange int64 = 30 * 1024 * 1024 * 1024

// ParseRange parses a Range header from an HTTP request
func ParseRange(rangeHeader string, size int64) ([]Range, error) {
	if rangeHeader == "" {
		return nil, nil
	}

	if !strings.HasPrefix(rangeHeader, "bytes=") {
		return nil, errors.New("invalid range header format: missing bytes= prefix")
	}

	ranges := strings.Split(strings.TrimPrefix(rangeHeader, "bytes="), ",")
	parsedRanges := make([]Range, 0, len(ranges))

	for _, rangeStr := range ranges {
		rangeStr = strings.TrimSpace(rangeStr)
		if rangeStr == "" {
			continue
		}

		parts := strings.Split(rangeStr, "-")
		if len(parts) != 2 {
			return nil, errors.New("invalid range format")
		}

		var start, end int64 = 0, size - 1
		var err error

		// Parse start position
		if parts[0] != "" {
			start, err = strconv.ParseInt(parts[0], 10, 64)
			if err != nil {
				return nil, fmt.Errorf("invalid range start: %w", err)
			}
		}

		// Parse end position
		if parts[1] != "" {
			end, err = strconv.ParseInt(parts[1], 10, 64)
			if err != nil {
				return nil, fmt.Errorf("invalid range end: %w", err)
			}
		} else if parts[0] == "" {
			// Handle suffix range (e.g., bytes=-500)
			start = size - end
			end = size - 1
		}

		// Validate range
		if start < 0 {
			return nil, errors.New("range start position cannot be negative")
		}

		if end < start {
			return nil, errors.New("range end position cannot be less than start position")
		}

		if start >= size {
			return nil, errors.New("range start position exceeds file size")
		}

		if end >= size {
			end = size - 1
		}

		length := end - start + 1
		parsedRanges = append(parsedRanges, Range{Start: start, Length: length})
	}

	return parsedRanges, nil
}

// ParseContentRange parses a Content-Range header value
func ParseContentRange(contentRange string) (start, length int64, err error) {
	if contentRange == "" {
		return 0, 0, errors.New("empty content-range header")
	}

	// Format: bytes start-end/size
	if !strings.HasPrefix(contentRange, "bytes ") {
		return 0, 0, errors.New("invalid content-range format")
	}

	contentRange = strings.TrimPrefix(contentRange, "bytes ")
	parts := strings.Split(contentRange, "/")
	if len(parts) != 2 {
		return 0, 0, errors.New("invalid content-range format")
	}

	rangeParts := strings.Split(parts[0], "-")
	if len(rangeParts) != 2 {
		return 0, 0, errors.New("invalid content-range format")
	}

	// Parse start position
	start, err = strconv.ParseInt(strings.TrimSpace(rangeParts[0]), 10, 64)
	if err != nil {
		return 0, 0, fmt.Errorf("invalid start position: %w", err)
	}

	// Parse end position
	end, err := strconv.ParseInt(strings.TrimSpace(rangeParts[1]), 10, 64)
	if err != nil {
		return 0, 0, fmt.Errorf("invalid end position: %w", err)
	}

	// Parse total size - we don't use this value but we validate it
	if parts[1] != "*" {
		_, err = strconv.ParseInt(strings.TrimSpace(parts[1]), 10, 64)
		if err != nil {
			return 0, 0, fmt.Errorf("invalid size: %w", err)
		}
	}

	length = end - start + 1
	return start, length, nil
}

// ApplyRangeToHttpHeader applies a range to an HTTP header
func ApplyRangeToHttpHeader(r Range, header http.Header) http.Header {
	if r.Start > 0 || r.Length > 0 {
		if r.Length < 0 {
			header.Set("Range", fmt.Sprintf("bytes=%d-", r.Start))
		} else {
			header.Set("Range", fmt.Sprintf("bytes=%d-%d", r.Start, r.Start+r.Length-1))
		}
	}
	return header
}

// SetContentRange sets the Content-Range header for a partial content response
func SetContentRange(header http.Header, start, length, size int64) {
	header.Set("Content-Range", fmt.Sprintf("bytes %d-%d/%d", start, start+length-1, size))
}

// IsSatisfiable checks if a range request is satisfiable
func IsSatisfiable(ranges []Range, size int64) bool {
	if len(ranges) == 0 {
		return true
	}

	for _, r := range ranges {
		if r.Start >= size {
			return false
		}
	}

	return true
}
