-- migrations_temp/000004_create_playlist_tables.up.sql
-- Defines tables related to playlists and their items.
-- This consolidates initial creation and subsequent alterations.

-- Playlist Table (t_playlist)
-- Based on 000001, 000004_create_playlist_tables.up.sql and 000012_add_visibility_to_playlist.up.sql
CREATE TABLE IF NOT EXISTS t_playlist (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id VARCHAR(36) NOT NULL, -- User who owns the playlist
    name VARCHAR(255) NOT NULL,
    description TEXT,
    visibility VARCHAR(20) NOT NULL DEFAULT 'private', -- 'private', 'unlisted', 'public' (from 000012)
    -- item_count INTEGER NOT NULL DEFAULT 0, -- This can be calculated dynamically or managed by application logic
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES t_user(id) ON DELETE CASCADE ON UPDATE CASCADE
);
-- Indexes for t_playlist
CREATE INDEX IF NOT EXISTS idx_playlist_user_id ON t_playlist(user_id);
CREATE INDEX IF NOT EXISTS idx_playlist_visibility ON t_playlist(visibility); -- From 000012

-- Playlist Item Table (t_playlist_item)
-- Based on 000001 and 000004_create_playlist_tables.up.sql
CREATE TABLE IF NOT EXISTS t_playlist_item (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    playlist_id INTEGER NOT NULL,
    work_id INTEGER NOT NULL,
    track_path VARCHAR(1024) NULL, -- Specific track within the work, NULL if adding the whole work
    "order" INTEGER NOT NULL DEFAULT 0, -- Order of the item within the playlist
    added_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Renamed from created_at for clarity
    FOREIGN KEY (playlist_id) REFERENCES t_playlist(id) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (work_id) REFERENCES t_work(id) ON DELETE CASCADE ON UPDATE CASCADE,
    UNIQUE (playlist_id, work_id, track_path) -- Ensures a work/track is not added multiple times to the same playlist
);
-- Indexes for t_playlist_item
CREATE INDEX IF NOT EXISTS idx_playlist_item_playlist_id ON t_playlist_item(playlist_id);
CREATE INDEX IF NOT EXISTS idx_playlist_item_work_id ON t_playlist_item(work_id);
CREATE INDEX IF NOT EXISTS idx_playlist_item_track_path ON t_playlist_item(track_path);

-- Triggers to update 'updated_at' timestamps
CREATE TRIGGER IF NOT EXISTS trigger_t_playlist_updated_at
AFTER UPDATE ON t_playlist
FOR EACH ROW
BEGIN
    UPDATE t_playlist SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
END;

-- No updated_at for t_playlist_item as it's typically an immutable record once added, order changes are on t_playlist or handled by app.
-- If items themselves can be updated (e.g., notes on an item), then a trigger would be needed.