<template>
  <div class="list-page q-pa-md" :class="{ 'bg-dark text-white': $q.dark.isActive }">
    <div class="text-h5 q-mb-md">{{ pageTitle }}</div>

    <q-input
      :dark="$q.dark.isActive"
      filled
      v-model="searchText"
      :placeholder="t('list.searchPlaceholder', { type: searchType })"
      class="q-mb-md"
    >
      <template v-slot:append>
        <q-icon name="search" />
      </template>
    </q-input>

    <div class="row q-col-gutter-md" v-if="!loading && filteredItems.length > 0">
      <div class="col-12 col-sm-6 col-md-3" v-for="item in filteredItems" :key="item.id">
        <q-item
          clickable
          :to="linkTo(item)"
          class="dense q-pa-xs list-item"
          :class="{ 'bg-dark': $q.dark.isActive }"
        >
          <q-item-section>
            <div class="row items-center no-wrap justify-between">
              <div class="ellipsis">{{ item.name }}</div>
              <q-chip
                size="sm"
                dense
                color="primary"
                text-color="white"
                class="q-ml-sm"
              >
                {{ item.count || 0 }}
              </q-chip>
            </div>
          </q-item-section>
        </q-item>
      </div>
    </div>

    <div v-if="loading" class="row justify-center q-my-md">
      <q-spinner-dots color="primary" size="40px" />
    </div>

    <div v-if="!loading && filteredItems.length === 0" class="text-center q-my-xl" :class="{ 'text-grey-6': $q.dark.isActive, 'text-grey-7': !$q.dark.isActive }">
      {{ t('list.noData') }}
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, getCurrentInstance } from 'vue'
import { useI18n } from 'vue-i18n'
import { useQuasar } from 'quasar'
import { useNotification } from '../composables/useNotification'

defineOptions({
  name: 'MetadataList'
})

const props = defineProps({
  restrict: {
    type: String,
    required: true,
    validator: (value) => ['circles', 'tags', 'vas'].includes(value)
  }
})

const { t } = useI18n()
const $q = useQuasar()
const { proxy } = getCurrentInstance()
const { showErrorNotification } = useNotification()

// Reactive data
const items = ref([])
const searchText = ref('')
const loading = ref(false)

// Computed properties
const pageTitle = computed(() => {
  const titles = {
    circles: t('nav.circles'),
    tags: t('nav.tags'),
    vas: t('nav.voiceActors')
  }
  return titles[props.restrict] || ''
})

const searchType = computed(() => {
  const types = {
    circles: t('list.circle'),
    tags: t('list.tag'),
    vas: t('list.va')
  }
  return types[props.restrict] || ''
})

const apiEndpoint = computed(() => {
  return `/api/v1/${props.restrict}`
})

const filteredItems = computed(() => {
  if (!searchText.value) {
    return items.value
  }

  const searchLower = searchText.value.toLowerCase()
  return items.value.filter(item =>
    item.name.toLowerCase().includes(searchLower)
  )
})

// Methods
const loadItems = async () => {
  loading.value = true
  try {
    const response = await proxy.$api.get(apiEndpoint.value)
    items.value = response.data || []
  } catch (error) {
    if (error.response?.status !== 401) {
      showErrorNotification(error.response?.data?.error || error.message || t('notification.loadListFailed', { type: props.restrict }))
    }
  } finally {
    loading.value = false
  }
}

const linkTo = (item) => {
  const params = {}
  if (props.restrict === 'circles') {
    params.keyword = `$circle:${item.name}$`
  } else if (props.restrict === 'tags') {
    params.keyword = `$tag:${item.name}$`
  } else if (props.restrict === 'vas') {
    params.keyword = `$va:${item.name}$`
  }

  return {
    name: 'works',
    query: params
  }
}

// Watchers
watch(() => props.restrict, () => {
  loadItems()
})

// Lifecycle
onMounted(() => {
  loadItems()
})


</script>

<!-- Styles moved to /src/css/components/List.scss -->
