package ports

import (
	"context"

	"github.com/Sakura-Byte/kikoeru-go/pkg/auth"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"

	// "github.com/Sakura-Byte/kikoeru-go/pkg/scanner" // Removed import
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/database"
)

// ScanStatus and ScannerStatusResponse types have been moved to github.com/Sakura-Byte/kikoeru-go/pkg/ports/scanner_port.go

// BackgroundTaskService defines the application service interface for managing background tasks.
// This interface is a "driven" port, implemented by the service layer and used by handlers.
// SetScannerService and SetWorkService methods are removed from the interface to break import cycle.
type BackgroundTaskService interface {
	SubmitTask(ctx context.Context, taskType models.TaskType, payload interface{}, claims *auth.Claims) (*models.BackgroundTask, error)
	GetTaskByID(ctx context.Context, taskID string) (*models.BackgroundTask, error)
	ListTasks(ctx context.Context, params database.ListBackgroundTaskParams) ([]*models.BackgroundTask, int64, error)
	CancelTask(ctx context.Context, taskID string, claims *auth.Claims) error

	// These methods are primarily for internal service use or by other services,
	// but are part of the public interface for setting dependencies.
	UpdateTaskStatus(ctx context.Context, taskID string, status models.TaskStatus, message string) error
	UpdateTaskProgress(ctx context.Context, taskID string, progress float64, message string) error
	SetTaskResult(ctx context.Context, taskID string, resultPayload interface{}, status models.TaskStatus, errorMessage string) error

	// Setters for dependencies are now on the concrete implementation, not the interface.
	// SetScannerService(scannerService *scanner.ScannerService)
	// SetWorkService(workService WorkService)
}
