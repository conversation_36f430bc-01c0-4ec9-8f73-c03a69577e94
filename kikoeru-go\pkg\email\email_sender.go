package email

import (
	"context"
	"errors"
	"fmt"
	"net/smtp"
	"sync"
	"time"

	"github.com/Sakura-Byte/kikoeru-go/pkg/config"
	"github.com/Sakura-Byte/kikoeru-go/pkg/log"
)

// EmailSender defines the interface for sending emails.
// This allows for easier testing and potential future replacement of the SMTP implementation.
type EmailSender interface {
	SendEmail(to, subject, body, ipAddress string) error
}

// SMTPEmailSender implements EmailSender using net/smtp.
type SMTPEmailSender struct {
	smtpCfg         *config.SMTPConfig
	authCfg         *config.AuthConfig
	mu              sync.Mutex
	emailLastSent   map[string]time.Time
	ipLastSent      map[string]time.Time
	emailDailyCount map[string]map[string]int // email -> YYYY-MM-DD -> count
	ipDailyCount    map[string]map[string]int // ip -> YYYY-MM-DD -> count
}

// NewSMTPEmailSender creates a new SMTPEmailSender.
func NewSMTPEmailSender(smtpCfg config.SMTPConfig, authCfg config.AuthConfig) *SMTPEmailSender {
	return &SMTPEmailSender{
		smtpCfg:         &smtpCfg,
		authCfg:         &authCfg,
		emailLastSent:   make(map[string]time.Time),
		ipLastSent:      make(map[string]time.Time),
		emailDailyCount: make(map[string]map[string]int),
		ipDailyCount:    make(map[string]map[string]int),
	}
}

func (s *SMTPEmailSender) checkLimits(to, ipAddress string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	now := time.Now()
	today := now.Format("2006-01-02")

	// --- Cooldown Checks ---
	// Email cooldown
	if lastSent, ok := s.emailLastSent[to]; ok {
		if now.Sub(lastSent) < time.Duration(s.authCfg.EmailCooldownSeconds)*time.Second {
			return fmt.Errorf("email cooldown active for %s, please wait", to)
		}
	}
	// IP cooldown
	if lastSent, ok := s.ipLastSent[ipAddress]; ok {
		if now.Sub(lastSent) < time.Duration(s.authCfg.IPCooldownSeconds)*time.Second {
			return fmt.Errorf("IP cooldown active for %s, please wait", ipAddress)
		}
	}

	// --- Daily Limit Checks ---
	// Email daily limit
	if s.emailDailyCount[to] == nil {
		s.emailDailyCount[to] = make(map[string]int)
	}
	// Clean up old dates for this email
	for date := range s.emailDailyCount[to] {
		if date != today {
			delete(s.emailDailyCount[to], date)
		}
	}
	if s.emailDailyCount[to][today] >= s.authCfg.EmailDailyLimit {
		return fmt.Errorf("daily email limit reached for %s", to)
	}

	// IP daily limit
	if s.ipDailyCount[ipAddress] == nil {
		s.ipDailyCount[ipAddress] = make(map[string]int)
	}
	// Clean up old dates for this IP
	for date := range s.ipDailyCount[ipAddress] {
		if date != today {
			delete(s.ipDailyCount[ipAddress], date)
		}
	}
	if s.ipDailyCount[ipAddress][today] >= s.authCfg.IPDailyLimit {
		return fmt.Errorf("daily IP limit reached for %s", ipAddress)
	}

	return nil
}

func (s *SMTPEmailSender) updateLimits(to, ipAddress string) {
	s.mu.Lock()
	defer s.mu.Unlock()

	now := time.Now()
	today := now.Format("2006-01-02")

	s.emailLastSent[to] = now
	s.ipLastSent[ipAddress] = now

	if s.emailDailyCount[to] == nil {
		s.emailDailyCount[to] = make(map[string]int)
	}
	s.emailDailyCount[to][today]++

	if s.ipDailyCount[ipAddress] == nil {
		s.ipDailyCount[ipAddress] = make(map[string]int)
	}
	s.ipDailyCount[ipAddress][today]++
}

// SendEmail sends an email using SMTP after checking rate limits.
func (s *SMTPEmailSender) SendEmail(to, subject, body, ipAddress string) error {
	if !s.authCfg.EnableEmailFeatures {
		log.Info(context.Background(), "Email features are disabled, skipping email send.", "to", to, "subject", subject)
		return nil // Or an error indicating features are off, depending on desired behavior
	}
	if s.smtpCfg.Host == "" || s.smtpCfg.Sender == "" {
		log.Warn(context.Background(), "SMTP host or sender not configured, skipping email send.")
		return errors.New("SMTP host or sender not configured")
	}

	// Check rate limits
	if err := s.checkLimits(to, ipAddress); err != nil {
		log.Warn(context.Background(), "Email sending blocked by rate limit", "to", to, "ip", ipAddress, "error", err)
		return err
	}

	auth := smtp.PlainAuth("", s.smtpCfg.Username, s.smtpCfg.Password, s.smtpCfg.Host)
	addr := fmt.Sprintf("%s:%d", s.smtpCfg.Host, s.smtpCfg.Port)

	msg := []byte("To: " + to + "\r\n" +
		"From: " + s.smtpCfg.Sender + "\r\n" +
		"Subject: " + subject + "\r\n" +
		"Content-Type: text/plain; charset=UTF-8\r\n" + // Specify content type
		"\r\n" +
		body)

	err := smtp.SendMail(addr, auth, s.smtpCfg.Sender, []string{to}, msg)
	if err != nil {
		log.Error(context.Background(), "Failed to send email", "to", to, "ip", ipAddress, "subject", subject, "error", err)
		return fmt.Errorf("failed to send email: %w", err)
	}

	// Update rate limits after successful send
	s.updateLimits(to, ipAddress)

	log.Info(context.Background(), "Email sent successfully", "to", to, "ip", ipAddress, "subject", subject)
	return nil
}

// DummyEmailSender is a placeholder that logs email attempts without sending.
type DummyEmailSender struct {
}

// NewDummyEmailSender creates a new DummyEmailSender.
func NewDummyEmailSender() *DummyEmailSender {
	return &DummyEmailSender{
	}
}

func (s *DummyEmailSender) SendEmail(to, subject, body, ipAddress string) error {
	log.Info(context.Background(), "Simulating email sending (email features disabled or SMTP not configured)",
		"to", to, "ip", ipAddress, "subject", subject, "body_preview", body[:min(len(body), 100)]+"...") // Log a preview
	return nil // Simulate success
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
