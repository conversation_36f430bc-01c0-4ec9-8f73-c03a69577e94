package models

import "time"

// StorageSource defines the GORM model for storing storage source configurations.
type StorageSource struct {
	ID uint `json:"id" gorm:"primaryKey;autoIncrement"`
	// MountPath string `json:"mount_path" gorm:"uniqueIndex;not null"` // Removed
	Order    int    `json:"order" gorm:"default:0"`
	Driver   string `json:"driver" gorm:"not null"`
	Addition string `json:"addition" gorm:"type:text"` // JSON string for driver-specific config, including root_folder_path or root_folder_id
	Remark   string `json:"remark"`                    // Admin remark, used as display name in UI
	Disabled bool   `json:"disabled" gorm:"default:false"`

	// DefaultRoot field is removed. Root configuration is now solely managed within the 'Addition' JSON.

	DownloadStrategy string `json:"download_strategy,omitempty" gorm:"column:download_strategy;type:varchar(10);default:'proxy';not null"`

	// CacheExpiration in minutes. 0 or negative means no cache or use driver/system default.
	CacheExpiration int `json:"cache_expiration,omitempty" gorm:"column:cache_expiration;default:0"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// TableName specifies the GORM table name for the StorageSource model.
func (StorageSource) TableName() string {
	return "t_storage_sources"
}
