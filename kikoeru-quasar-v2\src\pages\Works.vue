<template>
  <div>
    <div class="text-h5 text-weight-regular q-ma-md">
      {{ pageTitleComputed }}
      <span v-show="pagination.total_items">
        ({{ pagination.total_items }})
      </span>
    </div>

    <div :class="`row justify-center ${listMode ? 'list' : 'q-mx-md'}`">
      <q-infinite-scroll @load="onLoad" :offset="250" :disable="stopLoad" style="max-width: 1680px;" class="col">
        <div v-show="works.length" class="row justify-between q-mb-md q-mr-sm">
          <!-- 排序选择框 -->
          <q-select
            dense
            rounded
            outlined
            transition-show="scale"
            transition-hide="scale"
            v-model="sortOption"
            :options="sortOptionsComputed"
            :label="t('worksPage.sortBy')"
            class="col-auto"
          />

          <!-- 切换显示模式按钮 -->
          <q-btn-toggle
            dense
            spread
            rounded
            v-model="listMode"
            toggle-color="primary"
            :color="$q.dark.isActive ? 'dark' : 'white'"
            text-color="primary"
            :options="[
              { icon: 'apps', value: false, slot: 'grid-view' },
              { icon: 'list', value: true, slot: 'list-view' }
            ]"
            style="width: 85px;"
            class="col-auto"
          >
            <template v-slot:grid-view>
              <q-tooltip>{{ t('worksPage.gridView') }}</q-tooltip>
            </template>
            <template v-slot:list-view>
              <q-tooltip>{{ t('worksPage.listView') }}</q-tooltip>
            </template>
          </q-btn-toggle>

          <q-btn-toggle
            dense
            spread
            rounded
            v-model="showLabel"
            toggle-color="primary"
            :color="$q.dark.isActive ? 'dark' : 'white'"
            text-color="primary"
            :options="[
              { icon: 'label', value: true, slot: 'show-tags' },
              { icon: 'label_off', value: false, slot: 'hide-tags' }
            ]"
            style="width: 85px;"
            class="col-auto"
            v-if="$q.screen.width > 700 && listMode"
          >
            <template v-slot:show-tags>
              <q-tooltip>{{ t('worksPage.showTags') }}</q-tooltip>
            </template>
            <template v-slot:hide-tags>
              <q-tooltip>{{ t('worksPage.hideTags') }}</q-tooltip>
            </template>
          </q-btn-toggle>

          <q-btn-toggle
            dense
            spread
            rounded
            :disable="$q.screen.width < 1120"
            v-model="detailMode"
            toggle-color="primary"
            :color="$q.dark.isActive ? 'dark' : 'white'"
            text-color="primary"
            :options="[
              { icon: 'zoom_in', value: true, slot: 'detail-view' },
              { icon: 'zoom_out', value: false, slot: 'compact-view' },
            ]"
            style="width: 85px;"
            class="col-auto"
            v-if="$q.screen.width > 700 && !listMode"
          >
            <template v-slot:detail-view>
              <q-tooltip>{{ t('worksPage.detailView') }}</q-tooltip>
            </template>
            <template v-slot:compact-view>
              <q-tooltip>{{ t('worksPage.compactView') }}</q-tooltip>
            </template>
          </q-btn-toggle>

        </div>

        <q-list v-if="listMode" bordered separator class="shadow-2">
          <WorkListItem v-for="work_item in works" :key="work_item.id" :metadata="work_item" :showLabel="showLabel && $q.screen.width > 700" />
        </q-list>

        <div v-else class="row q-col-gutter-x-md q-col-gutter-y-lg">
          <div class="col-xs-12 col-sm-6 col-md-4" :class="detailMode ? 'col-lg-3 col-xl-3': 'col-lg-2 col-xl-2'" v-for="work_item in works" :key="work_item.id">
            <WorkCard :metadata="work_item" :thumbnailMode="!detailMode" class="fit"/>
          </div>
        </div>

        <div v-show="stopLoad" class="q-mt-lg q-mb-xl text-h6 text-bold text-center">{{ t('history.endOfList') }}</div>

        <template v-slot:loading>
          <div class="row justify-center q-my-md">
            <q-spinner-dots color="primary" size="40px" />
          </div>
        </template>
      </q-infinite-scroll>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onActivated, onDeactivated, watch, getCurrentInstance } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'
import { useQuasar } from 'quasar'
import WorkCard from '../components/WorkCard.vue'
import WorkListItem from '../components/WorkListItem.vue'
import { useNotification } from '../composables/useNotification'

// Component name for multi-word requirement
defineOptions({
  name: 'WorksList'
})

// Composables
const { t } = useI18n()
const route = useRoute()
const $q = useQuasar()
const { proxy } = getCurrentInstance()
const { showErrorNotification } = useNotification()

// Reactive data
const listMode = ref(false)
const showLabel = ref(true)
const detailMode = ref(true)
const stopLoad = ref(false)
const works = ref([])
const page = ref(1)
const pagination = ref({ current_page: 0, per_page: 20, total_items: 0 })

// Methods
const requestWorks = async () => {
  page.value = requestParams.value.page
  try {
    const response = await proxy.$api.get(url.value, {
      params: requestParams.value
    })

    const data = response.data
    const worksData = data.items || []

    works.value = (requestParams.value.page === 1) ? worksData.concat() : works.value.concat(worksData)
    pagination.value = data.pagination || { current_page: 0, per_page: 20, total_items: 0 }

    if (works.value.length >= pagination.value.total_items) {
      stopLoad.value = true
    }
  } catch (error) {
    if (error.response?.status !== 401) {
      showErrorNotification(error.response?.data?.error || error.message || t('notification.loadWorksFailed'))
    }
    stopLoad.value = true
  }
}

const reset = () => {
  stopLoad.value = true
  works.value = []
  pagination.value = { current_page: 0, per_page: 20, total_items: 0 }
  requestWorks().then(() => {
    if (works.value.length < pagination.value.total_items) {
      stopLoad.value = false
    }
  })
}

const onLoad = (index, done) => {
  requestWorks().then(() => done())
}

// Computed properties
const pageTitleComputed = computed(() => {
  const query = route.query
  if (query.keyword) {
    // Check if keyword contains special search syntax
    const circleMatch = query.keyword.match(/\$circle:(.+?)\$/)
    const tagMatch = query.keyword.match(/\$tag:(.+?)\$/)
    const vaMatch = query.keyword.match(/\$va:(.+?)\$/)

    if (circleMatch) {
      return t('worksPage.circleWorksTitle', { circle: circleMatch[1] })
    } else if (tagMatch) {
      return t('worksPage.tagWorksTitle', { tag: tagMatch[1] })
    } else if (vaMatch) {
      return t('worksPage.vaWorksTitle', { va: vaMatch[1] })
    } else {
      return t('worksPage.searchResultTitle', { keyword: query.keyword })
    }
  }
  return t('nav.mediaLibrary')
})

const sortOptionsComputed = computed(() => [
  {
    label: t('worksPage.sortOptions.releaseDesc'),
    order: 'release',
    sort: 'desc'
  },
  {
    label: t('worksPage.sortOptions.ratingDesc'),
    order: 'rate_average_2dp',
    sort: 'desc'
  },
  {
    label: t('worksPage.sortOptions.releaseAsc'),
    order: 'release',
    sort: 'asc'
  },
  {
    label: t('worksPage.sortOptions.salesDesc'),
    order: 'dl_count',
    sort: 'desc'
  },
  {
    label: t('worksPage.sortOptions.priceAsc'),
    order: 'price',
    sort: 'asc'
  },
  {
    label: t('worksPage.sortOptions.priceDesc'),
    order: 'price',
    sort: 'desc'
  },
  {
    label: t('worksPage.sortOptions.reviewsDesc'),
    order: 'review_count',
    sort: 'desc'
  },
  {
    label: t('worksPage.sortOptions.createdDesc'),
    order: 'create_date',
    sort: 'desc'
  }
])

const sortOption = computed({
  get() {
    const defaultSort = {
      label: t('worksPage.sortOptions.releaseDesc'),
      order: 'release',
      sort: 'desc'
    }
    if (localStorage.sortOption) {
      try {
        const storedOption = JSON.parse(localStorage.sortOption)
        const matchingOption = sortOptionsComputed.value.find(opt => opt.order === storedOption.order && opt.sort === storedOption.sort)
        return matchingOption || defaultSort
      } catch {
        localStorage.removeItem('sortOption')
        return defaultSort
      }
    }
    return defaultSort
  },
  set(newVal) {
    localStorage.sortOption = JSON.stringify({ order: newVal.order, sort: newVal.sort })
    reset()
  }
})

const url = computed(() => {
  const query = route.query
  if (query.keyword) {
    return `/api/v1/search/${encodeURIComponent(query.keyword)}`
  } else {
    return '/api/v1/works'
  }
})

const requestParams = computed(() => ({
  order: sortOption.value.order,
  sort: sortOption.value.sort,
  page: pagination.value.current_page + 1 || 1,
  page_size: 20
}))

// Load preferences from localStorage
onMounted(() => {
  if (localStorage.showLabel) {
    showLabel.value = (localStorage.showLabel === 'true')
  }
  if (localStorage.listMode) {
    listMode.value = (localStorage.listMode === 'true')
  }
  if (localStorage.detailMode) {
    detailMode.value = (localStorage.detailMode === 'true')
  }
})

// Lifecycle hooks
onActivated(() => {
  if (works.value.length < pagination.value.total_items) {
    stopLoad.value = false
  }
})

onDeactivated(() => {
  stopLoad.value = true
})

// Watchers
watch(url, () => {
  reset()
})

watch(showLabel, (newVal) => {
  localStorage.showLabel = newVal
})

watch(listMode, (newVal) => {
  localStorage.listMode = newVal
})

watch(detailMode, (newVal) => {
  localStorage.detailMode = newVal
})

watch(() => route.query, () => {
  // Handle route query changes
}, { deep: true, immediate: true })
</script>

<!-- Styles moved to /src/css/components/Works.scss -->
