package models

import (
	"database/sql" // Added for sql.NullString
	"time"
)

const (
	PlaylistVisibilityPrivate  = "private"
	PlaylistVisibilityUnlisted = "unlisted"
	PlaylistVisibilityPublic   = "public"
)

type Playlist struct {
	ID          uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	UserID      string    `gorm:"type:varchar(36);index;not null" json:"user_id"` // Changed from Username
	Name        string    `gorm:"type:varchar(255);not null" json:"name"`
	Description string    `gorm:"type:text" json:"description"`
	Visibility  string    `gorm:"type:varchar(20);default:'private';not null;index" json:"visibility"`
	ItemCount   int       `gorm:"-" json:"item_count"` // Calculated field
}

func (Playlist) TableName() string {
	return "t_playlist"
}

type PlaylistItem struct {
	ID         uint           `gorm:"primaryKey;autoIncrement" json:"id"`
	CreatedAt  time.Time      `json:"-"`
	PlaylistID uint           `gorm:"index;not null" json:"playlist_id"`
	WorkID     uint           `gorm:"index;not null" json:"work_id"`
	TrackPath  sql.NullString `gorm:"type:varchar(1024);index" json:"track_path,omitempty"` // Added TrackPath
	Order      int            `gorm:"not null;default:0" json:"order"`

	Work *Work `gorm:"foreignKey:WorkID" json:"work,omitempty"`
}

func (PlaylistItem) TableName() string {
	return "t_playlist_item"
}

// PlaylistWithItems has been moved to pkg/dto/playlist/playlist_dto.go as PlaylistWithItemsDTO
