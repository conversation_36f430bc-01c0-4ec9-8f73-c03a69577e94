import { defineBoot } from '#q-app/wrappers'
import axios from 'axios'
import { LocalStorage } from 'quasar'

// Create axios instance for the API
const api = axios.create({
  baseURL: '',
  timeout: 10000
})

// Add request interceptor to include auth token
api.interceptors.request.use(
  config => {
    const token = LocalStorage.getItem('jwt-token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// Add response interceptor to handle auth errors
api.interceptors.response.use(
  response => response,
  error => {
    if (error.response?.status === 401 && LocalStorage.getItem('jwt-token')) {
      // Handle token invalidation using Pinia store
      try {
        import('../stores/user.js').then(({ useUserStore }) => {
          const userStore = useUserStore()
          userStore.logout({ silent: true })
          userStore.setShowLoginDialog(true)
        })
      } catch (storeError) {
        console.warn('Failed to access Pinia user store:', storeError)
        // Fallback if store is not available
        LocalStorage.remove('jwt-token')
      }
    }

    // Global error handling for API errors
    // Don't show notification for 401 errors as they're already handled
    // Don't show notification for 404 errors on review endpoints (normal when user hasn't reviewed)
    const isReview404 = error.response?.status === 404 &&
                       error.config?.url?.includes('/api/v1/review/work/')

    if (error.response && error.response.status !== 401 && !isReview404) {
      // Use Pinia store for global notification
      try {
        import('../stores/notification.js').then(({ useNotificationStore }) => {
          const notificationStore = useNotificationStore()
          const errorMessage = error.response.data?.error ||
                              error.response.data?.message ||
                              `${error.response.status}: ${error.message}` ||
                              'An unexpected error occurred'
          notificationStore.notify({
            type: 'negative',
            message: errorMessage
          })
        })
      } catch (storeError) {
        console.warn('Failed to access notification store:', storeError)
      }
    }

    return Promise.reject(error)
  }
)

export default defineBoot(({ app }) => {
  // for use inside Vue files (Options API) through this.$axios and this.$api

  app.config.globalProperties.$axios = axios
  // ^ ^ ^ this will allow you to use this.$axios (for Vue Options API form)
  //       so you won't necessarily have to import axios in each vue file

  app.config.globalProperties.$api = api
  // ^ ^ ^ this will allow you to use this.$api (for Vue Options API form)
  //       so you can easily perform requests against your app's API
})

export { api }
