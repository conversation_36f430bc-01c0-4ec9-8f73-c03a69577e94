package zip

import (
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"sort"
	"strings"

	"github.com/Sakura-Byte/kikoeru-go/pkg/log"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/stream"
)

// MultipartReader combines multiple streams into one for reading multipart ZIP archives
type MultipartReader struct {
	streams []*models.StreamWithSeek
	pos     int64
	current int
}

// MultiReaderAt implements io.ReaderAt for multiple streams combined
type MultiReaderAt struct {
	streams   []*models.StreamWithSeek
	offsets   []int64 // Cumulative offsets for each stream
	totalSize int64
}

// sortMultipartStreams sorts streams in the correct order for multipart ZIP files
// Based on <PERSON><PERSON>'s implementation with improvements
func sortMultipartStreams(streams []*models.StreamWithSeek) []*models.StreamWithSeek {
	if len(streams) <= 1 {
		return streams
	}

	// Create a copy to avoid modifying the original slice
	sorted := make([]*models.StreamWithSeek, len(streams))
	copy(sorted, streams)

	// Apply <PERSON><PERSON>'s logic: if second file is .z01, move z01 files to front
	if len(sorted) > 1 && filepath.Ext(sorted[1].GetName()) == ".z01" {
		log.Debug(context.Background(), "Detected .z01 format, applying Alist sorting logic")
		// Move all .z01, .z02, etc. files to the front, then .zip file
		sorted = append(sorted[1:], sorted[0])
	}

	// Additional sorting for proper order within z01 files
	sort.Slice(sorted, func(i, j int) bool {
		nameI := sorted[i].GetName()
		nameJ := sorted[j].GetName()
		extI := filepath.Ext(nameI)
		extJ := filepath.Ext(nameJ)

		// Handle .z01/.z02/.z03 format - these should come first in order
		if hasZExtension(nameI) && hasZExtension(nameJ) {
			return extI < extJ // .z01 < .z02 < .z03
		}
		if hasZExtension(nameI) && !hasZExtension(nameJ) {
			return true // .z01 comes before .zip
		}
		if !hasZExtension(nameI) && hasZExtension(nameJ) {
			return false // .zip comes after .z01
		}

		// Handle .zip.001/.zip.002 format
		if hasNumberedExtension(nameI) && hasNumberedExtension(nameJ) {
			return nameI < nameJ
		}

		// Default alphabetical sort
		return nameI < nameJ
	})

	return sorted
}

// hasZExtension checks if filename has .z01, .z02, etc. extension
func hasZExtension(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	return len(ext) == 4 && ext[0] == '.' && ext[1] == 'z' &&
		ext[2] >= '0' && ext[2] <= '9' && ext[3] >= '0' && ext[3] <= '9'
}

// compareZExtensions compares .z01, .z02, etc. extensions
func compareZExtensions(name1, name2 string) bool {
	ext1 := strings.ToLower(filepath.Ext(name1))
	ext2 := strings.ToLower(filepath.Ext(name2))
	return ext1 < ext2
}

// hasNumberedExtension checks if filename has .zip.001, .zip.002, etc. extension
func hasNumberedExtension(filename string) bool {
	name := strings.ToLower(filename)
	return strings.Contains(name, ".zip.") && len(name) > 8
}

// compareNumberedExtensions compares .zip.001, .zip.002, etc. extensions
func compareNumberedExtensions(name1, name2 string) bool {
	return name1 < name2
}

// NewMultipartReader creates a new MultipartReader with properly sorted streams
func NewMultipartReader(streams []*models.StreamWithSeek) *MultipartReader {
	// Sort streams in the correct order
	sortedStreams := sortMultipartStreams(streams)

	// Log the sorted order for debugging
	log.Debug(context.Background(), "Creating MultipartReader with sorted streams")
	for i, stream := range sortedStreams {
		log.Debug(context.Background(), "Stream order", "index", i, "name", stream.GetName(), "size", stream.GetSize())
	}

	return &MultipartReader{
		streams: sortedStreams,
		pos:     0,
		current: 0,
	}
}

// Read reads from the combined streams
func (m *MultipartReader) Read(p []byte) (n int, err error) {
	if m.current >= len(m.streams) {
		return 0, io.EOF
	}

	totalRead := 0
	remaining := len(p)

	for remaining > 0 && m.current < len(m.streams) {
		// Read from current stream
		bytesRead, readErr := m.streams[m.current].Read(p[totalRead:])
		totalRead += bytesRead
		remaining -= bytesRead
		m.pos += int64(bytesRead)

		// If we reached the end of the current stream, move to the next one
		if readErr == io.EOF {
			if m.current < len(m.streams)-1 {
				m.current++
				// Reset the next stream to the beginning
				_, seekErr := m.streams[m.current].Seek(0, io.SeekStart)
				if seekErr != nil {
					return totalRead, seekErr
				}
				log.Debug(context.Background(), "Switched to next multipart stream", "stream_index", m.current)
			} else {
				// We've reached the end of all streams
				if totalRead > 0 {
					return totalRead, nil
				}
				return totalRead, io.EOF
			}
		} else if readErr != nil {
			// Some other error occurred
			return totalRead, readErr
		}

		// If we filled the buffer, we can return
		if remaining == 0 {
			break
		}
	}

	return totalRead, nil
}

// Seek seeks in the combined streams
func (m *MultipartReader) Seek(offset int64, whence int) (int64, error) {
	var newPos int64

	switch whence {
	case io.SeekStart:
		newPos = offset
	case io.SeekCurrent:
		newPos = m.pos + offset
	case io.SeekEnd:
		// Calculate total size
		totalSize := int64(0)
		for _, s := range m.streams {
			totalSize += s.GetSize()
		}
		newPos = totalSize + offset
	default:
		return 0, fmt.Errorf("invalid whence: %d", whence)
	}

	if newPos < 0 {
		return 0, fmt.Errorf("negative position: %d", newPos)
	}

	// Find which stream contains the new position
	var streamStart int64
	for i, s := range m.streams {
		streamSize := s.GetSize()
		if newPos < streamStart+streamSize {
			// Found the stream, seek within it
			m.current = i
			_, err := m.streams[i].Seek(newPos-streamStart, io.SeekStart)
			if err != nil {
				return 0, err
			}
			m.pos = newPos
			return newPos, nil
		}
		streamStart += streamSize
	}

	// If we get here, the position is beyond all streams
	m.current = len(m.streams) - 1
	_, err := m.streams[m.current].Seek(0, io.SeekEnd)
	if err != nil {
		return 0, err
	}
	m.pos = streamStart
	return m.pos, nil
}

// ReadAt reads at a specific offset from the combined streams
func (m *MultipartReader) ReadAt(p []byte, off int64) (n int, err error) {
	// Save current position
	currentPos := m.pos
	currentStream := m.current

	// Seek to the requested offset
	_, err = m.Seek(off, io.SeekStart)
	if err != nil {
		return 0, err
	}

	// Read data
	n, err = m.Read(p)

	// Restore position
	m.current = currentStream
	_, seekErr := m.Seek(currentPos, io.SeekStart)
	if seekErr != nil && err == nil {
		err = seekErr
	}

	return n, err
}

// Size returns the total size of all streams
func (m *MultipartReader) Size() int64 {
	totalSize := int64(0)
	for _, s := range m.streams {
		totalSize += s.GetSize()
	}
	return totalSize
}

// Close closes all streams
func (m *MultipartReader) Close() error {
	var firstErr error
	for _, s := range m.streams {
		if err := s.Close(); err != nil && firstErr == nil {
			firstErr = err
		}
	}
	return firstErr
}

// GetMultipartReader creates a MultipartReader from multiple streams
func (z *Zip) GetMultipartReader(streams []*models.StreamWithSeek) (*MultipartReader, error) {
	if len(streams) == 0 {
		return nil, fmt.Errorf("no streams provided")
	}

	log.Debug(context.Background(), "Creating multipart reader", "stream_count", len(streams))
	return NewMultipartReader(streams), nil
}

// CreateTempFile creates a temporary file from multiple streams
// This is a fallback method for libraries that don't support multipart reading
func (z *Zip) CreateTempFile(streams []*models.StreamWithSeek) (string, error) {
	if len(streams) == 0 {
		return "", fmt.Errorf("no streams provided")
	}

	// Sort streams in the correct order first
	sortedStreams := sortMultipartStreams(streams)

	log.Debug(context.Background(), "Creating temp file with sorted streams")
	for i, s := range sortedStreams {
		log.Debug(context.Background(), "Sorted stream order", "index", i, "name", s.GetName(), "size", s.GetSize())
	}

	// Create temporary file
	tempFile, err := os.CreateTemp("", "multipart-*.zip")
	if err != nil {
		return "", err
	}
	defer tempFile.Close()

	// Copy all streams to the temporary file in sorted order
	for i, s := range sortedStreams {
		log.Debug(context.Background(), "Copying stream to temp file", "index", i, "name", s.GetName(), "size", s.GetSize())

		// Reset stream position
		_, err = s.Seek(0, io.SeekStart)
		if err != nil {
			return "", err
		}

		// Copy stream to temp file
		_, err = io.Copy(tempFile, stream.ConvertStreamWithSeekToReaderSeeker(s))
		if err != nil {
			return "", err
		}
	}

	return tempFile.Name(), nil
}

// NewMultiReaderAt creates a new MultiReaderAt from multiple streams
// This is similar to Alist's stream.NewMultiReaderAt function
func NewMultiReaderAt(streams []*models.StreamWithSeek) (*MultiReaderAt, error) {
	if len(streams) == 0 {
		return nil, fmt.Errorf("no streams provided")
	}

	// Sort streams in the correct order
	sortedStreams := sortMultipartStreams(streams)

	// Calculate cumulative offsets
	offsets := make([]int64, len(sortedStreams))
	totalSize := int64(0)
	for i, stream := range sortedStreams {
		offsets[i] = totalSize
		totalSize += stream.GetSize()
	}

	log.Debug(context.Background(), "Created MultiReaderAt", "stream_count", len(sortedStreams), "total_size", totalSize)
	for i, stream := range sortedStreams {
		log.Debug(context.Background(), "Stream info", "index", i, "name", stream.GetName(), "size", stream.GetSize(), "offset", offsets[i])
	}

	return &MultiReaderAt{
		streams:   sortedStreams,
		offsets:   offsets,
		totalSize: totalSize,
	}, nil
}

// ReadAt implements io.ReaderAt
func (m *MultiReaderAt) ReadAt(p []byte, off int64) (n int, err error) {
	if off >= m.totalSize {
		return 0, io.EOF
	}

	// Find which stream contains the offset
	streamIndex := -1
	for i := len(m.offsets) - 1; i >= 0; i-- {
		if off >= m.offsets[i] {
			streamIndex = i
			break
		}
	}

	if streamIndex == -1 {
		return 0, fmt.Errorf("invalid offset: %d", off)
	}

	totalRead := 0
	remaining := len(p)

	for streamIndex < len(m.streams) && remaining > 0 {
		stream := m.streams[streamIndex]
		streamOffset := off - m.offsets[streamIndex]

		// Seek to the correct position in this stream
		_, err = stream.Seek(streamOffset, io.SeekStart)
		if err != nil {
			return totalRead, err
		}

		// Read from this stream
		bytesToRead := remaining
		streamRemaining := stream.GetSize() - streamOffset
		if int64(bytesToRead) > streamRemaining {
			bytesToRead = int(streamRemaining)
		}

		bytesRead, err := stream.Read(p[totalRead : totalRead+bytesToRead])
		totalRead += bytesRead
		remaining -= bytesRead
		off += int64(bytesRead)

		if err != nil && err != io.EOF {
			return totalRead, err
		}

		// If we've read all from this stream, move to the next
		if err == io.EOF || bytesRead == int(streamRemaining) {
			streamIndex++
		}
	}

	return totalRead, nil
}

// Size returns the total size of all streams
func (m *MultiReaderAt) Size() int64 {
	return m.totalSize
}

// Close closes all streams
func (m *MultiReaderAt) Close() error {
	var firstErr error
	for _, stream := range m.streams {
		if err := stream.Close(); err != nil && firstErr == nil {
			firstErr = err
		}
	}
	return firstErr
}
