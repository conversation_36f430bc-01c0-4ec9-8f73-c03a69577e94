package zip

import (
	"context"
	"fmt"
	"io"
	"os"

	"github.com/Sakura-Byte/kikoeru-go/pkg/log"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/stream"
)

// MultipartReader combines multiple streams into one for reading multipart ZIP archives
type MultipartReader struct {
	streams []*models.StreamWithSeek
	pos     int64
	current int
}

// NewMultipartReader creates a new MultipartReader
func NewMultipartReader(streams []*models.StreamWithSeek) *MultipartReader {
	return &MultipartReader{
		streams: streams,
		pos:     0,
		current: 0,
	}
}

// Read reads from the combined streams
func (m *MultipartReader) Read(p []byte) (n int, err error) {
	if m.current >= len(m.streams) {
		return 0, io.EOF
	}

	n, err = m.streams[m.current].Read(p)
	m.pos += int64(n)

	// If we reached the end of the current stream, move to the next one
	if err == io.EOF && m.current < len(m.streams)-1 {
		m.current++
		m.streams[m.current].Seek(0, io.SeekStart)
		err = nil
	}

	return n, err
}

// Seek seeks in the combined streams
func (m *MultipartReader) Seek(offset int64, whence int) (int64, error) {
	var newPos int64

	switch whence {
	case io.SeekStart:
		newPos = offset
	case io.SeekCurrent:
		newPos = m.pos + offset
	case io.SeekEnd:
		// Calculate total size
		totalSize := int64(0)
		for _, s := range m.streams {
			totalSize += s.GetSize()
		}
		newPos = totalSize + offset
	default:
		return 0, fmt.Errorf("invalid whence: %d", whence)
	}

	if newPos < 0 {
		return 0, fmt.Errorf("negative position: %d", newPos)
	}

	// Find which stream contains the new position
	var streamStart int64
	for i, s := range m.streams {
		streamSize := s.GetSize()
		if newPos < streamStart+streamSize {
			// Found the stream, seek within it
			m.current = i
			_, err := m.streams[i].Seek(newPos-streamStart, io.SeekStart)
			if err != nil {
				return 0, err
			}
			m.pos = newPos
			return newPos, nil
		}
		streamStart += streamSize
	}

	// If we get here, the position is beyond all streams
	m.current = len(m.streams) - 1
	_, err := m.streams[m.current].Seek(0, io.SeekEnd)
	if err != nil {
		return 0, err
	}
	m.pos = streamStart
	return m.pos, nil
}

// ReadAt reads at a specific offset from the combined streams
func (m *MultipartReader) ReadAt(p []byte, off int64) (n int, err error) {
	// Save current position
	currentPos := m.pos
	currentStream := m.current

	// Seek to the requested offset
	_, err = m.Seek(off, io.SeekStart)
	if err != nil {
		return 0, err
	}

	// Read data
	n, err = m.Read(p)

	// Restore position
	m.current = currentStream
	_, seekErr := m.Seek(currentPos, io.SeekStart)
	if seekErr != nil && err == nil {
		err = seekErr
	}

	return n, err
}

// Size returns the total size of all streams
func (m *MultipartReader) Size() int64 {
	totalSize := int64(0)
	for _, s := range m.streams {
		totalSize += s.GetSize()
	}
	return totalSize
}

// Close closes all streams
func (m *MultipartReader) Close() error {
	var firstErr error
	for _, s := range m.streams {
		if err := s.Close(); err != nil && firstErr == nil {
			firstErr = err
		}
	}
	return firstErr
}

// GetMultipartReader creates a MultipartReader from multiple streams
func (z *Zip) GetMultipartReader(streams []*models.StreamWithSeek) (*MultipartReader, error) {
	if len(streams) == 0 {
		return nil, fmt.Errorf("no streams provided")
	}

	log.Debug(context.Background(), "Creating multipart reader", "stream_count", len(streams))
	return NewMultipartReader(streams), nil
}

// CreateTempFile creates a temporary file from multiple streams
// This is a fallback method for libraries that don't support multipart reading
func (z *Zip) CreateTempFile(streams []*models.StreamWithSeek) (string, error) {
	if len(streams) == 0 {
		return "", fmt.Errorf("no streams provided")
	}

	// Create temporary file
	tempFile, err := os.CreateTemp("", "multipart-*.zip")
	if err != nil {
		return "", err
	}
	defer tempFile.Close()

	// Copy all streams to the temporary file
	for i, s := range streams {
		log.Debug(context.Background(), "Copying stream to temp file", "index", i, "size", s.GetSize())

		// Reset stream position
		_, err = s.Seek(0, io.SeekStart)
		if err != nil {
			return "", err
		}

		// Copy stream to temp file
		_, err = io.Copy(tempFile, stream.ConvertStreamWithSeekToReaderSeeker(s))
		if err != nil {
			return "", err
		}
	}

	return tempFile.Name(), nil
}
