package zip

import (
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"sort"
	"strings"

	"github.com/Sakura-Byte/kikoeru-go/pkg/log"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/stream"
)

// MultipartReader combines multiple streams into one for reading multipart ZIP archives
type MultipartReader struct {
	streams []*models.StreamWithSeek
	pos     int64
	current int
}

// sortMultipartStreams sorts streams in the correct order for multipart ZIP files
// For .zip + .z01/.z02/.z03 format: .z01, .z02, .z03, .zip (main file last)
// For .zip.001/.zip.002 format: .zip.001, .zip.002, etc.
func sortMultipartStreams(streams []*models.StreamWithSeek) []*models.StreamWithSeek {
	if len(streams) <= 1 {
		return streams
	}

	// Create a copy to avoid modifying the original slice
	sorted := make([]*models.StreamWithSeek, len(streams))
	copy(sorted, streams)

	// Sort based on file extension patterns
	sort.Slice(sorted, func(i, j int) bool {
		nameI := sorted[i].GetName()
		nameJ := sorted[j].GetName()

		// Handle .zip + .z01/.z02/.z03 format
		// Order should be: .z01, .z02, .z03, .zip
		if hasZExtension(nameI) && hasZExtension(nameJ) {
			return compareZExtensions(nameI, nameJ)
		}
		if hasZExtension(nameI) && !hasZExtension(nameJ) {
			return true // .z01 comes before .zip
		}
		if !hasZExtension(nameI) && hasZExtension(nameJ) {
			return false // .zip comes after .z01
		}

		// Handle .zip.001/.zip.002 format
		if hasNumberedExtension(nameI) && hasNumberedExtension(nameJ) {
			return compareNumberedExtensions(nameI, nameJ)
		}

		// Default alphabetical sort
		return nameI < nameJ
	})

	return sorted
}

// hasZExtension checks if filename has .z01, .z02, etc. extension
func hasZExtension(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	return len(ext) == 4 && ext[0] == '.' && ext[1] == 'z' &&
		ext[2] >= '0' && ext[2] <= '9' && ext[3] >= '0' && ext[3] <= '9'
}

// compareZExtensions compares .z01, .z02, etc. extensions
func compareZExtensions(name1, name2 string) bool {
	ext1 := strings.ToLower(filepath.Ext(name1))
	ext2 := strings.ToLower(filepath.Ext(name2))
	return ext1 < ext2
}

// hasNumberedExtension checks if filename has .zip.001, .zip.002, etc. extension
func hasNumberedExtension(filename string) bool {
	name := strings.ToLower(filename)
	return strings.Contains(name, ".zip.") && len(name) > 8
}

// compareNumberedExtensions compares .zip.001, .zip.002, etc. extensions
func compareNumberedExtensions(name1, name2 string) bool {
	return name1 < name2
}

// NewMultipartReader creates a new MultipartReader with properly sorted streams
func NewMultipartReader(streams []*models.StreamWithSeek) *MultipartReader {
	// Sort streams in the correct order
	sortedStreams := sortMultipartStreams(streams)

	// Log the sorted order for debugging
	log.Debug(context.Background(), "Creating MultipartReader with sorted streams")
	for i, stream := range sortedStreams {
		log.Debug(context.Background(), "Stream order", "index", i, "name", stream.GetName(), "size", stream.GetSize())
	}

	return &MultipartReader{
		streams: sortedStreams,
		pos:     0,
		current: 0,
	}
}

// Read reads from the combined streams
func (m *MultipartReader) Read(p []byte) (n int, err error) {
	if m.current >= len(m.streams) {
		return 0, io.EOF
	}

	totalRead := 0
	remaining := len(p)

	for remaining > 0 && m.current < len(m.streams) {
		// Read from current stream
		bytesRead, readErr := m.streams[m.current].Read(p[totalRead:])
		totalRead += bytesRead
		remaining -= bytesRead
		m.pos += int64(bytesRead)

		// If we reached the end of the current stream, move to the next one
		if readErr == io.EOF {
			if m.current < len(m.streams)-1 {
				m.current++
				// Reset the next stream to the beginning
				_, seekErr := m.streams[m.current].Seek(0, io.SeekStart)
				if seekErr != nil {
					return totalRead, seekErr
				}
				log.Debug(context.Background(), "Switched to next multipart stream", "stream_index", m.current)
			} else {
				// We've reached the end of all streams
				if totalRead > 0 {
					return totalRead, nil
				}
				return totalRead, io.EOF
			}
		} else if readErr != nil {
			// Some other error occurred
			return totalRead, readErr
		}

		// If we filled the buffer, we can return
		if remaining == 0 {
			break
		}
	}

	return totalRead, nil
}

// Seek seeks in the combined streams
func (m *MultipartReader) Seek(offset int64, whence int) (int64, error) {
	var newPos int64

	switch whence {
	case io.SeekStart:
		newPos = offset
	case io.SeekCurrent:
		newPos = m.pos + offset
	case io.SeekEnd:
		// Calculate total size
		totalSize := int64(0)
		for _, s := range m.streams {
			totalSize += s.GetSize()
		}
		newPos = totalSize + offset
	default:
		return 0, fmt.Errorf("invalid whence: %d", whence)
	}

	if newPos < 0 {
		return 0, fmt.Errorf("negative position: %d", newPos)
	}

	// Find which stream contains the new position
	var streamStart int64
	for i, s := range m.streams {
		streamSize := s.GetSize()
		if newPos < streamStart+streamSize {
			// Found the stream, seek within it
			m.current = i
			_, err := m.streams[i].Seek(newPos-streamStart, io.SeekStart)
			if err != nil {
				return 0, err
			}
			m.pos = newPos
			return newPos, nil
		}
		streamStart += streamSize
	}

	// If we get here, the position is beyond all streams
	m.current = len(m.streams) - 1
	_, err := m.streams[m.current].Seek(0, io.SeekEnd)
	if err != nil {
		return 0, err
	}
	m.pos = streamStart
	return m.pos, nil
}

// ReadAt reads at a specific offset from the combined streams
func (m *MultipartReader) ReadAt(p []byte, off int64) (n int, err error) {
	// Save current position
	currentPos := m.pos
	currentStream := m.current

	// Seek to the requested offset
	_, err = m.Seek(off, io.SeekStart)
	if err != nil {
		return 0, err
	}

	// Read data
	n, err = m.Read(p)

	// Restore position
	m.current = currentStream
	_, seekErr := m.Seek(currentPos, io.SeekStart)
	if seekErr != nil && err == nil {
		err = seekErr
	}

	return n, err
}

// Size returns the total size of all streams
func (m *MultipartReader) Size() int64 {
	totalSize := int64(0)
	for _, s := range m.streams {
		totalSize += s.GetSize()
	}
	return totalSize
}

// Close closes all streams
func (m *MultipartReader) Close() error {
	var firstErr error
	for _, s := range m.streams {
		if err := s.Close(); err != nil && firstErr == nil {
			firstErr = err
		}
	}
	return firstErr
}

// GetMultipartReader creates a MultipartReader from multiple streams
func (z *Zip) GetMultipartReader(streams []*models.StreamWithSeek) (*MultipartReader, error) {
	if len(streams) == 0 {
		return nil, fmt.Errorf("no streams provided")
	}

	log.Debug(context.Background(), "Creating multipart reader", "stream_count", len(streams))
	return NewMultipartReader(streams), nil
}

// CreateTempFile creates a temporary file from multiple streams
// This is a fallback method for libraries that don't support multipart reading
func (z *Zip) CreateTempFile(streams []*models.StreamWithSeek) (string, error) {
	if len(streams) == 0 {
		return "", fmt.Errorf("no streams provided")
	}

	// Create temporary file
	tempFile, err := os.CreateTemp("", "multipart-*.zip")
	if err != nil {
		return "", err
	}
	defer tempFile.Close()

	// Copy all streams to the temporary file
	for i, s := range streams {
		log.Debug(context.Background(), "Copying stream to temp file", "index", i, "size", s.GetSize())

		// Reset stream position
		_, err = s.Seek(0, io.SeekStart)
		if err != nil {
			return "", err
		}

		// Copy stream to temp file
		_, err = io.Copy(tempFile, stream.ConvertStreamWithSeekToReaderSeeker(s))
		if err != nil {
			return "", err
		}
	}

	return tempFile.Name(), nil
}
