<template>
  <q-dialog v-model="showDialog" persistent>
    <q-card :dark="$q.dark.isActive" :class="{ 'bg-dark': $q.dark.isActive }">
      <div class="q-pa-sm">
        <q-time
          v-model="time"
          now-btn
          :dark="$q.dark.isActive"
        />
      </div>

      <div class="row justify-between">
        <q-card-actions>
          <q-btn flat :label="t('sleepMode.cancel')" color="primary" @click="clearSleepTimer" :disable="!sleepMode" v-close-popup />
        </q-card-actions>

        <q-card-actions align="right">
          <q-btn flat :label="t('common.cancel')" color="primary" v-close-popup />
          <q-btn flat :label="t('common.confirm')" color="primary" @click="setSleepTimer" v-close-popup />
        </q-card-actions>
      </div>

    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useQuasar } from 'quasar'
import { useAudioPlayer } from '../composables/useAudioPlayer'
import { useNotification } from '../composables/useNotification'

defineOptions({
  name: 'SleepMode'
})

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

const { t } = useI18n()
const $q = useQuasar()
const { sleepTime, sleepMode, setSleepMode, clearSleepMode } = useAudioPlayer()
const { showSuccessNotification } = useNotification()

// Reactive data
const time = ref('00:00')

// Computed properties
const showDialog = computed({
  get() {
    return props.modelValue
  },
  set(val) {
    emit('update:modelValue', val)
  }
})

// Watchers
watch(showDialog, (visible) => {
  if (visible) {
    if (!sleepMode.value) {
      const currentTime = new Date()
      time.value = currentTime.getHours().toString().padStart(2, '0') + ':' + currentTime.getMinutes().toString().padStart(2, '0')
    } else {
      time.value = sleepTime.value
    }
  }
})

// Methods
const setSleepTimer = () => {
  setSleepMode(time.value)
  // Persist sleep timer
  try {
    $q.sessionStorage.set('sleepTime', time.value)
    $q.sessionStorage.set('sleepMode', true)
  } catch {
    console.log('Web Storage API error')
  }
  showSuccessNotification(t('sleepMode.willStopAt', { time: time.value }))
}

const clearSleepTimer = () => {
  clearSleepMode()
  try {
    $q.sessionStorage.set('sleepTime', null)
    $q.sessionStorage.set('sleepMode', false)
  } catch {
    console.log('Web Storage API error')
  }
  showSuccessNotification(t('sleepMode.disabled'))
}

// Lifecycle
onMounted(() => {
  try {
    if ($q.sessionStorage.getItem('sleepMode')) {
      setSleepMode($q.sessionStorage.getItem('sleepTime'))
    }
  } catch {
    console.log('Web Storage API error')
  }
})


</script>
