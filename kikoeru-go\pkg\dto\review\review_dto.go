package review

import (
	dto_work "github.com/Sakura-Byte/kikoeru-go/pkg/dto/work"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
)

// ReviewResponseItem DTO for items in GET /review list.
// This structure was formerly defined in review_service.go.
type ReviewResponseItem struct {
	models.Review
	Work *dto_work.WorkDTO `json:"work,omitempty"` // Embed full work details
}

// PutReviewRequest DTO for PUT /review endpoint
type PutReviewRequest struct {
	WorkID     string                `json:"work_id" binding:"required"` // RJID
	Rating     *int                  `json:"rating" binding:"omitempty,min=1,max=5"`
	ReviewText *string               `json:"review_text" binding:"omitempty"`
	Progress   models.ProgressStatus `json:"progress" binding:"omitempty"`
}
