<template>
  <q-slide-transition class="bordered elevated">
    <div v-show="currentPlayingFile.trackPath && hide" class="row player-bar" :style="isDark ? 'background-color: #5c5c5c;' : ''">
      <q-item clickable v-ripple @click="toggleHide()" style="padding: 0px 5px;" class="col non-selectable">
        <q-item-section avatar>
          <q-img transition="fade" :src="samCoverUrl" style="height: 50px; width: 50px" class="rounded-borders" />
        </q-item-section>

        <q-item-section>
          <q-item-label lines="2" :class="{ 'text-white': isDark }">{{ currentPlayingFile.title }}</q-item-label>
          <q-item-label caption lines="1" :class="{ 'text-grey-5': isDark }">{{ currentPlayingFile.workTitle }}</q-item-label>
        </q-item-section>
      </q-item>

      <q-btn flat size="lg" icon="skip_previous" @click="previousTrack()" style="height: 60px; width: 60px" class="col-auto gt-sm" :color="isDark ? 'grey-5' : 'black'"/>
      <q-btn flat size="lg" :icon="playingIcon" @click="togglePlaying()" style="height: 60px; width: 60px" class="col-auto" :color="isDark ? 'grey-5' : 'black'" />
      <q-btn flat size="lg" icon="skip_next" @click="nextTrack()" style="height: 60px; width: 60px" class="col-auto gt-sm" :color="isDark ? 'grey-5' : 'black'"/>
    </div>
  </q-slide-transition>
</template>

<script setup>
import { computed, defineOptions } from 'vue'
import { useQuasar } from 'quasar'
import { useAudioPlayer } from '../composables/useAudioPlayer'

defineOptions({
  name: 'PlayerBar'
})

const $q = useQuasar()
const {
  hide,
  playing,
  currentPlayingFile,
  toggleHide,
  togglePlaying,
  nextTrack,
  previousTrack
} = useAudioPlayer()

const samCoverUrl = computed(() => {
  const currentFile = currentPlayingFile.value
  if (!currentFile.originalId) return ""
  return `/api/v1/cover/${currentFile.originalId}?type=sam`
})

const playingIcon = computed(() => {
  return playing.value ? "pause" : "play_arrow"
})

const isDark = computed(() => {
  return $q.dark.isActive
})
</script>

<style lang="scss" scoped>
.body--dark {
  .bordered {
    border-color: rgba(255, 255, 255, 0.12) !important;
  }

  .elevated {
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.5) !important;
  }

  .player-bar {
    background-color: #424242 !important;
  }
}
</style>
