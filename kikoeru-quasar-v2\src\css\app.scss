// app global css in SCSS form

// Import component-specific styles
@import './components/AudioElement.scss';
@import './components/LyricsBar.scss';
@import './components/List.scss';
@import './components/CoverSFW.scss';
@import './components/PlaylistCard.scss';
@import './components/pages_admin_AdminProfile.scss';
@import './components/pages_Dashboard_Advanced.scss';
@import './components/pages_Dashboard_FeedbackManage.scss';
@import './components/pages_Dashboard_Scanner.scss';
@import './components/Settings.scss';
@import './components/Works.scss';
@import './components/MainLayout.scss';
@import './components/DashboardLayout.scss';
@import './components/WorkTree.scss';

// Global styles
.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ellipsis-2-lines {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

// Global dark mode style to remove all highlights/glows
.body--dark {
  * {
    text-shadow: none !important;
  }
  
  .q-hoverable:hover > .q-focus-helper {
    background: rgba(255, 255, 255, 0.1) !important;
  }
}
