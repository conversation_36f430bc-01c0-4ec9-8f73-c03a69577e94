package models

import (
	"time"

	"github.com/Sakura-Byte/kikoeru-go/pkg/utils"
	"gorm.io/gorm"
)

// Circle 对应数据库中的 t_circle 表
type Circle struct {
	ID        string    `gorm:"primaryKey;type:varchar(36)" json:"id"`              // UUID
	Name      string    `gorm:"type:varchar(255);not null;uniqueIndex" json:"name"` // 社团名称通常应该是唯一的
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// 反向关联 (可选, GORM 可以处理)
	// Works []Work `gorm:"foreignKey:circle_id" json:"works,omitempty"`
}

// TableName 指定 GORM 使用的表名
func (Circle) TableName() string {
	return "t_circle"
}

// BeforeCreate hook to generate UUID v5 based on circle name
func (c *Circle) BeforeCreate(tx *gorm.DB) (err error) {
	if c.ID == "" && c.Name != "" {
		c.ID, err = utils.GenerateUUIDv5ForName(c.Name)
		if err != nil {
			return err
		}
	}
	return
}
