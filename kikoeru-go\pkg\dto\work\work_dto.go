package work_dto

import (
	"time"

	circle_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/circle" // Added circle_dto import
	tag_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/tag"
	va_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/va" // Added va_dto import
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
)

// WorkDTO is the Data Transfer Object for work information.
// Used for API responses like GET /api/v1/works/{identifier}/info and other internal transfers.
type WorkDTO struct {
	ID              uint            `json:"id"`
	CreatedAt       time.Time       `json:"created_at"`
	UpdatedAt       time.Time       `json:"updated_at"`
	OriginalID      *string         `json:"original_id,omitempty"`
	WorkType        *string         `json:"work_type,omitempty"`
	Title           string          `json:"title"`
	CircleID        *int64          `json:"circle_id,omitempty"`
	AgeRating       *string         `json:"age_rating,omitempty"`
	NSFW            *bool           `json:"nsfw,omitempty"` // 添加NSFW字段
	ReleaseDate     *string         `json:"release_date,omitempty"`
	RateCount       *int64          `json:"rate_count,omitempty"`
	RateAverage2DP  *float64        `json:"rate_average_2dp,omitempty"`
	Rank            models.RankList `json:"rank,omitempty"`
	LyricStatus     string          `json:"lyric_status"`
	Language        *string         `json:"language,omitempty"`
	Duration        *int64          `json:"duration,omitempty"`
	DlCount         *int64          `json:"dl_count,omitempty"`
	Price           *int64          `json:"price,omitempty"`
	ReviewCount     *int64          `json:"review_count,omitempty"`
	RateCountDetail *string         `json:"rate_count_detail,omitempty"`
	StorageID       uint            `json:"storage_id"`
	PathInStorage   string          `json:"path_in_storage"`

	Circle *circle_dto.CircleDTO `json:"circle,omitempty"` // Changed to circle_dto.CircleDTO
	Tags   []*tag_dto.TagDTO     `json:"tags,omitempty"`
	VAs    []*va_dto.VADTO       `json:"vas,omitempty"` // Changed to va_dto.VADTO

	// URL fields
	ImageMainURL *string `json:"image_main_url,omitempty"`
	ImageSamURL  *string `json:"image_sam_url,omitempty"`

	// 用户特定字段
	UserRating *int `json:"user_rating,omitempty"` // 添加用户评分字段
}

// RandomQueueParams DTO for GET /api/v1/queue/random.
type RandomQueueParams struct {
	SourcePlaylistID   *uint    `form:"source_playlist_id"`
	IncludeTagNames    []string `form:"include_tags"`
	ExcludeTagNames    []string `form:"exclude_tags"`
	IncludeVANames     []string `form:"include_vas"`
	ExcludeVANames     []string `form:"exclude_vas"`
	IncludeCircleNames []string `form:"include_circles"`
	ExcludeCircleNames []string `form:"exclude_circles"`
	WorkType           *string  `form:"work_type"`
	MaxQueueSize       int      `form:"max_size,default=50"`
	MinRating          *float64 `form:"min_rating"`
}

// UpdateWorkRequest DTO for updating work details.
type UpdateWorkRequest struct {
	Title       *string  `json:"title"`
	OriginalID  *string  `json:"original_id"`
	WorkType    *string  `json:"work_type"`
	CircleName  *string  `json:"circle_name"`
	AgeRating   *string  `json:"age_rating"`
	ReleaseDate *string  `json:"release_date"`
	Language    *string  `json:"language"`
	Tags        []string `json:"tags"`
	VAs         []string `json:"vas"`
	LyricStatus *string  `json:"lyric_status"`
	AdminCover  *string  `json:"admin_cover,omitempty"`
}
