<template>
  <q-item v-if="history.work" style="padding: 5px;">
    <q-item-section avatar style="padding: 0px 5px 0px 0px;">
      <router-link :to="workUrl">
        <q-img transition="fade" :src="samCoverUrl" style="height: 60px; width: 60px;" />
      </router-link>
    </q-item-section>

    <q-item-section>
      <q-item-label lines="2" class="text">
        <router-link :to="workUrl" class="text-black">
          {{ history.work.title }}
        </router-link>
      </q-item-label>

      <q-item-label v-if="history.track_path" caption>
        {{ t('history.track') }}: {{ getTrackName(history.track_path) }}
      </q-item-label>

      <q-item-label>
        <div class="row q-gutter-x-sm q-gutter-y-xs items-center">
          <!-- 播放进度 -->
          <q-chip
            color="primary"
            text-color="white"
            dense
            size="sm"
            class="col-auto"
          >
            {{ formatProgress(history.progress_percentage) }}
          </q-chip>

          <!-- 播放位置 -->
          <span class="col-auto text-grey-7">
            {{ formatTime(history.playback_position_seconds) }}
          </span>

          <!-- 完成状态 -->
          <q-chip
            v-if="history.is_finished"
            color="green"
            text-color="white"
            dense
            size="sm"
            class="col-auto"
          >
            {{ t('history.finished') }}
          </q-chip>

          <!-- 社团名 -->
          <router-link v-if="history.work.circle" :to="`/works?keyword=$circle:${history.work.circle.name}$`" class="col-auto text-grey">
            {{ history.work.circle.name }}
          </router-link>
        </div>
      </q-item-label>

      <!-- 时间信息 -->
      <q-item-label caption>
        {{ t('history.playedAt') }}: {{ formatDate(history.updated_at) }}
      </q-item-label>
    </q-item-section>

    <!-- 操作按钮 -->
    <q-item-section side>
      <q-btn
        dense
        round
        color="negative"
        icon="delete"
        size="sm"
        @click.stop="confirmDelete"
      >
        <q-tooltip>{{ t('history.deleteRecord') }}</q-tooltip>
      </q-btn>
    </q-item-section>

    <!-- 删除确认对话框 -->
    <q-dialog v-model="showDeleteDialog" persistent>
      <q-card>
        <q-card-section>
          <div class="text-h6">{{ t('history.confirmDelete.title') }}</div>
          <div class="text-body2">{{ t('history.confirmDelete.message') }}</div>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="t('common.cancel')" v-close-popup />
          <q-btn flat :label="t('common.delete')" color="negative" @click="deleteHistory" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-item>
</template>

<script setup>
import { ref, computed, getCurrentInstance } from 'vue'
import { useI18n } from 'vue-i18n'
import { useNotification } from '../composables/useNotification'

defineOptions({
  name: 'PlayHistoryItem'
})

const props = defineProps({
  history: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['delete'])

const { t, locale } = useI18n()
const { proxy } = getCurrentInstance()
const { showSuccessNotification, showErrorNotification } = useNotification()

// Reactive data
const showDeleteDialog = ref(false)

// Computed properties
const samCoverUrl = computed(() => {
  // Use the new API format for thumbnail images
  return props.history.work?.original_id ? `/api/v1/cover/${props.history.work.original_id}?type=sam` : ""
})

const workUrl = computed(() => {
  const baseUrl = `/work/${props.history.work.original_id}`

  // If there's no track path or playback position, return basic URL
  if (!props.history.track_path && !props.history.playback_position_seconds) {
    return baseUrl
  }

  // Build hash parameters for navigation
  const hashParams = new URLSearchParams()

  if (props.history.track_path) {
    // Extract folder path and file name from track path
    const trackPath = props.history.track_path
    const lastSlashIndex = trackPath.lastIndexOf('/')

    if (lastSlashIndex > 0) {
      // There's a folder path
      const folderPath = trackPath.substring(0, lastSlashIndex)
      const fileName = trackPath.substring(lastSlashIndex + 1)
      hashParams.set('folder', folderPath)
      hashParams.set('file', fileName)
    } else {
      // File is in root folder
      hashParams.set('file', trackPath)
    }
  }

  if (props.history.playback_position_seconds > 0) {
    hashParams.set('time', props.history.playback_position_seconds.toString())
  }

  return hashParams.toString() ? `${baseUrl}#${hashParams.toString()}` : baseUrl
})

// Methods
const getTrackName = (trackPath) => {
  if (!trackPath) return ''
  const parts = trackPath.split('/')
  return parts[parts.length - 1] // Get filename
}

const formatProgress = (percentage) => {
  if (!percentage && percentage !== 0) return '0%'
  return `${Math.round(percentage * 100)}%`
}

const formatTime = (seconds) => {
  if (!seconds && seconds !== 0) return '0:00'
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString(locale.value, {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const confirmDelete = () => {
  showDeleteDialog.value = true
}

const deleteHistory = async () => {
  try {
    await proxy.$api.delete(`/api/v1/me/history/${props.history.id}`)
    showDeleteDialog.value = false
    showSuccessNotification(t('notification.historyDeleted'))
    emit('delete', props.history.id)
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('notification.historyDeleteFailed'))
  }
}


</script>
