<template>
  <q-item clickable :to="`/work/${metadata.original_id}`" style="padding: 5px;">
    <q-item-section avatar style="padding: 0px 5px 0px 0px;">
      <router-link :to="`/work/${metadata.original_id}`">
        <q-img transition="fade" :src="samCoverUrl" style="height: 60px; width: 60px;" />
      </router-link>
    </q-item-section>

    <q-item-section>
      <q-item-label lines="2" class="text">
        <router-link :to="`/work/${metadata.original_id}`" class="text-black text-decoration-none">
          {{ metadata.title }}
        </router-link>
      </q-item-label>

      <q-item-label>
        <div class="row q-gutter-x-sm q-gutter-y-xs">
          <router-link v-if="metadata.circle" :to="`/works?circle=${metadata.circle.name}`" class="col-auto text-grey text-decoration-none">
            {{ metadata.circle.name }}
          </router-link>

          <span v-if="metadata.circle && metadata.vas && metadata.vas.length > 0" class="col-auto">/</span>

          <router-link
            v-for="(va, index) in metadata.vas"
            :to="`/works?va=${va.name}`"
            :key="index"
            class="col-auto text-primary text-decoration-none"
          >
            {{ va.name }}
          </router-link>
        </div>
      </q-item-label>

      <q-item-label v-if="showLabel && $q.screen.width > 700 && metadata.tags">
        <div class="row q-gutter-x-sm q-gutter-y-xs">
          <router-link
            v-for="(tag, index) in metadata.tags"
            :to="`/works?tag=${tag.name}`"
            :key="index"
            class="col-auto text-grey text-decoration-none"
          >
            {{ tag.name }}
          </router-link>
        </div>
      </q-item-label>
    </q-item-section>
  </q-item>
</template>

<script setup>
import { computed } from 'vue'
import { useQuasar } from 'quasar'

defineOptions({
  name: 'WorkListItem'
})

const props = defineProps({
  metadata: {
    type: Object,
    required: true
  },
  showLabel: {
    type: Boolean,
    default: true
  }
})

const $q = useQuasar()

// Computed properties
const samCoverUrl = computed(() => {
  // Use the new API format for thumbnail images
  return props.metadata.original_id ? `/api/v1/cover/${props.metadata.original_id}?type=sam` : ""
})
</script>
