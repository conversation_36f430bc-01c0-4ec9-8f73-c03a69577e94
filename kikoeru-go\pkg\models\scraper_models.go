package models

// ScrapedTag represents a tag scraped from an external source.
// Moved to pkg/dto/scraper/scraper_dto.go

// ScrapedCircle represents a circle scraped from an external source.
// Moved to pkg/dto/scraper/scraper_dto.go

// ScrapedVA represents a voice actor scraped from an external source.
// Moved to pkg/dto/scraper/scraper_dto.go

// ScrapedWorkData holds all the metadata scraped for a work from an external source.
// Moved to pkg/dto/scraper/scraper_dto.go
