<template>
  <router-link v-if="originalId" :to="`/work/${originalId}`">
    <q-img
      :src="coverUrl"
      :ratio="4/3"
      style="max-width: 560px;"
      transition="fade"
    >
      <div class="absolute-top-left transparent" style="padding: 0;">
        <q-chip dense square color="brown" text-color="white" class="q-ma-sm">
          {{originalId}}
        </q-chip>
      </div>

      <div v-if="release" class="absolute-bottom-right" style="padding: 5px;">
        {{release}}
      </div>
    </q-img>
  </router-link>

  <!-- Fallback when originalId is not available -->
  <div v-else class="q-pa-md bg-grey-3 text-center" style="aspect-ratio: 4/3; max-width: 560px; display: flex; align-items: center; justify-content: center;">
    <q-icon name="image" size="48px" color="grey-6" />
  </div>
</template>

<script setup>
import { computed } from 'vue'

defineOptions({
  name: 'CoverSFW'
})

const props = defineProps({
  originalId: {
    type: String,
    required: false,
    default: ''
  },
  release: {
    type: String,
    required: false,
    default: ''
  }
})

const coverUrl = computed(() => {
  // Use the new API format: /api/v1/cover/:originalID?type=main
  return props.originalId ? `/api/v1/cover/${props.originalId}?type=main` : ""
})
</script>

<!-- Styles moved to /src/css/components/CoverSFW.scss -->
