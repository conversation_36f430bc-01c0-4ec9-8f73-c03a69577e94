package database

import (
	"context"
	"errors"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
	"github.com/Sakura-Byte/kikoeru-go/pkg/domain/model"
	"github.com/Sakura-Byte/kikoeru-go/pkg/domain/repository"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// SubtitleRepository implements repository.SubtitleRepository using SQL database
type SubtitleRepository struct {
	db *gorm.DB
}

// NewSubtitleRepository creates a new subtitle repository instance
func NewSubtitleRepository(db *gorm.DB) repository.SubtitleRepository {
	return &SubtitleRepository{db: db}
}

// CreateSubtitle creates a new subtitle in the database
func (r *SubtitleRepository) CreateSubtitle(ctx context.Context, subtitle *model.Subtitle) error {
	return r.db.WithContext(ctx).Create(subtitle).Error
}

// GetSubtitleByID retrieves a subtitle by its ID
func (r *SubtitleRepository) GetSubtitleByID(ctx context.Context, id uint) (*model.Subtitle, error) {
	var subtitle model.Subtitle
	err := r.db.WithContext(ctx).First(&subtitle, id).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, apperrors.ErrSubtitleNotFound
	}
	return &subtitle, err
}

// GetSubtitleByUUID retrieves a subtitle by its UUID
func (r *SubtitleRepository) GetSubtitleByUUID(ctx context.Context, uuid string) (*model.Subtitle, error) {
	var subtitle model.Subtitle
	err := r.db.WithContext(ctx).Where("uuid = ?", uuid).First(&subtitle).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, apperrors.ErrSubtitleNotFound
	}
	return &subtitle, err
}

// UpdateSubtitle updates an existing subtitle
func (r *SubtitleRepository) UpdateSubtitle(ctx context.Context, subtitle *model.Subtitle) error {
	return r.db.WithContext(ctx).Save(subtitle).Error
}

// DeleteSubtitle deletes a subtitle by ID
func (r *SubtitleRepository) DeleteSubtitle(ctx context.Context, id uint) error {
	result := r.db.WithContext(ctx).Delete(&model.Subtitle{}, id)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return apperrors.ErrSubtitleNotFound
	}
	return nil
}

// FindSubtitlesForTrack finds subtitles for a specific track
func (r *SubtitleRepository) FindSubtitlesForTrack(ctx context.Context, originalID, trackPath string, isInArchive bool, archivePath string) ([]model.Subtitle, error) {
	var subtitles []model.Subtitle

	// Join with subtitle_track table to find subtitles associated with the specific track
	query := r.db.WithContext(ctx).
		Table("t_subtitle").
		Joins("INNER JOIN t_subtitle_track ON t_subtitle.id = t_subtitle_track.subtitle_id").
		Where("t_subtitle.original_id = ?", originalID).
		Where("t_subtitle_track.track_path = ?", trackPath).
		Where("t_subtitle_track.is_in_archive = ?", isInArchive)

	if isInArchive && archivePath != "" {
		query = query.Where("t_subtitle_track.archive_path = ?", archivePath)
	}

	// Only return public subtitles in this query
	query = query.Where("t_subtitle.is_public = ?", true)

	err := query.
		Select("t_subtitle.*").
		Order("t_subtitle.up_votes - t_subtitle.down_votes DESC, t_subtitle.created_at DESC").
		Find(&subtitles).Error
	return subtitles, err
}

// GetAllSubtitles retrieves all subtitles with pagination
func (r *SubtitleRepository) GetAllSubtitles(ctx context.Context, page, pageSize int) ([]model.Subtitle, int, error) {
	var subtitles []model.Subtitle
	var total int64

	offset := (page - 1) * pageSize

	// Count total records
	if err := r.db.WithContext(ctx).Model(&model.Subtitle{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get paginated records
	err := r.db.WithContext(ctx).
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&subtitles).Error

	return subtitles, int(total), err
}

// GetUserVote gets the user's vote for a subtitle
func (r *SubtitleRepository) GetUserVote(ctx context.Context, subtitleID uint, userID string) (*model.SubtitleVote, error) {
	var vote model.SubtitleVote
	err := r.db.WithContext(ctx).
		Where("subtitle_id = ? AND user_id = ?", subtitleID, userID).
		First(&vote).Error

	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil // No vote found is not an error
	}

	return &vote, err
}

// SaveVote saves a user's vote for a subtitle
func (r *SubtitleRepository) SaveVote(ctx context.Context, vote *model.SubtitleVote) error {
	// Use upsert to handle both new votes and updates
	return r.db.WithContext(ctx).
		Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "subtitle_id"}, {Name: "user_id"}},
			DoUpdates: clause.AssignmentColumns([]string{"vote", "updated_at"}),
		}).
		Create(vote).Error
}

// DeleteVote removes a user's vote for a subtitle
func (r *SubtitleRepository) DeleteVote(ctx context.Context, subtitleID uint, userID string) error {
	result := r.db.WithContext(ctx).
		Where("subtitle_id = ? AND user_id = ?", subtitleID, userID).
		Delete(&model.SubtitleVote{})

	if result.Error != nil {
		return result.Error
	}

	if result.RowsAffected == 0 {
		return apperrors.ErrVoteNotFound
	}

	return nil
}

// GetSubtitleCountForWork gets the count of subtitles for a work
func (r *SubtitleRepository) GetSubtitleCountForWork(ctx context.Context, workID uint) (int, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.Subtitle{}).
		Where("work_id = ?", workID).
		Count(&count).Error
	return int(count), err
}

// GetSubtitleCountForUser gets the count of subtitles uploaded by a user
func (r *SubtitleRepository) GetSubtitleCountForUser(ctx context.Context, userID string) (int, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.Subtitle{}).
		Where("uploader_id = ?", userID).
		Count(&count).Error
	return int(count), err
}

// IsSubtitleOwner checks if a user is the owner of a subtitle
func (r *SubtitleRepository) IsSubtitleOwner(ctx context.Context, subtitleID uint, userID string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.Subtitle{}).
		Where("id = ? AND uploader_id = ?", subtitleID, userID).
		Count(&count).Error
	return count > 0, err
}

// AddTrackToSubtitle adds a track association to a subtitle
func (r *SubtitleRepository) AddTrackToSubtitle(ctx context.Context, subtitleID uint, trackPath string, isInArchive bool, archivePath string) error {
	track := model.SubtitleTrack{
		SubtitleID:  subtitleID,
		TrackPath:   trackPath,
		IsInArchive: isInArchive,
		ArchivePath: archivePath,
	}
	return r.db.WithContext(ctx).Create(&track).Error
}

// GetTracksForSubtitle gets all tracks associated with a subtitle
func (r *SubtitleRepository) GetTracksForSubtitle(ctx context.Context, subtitleID uint) ([]model.SubtitleTrack, error) {
	var tracks []model.SubtitleTrack
	err := r.db.WithContext(ctx).
		Where("subtitle_id = ?", subtitleID).
		Find(&tracks).Error
	return tracks, err
}

// RemoveTrackFromSubtitle removes a track association from a subtitle
func (r *SubtitleRepository) RemoveTrackFromSubtitle(ctx context.Context, subtitleID uint, trackPath string) error {
	result := r.db.WithContext(ctx).
		Where("subtitle_id = ? AND track_path = ?", subtitleID, trackPath).
		Delete(&model.SubtitleTrack{})

	if result.Error != nil {
		return result.Error
	}

	if result.RowsAffected == 0 {
		return apperrors.ErrTrackNotFound
	}

	return nil
}
