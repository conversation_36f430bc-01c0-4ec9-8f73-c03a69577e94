/* MainLayout.scss - Styles for MainLayout component */

.drawer-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.main-scroll {
  flex: 1 1 auto;
  height: 0; /* Important for proper scrolling */
}

.bottom-links {
  flex: 0 0 auto;
}

// Remove the old sidebar styles
.sidebar-container {
  display: none;
}

// Add smooth transition for drawer width changes
.drawer-transition {
  transition: transform 0.3s cubic-bezier(0.23, 1, 0.32, 1);
}

// Fix drawer behavior specifically for icon positioning
.q-drawer {
  .q-drawer__content {
    overflow-x: hidden !important;
    white-space: nowrap;
  }
  
  // Position items to ensure stable icon placement
  .q-item {
    padding: 0;
    height: 50px;
    min-height: 50px;
    width: 100%;
    position: relative;
    display: flex;
  }
  
  // Fixed position for avatar section to prevent movement
  .q-item__section--avatar {
    position: absolute !important;
    left: 0;
    top: 0;
    width: 50px !important;
    height: 50px !important;
    min-width: 50px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    
    // Ensure icon stays perfectly centered
    .q-icon {
      position: absolute;
      width: 24px;
      height: 24px;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%) !important;
    }
  }
  
  // Main content positioned to leave space for icon
  .q-item__section--main {
    margin-left: 50px;
    flex: 1 1 auto;
    transition: opacity 0.28s;
    min-width: 0;
  }
}

// Mini drawer specific styles
.q-drawer--mini {
  .q-item__section:not(.q-item__section--avatar),
  .q-item__label {
    display: none !important;
    opacity: 0 !important;
  }
  
  .q-item {
    justify-content: center;
  }
}

// Dark mode styles
.body--dark {
  .kikoeru-layout {
    background-color: #121212 !important;
  }

  .q-drawer .bg-grey-1 {
    background-color: #1d1d1d !important;
  }

  .bottom-links {
    background-color: #1d1d1d !important;
  }

  .q-card {
    background-color: #1d1d1d !important;
  }

  // Additional dark mode styles
  .text-black {
    color: #f0f0f0 !important;
  }

  .text-grey {
    color: #c0c0c0 !important;
  }

  .q-separator {
    background: rgba(255, 255, 255, 0.12);
  }

  .q-chip {
    background-color: #2d2d2d;
    color: #e0e0e0;
  }
  
  // Remove highlight and shadow effects from elements in dark mode
  .shadow-1, .shadow-2, .shadow-3, .shadow-4, .shadow-5, .shadow-6, .shadow-7, .shadow-8, .shadow-9, .shadow-10,
  .shadow-up-1, .shadow-up-2, .shadow-up-3, .shadow-up-4, .shadow-up-5, .shadow-up-6, .shadow-up-7, .shadow-up-8, .shadow-up-9, .shadow-up-10 {
    box-shadow: none !important;
  }
  
  // Remove highlights from borders
  .q-card, .q-item, .q-chip, .q-btn, .q-list--bordered, .rounded-borders, .q-field__control {
    border-color: rgba(255, 255, 255, 0.12) !important;
  }
}

// Advanced search styles
.advanced-search-menu {
  .q-item {
    min-height: 40px;
  }

  .q-item-label.header {
    padding: 8px 16px 4px;
    font-size: 0.8rem;
    letter-spacing: 0.5px;
    color: var(--q-primary);
  }

  .q-icon {
    font-size: 1.2rem;
  }
}

// Make dialogs more modern
.q-dialog {
  .q-card {
    border-radius: 8px;

    .q-card-section:first-child {
      padding-bottom: 8px;
    }
  }

  .q-slider {
    margin: 16px 0;
  }
}

// Search container and chips styles
.search-container {
  position: relative;
  display: flex;
  align-items: center;
  min-width: 200px;

  .search-input {
    flex: 1;

    .q-field__control {
      padding: 0 8px;
    }

    .q-field__prepend {
      padding-right: 0;
    }
  }

  .search-chips-container {
    display: flex;
    flex-wrap: nowrap;
    max-width: none;
    gap: 4px;
    overflow-x: auto;
    white-space: nowrap;
    scrollbar-width: none; /* For Firefox */
    
    /* Hide scrollbar for Chrome/Safari */
    &::-webkit-scrollbar {
      display: none;
    }

    .search-chip {
      margin: 0;
      flex-shrink: 0;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      cursor: pointer;
      transition: all 0.2s ease;
      
      &:hover {
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        transform: translateY(-1px);
      }
      
      &:active {
        transform: translateY(0);
      }
    }
  }
}

// Remove underlines from all links
.text-decoration-none {
  text-decoration: none;
}

// CSS styles for advanced search dialog
.advanced-search-dialog {
  width: 350px;
  max-width: 90vw;

  .q-item {
    min-height: 40px;
  }

  .q-item-label.header {
    padding: 8px 16px 4px;
    font-size: 0.8rem;
    letter-spacing: 0.5px;
    color: var(--q-primary);
  }

  .q-icon {
    font-size: 1.2rem;
  }
}

// Add a general style to prevent text wrapping in q-item-label elements
.q-item-label {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

// Add styles for dialog text headings
.q-dialog {
  .text-h6 {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}