package handler

import (
	"net/http"

	"github.com/Sakura-Byte/kikoeru-go/pkg/http/rest/common"
	"github.com/Sakura-Byte/kikoeru-go/pkg/log"
	"github.com/Sakura-Byte/kikoeru-go/pkg/ports" // Import ports package
	"github.com/gin-gonic/gin"
)

type SummaryHandler struct {
	service ports.SummaryService // Changed to ports.SummaryService
}

// NewSummaryHandler creates a new SummaryHandler
func NewSummaryHandler(ss ports.SummaryService) *SummaryHandler {
	return &SummaryHandler{
		service: ss,
	}
}

// ListCircles godoc
// @Summary List all circles with work counts
// @Description Get a list of all circles and the number of works associated with each.
// @Tags Summary
// @Produce json
// @Success 200 {array} database.CircleWithWorkCount
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/circles [get]
func (h *SummaryHandler) ListCircles(c *gin.Context) {
	circles, _, err := h.service.ListAllCirclesWithWorkCount(c.Request.Context())
	if err != nil {
		log.Error(c.Request.Context(), "Failed to list circles with work count", "error", err)
		common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to list circles with work count")
		return
	}
	common.SendSuccessResponse(c, http.StatusOK, circles)
}

// ListTags godoc
// @Summary List all tags with work counts
// @Description Get a list of all tags and the number of works associated with each.
// @Tags Summary
// @Produce json
// @Success 200 {array} database.TagWithWorkCount
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/tags [get]
func (h *SummaryHandler) ListTags(c *gin.Context) {
	tags, err := h.service.ListAllTagsWithWorkCount(c.Request.Context())
	if err != nil {
		log.Error(c.Request.Context(), "Failed to list tags with work count", "error", err)
		common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to list tags with work count")
		return
	}
	common.SendSuccessResponse(c, http.StatusOK, tags)
}

// ListVAs godoc
// @Summary List all VAs with work counts
// @Description Get a list of all VAs (Voice Actors) and the number of works associated with each.
// @Tags Summary
// @Produce json
// @Success 200 {array} database.VAWithWorkCount
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/vas [get]
func (h *SummaryHandler) ListVAs(c *gin.Context) {
	vas, err := h.service.ListAllVAsWithWorkCount(c.Request.Context())
	if err != nil {
		log.Error(c.Request.Context(), "Failed to list VAs with work count", "error", err)
		common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to list VAs with work count")
		return
	}
	common.SendSuccessResponse(c, http.StatusOK, vas)
}
