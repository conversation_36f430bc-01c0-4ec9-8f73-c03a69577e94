/**
 * Utility functions for lyric file discovery and matching
 */

/**
 * Get basename of file without extension
 * @param {string} filename - The filename to process
 * @returns {string} - Basename without extension
 */
export function basenameWithoutExt(filename) {
  if (!filename) return ''
  
  // Get the last part after any path separators
  const basename = filename.split(/[/\\]/).pop()
  
  // Remove extension
  const lastDotIndex = basename.lastIndexOf('.')
  if (lastDotIndex === -1) return basename
  
  return basename.substring(0, lastDotIndex)
}

/**
 * Calculate edit distance between two strings
 * @param {string} s1 - First string
 * @param {string} s2 - Second string
 * @returns {number} - Edit distance
 */
function editDistance(s1, s2) {
  const matrix = []
  
  // Initialize matrix
  for (let i = 0; i <= s2.length; i++) {
    matrix[i] = [i]
  }
  for (let j = 0; j <= s1.length; j++) {
    matrix[0][j] = j
  }
  
  // Fill matrix
  for (let i = 1; i <= s2.length; i++) {
    for (let j = 1; j <= s1.length; j++) {
      if (s2.charAt(i - 1) === s1.charAt(j - 1)) {
        matrix[i][j] = matrix[i - 1][j - 1]
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j - 1] + 1, // substitution
          matrix[i][j - 1] + 1,     // insertion
          matrix[i - 1][j] + 1      // deletion
        )
      }
    }
  }
  
  return matrix[s2.length][s1.length]
}

/**
 * Calculate similarity between two strings
 * @param {string} s1 - First string
 * @param {string} s2 - Second string
 * @returns {[number, number]} - [similarity ratio, edit distance]
 */
export function similarity(s1, s2) {
  let longer = s1
  let shorter = s2
  if (s1.length < s2.length) {
    longer = s2
    shorter = s1
  }
  const longerLength = longer.length
  if (longerLength === 0) {
    return [1.0, 0]
  }
  
  const ed = editDistance(longer, shorter)
  const sim = (longerLength - ed) / longerLength
  return [sim, ed]
}

/**
 * Calculate bidirectional similarity between two strings
 * @param {string} s1 - First string
 * @param {string} s2 - Second string
 * @returns {number} - Bidirectional similarity ratio
 */
export function bidirectionSimilarity(s1, s2) {
  let longer = s1
  let shorter = s2
  if (s1.length < s2.length) {
    longer = s2
    shorter = s1
  }
  const longerLength = longer.length
  const shorterLength = shorter.length

  if (longerLength === 0) {
    return 1.0
  }

  const buf = Array(longerLength).fill(0)
  for (let i = 0; i < shorterLength; ++i) {
    if (longer[i] === shorter[i]) buf[i]++
    if (longer[longerLength - i - 1] === shorter[shorterLength - i - 1]) buf[longerLength - i - 1]++
  }

  const samePortion = buf.reduce((acc, x) => acc + (x === 0 ? 0 : 1), 0)
  const value = samePortion / shorterLength
  return value
}

/**
 * Check if audio filename matches lyric filename
 * @param {string} aname - Audio filename
 * @param {string} lname - Lyric filename (without extension)
 * @returns {boolean} - Whether they match
 */
export function audioLyricNameMatch(aname, lname) {
  const oname = basenameWithoutExt(aname)
  const dname = lname
  
  if (oname === dname) return true // 完全相等
  else if (oname.includes(dname)) return true

  // 相似性判断，要排除一种情况就是作品文件名称之间极其相似，只有数字序号不同，这个时候相似度极高，需要特殊处理
  
  // 针对SEなし SEなし 这类文件名进行处理
  if (oname.includes("あり") && oname.replace(/あり/g, "なし") === dname) {
    return true
  }
  if (oname.includes("なし") && oname.replace(/なし/g, "あり") === dname) {
    return true
  }

  // 去掉文件名中所有的数字后，检查字符串是否一致，
  // 如果一致，说明两个文件名只有数字不同，不进行任何相似度判断
  // 直接判定为不同的两个文件
  const digitDetector = /\d/g
  if (digitDetector.test(oname) && digitDetector.test(dname) && oname.replace(digitDetector, "") === dname.replace(digitDetector, "")) {
    return false
  }

  const [sim, ed] = similarity(oname, dname)
  if (oname.length === dname.length && ed <= 2) {
    // 如果两个字符串长度一样，编辑距离相差小于2，则认为两者是仅序号不同的文件，将其判定为不匹配的音频和字幕
    return false
  }

  if (sim > 0.8) return true
  else if (bidirectionSimilarity(oname, dname) > 0.9) return true

  return false
}

/**
 * Check if file has lyric extension
 * @param {string} filename - The filename to check
 * @returns {boolean} - Whether it's a lyric file
 */
export function isLyricFile(filename) {
  if (!filename) return false
  const ext = filename.toLowerCase().split('.').pop()
  return ['lrc', 'ass', 'vtt', 'srt'].includes(ext)
}

/**
 * Get file extension
 * @param {string} filename - The filename
 * @returns {string} - File extension (without dot)
 */
export function getFileExtension(filename) {
  if (!filename) return ''
  const parts = filename.toLowerCase().split('.')
  return parts.length > 1 ? parts.pop() : ''
}

/**
 * Calculate similarity percentage for display
 * @param {string} audioName - Audio filename
 * @param {string} lyricName - Lyric filename
 * @returns {number} - Similarity percentage (0-100)
 */
export function calculateSimilarityPercentage(audioName, lyricName) {
  const oname = basenameWithoutExt(audioName)
  const dname = basenameWithoutExt(lyricName)
  
  if (oname === dname) return 100
  
  const [sim] = similarity(oname, dname)
  const bidirSim = bidirectionSimilarity(oname, dname)
  
  return Math.round(Math.max(sim, bidirSim) * 100)
}
