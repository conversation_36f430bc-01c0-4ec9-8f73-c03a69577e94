package ports

import (
	"context"

	playhistory_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/playhistory" // Added DTO import
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
)

// PlayHistoryService defines the application service interface for play history business logic.
// This interface is a "driven" port, implemented by the service layer and used by handlers.
type PlayHistoryService interface {
	RecordPlayPosition(ctx context.Context, userID string, req playhistory_dto.RecordPlayPositionRequest) error
	GetUserPlayHistory(
		ctx context.Context, userID string, page int, pageSize int, orderBy string, sortDirection string,
	) (records []*models.PlayHistory, totalCount int64, err error)
	DeletePlayHistory(ctx context.Context, userID string, historyID uint) error
}
