package ports

import (
	"context"
)

// ScanStatus represents the status of a scan task. Moved from scanner.
type ScanStatus string

const (
	StatusIdle                      ScanStatus = "idle"
	StatusScanningAllStorageSources ScanStatus = "scanning_all_storage_sources"
	StatusScanningStorageSource     ScanStatus = "scanning_storage_source"
	StatusError                     ScanStatus = "error"
)

// ScannerStatusResponse provides information about the current scanner status. Moved from scanner.
type ScannerStatusResponse struct {
	Status                   ScanStatus `json:"status"`
	CurrentStorageIdentifier string     `json:"current_storage_identifier,omitempty"`
	ProcessedWorks           int        `json:"processed_works,omitempty"`
	TotalWorksInLib          int        `json:"total_works_in_lib,omitempty"`
	LastError                string     `json:"last_error,omitempty"`
	Message                  string     `json:"message,omitempty"`
}

// ProgressUpdater is a function type for updating task progress. Moved from scanner.
type ProgressUpdater func(taskID string, progress float64, message string) error

// ScannerService defines the application service interface for scanning operations.
// This interface is a "driven" port, implemented by the scanner package and used by handlers/other services.
type ScannerService interface {
	GetStatus() ScannerStatusResponse
	StopScan()
	ScanAllLibraries(ctx context.Context) error
	ScanLibrary(ctx context.Context, storageSourceIdentifier string, taskIDForProgress string, updater ProgressUpdater) error
}
