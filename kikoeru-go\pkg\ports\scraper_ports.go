package ports

import (
	"context"

	scraper_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/scraper" // For models.Work, models.ScrapedWorkData
)

// ScrapeOptions defines options for a scraping operation.
type ScrapeOptions struct {
	ForceUpdate        bool
	ScrapeMetadata     bool
	ScrapeCover        bool
	DownloadCover      bool
	TargetCoverRelPath string // Optional: if cover needs to be saved to a specific relative path within work's dir
	PreferredLang      string // e.g., "ja-jp", "en-us"
}

// ScraperService defines the interface for scraping work metadata and covers.
type ScraperService interface {
	// ScrapeDLsiteWorkByRJID scrapes data for a given RJID from DLSite.
	ScrapeDLsiteWorkByRJID(ctx context.Context, rjID string, language string) (*scraper_dto.ScrapedWorkData, error)

	// ScrapeWorkDataAndCover performs scraping based on an originalID and options,
	// returning the scraped data and raw main and sam cover image bytes if downloaded.
	// It does not interact directly with the database Work model for updates.
	ScrapeWorkDataAndCover(ctx context.Context, originalID string, options ScrapeOptions) (*scraper_dto.ScrapedWorkData, []byte, []byte, error) // Modified to return two byte slices
	// TriggerScrapeForWork and TriggerScrapeAllWorks will be handled by WorkService,
	// which will call ScrapeWorkDataAndCover.
}

// TriggerScrapeRequest defines the parameters for manually triggering a scrape operation.
// This DTO is used by services like WorkService, BackgroundTaskService, and ScannerService.
type TriggerScrapeRequest struct {
	ForceUpdate   bool   `json:"force_update"`
	PreferredLang string `json:"preferred_lang,omitempty"`
}
