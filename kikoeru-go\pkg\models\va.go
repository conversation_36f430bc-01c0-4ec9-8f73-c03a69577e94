package models

import (
	"time"

	"github.com/Sakura-Byte/kikoeru-go/pkg/utils"
	"gorm.io/gorm"
)

// VA 对应数据库中的 t_va 表 (声优)
type VA struct {
	ID        string    `gorm:"primaryKey;type:varchar(36)" json:"id"`              // UUID v5, 基于 name 生成的固定值
	Name      string    `gorm:"type:varchar(255);not null;uniqueIndex" json:"name"` // 声优名称通常应该是唯一的
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	WorkCount int64     `gorm:"-" json:"work_count,omitempty"` // Used to store count of associated works

	// 反向关联 (可选, GORM 可以处理)
	Works []*Work `gorm:"many2many:r_work_vas;" json:"works,omitempty"`
}

// TableName 指定 GORM 使用的表名
func (VA) TableName() string {
	return "t_va"
}

// BeforeCreate hook to generate UUID v5 based on VA name
func (v *VA) BeforeCreate(tx *gorm.DB) (err error) {
	if v.ID == "" && v.Name != "" {
		v.ID, err = utils.GenerateUUIDv5ForName(v.Name)
		if err != nil {
			return err
		}
	}
	return
}

// r_work_vas 是 Work 和 VA 之间的连接表。
// GORM 会自动创建和管理这个表，如果它不存在并且你在模型中定义了 many2many 关系。
// GORM 标签中的 "r_work_vas" 已经指定了连接表的名称。
