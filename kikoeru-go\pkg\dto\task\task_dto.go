package task_dto

import (
	"encoding/json"
	"time"
)

// ScanLibraryTaskPayload defines the payload for a scan library task.
type ScanLibraryTaskPayload struct {
	StorageID string `json:"storage_id"` // Changed from LibraryName to StorageID
}

// ScrapeAllWorksTaskPayload defines the payload for scraping all works.
type ScrapeAllWorksTaskPayload struct {
	ForceUpdate   bool   `json:"force_update"`
	PreferredLang string `json:"preferred_lang,omitempty"`
	// Add other relevant options from ScrapeOptions if needed
}

// ScrapeSingleWorkTaskPayload defines the payload for scraping a single work.
type ScrapeSingleWorkTaskPayload struct {
	WorkID        uint   `json:"work_id"`
	ForceUpdate   bool   `json:"force_update"`
	PreferredLang string `json:"preferred_lang,omitempty"`
}

// BackgroundTaskResponseDTO is the DTO for BackgroundTask responses.
// It mirrors models.BackgroundTask but allows for future API-specific adjustments.
type BackgroundTaskResponseDTO struct {
	ID                string          `json:"id"`
	TaskType          string          `json:"task_type"` // Based on models.TaskType (string)
	Status            string          `json:"status"`    // Based on models.TaskStatus (string)
	Progress          float64         `json:"progress"`
	Message           string          `json:"message,omitempty"`
	Payload           json.RawMessage `json:"payload,omitempty"` // Exposing raw payload for now
	Result            json.RawMessage `json:"result,omitempty"`  // Exposing raw result for now
	Error             string          `json:"error_message,omitempty"`
	SubmittedByUserID string          `json:"submitted_by_user_id,omitempty"`
	CreatedAt         time.Time       `json:"created_at"`
	StartedAt         *time.Time      `json:"started_at,omitempty"`
	CompletedAt       *time.Time      `json:"completed_at,omitempty"`
	UpdatedAt         time.Time       `json:"updated_at"`
}
