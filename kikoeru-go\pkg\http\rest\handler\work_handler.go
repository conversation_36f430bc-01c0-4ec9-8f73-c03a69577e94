package handler

import (
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"strconv"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"         // Added for apperrors
	work_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/work" // Added for DTOs
	"github.com/Sakura-Byte/kikoeru-go/pkg/http/middleware"
	"github.com/Sakura-Byte/kikoeru-go/pkg/http/rest/common"
	"github.com/Sakura-Byte/kikoeru-go/pkg/log"

	// "github.com/Sakura-Byte/kikoeru-go/pkg/models" // No longer directly used
	"github.com/Sakura-Byte/kikoeru-go/pkg/ports"
	// "github.com/Sakura-Byte/kikoeru-go/pkg/service" // Removed, using ports
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/database"
	"github.com/gin-gonic/gin"

	tag_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/tag" // Added tag_dto import
	va_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/va"   // Added va_dto import
)

type WorkHandler struct {
	service ports.WorkService
}

// WorkTagRequest is now represented by tag_dto.TagNameDTO
// WorkVARequest is now represented by va_dto.VANameDTO
type WorkTagsReplaceRequest struct {
	Names []string `json:"names"`
}
type WorkVAsReplaceRequest struct {
	Names []string `json:"names"`
}

// --- End DTOs ---

// NewWorkHandler creates a new WorkHandler
func NewWorkHandler(service ports.WorkService) *WorkHandler {
	return &WorkHandler{
		service: service,
	}
}

func (h *WorkHandler) GetWork(c *gin.Context) {
	originalID := c.Param("originalID")
	if originalID == "" {
		common.SendErrorResponse(c, http.StatusBadRequest, "Work OriginalID cannot be empty")
		return
	}

	work, err := h.service.GetWorkByOriginalID(c.Request.Context(), originalID)
	if err != nil {
		if errors.Is(err, apperrors.ErrWorkNotFound) { // Use apperrors
			common.SendErrorResponse(c, http.StatusNotFound, "Work not found")
			return
		}
		log.Error(c.Request.Context(), "Failed to get work by originalID", "original_id", originalID, "error", err)
		common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to retrieve work")
		return
	}
	common.SendSuccessResponse(c, http.StatusOK, work)
}

// GetWorkInfo handles the new endpoint GET /api/v1/works/:originalID/info
func (h *WorkHandler) GetWorkInfo(c *gin.Context) {
	originalID := c.Param("originalID")
	if originalID == "" {
		common.SendErrorResponse(c, http.StatusBadRequest, "Work originalID cannot be empty")
		return
	}

	workInfo, err := h.service.GetWorkInfoByOriginalID(c.Request.Context(), originalID)
	if err != nil {
		if errors.Is(err, apperrors.ErrWorkNotFound) { // Use apperrors
			common.SendErrorResponse(c, http.StatusNotFound, "Work not found")
			return
		}
		log.Error(c.Request.Context(), "Failed to get work info", "original_id", originalID, "error", err)
		common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to retrieve work information")
		return
	}
	common.SendSuccessResponse(c, http.StatusOK, workInfo)
}

func (h *WorkHandler) ListWorks(c *gin.Context) {
	var params database.ListWorksParams
	if err := c.ShouldBindQuery(&params); err != nil {
		log.Warn(c.Request.Context(), "Failed to bind query parameters for ListWorks", "error", err)
		common.SendErrorResponse(c, http.StatusBadRequest, fmt.Sprintf("Invalid query parameters: %v", err))
		return
	}

	// 从查询参数中获取过滤和排序选项
	// order 可选值: release, create_date, rating, dl_count, price, rate_average_2dp, review_count
	orderParam := c.Query("order")
	if orderParam != "" {
		switch orderParam {
		case "release":
			params.SortBy = "release_date"
		case "create_date":
			params.SortBy = "created_at"
		case "rating":
			params.SortBy = "rate_average_2dp"
		case "dl_count":
			params.SortBy = "dl_count"
		case "price":
			params.SortBy = "price"
		case "rate_average_2dp":
			params.SortBy = "rate_average_2dp"
		case "review_count":
			params.SortBy = "review_count"
		default:
			params.SortBy = "id"
		}
	}

	// sort: desc/asc
	sortParam := c.Query("sort")
	if sortParam == "desc" || sortParam == "asc" {
		params.SortOrder = sortParam
	}

	// subtitle=0 表示不启用基于 subtitlestatus 的筛选，=1 表示仅显示有字幕的作品
	subtitleParam := c.Query("subtitle")
	if subtitleParam == "1" {
		// 设置筛选条件，只显示有字幕的作品
		lyricStatus := "有字幕"
		params.LyricStatus = &lyricStatus
	}

	works, totalCount, err := h.service.ListWorks(c.Request.Context(), params)
	if err != nil {
		log.Error(c.Request.Context(), "Failed to list works", "params", fmt.Sprintf("%+v", params), "error", err)
		common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to retrieve works")
		return
	}
	common.SendPaginatedResponse(c, http.StatusOK, works, totalCount, params.Page, params.PageSize)
}

// SearchWorks 处理搜索端点 GET /api/v1/search/:query
func (h *WorkHandler) SearchWorks(c *gin.Context) {
	var params database.ListWorksParams
	if err := c.ShouldBindQuery(&params); err != nil {
		log.Warn(c.Request.Context(), "Failed to bind query parameters for SearchWorks", "error", err)
		common.SendErrorResponse(c, http.StatusBadRequest, fmt.Sprintf("Invalid query parameters: %v", err))
		return
	}

	// 获取搜索查询
	query := c.Param("query")
	if query == "" {
		common.SendErrorResponse(c, http.StatusBadRequest, "Search query cannot be empty")
		return
	}

	// URL解码
	decodedQuery, err := url.QueryUnescape(query)
	if err != nil {
		log.Warn(c.Request.Context(), "Failed to decode search query", "query", query, "error", err)
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid search query format")
		return
	}

	// 设置高级搜索查询参数
	params.AdvancedSearchQuery = decodedQuery

	// 从查询参数中获取过滤和排序选项（与ListWorks相同的逻辑）
	orderParam := c.Query("order")
	if orderParam != "" {
		switch orderParam {
		case "release":
			params.SortBy = "release_date"
		case "create_date":
			params.SortBy = "created_at"
		case "rating":
			params.SortBy = "rate_average_2dp"
		case "dl_count":
			params.SortBy = "dl_count"
		case "price":
			params.SortBy = "price"
		case "rate_average_2dp":
			params.SortBy = "rate_average_2dp"
		case "review_count":
			params.SortBy = "review_count"
		default:
			params.SortBy = "id"
		}
	}

	sortParam := c.Query("sort")
	if sortParam == "desc" || sortParam == "asc" {
		params.SortOrder = sortParam
	}

	subtitleParam := c.Query("subtitle")
	if subtitleParam == "1" {
		lyricStatus := "有字幕"
		params.LyricStatus = &lyricStatus
	}

	// 调用service层的方法进行搜索
	works, totalCount, err := h.service.ListWorks(c.Request.Context(), params)
	if err != nil {
		log.Error(c.Request.Context(), "Failed to search works", "query", decodedQuery, "params", fmt.Sprintf("%+v", params), "error", err)
		common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to search works")
		return
	}

	common.SendPaginatedResponse(c, http.StatusOK, works, totalCount, params.Page, params.PageSize)
}
func (h *WorkHandler) GetRandomQueue(c *gin.Context) {
	var params work_dto.RandomQueueParams
	if err := c.ShouldBindQuery(&params); err != nil {
		log.Warn(c.Request.Context(), "Failed to bind query parameters for GetRandomQueue", "error", err)
		common.SendErrorResponse(c, http.StatusBadRequest, fmt.Sprintf("Invalid query parameters: %v", err))
		return
	}
	claims := middleware.GetUserClaims(c)
	if claims == nil && params.SourcePlaylistID != nil && *params.SourcePlaylistID > 0 {
		log.Warn(c.Request.Context(), "Attempt to get random queue from specific playlist without valid claims")
		common.SendErrorResponse(c, http.StatusUnauthorized, "Authentication required to generate queue from specific playlist")
		return
	}
	works, err := h.service.GetRandomWorkQueue(c.Request.Context(), claims, params)
	if err != nil {
		log.Error(c.Request.Context(), "Failed to get random work queue", "params", fmt.Sprintf("%+v", params), "error", err)
		common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to generate random queue")
		return
	}
	if len(works) == 0 {
		common.SendSuccessResponse(c, http.StatusOK, []gin.H{})
		return
	}
	common.SendSuccessResponse(c, http.StatusOK, works)
}
func (h *WorkHandler) UpdateWorkByAdmin(c *gin.Context) {
	authClaims := middleware.GetUserClaims(c)
	if authClaims == nil {
		common.SendErrorResponse(c, http.StatusUnauthorized, "Authentication required")
		return
	}
	idStr := c.Param("workID")
	workID, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid work ID format")
		return
	}
	var req work_dto.UpdateWorkRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Warn(c.Request.Context(), "Failed to bind JSON for UpdateWorkByAdmin", "error", err)
		common.SendErrorResponse(c, http.StatusBadRequest, fmt.Sprintf("Invalid request payload: %v", err))
		return
	}
	updatedWork, err := h.service.UpdateWorkByAdmin(c.Request.Context(), authClaims, uint(workID), req)
	if err != nil {
		if errors.Is(err, apperrors.ErrWorkNotFound) { // Use apperrors
			common.SendErrorResponse(c, http.StatusNotFound, "Work not found")
		} else if errors.Is(err, apperrors.ErrAdminAccessDenied) { // Use apperrors
			common.SendErrorResponse(c, http.StatusForbidden, "Admin access denied")
		} else {
			log.Error(c.Request.Context(), "Failed to update work by admin", "work_id", workID, "error", err)
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to update work")
		}
		return
	}
	common.SendSuccessResponse(c, http.StatusOK, updatedWork)
}
func (h *WorkHandler) DeleteWorkByAdmin(c *gin.Context) {
	authClaims := middleware.GetUserClaims(c)
	if authClaims == nil {
		common.SendErrorResponse(c, http.StatusUnauthorized, "Authentication required")
		return
	}
	idStr := c.Param("workID")
	workID, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid work ID format")
		return
	}
	err = h.service.DeleteWorkByAdmin(c.Request.Context(), authClaims, uint(workID))
	if err != nil {
		if errors.Is(err, apperrors.ErrWorkNotFound) { // Use apperrors
			common.SendErrorResponse(c, http.StatusNotFound, "Work not found")
		} else if errors.Is(err, apperrors.ErrAdminAccessDenied) { // Use apperrors
			common.SendErrorResponse(c, http.StatusForbidden, "Admin access denied")
		} else {
			log.Error(c.Request.Context(), "Failed to delete work by admin", "work_id", workID, "error", err)
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to delete work")
		}
		return
	}
	common.SendSuccessResponse(c, http.StatusOK, gin.H{"message": fmt.Sprintf("Work %d deleted successfully", workID)})
}
func (h *WorkHandler) AddTagToWork(c *gin.Context) {
	authClaims := middleware.GetUserClaims(c)
	if authClaims == nil {
		common.SendErrorResponse(c, http.StatusUnauthorized, "Authentication required")
		return
	}
	workIDStr := c.Param("workID")
	workID, err := strconv.ParseUint(workIDStr, 10, 32)
	if err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid work ID")
		return
	}
	var req tag_dto.TagNameDTO // Changed to tag_dto.TagNameDTO
	if err := c.ShouldBindJSON(&req); err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid request: "+err.Error())
		return
	}
	err = h.service.AddTagToWorkByName(c.Request.Context(), authClaims, uint(workID), req.Name)
	if err != nil {
		if errors.Is(err, apperrors.ErrWorkNotFound) || errors.Is(err, apperrors.ErrTagNotFound) { // Use apperrors
			common.SendErrorResponse(c, http.StatusNotFound, err.Error())
		} else if errors.Is(err, apperrors.ErrAdminAccessDenied) { // Use apperrors
			common.SendErrorResponse(c, http.StatusForbidden, err.Error())
		} else {
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to add tag to work: "+err.Error())
		}
		return
	}
	common.SendSuccessResponse(c, http.StatusOK, gin.H{"message": "Tag added to work successfully"})
}
func (h *WorkHandler) RemoveTagFromWork(c *gin.Context) {
	authClaims := middleware.GetUserClaims(c)
	if authClaims == nil {
		common.SendErrorResponse(c, http.StatusUnauthorized, "Authentication required")
		return
	}
	workIDStr := c.Param("workID")
	workID, err := strconv.ParseUint(workIDStr, 10, 32)
	if err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid work ID")
		return
	}
	var req tag_dto.TagNameDTO // Changed to tag_dto.TagNameDTO
	if err := c.ShouldBindJSON(&req); err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid request: "+err.Error())
		return
	}
	err = h.service.RemoveTagFromWorkByName(c.Request.Context(), authClaims, uint(workID), req.Name)
	if err != nil {
		if errors.Is(err, apperrors.ErrWorkNotFound) || errors.Is(err, apperrors.ErrTagNotFound) { // Use apperrors
			common.SendErrorResponse(c, http.StatusNotFound, err.Error())
		} else if errors.Is(err, apperrors.ErrAdminAccessDenied) { // Use apperrors
			common.SendErrorResponse(c, http.StatusForbidden, err.Error())
		} else {
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to remove tag from work: "+err.Error())
		}
		return
	}
	common.SendSuccessResponse(c, http.StatusOK, gin.H{"message": "Tag removed from work successfully"})
}
func (h *WorkHandler) ReplaceWorkTags(c *gin.Context) {
	authClaims := middleware.GetUserClaims(c)
	if authClaims == nil {
		common.SendErrorResponse(c, http.StatusUnauthorized, "Authentication required")
		return
	}
	workIDStr := c.Param("workID")
	workID, err := strconv.ParseUint(workIDStr, 10, 32)
	if err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid work ID")
		return
	}
	var req WorkTagsReplaceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid request: "+err.Error())
		return
	}
	err = h.service.ReplaceWorkTagsByNames(c.Request.Context(), authClaims, uint(workID), req.Names)
	if err != nil {
		if errors.Is(err, apperrors.ErrWorkNotFound) { // Use apperrors
			common.SendErrorResponse(c, http.StatusNotFound, err.Error())
		} else if errors.Is(err, apperrors.ErrAdminAccessDenied) { // Use apperrors
			common.SendErrorResponse(c, http.StatusForbidden, err.Error())
		} else {
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to replace work tags: "+err.Error())
		}
		return
	}
	common.SendSuccessResponse(c, http.StatusOK, gin.H{"message": "Work tags replaced successfully"})
}
func (h *WorkHandler) AddVAToWork(c *gin.Context) {
	authClaims := middleware.GetUserClaims(c)
	if authClaims == nil {
		common.SendErrorResponse(c, http.StatusUnauthorized, "Authentication required")
		return
	}
	workIDStr := c.Param("workID")
	workID, err := strconv.ParseUint(workIDStr, 10, 32)
	if err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid work ID")
		return
	}
	var req va_dto.VANameDTO // Changed to va_dto.VANameDTO
	if err := c.ShouldBindJSON(&req); err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid request: "+err.Error())
		return
	}
	err = h.service.AddVAToWorkByName(c.Request.Context(), authClaims, uint(workID), req.Name)
	if err != nil {
		if errors.Is(err, apperrors.ErrWorkNotFound) || errors.Is(err, apperrors.ErrVANotFound) { // Use apperrors
			common.SendErrorResponse(c, http.StatusNotFound, err.Error())
		} else if errors.Is(err, apperrors.ErrAdminAccessDenied) { // Use apperrors
			common.SendErrorResponse(c, http.StatusForbidden, err.Error())
		} else {
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to add VA to work: "+err.Error())
		}
		return
	}
	common.SendSuccessResponse(c, http.StatusOK, gin.H{"message": "VA added to work successfully"})
}
func (h *WorkHandler) RemoveVAFromWork(c *gin.Context) {
	authClaims := middleware.GetUserClaims(c)
	if authClaims == nil {
		common.SendErrorResponse(c, http.StatusUnauthorized, "Authentication required")
		return
	}
	workIDStr := c.Param("workID")
	workID, err := strconv.ParseUint(workIDStr, 10, 32)
	if err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid work ID")
		return
	}
	vaName, err := url.PathUnescape(c.Param("vaName"))
	if err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid VA name in URL")
		return
	}
	err = h.service.RemoveVAFromWorkByName(c.Request.Context(), authClaims, uint(workID), vaName)
	if err != nil {
		if errors.Is(err, apperrors.ErrWorkNotFound) || errors.Is(err, apperrors.ErrVANotFound) { // Use apperrors
			common.SendErrorResponse(c, http.StatusNotFound, err.Error())
		} else if errors.Is(err, apperrors.ErrAdminAccessDenied) { // Use apperrors
			common.SendErrorResponse(c, http.StatusForbidden, err.Error())
		} else {
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to remove VA from work: "+err.Error())
		}
		return
	}
	common.SendSuccessResponse(c, http.StatusOK, gin.H{"message": "VA removed from work successfully"})
}
func (h *WorkHandler) ReplaceWorkVAs(c *gin.Context) {
	authClaims := middleware.GetUserClaims(c)
	if authClaims == nil {
		common.SendErrorResponse(c, http.StatusUnauthorized, "Authentication required")
		return
	}
	workIDStr := c.Param("workID")
	workID, err := strconv.ParseUint(workIDStr, 10, 32)
	if err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid work ID")
		return
	}
	var req WorkVAsReplaceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid request: "+err.Error())
		return
	}
	err = h.service.ReplaceWorkVAsByNames(c.Request.Context(), authClaims, uint(workID), req.Names)
	if err != nil {
		if errors.Is(err, apperrors.ErrWorkNotFound) { // Use apperrors
			common.SendErrorResponse(c, http.StatusNotFound, err.Error())
		} else if errors.Is(err, apperrors.ErrAdminAccessDenied) { // Use apperrors
			common.SendErrorResponse(c, http.StatusForbidden, err.Error())
		} else {
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to replace work VAs: "+err.Error())
		}
		return
	}
	common.SendSuccessResponse(c, http.StatusOK, gin.H{"message": "Work VAs replaced successfully"})
}

// --- Manual Scrape Triggers (Admin) ---

func (h *WorkHandler) TriggerScrapeForWorkByAdmin(c *gin.Context) {
	authClaims := middleware.GetUserClaims(c)
	if authClaims == nil {
		common.SendErrorResponse(c, http.StatusUnauthorized, "Invalid or missing authentication claims")
		return
	}

	workIDStr := c.Param("workID")
	workID, err := strconv.ParseUint(workIDStr, 10, 32)
	if err != nil {
		common.SendErrorResponse(c, http.StatusBadRequest, "Invalid work ID format")
		return
	}

	var req ports.TriggerScrapeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Warn(c.Request.Context(), "Failed to bind JSON for TriggerScrapeForWork", "error", err)
		common.SendErrorResponse(c, http.StatusBadRequest, fmt.Sprintf("Invalid request payload: %v", err))
		return
	}

	scrapeOptions := ports.ScrapeOptions{
		ForceUpdate:    req.ForceUpdate,
		PreferredLang:  req.PreferredLang,
		ScrapeMetadata: true,
		ScrapeCover:    true,
		DownloadCover:  true,
	}
	err = h.service.TriggerScrapeForWorkByAdmin(c.Request.Context(), authClaims, uint(workID), scrapeOptions)
	if err != nil {
		if errors.Is(err, apperrors.ErrWorkNotFound) { // Use apperrors
			common.SendErrorResponse(c, http.StatusNotFound, "Work not found to scrape")
		} else if errors.Is(err, apperrors.ErrAdminAccessDenied) { // Use apperrors
			common.SendErrorResponse(c, http.StatusForbidden, "Admin access denied")
		} else {
			log.Error(c.Request.Context(), "Failed to trigger scrape for work", "work_id", workID, "error", err)
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to trigger scrape for work: "+err.Error())
		}
		return
	}
	common.SendSuccessResponse(c, http.StatusAccepted, gin.H{"message": "Scrape task for work accepted"})
}

func (h *WorkHandler) TriggerScrapeForAllWorksByAdmin(c *gin.Context) {
	authClaims := middleware.GetUserClaims(c)
	if authClaims == nil {
		common.SendErrorResponse(c, http.StatusUnauthorized, "Invalid or missing authentication claims")
		return
	}

	var req ports.TriggerScrapeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Warn(c.Request.Context(), "Failed to bind JSON for TriggerScrapeForAllWorks", "error", err)
		common.SendErrorResponse(c, http.StatusBadRequest, fmt.Sprintf("Invalid request payload: %v", err))
		return
	}

	scrapeOptions := ports.ScrapeOptions{
		ForceUpdate:    req.ForceUpdate,
		PreferredLang:  req.PreferredLang,
		ScrapeMetadata: true,
		ScrapeCover:    true,
		DownloadCover:  true,
	}
	err := h.service.TriggerScrapeForAllWorksByAdmin(c.Request.Context(), authClaims, scrapeOptions)
	if err != nil {
		if errors.Is(err, apperrors.ErrAdminAccessDenied) { // Use apperrors
			common.SendErrorResponse(c, http.StatusForbidden, "Admin access denied")
		} else {
			log.Error(c.Request.Context(), "Failed to trigger scrape for all works", "error", err)
			common.SendErrorResponse(c, http.StatusInternalServerError, "Failed to trigger scrape for all works: "+err.Error())
		}
		return
	}
	common.SendSuccessResponse(c, http.StatusAccepted, gin.H{"message": "Scrape task for all works accepted"})
}
