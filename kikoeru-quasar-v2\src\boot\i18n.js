import { defineBoot } from '#q-app/wrappers'
import { createI18n } from 'vue-i18n'
import messages from 'src/i18n'
import { LocalStorage } from 'quasar'

// Get the browser's language
const getBrowserLocale = () => {
  const navigatorLang = navigator.language
  const browserLang = navigatorLang.split('-')[0]
  const locales = Object.keys(messages)

  // Check if the full browser locale is supported
  if (locales.includes(navigatorLang)) {
    return navigatorLang
  }

  // Check if we support any language that begins with the browser language
  const matchedLocale = locales.find(locale => locale.startsWith(browserLang))
  if (matchedLocale) {
    return matchedLocale
  }

  return 'en-US' // Default locale
}

// Create the i18n instance outside the boot function to export it
const savedLocale = typeof localStorage !== 'undefined'
  ? LocalStorage.getItem('locale') || getBrowserLocale()
  : 'en-US'

// Create i18n instance with Vue 3 compatible options
const i18n = createI18n({
  legacy: false, // Use Composition API mode for Vue 3
  locale: savedLocale,
  fallbackLocale: 'en-US',
  globalInjection: true,
  messages
})

export default defineBoot(({ app }) => {
  // Set i18n instance on app
  // Important: Only use it once to avoid "Plugin has already been applied" warning
  app.use(i18n)
})

// Export the i18n instance for use in components
export { i18n }
