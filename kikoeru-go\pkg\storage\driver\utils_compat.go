package driver

import (
	"crypto/md5"
	"encoding/hex"
	"os"
	"path/filepath"
	"strings"
)

// GetMD5EncodeStr generates an MD5 hex string for the given text.
func GetMD5EncodeStr(text string) string {
	hasher := md5.New()
	hasher.Write([]byte(text))
	return hex.EncodeToString(hasher.Sum(nil))
}

// GetFileType determines the file type based on its extension.
// It returns constants defined in alist_model.go (e.g., ConfTypeVideo).
func GetFileType(filename string) string {
	ext := strings.ToLower(filepath.Ext(filename))
	switch ext {
	case ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".webp", ".heic", ".heif", ".avif":
		return ConfTypeImage
	case ".mp4", ".mkv", ".avi", ".mov", ".wmv", ".flv", ".webm", ".ts", ".mpg", ".mpeg", ".m2ts", ".ogm":
		return ConfTypeVideo
	case ".mp3", ".flac", ".wav", ".ogg", ".aac", ".m4a", ".opus", ".ape", ".dsf", ".dff":
		return ConfTypeAudio
	case ".txt", ".md", ".srt", ".ass", ".lrc", ".json", ".yaml", ".xml", ".html", ".css", ".js", ".log", ".ini", ".cfg":
		return ConfTypeText
	// Add more types as needed
	default:
		return "application/octet-stream" // Or a generic "unknown" or "application" type
	}
}

// Exists checks if a file or directory exists.
func Exists(path string) bool {
	_, err := os.Stat(path)
	return err == nil || os.IsExist(err)
}

// IsDir checks if a path is a directory.
func IsDir(path string) bool {
	s, err := os.Stat(path)
	if err != nil {
		return false
	}
	return s.IsDir()
}

// IsFile checks if a path is a regular file.
func IsFile(path string) bool {
	s, err := os.Stat(path)
	if err != nil {
		return false
	}
	return !s.IsDir()
}
