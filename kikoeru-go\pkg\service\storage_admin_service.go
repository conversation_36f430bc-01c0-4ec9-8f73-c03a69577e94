package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
	"github.com/Sakura-Byte/kikoeru-go/pkg/config"
	dto_storage "github.com/Sakura-Byte/kikoeru-go/pkg/dto/storage" // Added DTO import
	"github.com/Sakura-Byte/kikoeru-go/pkg/log"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"github.com/Sakura-Byte/kikoeru-go/pkg/ports" // Import ports package
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/database"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/driver"
)

// Error definitions moved to kikoeru-go/pkg/apperrors/errors.go

// CreateStorageSourceRequest and UpdateStorageSourceRequest DTOs have been moved to github.com/Sakura-Byte/kikoeru-go/pkg/ports

// StorageAdminService interface has been moved to github.com/Sakura-Byte/kikoeru-go/pkg/ports

type storageAdminService struct {
	storageRepo   database.StorageSourceRepository
	workRepo      database.WorkRepository
	tagRepo       database.TagRepository
	vaRepo        database.VARepository
	circleRepo    database.CircleRepository
	driverManager *driver.StorageDriverManager
	appConfig     *config.AppConfig
}

// Ensure storageAdminService implements ports.StorageAdminService
var _ ports.StorageAdminService = (*storageAdminService)(nil)

func NewStorageAdminService(
	storageRepo database.StorageSourceRepository,
	_ database.UserRepository,
	driverManager *driver.StorageDriverManager,
	appConfig *config.AppConfig,
	workRepo database.WorkRepository,
	tagRepo database.TagRepository,
	vaRepo database.VARepository,
	circleRepo database.CircleRepository,
) ports.StorageAdminService { // Changed return type to ports.StorageAdminService
	return &storageAdminService{
		storageRepo:   storageRepo,
		workRepo:      workRepo,
		tagRepo:       tagRepo,
		vaRepo:        vaRepo,
		circleRepo:    circleRepo,
		driverManager: driverManager,
		appConfig:     appConfig,
	}
}

func mapModelToStorageSourceDTO(model *models.StorageSource) *dto_storage.StorageSourceResponseDTO {
	if model == nil {
		return nil
	}
	return &dto_storage.StorageSourceResponseDTO{
		ID:               model.ID,
		Order:            model.Order,
		Driver:           model.Driver,
		Addition:         model.Addition,
		Remark:           model.Remark,
		Disabled:         model.Disabled,
		DownloadStrategy: model.DownloadStrategy,
		CacheExpiration:  model.CacheExpiration,
		CreatedAt:        model.CreatedAt,
		UpdatedAt:        model.UpdatedAt,
	}
}

func (s *storageAdminService) ListStorageSources(ctx context.Context) ([]*dto_storage.StorageSourceResponseDTO, error) {
	sourcesModel, _, err := s.storageRepo.ListAll(ctx, true, 1, 0) // Assuming PageSize 0 means all for ListAll
	if err != nil {
		log.Error(ctx, "Failed to list storage sources from repository", "error", err)
		return nil, apperrors.ErrStorageSourceOperationFailed
	}
	dtos := make([]*dto_storage.StorageSourceResponseDTO, len(sourcesModel))
	for i, model := range sourcesModel {
		dtos[i] = mapModelToStorageSourceDTO(model)
	}
	return dtos, nil
}

func (s *storageAdminService) GetStorageSourceByID(ctx context.Context, id uint) (*dto_storage.StorageSourceResponseDTO, error) {
	sourceModel, err := s.storageRepo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, apperrors.ErrStorageNotFound) {
			return nil, apperrors.ErrStorageNotFound
		}
		log.Error(ctx, "Failed to get storage source by ID from repository", "id", id, "error", err)
		return nil, apperrors.ErrStorageSourceOperationFailed
	}
	return mapModelToStorageSourceDTO(sourceModel), nil
}

func (s *storageAdminService) CreateStorageSource(ctx context.Context, req dto_storage.CreateStorageSourceRequest) (*dto_storage.StorageSourceResponseDTO, error) { // Use dto_storage
	if req.Driver == "" {
		return nil, fmt.Errorf("%w: driver type cannot be empty", apperrors.ErrStorageSourceInvalidInput)
	}

	additionMapForDriver := req.Addition
	if additionMapForDriver == nil {
		additionMapForDriver = make(map[string]interface{})
	}

	if req.RootPath != "" {
		additionMapForDriver["root_folder_path"] = req.RootPath
	}
	if req.RootID != "" {
		additionMapForDriver["root_folder_id"] = req.RootID
	}

	jsonData, err := json.Marshal(additionMapForDriver)
	if err != nil {
		log.Error(ctx, "Failed to marshal driver addition to JSON", "driver", req.Driver, "error", err)
		return nil, fmt.Errorf("%w: failed to process driver addition: %v", apperrors.ErrStorageSourceInvalidInput, err)
	}
	additionJSON := string(jsonData)

	cacheExpValue := 0
	if req.CacheExpiration != nil {
		cacheExpValue = *req.CacheExpiration
	}

	downloadStrategy := driver.DownloadStrategyProxy
	if req.DownloadStrategy != "" {
		if req.DownloadStrategy != driver.DownloadStrategyProxy && req.DownloadStrategy != driver.DownloadStrategyRedirect {
			return nil, fmt.Errorf("%w: invalid download_strategy value '%s'", apperrors.ErrStorageSourceInvalidInput, req.DownloadStrategy)
		}
		downloadStrategy = req.DownloadStrategy
	}

	storage := &models.StorageSource{
		Driver:           req.Driver,
		Remark:           req.Remark,
		Order:            int(req.Order),
		Disabled:         req.Disabled,
		DownloadStrategy: downloadStrategy,
		Addition:         additionJSON,
		CacheExpiration:  cacheExpValue,
	}

	err = s.storageRepo.Create(ctx, storage)
	if err != nil {
		log.Error(ctx, "Failed to create storage source in repository", "remark", storage.Remark, "driver", storage.Driver, "error", err)
		return nil, apperrors.ErrStorageSourceOperationFailed
	}
	log.Info(ctx, "Storage source created, triggering driver reload", "id", storage.ID, "remark", storage.Remark)
	if errReload := s.driverManager.ReloadAllDrivers(ctx); errReload != nil {
		log.Error(ctx, "Failed to reload drivers after creating storage source", "id", storage.ID, "error", errReload)
		// Non-fatal for the creation itself, but log it.
	}
	return mapModelToStorageSourceDTO(storage), nil
}

func (s *storageAdminService) UpdateStorageSource(ctx context.Context, id uint, req dto_storage.UpdateStorageSourceRequest) (*dto_storage.StorageSourceResponseDTO, error) { // Use dto_storage
	if id == 0 {
		return nil, fmt.Errorf("%w: storage source ID is required for update", apperrors.ErrStorageSourceInvalidInput)
	}

	existingStorage, err := s.storageRepo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, apperrors.ErrStorageNotFound) {
			return nil, apperrors.ErrStorageNotFound
		}
		log.Error(ctx, "UpdateStorageSource: Failed to get existing storage source", "id", id, "error", err)
		return nil, apperrors.ErrStorageSourceOperationFailed
	}

	if req.Remark != nil {
		existingStorage.Remark = *req.Remark
	}
	if req.Order != nil {
		existingStorage.Order = int(*req.Order)
	}
	if req.Disabled != nil {
		existingStorage.Disabled = *req.Disabled
	}
	if req.DownloadStrategy != nil && *req.DownloadStrategy != "" {
		if *req.DownloadStrategy != driver.DownloadStrategyProxy && *req.DownloadStrategy != driver.DownloadStrategyRedirect {
			return nil, fmt.Errorf("%w: invalid download_strategy value '%s'", apperrors.ErrStorageSourceInvalidInput, *req.DownloadStrategy)
		}
		existingStorage.DownloadStrategy = *req.DownloadStrategy
	}
	if req.CacheExpiration != nil {
		existingStorage.CacheExpiration = *req.CacheExpiration
	}

	currentAdditionMap := make(map[string]interface{})
	if existingStorage.Addition != "" {
		if errUnmarshal := json.Unmarshal([]byte(existingStorage.Addition), &currentAdditionMap); errUnmarshal != nil {
			log.Error(ctx, "Failed to unmarshal existing driver addition for update", "id", id, "error", errUnmarshal)
			return nil, fmt.Errorf("failed to process existing driver addition: %w", errUnmarshal)
		}
	}

	if req.Addition != nil {
		for k, v := range req.Addition {
			currentAdditionMap[k] = v
		}
	}

	if req.RootPath != nil {
		currentAdditionMap["root_folder_path"] = *req.RootPath
		delete(currentAdditionMap, "root_folder_id")
	}
	if req.RootID != nil {
		currentAdditionMap["root_folder_id"] = *req.RootID
		delete(currentAdditionMap, "root_folder_path")
	}

	newAdditionJSON, errMarshal := json.Marshal(currentAdditionMap)
	if errMarshal != nil {
		log.Error(ctx, "Failed to marshal updated driver addition to JSON", "id", id, "error", errMarshal)
		return nil, fmt.Errorf("%w: failed to process updated driver addition: %v", apperrors.ErrStorageSourceInvalidInput, errMarshal)
	}
	existingStorage.Addition = string(newAdditionJSON)

	err = s.storageRepo.Update(ctx, existingStorage)
	if err != nil {
		log.Error(ctx, "Failed to update storage source in repository", "id", id, "error", err)
		return nil, apperrors.ErrStorageSourceOperationFailed
	}
	log.Info(ctx, "Storage source updated, triggering driver reload", "id", id)
	if errReload := s.driverManager.ReloadAllDrivers(ctx); errReload != nil {
		log.Error(ctx, "Failed to reload drivers after updating storage source", "id", id, "error", errReload)
		// Non-fatal for the update itself, but log it.
	}
	return mapModelToStorageSourceDTO(existingStorage), nil
}

func (s *storageAdminService) DeleteStorageSource(ctx context.Context, id uint) error {
	if id == 0 {
		return apperrors.ErrStorageSourceInvalidInput
	}
	_, err := s.storageRepo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, apperrors.ErrStorageNotFound) {
			return apperrors.ErrStorageNotFound
		}
		return apperrors.ErrStorageSourceOperationFailed
	}

	// First, delete all works associated with this storage source
	works, _, err := s.workRepo.List(ctx, database.ListWorksParams{
		StorageID: &id,
	})
	if err != nil {
		log.Error(ctx, "Failed to list works for storage source deletion", "id", id, "error", err)
		return apperrors.ErrStorageSourceOperationFailed
	}

	log.Info(ctx, "Deleting works associated with storage source", "id", id, "work_count", len(works))

	// Delete associated works
	for _, work := range works {
		// Delete the work
		if err := s.workRepo.Delete(ctx, work.ID); err != nil {
			log.Error(ctx, "Failed to delete work during storage source deletion", "storage_id", id, "work_id", work.ID, "error", err)
			// Continue with other works even if one fails
		}
	}

	// Then delete the storage source itself
	err = s.storageRepo.Delete(ctx, id)
	if err != nil {
		log.Error(ctx, "Failed to delete storage source", "id", id, "error", err)
		return apperrors.ErrStorageSourceOperationFailed
	}

	// After deleting the works and storage, check for orphaned entities
	s.cleanupOrphanedEntities(ctx)

	log.Info(ctx, "Storage source deleted, triggering driver reload", "id", id)
	return s.driverManager.ReloadAllDrivers(ctx)
}

// cleanupOrphanedEntities checks if tags, VAs, and circles are no longer associated with any works,
// and deletes them if they're orphaned (have 0 work count)
func (s *storageAdminService) cleanupOrphanedEntities(
	ctx context.Context,
) {
	// Clean up all orphaned tags, not just the ones we collected
	log.Info(ctx, "Checking for orphaned tags to clean up")
	tags, err := s.tagRepo.ListAllWithWorkCount(ctx)
	if err == nil {
		for _, tag := range tags {
			if tag.WorkCount == 0 {
				log.Info(ctx, "Deleting orphaned tag", "id", tag.ID, "name", tag.Name)
				if err := s.tagRepo.DeleteByID(ctx, tag.ID); err != nil {
					log.Warn(ctx, "Failed to delete orphaned tag", "id", tag.ID, "error", err)
				}
			}
		}
	} else {
		log.Warn(ctx, "Failed to list tags with work count for cleanup", "error", err)
	}

	// Clean up all orphaned VAs, not just the ones we collected
	log.Info(ctx, "Checking for orphaned VAs to clean up")
	vas, err := s.vaRepo.ListAllWithWorkCount(ctx)
	if err == nil {
		for _, va := range vas {
			if va.WorkCount == 0 {
				log.Info(ctx, "Deleting orphaned VA", "id", va.ID, "name", va.Name)
				if err := s.vaRepo.DeleteByID(ctx, va.ID); err != nil {
					log.Warn(ctx, "Failed to delete orphaned VA", "id", va.ID, "error", err)
				}
			}
		}
	} else {
		log.Warn(ctx, "Failed to list VAs with work count for cleanup", "error", err)
	}

	// Clean up all orphaned circles, not just the ones we collected
	log.Info(ctx, "Checking for orphaned circles to clean up")
	circles, _, err := s.circleRepo.ListAllWithWorkCount(ctx)
	if err == nil {
		for _, circle := range circles {
			if circle.WorkCount == 0 {
				log.Info(ctx, "Deleting orphaned circle", "id", circle.ID, "name", circle.Name)
				if err := s.circleRepo.Delete(ctx, circle.ID); err != nil {
					log.Warn(ctx, "Failed to delete orphaned circle", "id", circle.ID, "error", err)
				}
			}
		}
	} else {
		log.Warn(ctx, "Failed to list circles with work count for cleanup", "error", err)
	}
}

func (s *storageAdminService) ReloadAllDrivers(ctx context.Context) error {
	log.Info(ctx, "ReloadAllDrivers service method called.")
	return s.driverManager.ReloadAllDrivers(ctx)
}

func (s *storageAdminService) TestStorageSourceConnection(ctx context.Context, sourceConfig models.StorageSource) error {
	log.Info(ctx, "Attempting to test storage source connection", "driver", sourceConfig.Driver, "remark", sourceConfig.Remark)
	if sourceConfig.Driver == "" {
		return fmt.Errorf("%w: driver type cannot be empty", apperrors.ErrStorageSourceInvalidInput)
	}

	commonCfg := driver.AlistDriverCommonConfig{
		Name:      sourceConfig.Driver,
		StorageID: 0,
	}
	if s.appConfig != nil && s.appConfig.Server.ReadTimeout > 0 {
		commonCfg.RequestTimeout = s.appConfig.Server.ReadTimeout
	} else {
		commonCfg.RequestTimeout = time.Second * 30
	}

	additionJSON := sourceConfig.Addition

	err := s.driverManager.TestNewDriverConnection(ctx, sourceConfig.Driver, commonCfg, additionJSON)
	if err != nil {
		log.Warn(ctx, "Storage source connection test failed", "driver", sourceConfig.Driver, "remark", sourceConfig.Remark, "error", err)
		return fmt.Errorf("connection test failed for driver '%s' (Remark: '%s'): %w", sourceConfig.Driver, sourceConfig.Remark, err)
	}
	log.Info(ctx, "Storage source connection test successful", "driver", sourceConfig.Driver, "remark", sourceConfig.Remark)
	return nil
}

func (s *storageAdminService) GetDriverDefinitions(ctx context.Context) ([]driver.APIDriverDefinition, error) {
	log.Info(ctx, "Fetching all driver definitions.")
	if s.driverManager == nil {
		log.Error(ctx, "DriverManager is not initialized in StorageAdminService.")
		return nil, errors.New("driver manager not initialized")
	}
	definitions := s.driverManager.GetDriverDefinitions()
	return definitions, nil
}

// CountStorageSources returns the total number of storage sources
func (s *storageAdminService) CountStorageSources() (int64, error) {
	// Get a context with a short timeout
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Get all storage sources and count them
	_, totalCount, err := s.storageRepo.ListAll(ctx, true, 1, 0)
	if err != nil {
		log.Error(context.Background(),"Failed to count storage sources", "error", err)
		return 0, fmt.Errorf("failed to count storage sources: %w", err)
	}

	return totalCount, nil
}
