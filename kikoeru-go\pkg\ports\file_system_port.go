package ports

import (
	"context"
	"io"
	"time"
)

// FileSystemEntry represents a file or directory in the file system. Moved from service.
type FileSystemEntry struct {
	Name         string    `json:"name"`
	IsDir        bool      `json:"is_dir"`
	Path         string    `json:"path"` // Full path relative to the storage source root
	Size         int64     `json:"size"` // Size in bytes
	ModifiedTime time.Time `json:"modified_time"`
	Type         string    `json:"type"` // e.g., "audio", "image", "text", "video", "folder", "unknown"
}

// ListRequest is the request payload for listing files/directories. Moved from service.
type ListRequest struct {
	StorageID uint   `json:"storage_id" binding:"required"`
	Path      string `json:"path" binding:"required"` // Path relative to the storage source root
	Refresh   bool   `json:"refresh"`                 // Optional: to force refresh from source
}

// TreeRequest is the request payload for getting a directory tree
type TreeRequest struct {
	StorageID uint   `json:"storage_id" binding:"required"`
	Path      string `json:"path" binding:"required"` // Root path relative to the storage source
	Refresh   bool   `json:"refresh"`                 // Optional: to force refresh from source
}

// TreeNode represents a file or directory in the file system tree
type TreeNode struct {
	Entry    FileSystemEntry `json:"entry"`              // The file system entry information
	Children []TreeNode      `json:"children,omitempty"` // Child nodes, only for directories
}

// LinkResponse is the response payload for getting a file link. Moved from service.
type LinkResponse struct {
	URL string `json:"url"` // Could be a direct link or a proxy link
}

// ArchiveRequest is the request payload for listing files in an archive.
type ArchiveRequest struct {
	StorageID     uint   `json:"storage_id" binding:"required"`
	PathInStorage string `json:"path_in_storage" binding:"required"` // Path to the archive file in storage
	PathInArchive string `json:"path_in_archive"`                    // Path inside the archive (empty for root)
	Password      string `json:"password,omitempty"`                 // Optional password for encrypted archives
	Encoding      string `json:"encoding,omitempty"`                 // Optional encoding for file names (e.g., "shift-jis")
	Refresh       bool   `json:"refresh"`                            // Force refresh from source
}

// ArchiveEntry represents a file or directory within an archive
type ArchiveEntry struct {
	Name         string    `json:"name"`
	IsDir        bool      `json:"is_dir"`
	Path         string    `json:"path"` // Path within the archive
	Size         int64     `json:"size"` // Size in bytes
	ModifiedTime time.Time `json:"modified_time"`
	Type         string    `json:"type"` // Added: e.g., "audio", "image", "text", "video", "folder", "unknown"
}

// ArchiveTreeRequest is the request payload for getting an archive's directory tree.
// Similar to TreeRequest if paths can be made to distinguish.
// For now, specific for archives.
type ArchiveTreeRequest struct {
	StorageID     uint   `json:"storage_id" binding:"required"`
	PathInStorage string `json:"path_in_storage" binding:"required"` // Path to the archive file in storage
	PathInArchive string `json:"path_in_archive"`                    // Root path inside the archive (empty for root)
	Password      string `json:"password,omitempty"`                 // Optional password
	Encoding      string `json:"encoding,omitempty"`                 // Optional encoding for file names (e.g., "shift-jis")
	Refresh       bool   `json:"refresh"`                            // Optional: to force refresh
}

// ArchiveTreeNode represents a node in the archive's directory tree.
type ArchiveTreeNode struct {
	Entry    ArchiveEntry       `json:"entry"`
	Children []*ArchiveTreeNode `json:"children,omitempty"`
}

// FileSystemService defines the application service interface for file system operations.
// This interface is a "driven" port, implemented by the service layer and used by handlers.
type FileSystemService interface {
	// List lists files and directories at the given path within a storage source.
	List(ctx context.Context, req ListRequest) ([]FileSystemEntry, error)

	// Tree returns a tree structure of files and directories starting from the given path.
	Tree(ctx context.Context, req TreeRequest) (*TreeNode, error)

	// Get retrieves a single file or directory entry by its path within a storage source.
	// This method will typically use the underlying driver's List method to find the entry.
	Get(ctx context.Context, storageID uint, path string) (*FileSystemEntry, error)

	// GetLink generates a direct or proxy URL for accessing a file.
	GetLink(ctx context.Context, storageID uint, path string) (*LinkResponse, error)

	// GetFileStream retrieves a file stream and its metadata for proxying, optionally handling a range request.
	// Parameters:
	// - ctx: Context for the operation.
	// - storageID: The ID of the storage source.
	// - path: The path of the file relative to the storage source's root.
	// - rangeHeader: The HTTP Range header string (e.g., "bytes=0-1023"). Can be empty.
	// Returns:
	// - stream: An io.ReadCloser for the file content (potentially a partial stream if range is applied).
	// - contentType: The MIME type of the file.
	// - originalContentLength: The total size of the file in bytes.
	// - actualContentLength: The length of the content being returned in the stream (equals originalContentLength if no range, or range length).
	// - filename: The name of the file to use in Content-Disposition header.
	// - httpStatus: http.StatusOK (200) or http.StatusPartialContent (206).
	// - err: An error if one occurred.
	// The caller is responsible for closing the stream.
	GetFileStream(ctx context.Context, storageID uint, path string, rangeHeader string) (stream io.ReadCloser, contentType string, originalContentLength int64, actualContentLength int64, filename string, httpStatus int, err error)

	// ListArchive lists files and directories within an archive file
	ListArchive(ctx context.Context, req ArchiveRequest) ([]ArchiveEntry, error)

	// GetArchiveFileStream extracts and streams a file from within an archive
	// Parameters:
	// - ctx: Context for the operation.
	// - storageID: The ID of the storage source.
	// - archivePath: The path to the archive file in the storage.
	// - filePathInArchive: The path to the file within the archive.
	// - password: Optional password for encrypted archives.
	// - encoding: Optional encoding for file names in the archive (e.g., "shift-jis").
	// - rangeHeader: The HTTP Range header string (e.g., "bytes=0-1023"). Can be empty.
	// Returns:
	// - stream: An io.ReadCloser for the file content (potentially a partial stream if range is applied).
	// - contentType: The MIME type of the file.
	// - originalContentLength: The total size of the file in bytes.
	// - actualContentLength: The length of the content being returned in the stream (equals originalContentLength if no range, or range length).
	// - filename: The name of the file to use in Content-Disposition header.
	// - httpStatus: http.StatusOK (200) or http.StatusPartialContent (206).
	// - err: An error if one occurred.
	GetArchiveFileStream(ctx context.Context, storageID uint, archivePath string, filePathInArchive string, password string, encoding string, rangeHeader string) (stream io.ReadCloser, contentType string, originalContentLength int64, actualContentLength int64, filename string, httpStatus int, err error)

	// TreeArchive builds a recursive directory tree from an archive.
	TreeArchive(ctx context.Context, req ArchiveTreeRequest) (*ArchiveTreeNode, error)
}
