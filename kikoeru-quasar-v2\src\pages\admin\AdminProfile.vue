<template>
  <q-page padding>
    <div class="q-pa-md">
      <q-card class="my-card">
        <q-card-section>
          <div class="text-h6">{{ t('adminProfile.title') }}</div>
        </q-card-section>

        <q-card-section class="q-pt-none">
          <q-form @submit="updateAdminProfile" class="q-gutter-md">
            <q-input
              v-model="adminUsername"
              :label="t('adminProfile.username')"
              outlined
              hint="Leave empty if you don't want to change your username"
            />

            <q-input
              v-model="adminNewPassword"
              :type="passwordVisible ? 'text' : 'password'"
              :label="t('adminProfile.newPassword')"
              outlined
              hint="Leave empty if you don't want to change your password"
              :error="passwordError"
              :error-message="passwordErrorMessage"
            >
              <template v-slot:append>
                <q-icon
                  :name="passwordVisible ? 'visibility' : 'visibility_off'"
                  class="cursor-pointer"
                  @click="passwordVisible = !passwordVisible"
                />
              </template>
            </q-input>

            <q-input
              v-model="adminConfirmPassword"
              :type="passwordVisible ? 'text' : 'password'"
              :label="t('adminProfile.confirmPassword')"
              outlined
              :error="passwordError"
              :error-message="passwordErrorMessage"
              hint="Please confirm your new password"
            >
              <template v-slot:append>
                <q-icon
                  :name="passwordVisible ? 'visibility' : 'visibility_off'"
                  class="cursor-pointer"
                  @click="passwordVisible = !passwordVisible"
                />
              </template>
            </q-input>

            <q-banner v-if="successMessage" class="bg-positive text-white">
              {{ successMessage }}
            </q-banner>

            <q-banner v-if="errorMessage" class="bg-negative text-white">
              {{ errorMessage }}
            </q-banner>

            <div>
              <q-btn
                type="submit"
                color="primary"
                :loading="loading"
                class="full-width"
                size="lg"
              >
                {{ t('adminProfile.save') }}
              </q-btn>
            </div>
          </q-form>
        </q-card-section>
      </q-card>
    </div>
  </q-page>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from 'vue'
import { useI18n } from 'vue-i18n'
import { useQuasar } from 'quasar'
import { useAuth } from '../../composables/useAuth'

defineOptions({
  name: 'AdminProfile'
})

const { t } = useI18n()
const $q = useQuasar()
const { proxy } = getCurrentInstance()
const { user, updateUsername } = useAuth()

// Reactive data
const adminUsername = ref('')
const adminNewPassword = ref('')
const adminConfirmPassword = ref('')
const passwordVisible = ref(false)
const successMessage = ref('')
const errorMessage = ref('')
const passwordError = ref(false)
const passwordErrorMessage = ref('')
const loading = ref(false)

// Methods
const loadAdminProfile = async () => {
  try {
    const response = await proxy.$api.get('/auth/me')
    adminUsername.value = response.data.username
  } catch (error) {
    console.error('Failed to load admin profile:', error)
    errorMessage.value = t('adminProfile.loadError')
  }
}

const validatePasswords = () => {
  // Reset error state
  passwordError.value = false
  passwordErrorMessage.value = ''

  // If password field is empty, no validation needed (not changing password)
  if (!adminNewPassword.value) {
    return true
  }

  // Check that passwords match
  if (adminNewPassword.value !== adminConfirmPassword.value) {
    passwordError.value = true
    passwordErrorMessage.value = t('adminProfile.passwordsMustMatch')
    return false
  }

  return true
}

const updateAdminProfile = async () => {
  // Validate passwords first
  if (!validatePasswords()) {
    return
  }

  loading.value = true
  successMessage.value = ''
  errorMessage.value = ''

  try {
    // Prepare request payload based on what's changed
    const payload = {}

    // Only add username to payload if it's not empty and different from current username
    if (adminUsername.value && adminUsername.value !== user.value?.username) {
      payload.newUsername = adminUsername.value
    }

    // Only add password to payload if it's not empty
    if (adminNewPassword.value) {
      payload.newPassword = adminNewPassword.value
    }

    // Only make the API call if there are changes
    if (Object.keys(payload).length > 0) {
      await proxy.$api.post('/admin/profile/update', payload)

      // Update local store if username changed
      if (payload.newUsername) {
        updateUsername(payload.newUsername)
      }

      successMessage.value = t('adminProfile.updateSuccess')
      adminNewPassword.value = '' // Clear password field after update
      adminConfirmPassword.value = '' // Clear confirmation field too

      $q.notify({
        color: 'positive',
        message: t('adminProfile.updateSuccess'),
        icon: 'check_circle'
      })
    } else {
      // No changes made
      $q.notify({
        color: 'info',
        message: t('adminProfile.noChanges'),
        icon: 'info'
      })
    }
  } catch (error) {
    console.error('Failed to update admin profile:', error)
    errorMessage.value = error.response?.data?.message || t('adminProfile.updateError')

    $q.notify({
      color: 'negative',
      message: errorMessage.value,
      icon: 'error'
    })
  } finally {
    loading.value = false
  }
}

// Lifecycle
onMounted(() => {
  loadAdminProfile()
})
</script>

<!-- Styles moved to /src/css/components/pages_admin_AdminProfile.scss -->
