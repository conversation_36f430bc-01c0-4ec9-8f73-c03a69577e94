<template>
  <div>
    <router-link :to="`/work/${metadata.original_id}`">
      <CoverSFW :originalId="metadata.original_id" :nsfw="metadata.nsfw" :release="metadata.release_date" />
    </router-link>

    <div class="q-pa-sm">
      <div class="q-px-sm q-py-none">
        <!-- 标题 -->
        <div class="text-h6 text-weight-regular">
          <router-link :to="`/work/${metadata.original_id}`" class="text-black no-underline">
            {{metadata.title}}
          </router-link>
        </div>

        <!-- 社团名 -->
        <div v-if="metadata.circle" class="text-subtitle1 text-weight-regular">
          <router-link :to="`/works?keyword=$circle:${metadata.circle.name}$`" class="text-grey no-underline">
            {{metadata.circle.name}}
          </router-link>
        </div>

        <!-- 评价&评论 -->
        <div class="row items-center q-gutter-xs">
          <!-- 评价 -->
          <div class="col-auto">
            <q-rating
              v-model="rating"
              @update:model-value="setRating"
              name="rating"
              size="sm"
              :color="userMarked ? 'blue' : 'amber'"
              icon="star_border"
              icon-selected="star"
              icon-half="star_half"
            />

            <!-- 评价分布明细 -->
            <q-tooltip v-if="metadata.rate_count_detail && sortedRatings" content-class="text-subtitle1">
              <div>{{ $t('work.averageRating') }}: {{metadata.rate_average_2dp}}</div>
              <div v-for="(rate, index) in sortedRatings" :key="index" class="row items-center">
                <div class="col"> {{ $t('work.starRating', { count: rate.review_point }) }} </div>

                <!-- 评价占比 -->
                <q-linear-progress
                  :value="rate.ratio/100"
                  color="amber"
                  track-color="white"
                  style="height: 15px; width: 100px"
                  class="col-auto"
                />

                <div class="col q-mx-sm"> ({{rate.count}}) </div>
              </div>
            </q-tooltip>
          </div>

          <div class="col-auto">
            <span class="text-weight-medium text-body1 text-red">{{metadata.rate_average_2dp || 0}}</span>
            <span class="text-grey"> ({{metadata.rate_count || 0}})</span>
          </div>

          <!-- 评论数量 -->
          <div class="col-auto q-px-sm">
            <q-icon name="chat" size="xs" />
            <span class="text-grey"> ({{metadata.review_count || 0}})</span>
          </div>

          <!-- DLsite链接 -->
          <div class="col-auto">
            <q-icon name="launch" size="xs" />
            <a class="text-blue" :href="`https://www.dlsite.com/home/<USER>/=/product_id/${metadata.original_id}.html`" rel="noreferrer noopener" target="_blank">DLsite</a>
          </div>
        </div>
      </div>

      <!-- 价格&售出数 -->
      <div class="q-pt-sm q-pb-none">
        <span class="q-mx-sm text-weight-medium text-h6 text-red">{{metadata.price || 0}} {{ $t('work.currency') }}</span>
        {{ $t('work.soldCount') }}: {{metadata.dl_count || 0}}
        <!-- Age rating badge -->
        <q-badge class="q-ml-sm" :color="metadata.age_rating === 'adult' ? 'red' : metadata.age_rating === 'r15' ? 'blue' : 'green'" :label="$t(`workCard.ageRating.${metadata.age_rating}`)" />
      </div>

      <!-- 标签 -->
      <div class="q-px-none q-py-sm" v-if="showTags && metadata.tags">
        <router-link
          v-for="(tag, index) in metadata.tags"
          :to="`/works?keyword=$tag:${tag.name}$`"
          :key="index"
        >
          <q-chip size="md" class="shadow-4">
            {{tag.name}}
          </q-chip>
        </router-link>
      </div>

      <!-- 声优 -->
      <div class="q-px-none q-pt-sm q-py-sm" v-if="metadata.vas">
        <router-link
          v-for="(va, index) in metadata.vas"
          :to="`/works?keyword=$va:${va.name}$`"
          :key="index"
        >
          <q-chip square size="md" class="shadow-4" color="teal" text-color="white">
            {{va.name}}
          </q-chip>
        </router-link>
      </div>

      <q-btn-dropdown
        dense
        class="q-mt-sm shadow-4 q-mx-xs q-pl-sm"
        color="cyan"
        :label="$t('work.markProgress')"
      >
        <q-list>
          <q-item clickable @click="setProgress('marked')" class="q-pa-xs">
            <q-item-section avatar>
              <q-avatar icon="headset" v-show="progress === 'marked'" />
            </q-item-section>
            <q-item-section>
              <q-item-label>{{ $t('review.progress.wantToListen') }}</q-item-label>
            </q-item-section>
          </q-item>

          <q-item clickable @click="setProgress('listening')" class="q-pa-xs">
            <q-item-section avatar>
              <q-avatar icon="headset" v-show="progress === 'listening'" />
            </q-item-section>
            <q-item-section>
              <q-item-label>{{ $t('review.progress.listening') }}</q-item-label>
            </q-item-section>
          </q-item>

          <q-item clickable @click="setProgress('listened')" class="q-pa-xs">
            <q-item-section avatar>
              <q-avatar icon="headset" v-show="progress === 'listened'" />
            </q-item-section>
            <q-item-section>
              <q-item-label>{{ $t('review.progress.listened') }}</q-item-label>
            </q-item-section>
          </q-item>

          <q-item clickable @click="setProgress('replay')" class="q-pa-xs">
            <q-item-section avatar>
              <q-avatar icon="headset" v-show="progress === 'replay'" />
            </q-item-section>
            <q-item-section>
              <q-item-label>{{ $t('review.progress.relistening') }}</q-item-label>
            </q-item-section>
          </q-item>

          <q-item clickable @click="setProgress('postponed')" class="q-pa-xs">
            <q-item-section avatar>
              <q-avatar icon="headset" v-show="progress === 'postponed'" />
            </q-item-section>
            <q-item-section>
              <q-item-label>{{ $t('review.progress.postponed') }}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </q-btn-dropdown>

      <q-btn dense @click="showReviewDialog = true" color="cyan q-mt-sm shadow-4 q-mx-xs q-px-sm" :label="$t('work.writeReview')" />

      <q-btn dense @click="openSubtitleDialog" color="purple q-mt-sm shadow-4 q-mx-xs q-px-sm" :label="$t('work.uploadSubtitle')" />

      <WriteReview v-if="showReviewDialog" @closed="processReview" :originalId="metadata.original_id" :metadata="metadata" :existingReview="userReviewData"></WriteReview>

      <UploadSubtitleDialog
        v-if="showSubtitleDialog"
        @closed="processSubtitleUpload"
        @uploaded="processSubtitleUpload"
        :originalId="metadata.original_id"
        :treeData="workTreeData"
        :storageId="metadata.storage_id"
        :pathInStorage="metadata.path_in_storage"
        :workTitle="metadata.title"
        :treeLoading="loadingTreeData"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, getCurrentInstance } from 'vue'
import { useI18n } from 'vue-i18n'
import CoverSFW from './CoverSFW.vue'
import WriteReview from './WriteReview.vue'
import UploadSubtitleDialog from './UploadSubtitleDialog.vue'
import { useAuth } from '../composables/useAuth'
import { useWorkInfo } from '../composables/useWorkInfo'
import { useNotification } from '../composables/useNotification'
import fileSystemService from 'src/services/fileSystemService'

defineOptions({
  name: 'WorkDetails'
})

const props = defineProps({
  metadata: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['reset'])

const { t } = useI18n()
const { proxy } = getCurrentInstance()
const { isLoggedIn } = useAuth()
const { getTreeData, setTreeData } = useWorkInfo()
const { showSuccessNotification, showErrorNotification } = useNotification()

// Reactive data
const rating = ref(0)
const userMarked = ref(false)
const progress = ref('')
const userReviewData = ref(null)
const showReviewDialog = ref(false)
const showSubtitleDialog = ref(false)
const showTags = ref(true)
const workTreeData = ref(null)
const loadingTreeData = ref(false)

// Computed properties
const sortedRatings = computed(() => {
  if (!props.metadata.rate_count_detail) return null

  try {
    const rateDetail = typeof props.metadata.rate_count_detail === 'string'
      ? JSON.parse(props.metadata.rate_count_detail)
      : props.metadata.rate_count_detail

    // Check if it's already in the correct array format (from backend)
    if (Array.isArray(rateDetail)) {
      // Backend format: array of {review_point, count, ratio}
      return rateDetail.sort((a, b) => b.review_point - a.review_point)
    } else {
      // Legacy format: object with rating as keys
      const ratings = Object.keys(rateDetail).map(rating => {
        const count = rateDetail[rating]
        const total = props.metadata.rate_count || 1
        return {
          review_point: parseInt(rating),
          count: count,
          ratio: (count / total) * 100
        }
      })
      return ratings.sort((a, b) => b.review_point - a.review_point)
    }
  } catch (error) {
    console.warn('Failed to parse rate_count_detail:', error)
    return null
  }
})

// Methods
const fetchUserReview = async () => {
  // Only fetch if logged in
  if (!isLoggedIn.value) return

  // Fetch user's review for this specific work using the new efficient endpoint
  try {
    const response = await proxy.$api.get(`/api/v1/review/work/${props.metadata.original_id}`)

    if (response.data) {
      // Store the full review data for passing to WriteReview component
      userReviewData.value = response.data

      // Update UI state
      progress.value = response.data.progress || ''
      // Also update rating if user has rated this work
      if (response.data.rating) {
        userMarked.value = true
        rating.value = response.data.rating
      }
    }
  } catch (error) {
    // Ignore 404 errors - it's normal when user hasn't reviewed this work yet
    // Only log other types of errors
    if (error.response?.status !== 404) {
      console.warn('Failed to fetch user review:', error)
    }
    // For 404, silently continue - no review exists for this work, which is normal
    userReviewData.value = null
  }
}

// Check if tree data is already in the store, if not fetch it
const checkTreeDataFromStore = async () => {
  if (props.metadata && props.metadata.original_id) {
    // Try to get from Pinia store
    const storeTreeData = getTreeData(props.metadata.original_id)
    if (storeTreeData && storeTreeData.treeData) {
      workTreeData.value = storeTreeData.treeData
      console.log('Found tree data in store for', props.metadata.original_id)
    } else {
      // Tree data not in store, fetch it proactively for WorkTree component
      console.log('Tree data not in store, fetching for', props.metadata.original_id)
      await fetchTreeData()
    }
  }
}

const setProgress = (newProgress) => {
  progress.value = newProgress
  const submitPayload = {
    'work_id': props.metadata.original_id,
    'progress': newProgress
  }
  submitReview(submitPayload)
}

const setRating = (newRating) => {
  const submitPayload = {
    'work_id': props.metadata.original_id,
    'rating': newRating
  }
  userMarked.value = true
  submitReview(submitPayload)
}

const submitReview = (payload) => {
  proxy.$api.put('/api/v1/review', payload)
    .then(() => {
      showSuccessNotification(t('notification.reviewSubmitted'))
      emit('reset')
    })
    .catch((error) => {
      showErrorNotification(error.response?.data?.error || error.message || t('notification.reviewSubmitFailed'))
    })
}

const processReview = () => {
  showReviewDialog.value = false
  // Refresh user review data after dialog closes
  fetchUserReview()
  emit('reset')
}

const openSubtitleDialog = () => {
  // Check if we need to fetch tree data
  if (!workTreeData.value) {
    fetchTreeData()
  }
  showSubtitleDialog.value = true
}

const processSubtitleUpload = () => {
  showSubtitleDialog.value = false
  emit('reset')
}

// Fetch tree data for the work
const fetchTreeData = async () => {
  if (!props.metadata || !props.metadata.storage_id || !props.metadata.path_in_storage) {
    showErrorNotification('Cannot load audio files: missing storage information')
    return
  }

  try {
    loadingTreeData.value = true

    // Get storage ID and base path for this work
    const storageId = props.metadata.storage_id
    const basePath = props.metadata.path_in_storage

    const response = await fileSystemService.tree(storageId, basePath)
    if (response && response.data) {
      workTreeData.value = response.data

      // Store in Pinia for future use
      setTreeData({
        originalId: props.metadata.original_id,
        storageId: storageId,
        treeData: workTreeData.value,
        pathInStorage: basePath
      })

      console.log('Fetched work tree data successfully')
    }
  } catch (error) {
    console.error('Failed to fetch work tree data:', error)
    showErrorNotification('Failed to load audio files. Please try again.')
  } finally {
    loadingTreeData.value = false
  }
}

// Watchers
watch(() => props.metadata, (newMetaData) => {
  // 需要用watch因为父component pages/work.vue是先用空值初始化的
  // Only check for user ratings if not in guest mode
  if (isLoggedIn.value) {
    if (newMetaData.user_rating) {
      userMarked.value = true
      rating.value = newMetaData.user_rating
    } else {
      userMarked.value = false
      rating.value = newMetaData.rate_average_2dp || 0
    }

    // Note: progress is now handled by the review system
    // We might need to fetch user's review for this work
    fetchUserReview()
  } else {
    // Guest mode - just show average rating
    userMarked.value = false
    rating.value = newMetaData.rate_average_2dp || 0
  }

  // 检查是否有有效标签
  if (!newMetaData.tags || newMetaData.tags.length === 0) {
    showTags.value = false
  }
})

// Lifecycle
onMounted(async () => {
  // Only check for user ratings if not in guest mode
  if (isLoggedIn.value) {
    if (props.metadata.user_rating) {
      userMarked.value = true
      rating.value = props.metadata.user_rating
    } else {
      userMarked.value = false
      rating.value = props.metadata.rate_average_2dp || 0
      // Initial fetch of user review
      fetchUserReview()
    }
  } else {
    // Guest mode - just show average rating
    userMarked.value = false
    rating.value = props.metadata.rate_average_2dp || 0
  }

  // 检查是否有有效标签
  if (!props.metadata.tags || props.metadata.tags.length === 0) {
    showTags.value = false
  }

  // Try to get tree data from store, or fetch it if not available
  await checkTreeDataFromStore()
})
</script>

<style>
.no-underline {
  text-decoration: none;
}
</style>
