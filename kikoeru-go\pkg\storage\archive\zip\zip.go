package zip

import (
	"context"
	"fmt"
	"io"
	"io/fs"
	"os"
	stdpath "path"
	"strings"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
	"github.com/Sakura-Byte/kikoeru-go/pkg/log"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/archive/tool"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/stream"
	"golang.org/x/text/encoding"
	"golang.org/x/text/encoding/japanese"
	"golang.org/x/text/encoding/korean"
	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/encoding/traditionalchinese"

	"github.com/klauspost/compress/zip"
)

// Zip is the handler for ZIP archive operations
type Zip struct {
}

func (*Zip) AcceptedExtensions() []string {
	return []string{
		".zip",
	}
}

func (*Zip) AcceptedMultipartExtensions() map[string]tool.MultipartExtension {
	return map[string]tool.MultipartExtension{
		".zip":     {Pattern: ".z%.2d", StartFrom: 1},
		".zip.001": {Pattern: ".zip.%.3d", StartFrom: 2},
	}
}

// GetMeta retrieves metadata from a ZIP archive
func (z *Zip) GetMeta(ss *models.StreamWithSeek, args models.ArchiveArgs) (models.ArchiveMeta, error) {
	zipReader, err := z.getReader(ss, args)
	if err != nil {
		return nil, err
	}

	var isEncrypted bool
	var objects []models.Obj

	// Walk through all files in the ZIP
	for _, zipFile := range zipReader.File {
		file := NewZipFileWrapper(zipFile)

		// Apply text encoding if specified
		name := decodeName(zipFile, args.Encoding)

		// Check if any file is encrypted
		if file.IsEncrypted() {
			isEncrypted = true
			if args.Password != "" {
				file.SetPassword(args.Password)
			}
		}

		// Get file info
		info := file.FileInfo()
		path := name
		objects = append(objects, &models.Object{
			Name:     info.Name(),
			Path:     path,
			Size:     info.Size(),
			Modified: info.ModTime(),
			IsFolder: info.IsDir(),
		})
	}

	// Create tree structure
	tree := make([]models.ObjTree, len(objects))
	for i, obj := range objects {
		tree[i] = &models.ObjectTree{Object: *obj.(*models.Object)}
	}

	return &models.ArchiveMetaInfo{
		Comment:   zipReader.Comment,
		Encrypted: isEncrypted,
		Tree:      tree,
	}, nil
}

// List lists files in a ZIP archive
func (z *Zip) List(ss *models.StreamWithSeek, args models.ArchiveInnerArgs) ([]models.Obj, error) {
	zipReader, err := z.getReader(ss, args.ArchiveArgs)
	if err != nil {
		return nil, err
	}

	innerPath := strings.TrimPrefix(args.InnerPath, "/")
	if innerPath == "" {
		innerPath = "."
	}

	var result []models.Obj
	isRootPath := innerPath == "."

	// Track directories we've already added
	seenDirs := make(map[string]bool)

	for _, zipFile := range zipReader.File {
		file := NewZipFileWrapper(zipFile)

		// Apply text encoding if specified
		name := decodeName(zipFile, args.ArchiveArgs.Encoding)

		// Handle password if file is encrypted
		if file.IsEncrypted() {
			if args.ArchiveArgs.Password == "" {
				return nil, apperrors.ErrPasswordRequired
			}
			file.SetPassword(args.ArchiveArgs.Password)
		}

		// Get the relative path for comparison
		relativePath := name
		if isRootPath {
			// For root listing, we only want top-level items
			if strings.Contains(relativePath, "/") {
				parts := strings.SplitN(relativePath, "/", 2)
				dirName := parts[0]

				// Add top-level directory if we haven't seen it yet
				if !seenDirs[dirName] {
					seenDirs[dirName] = true
					result = append(result, &models.Object{
						Name:     dirName,
						Path:     dirName,
						Size:     0,
						Modified: file.ModTime(),
						IsFolder: true,
					})
				}
				continue
			}
			// Direct file in root
			result = append(result, &models.Object{
				Name:     file.Name(),
				Path:     file.Name(),
				Size:     file.Size(),
				Modified: file.ModTime(),
				IsFolder: file.IsDir(),
			})
		} else {
			// For non-root paths, check if file is in the requested directory
			dirPath := innerPath
			if !strings.HasSuffix(dirPath, "/") {
				dirPath += "/"
			}

			// Check if this file is directly in the requested directory
			if strings.HasPrefix(relativePath, dirPath) {
				relToDir := relativePath[len(dirPath):]
				// Skip files in subdirectories
				if strings.Contains(relToDir, "/") {
					parts := strings.SplitN(relToDir, "/", 2)
					dirName := parts[0]

					// Add subdirectory if we haven't seen it yet
					if !seenDirs[dirName] {
						seenDirs[dirName] = true
						result = append(result, &models.Object{
							Name:     dirName,
							Path:     dirPath + dirName,
							Size:     0,
							Modified: file.ModTime(),
							IsFolder: true,
						})
					}
				} else if relToDir != "" {
					// This is a direct file in the requested directory
					result = append(result, &models.Object{
						Name:     relToDir,
						Path:     relativePath,
						Size:     file.Size(),
						Modified: file.ModTime(),
						IsFolder: file.IsDir(),
					})
				}
			}
		}
	}

	return result, nil
}

// Extract extracts a file from a ZIP archive
func (z *Zip) Extract(ss *models.StreamWithSeek, args models.ArchiveInnerArgs) (io.ReadCloser, int64, error) {
	zipReader, err := z.getReader(ss, args.ArchiveArgs)
	if err != nil {
		return nil, 0, err
	}

	innerPath := strings.TrimPrefix(args.InnerPath, "/")

	// Find the requested file in the ZIP
	for _, zipFile := range zipReader.File {
		file := NewZipFileWrapper(zipFile)
		name := decodeName(zipFile, args.ArchiveArgs.Encoding)

		if name == innerPath {
			// Handle password if needed
			if file.IsEncrypted() {
				if args.ArchiveArgs.Password == "" {
					return nil, 0, apperrors.ErrPasswordRequired
				}
				file.SetPassword(args.ArchiveArgs.Password)
			}

			// Open the file
			rc, err := file.Open()
			if err != nil {
				return nil, 0, filterZipError(err)
			}

			return rc, file.Size(), nil
		}
	}

	return nil, 0, fmt.Errorf("file not found in archive: %s", innerPath)
}

// Decompress extracts files from a ZIP archive to a directory
func (z *Zip) Decompress(ss *models.StreamWithSeek, outputPath string, args models.ArchiveInnerArgs, up func(float64)) error {
	zipReader, err := z.getReader(ss, args.ArchiveArgs)
	if err != nil {
		return err
	}

	innerPath := strings.TrimPrefix(args.InnerPath, "/")
	isDir := false

	// Check if we're extracting a single file or a directory
	if innerPath == "" {
		// Extract entire archive
		isDir = true
	} else {
		// Check if the specified path is a directory
		for _, zipFile := range zipReader.File {
			file := NewZipFileWrapper(zipFile)
			name := decodeName(zipFile, args.ArchiveArgs.Encoding)
			if name == innerPath+"/" || strings.HasPrefix(name, innerPath+"/") {
				isDir = true
				break
			} else if name == innerPath {
				// Found exact file match
				if file.IsEncrypted() {
					if args.ArchiveArgs.Password == "" {
						return apperrors.ErrPasswordRequired
					}
					file.SetPassword(args.ArchiveArgs.Password)
				}

				// Extract single file
				return extractSingleFile(file, outputPath, up)
			}
		}
	}

	if isDir {
		// Extract directory contents
		prefix := ""
		if innerPath != "" {
			prefix = innerPath + "/"
		}

		totalSize := int64(0)
		filesToExtract := make([]*ZipFileWrapper, 0)

		// Calculate total size and collect files
		for _, zipFile := range zipReader.File {
			file := NewZipFileWrapper(zipFile)
			name := decodeName(zipFile, args.ArchiveArgs.Encoding)

			if prefix == "" || name == prefix || strings.HasPrefix(name, prefix) {
				// Handle password if needed
				if file.IsEncrypted() {
					if args.ArchiveArgs.Password == "" {
						return apperrors.ErrPasswordRequired
					}
					file.SetPassword(args.ArchiveArgs.Password)
				}

				totalSize += file.Size()
				filesToExtract = append(filesToExtract, file)
			}
		}

		// Extract files
		var extractedSize int64
		for _, file := range filesToExtract {
			name := decodeName(file.File, args.ArchiveArgs.Encoding)

			// Get relative path
			relPath := name
			if prefix != "" {
				relPath = strings.TrimPrefix(name, prefix)
			}

			// Skip empty paths
			if relPath == "" {
				continue
			}

			targetPath := stdpath.Join(outputPath, relPath)

			if file.IsDir() {
				// Create directory
				err = os.MkdirAll(targetPath, 0755)
				if err != nil {
					return err
				}
			} else {
				// Create parent directories
				err = os.MkdirAll(stdpath.Dir(targetPath), 0755)
				if err != nil {
					return err
				}

				// Extract file
				err = extractFileToPath(file, targetPath)
				if err != nil {
					return filterZipError(err)
				}

				extractedSize += file.Size()
				if totalSize > 0 {
					up(float64(extractedSize) / float64(totalSize) * 100)
				}
			}
		}

		return nil
	}

	return fmt.Errorf("path not found in archive: %s", innerPath)
}

// GetFS provides an fs.FS interface for the ZIP archive
func (z *Zip) GetFS(ss *models.StreamWithSeek, args models.ArchiveArgs) (fs.FS, error) {
	zipReader, err := z.getReader(ss, args)
	if err != nil {
		return nil, err
	}

	return &ZipFS{
		reader:   zipReader,
		encoding: args.Encoding,
	}, nil
}

// GetMetaMultipart handles metadata for multipart ZIP archives
func (z *Zip) GetMetaMultipart(streams []*models.StreamWithSeek, args models.ArchiveArgs) (models.ArchiveMeta, error) {
	// Use the MultipartReader to combine streams
	multipartReader, err := z.GetMultipartReader(streams)
	if err != nil {
		return nil, err
	}
	defer multipartReader.Close()

	// Create a StreamWithSeek wrapper for the multipart reader
	multipartStream := &models.StreamWithSeek{
		ReadCloser: multipartReader,
		Seeker:     multipartReader,
		Size:       multipartReader.Size(),
		Name:       args.Filename,
	}

	// Use the standard GetMeta method with the combined stream
	return z.GetMeta(multipartStream, args)
}

// ListMultipart handles listing for multipart ZIP archives
func (z *Zip) ListMultipart(streams []*models.StreamWithSeek, args models.ArchiveInnerArgs) ([]models.Obj, error) {
	// Use the MultipartReader to combine streams
	multipartReader, err := z.GetMultipartReader(streams)
	if err != nil {
		return nil, err
	}
	defer multipartReader.Close()

	// Create a StreamWithSeek wrapper for the multipart reader
	multipartStream := &models.StreamWithSeek{
		ReadCloser: multipartReader,
		Seeker:     multipartReader,
		Size:       multipartReader.Size(),
		Name:       args.ArchiveArgs.Filename,
	}

	// Use the standard List method with the combined stream
	return z.List(multipartStream, args)
}

// ExtractMultipart handles extraction from multipart ZIP archives
func (z *Zip) ExtractMultipart(streams []*models.StreamWithSeek, args models.ArchiveInnerArgs) (io.ReadCloser, int64, error) {
	// Use the MultipartReader to combine streams
	multipartReader, err := z.GetMultipartReader(streams)
	if err != nil {
		return nil, 0, err
	}

	// Create a StreamWithSeek wrapper for the multipart reader
	multipartStream := &models.StreamWithSeek{
		ReadCloser: multipartReader,
		Seeker:     multipartReader,
		Size:       multipartReader.Size(),
		Name:       args.ArchiveArgs.Filename,
	}

	// Use the standard Extract method with the combined stream
	return z.Extract(multipartStream, args)
}

// DecompressMultipart handles decompression for multipart ZIP archives
func (z *Zip) DecompressMultipart(streams []*models.StreamWithSeek, outputPath string, args models.ArchiveInnerArgs, up func(float64)) error {
	// Use the MultipartReader to combine streams
	multipartReader, err := z.GetMultipartReader(streams)
	if err != nil {
		return err
	}
	defer multipartReader.Close()

	// Create a StreamWithSeek wrapper for the multipart reader
	multipartStream := &models.StreamWithSeek{
		ReadCloser: multipartReader,
		Seeker:     multipartReader,
		Size:       multipartReader.Size(),
		Name:       args.ArchiveArgs.Filename,
	}

	// Use the standard Decompress method with the combined stream
	return z.Decompress(multipartStream, outputPath, args, up)
}

// GetFSMultipart provides an fs.FS interface for multipart ZIP archives
func (z *Zip) GetFSMultipart(streams []*models.StreamWithSeek, args models.ArchiveArgs) (fs.FS, error) {
	// Use the MultipartReader to combine streams
	multipartReader, err := z.GetMultipartReader(streams)
	if err != nil {
		return nil, err
	}

	// Create a StreamWithSeek wrapper for the multipart reader
	multipartStream := &models.StreamWithSeek{
		ReadCloser: multipartReader,
		Seeker:     multipartReader,
		Size:       multipartReader.Size(),
		Name:       args.Filename,
	}

	// Use the standard GetFS method with the combined stream
	return z.GetFS(multipartStream, args)
}

// Helper methods

// getReader creates a ZIP reader from a stream
func (z *Zip) getReader(ss *models.StreamWithSeek, args models.ArchiveArgs) (*zip.Reader, error) {
	// Convert to readAtSeeker
	readAtSeeker := &readAtSeekerWrapper{
		reader: stream.ConvertStreamWithSeekToReaderSeeker(ss),
		size:   ss.GetSize(),
	}

	// Create ZIP reader
	reader, err := zip.NewReader(readAtSeeker, ss.GetSize())
	if err != nil {
		return nil, filterZipError(err)
	}

	return reader, nil
}

// Helper for extracting a single file
func extractSingleFile(file *ZipFileWrapper, outputPath string, up func(float64)) error {
	rc, err := file.Open()
	if err != nil {
		return filterZipError(err)
	}
	defer rc.Close()

	// Create output file
	outFile, err := os.Create(outputPath)
	if err != nil {
		return err
	}
	defer outFile.Close()

	// Copy with progress
	return copyWithProgress(outFile, rc, file.Size(), up)
}

// Helper for extracting a file to a specific path
func extractFileToPath(file *ZipFileWrapper, targetPath string) error {
	rc, err := file.Open()
	if err != nil {
		return filterZipError(err)
	}
	defer rc.Close()

	// Create output file
	outFile, err := os.Create(targetPath)
	if err != nil {
		return err
	}
	defer outFile.Close()

	// Copy file contents
	_, err = io.Copy(outFile, rc)
	return err
}

// readAtSeekerWrapper adapts our stream to the interface required by zip.NewReader
type readAtSeekerWrapper struct {
	reader io.ReadSeeker
	size   int64
}

func (r *readAtSeekerWrapper) Read(p []byte) (n int, err error) {
	return r.reader.Read(p)
}

func (r *readAtSeekerWrapper) ReadAt(p []byte, off int64) (n int, err error) {
	// Save current position
	currentPos, err := r.reader.Seek(0, io.SeekCurrent)
	if err != nil {
		return 0, err
	}

	// Seek to offset
	_, err = r.reader.Seek(off, io.SeekStart)
	if err != nil {
		return 0, err
	}

	// Read data
	n, err = r.reader.Read(p)

	// Restore position
	_, seekErr := r.reader.Seek(currentPos, io.SeekStart)
	if seekErr != nil && err == nil {
		err = seekErr
	}

	return n, err
}

func (r *readAtSeekerWrapper) Seek(offset int64, whence int) (int64, error) {
	return r.reader.Seek(offset, whence)
}

func (r *readAtSeekerWrapper) Size() int64 {
	return r.size
}

// copyWithProgress copies data with progress updates
func copyWithProgress(dst io.Writer, src io.Reader, totalSize int64, up func(float64)) error {
	buf := make([]byte, 32*1024) // 32KB buffer
	var written int64

	for {
		nr, er := src.Read(buf)
		if nr > 0 {
			nw, ew := dst.Write(buf[:nr])
			if nw > 0 {
				written += int64(nw)
				if totalSize > 0 {
					up(float64(written) / float64(totalSize) * 100)
				}
			}
			if ew != nil {
				return ew
			}
			if nr != nw {
				return io.ErrShortWrite
			}
		}
		if er != nil {
			if er == io.EOF {
				return nil
			}
			return er
		}
	}
}

// decodeName applies encoding to ZIP file names if specified
func decodeName(f *zip.File, encodingName string) string {
	// If file already uses UTF-8, return as-is
	if !f.NonUTF8 {
		return f.Name
	}

	// If encoding is specified, try to decode
	if encodingName != "" {
		enc, err := toGolangEncoding(encodingName)
		if err == nil && enc != nil {
			dec := enc.NewDecoder()
			name, err := dec.String(f.Name)
			if err == nil {
				return name
			}
		}
	}

	// Default case, return as-is
	return f.Name
}

// toGolangEncoding converts string encoding name to golang.org/x/text/encoding.Encoding
func toGolangEncoding(encName string) (encoding.Encoding, error) {
	if encName == "" {
		return nil, nil // nil often means UTF-8 or system default for libraries
	}
	switch strings.ToLower(encName) {
	case "shift-jis", "sjis", "shift_jis":
		return japanese.ShiftJIS, nil
	case "euc-jp":
		return japanese.EUCJP, nil
	case "gbk": // GBK is often handled by GB18030 which is a superset
		return simplifiedchinese.GB18030, nil
	case "gb18030":
		return simplifiedchinese.GB18030, nil
	case "big5":
		return traditionalchinese.Big5, nil
	case "euc-kr":
		return korean.EUCKR, nil
	case "utf-8", "utf8":
		return nil, nil // Explicitly UTF-8, often represented by nil TextEncoding
	default:
		return nil, fmt.Errorf("unsupported encoding: %s", encName)
	}
}

// filterZipError filters and standardizes ZIP-related errors
func filterZipError(err error) error {
	if err == nil {
		return nil
	}

	errMsg := err.Error()
	log.Debug(context.Background(), "filterZipError: analyzing error", "error", err)

	if strings.Contains(errMsg, "password") ||
		strings.Contains(errMsg, "encrypted") ||
		strings.Contains(errMsg, "decrypt") ||
		strings.Contains(errMsg, "authentication failed") {

		log.Debug(context.Background(), "filterZipError: detected password-related error")

		// Different archive tools use different error messages for wrong passwords
		if strings.Contains(errMsg, "wrong") ||
			strings.Contains(errMsg, "incorrect") ||
			strings.Contains(errMsg, "invalid") ||
			strings.Contains(errMsg, "bad") ||
			strings.Contains(errMsg, "authentication failed") {
			log.Debug(context.Background(), "filterZipError: detected invalid password error")
			return apperrors.ErrInvalidPassword
		}

		log.Debug(context.Background(), "filterZipError: detected password required error")
		return apperrors.ErrPasswordRequired
	}
	return err
}

// Make sure we implement all required interfaces
var _ tool.Tool = (*Zip)(nil)
var _ tool.MultipartTool = (*Zip)(nil)

func init() {
	tool.RegisterTool(&Zip{})
}
