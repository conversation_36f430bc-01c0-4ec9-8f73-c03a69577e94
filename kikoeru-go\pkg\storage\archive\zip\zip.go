package zip

import (
	"context"
	"fmt"
	"io"
	"io/fs"
	"os"
	stdpath "path"
	"strings"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
	"github.com/Sakura-Byte/kikoeru-go/pkg/log"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/archive/tool"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/stream"
	"golang.org/x/text/encoding"
	"golang.org/x/text/encoding/japanese"
	"golang.org/x/text/encoding/korean"
	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/encoding/traditionalchinese"

	"github.com/yeka/zip"
)

// Zip is the handler for ZIP archive operations
type Zip struct {
}

// WrapReader wraps a zip.Reader to implement folder traversal interface
type WrapReader struct {
	Reader *zip.Reader
}

// WrapFile wraps a zip.File to implement file interface
type WrapFile struct {
	f *zip.File
}

// WrapFileInfo wraps fs.FileInfo with decoded name
type WrapFileInfo struct {
	fs.FileInfo
	decodedName string
}

func (f *WrapFileInfo) Name() string {
	return f.decodedName
}

func (f *WrapFile) Name() string {
	return f.f.Name
}

func (f *WrapFile) FileInfo() fs.FileInfo {
	return f.f.FileInfo()
}

func (f *WrapFile) Open() (io.ReadCloser, error) {
	return f.f.Open()
}

func (f *WrapFile) IsEncrypted() bool {
	return f.f.IsEncrypted()
}

func (f *WrapFile) SetPassword(password string) {
	f.f.SetPassword(password)
}

func (*Zip) AcceptedExtensions() []string {
	return []string{
		".zip",
	}
}

func (*Zip) AcceptedMultipartExtensions() map[string]tool.MultipartExtension {
	return map[string]tool.MultipartExtension{
		".zip":     {Pattern: ".z%.2d", StartFrom: 1},
		".zip.001": {Pattern: ".zip.%.3d", StartFrom: 2},
	}
}

// GetMeta retrieves metadata from a ZIP archive
func (z *Zip) GetMeta(ss *models.StreamWithSeek, args models.ArchiveArgs) (models.ArchiveMeta, error) {
	zipReader, err := z.getReaderSingle(ss, args)
	if err != nil {
		return nil, err
	}

	var isEncrypted bool
	var objects []models.Obj

	// Walk through all files in the ZIP
	for _, zipFile := range zipReader.File {
		// Apply text encoding if specified
		name := decodeName(zipFile.Name, args.Encoding)

		// Check if any file is encrypted
		if zipFile.IsEncrypted() {
			isEncrypted = true
			if args.Password != "" {
				zipFile.SetPassword(args.Password)
			}
		}

		// Get file info
		info := zipFile.FileInfo()
		path := name
		objects = append(objects, &models.Object{
			Name:     info.Name(),
			Path:     path,
			Size:     info.Size(),
			Modified: info.ModTime(),
			IsFolder: info.IsDir(),
		})
	}

	// Create tree structure
	tree := make([]models.ObjTree, len(objects))
	for i, obj := range objects {
		tree[i] = &models.ObjectTree{Object: *obj.(*models.Object)}
	}

	return &models.ArchiveMetaInfo{
		Comment:   zipReader.Comment,
		Encrypted: isEncrypted,
		Tree:      tree,
	}, nil
}

// List lists files in a ZIP archive
func (z *Zip) List(ss *models.StreamWithSeek, args models.ArchiveInnerArgs) ([]models.Obj, error) {
	zipReader, err := z.getReaderSingle(ss, args.ArchiveArgs)
	if err != nil {
		return nil, err
	}

	innerPath := strings.TrimPrefix(args.InnerPath, "/")
	if innerPath == "" {
		innerPath = "."
	}

	var result []models.Obj
	isRootPath := innerPath == "."

	// Track directories we've already added
	seenDirs := make(map[string]bool)

	for _, zipFile := range zipReader.File {
		// Apply text encoding if specified
		name := decodeName(zipFile.Name, args.ArchiveArgs.Encoding)

		// Handle password if file is encrypted
		if zipFile.IsEncrypted() {
			if args.ArchiveArgs.Password == "" {
				return nil, apperrors.ErrPasswordRequired
			}
			zipFile.SetPassword(args.ArchiveArgs.Password)
		}

		// Get the relative path for comparison
		relativePath := name
		if isRootPath {
			// For root listing, we only want top-level items
			if strings.Contains(relativePath, "/") {
				parts := strings.SplitN(relativePath, "/", 2)
				dirName := parts[0]

				// Add top-level directory if we haven't seen it yet
				if !seenDirs[dirName] {
					seenDirs[dirName] = true
					result = append(result, &models.Object{
						Name:     dirName,
						Path:     dirName,
						Size:     0,
						Modified: zipFile.ModTime(),
						IsFolder: true,
					})
				}
				continue
			}
			// Direct file in root
			info := zipFile.FileInfo()
			result = append(result, &models.Object{
				Name:     info.Name(),
				Path:     info.Name(),
				Size:     info.Size(),
				Modified: info.ModTime(),
				IsFolder: info.IsDir(),
			})
		} else {
			// For non-root paths, check if file is in the requested directory
			dirPath := innerPath
			if !strings.HasSuffix(dirPath, "/") {
				dirPath += "/"
			}

			// Check if this file is directly in the requested directory
			if strings.HasPrefix(relativePath, dirPath) {
				relToDir := relativePath[len(dirPath):]
				// Skip files in subdirectories
				if strings.Contains(relToDir, "/") {
					parts := strings.SplitN(relToDir, "/", 2)
					dirName := parts[0]

					// Add subdirectory if we haven't seen it yet
					if !seenDirs[dirName] {
						seenDirs[dirName] = true
						result = append(result, &models.Object{
							Name:     dirName,
							Path:     dirPath + dirName,
							Size:     0,
							Modified: zipFile.ModTime(),
							IsFolder: true,
						})
					}
				} else if relToDir != "" {
					// This is a direct file in the requested directory
					info := zipFile.FileInfo()
					result = append(result, &models.Object{
						Name:     relToDir,
						Path:     relativePath,
						Size:     info.Size(),
						Modified: info.ModTime(),
						IsFolder: info.IsDir(),
					})
				}
			}
		}
	}

	return result, nil
}

// Extract extracts a file from a ZIP archive
func (z *Zip) Extract(ss *models.StreamWithSeek, args models.ArchiveInnerArgs) (io.ReadCloser, int64, error) {
	zipReader, err := z.getReaderSingle(ss, args.ArchiveArgs)
	if err != nil {
		return nil, 0, err
	}

	innerPath := strings.TrimPrefix(args.InnerPath, "/")

	// Find the requested file in the ZIP
	for _, zipFile := range zipReader.File {
		name := decodeName(zipFile.Name, args.ArchiveArgs.Encoding)

		if name == innerPath {
			// Handle password if needed
			if zipFile.IsEncrypted() {
				if args.ArchiveArgs.Password == "" {
					return nil, 0, apperrors.ErrPasswordRequired
				}
				zipFile.SetPassword(args.ArchiveArgs.Password)
			}

			// Open the file
			rc, err := zipFile.Open()
			if err != nil {
				return nil, 0, filterZipError(err)
			}

			return rc, int64(zipFile.UncompressedSize64), nil
		}
	}

	return nil, 0, fmt.Errorf("file not found in archive: %s", innerPath)
}

// Decompress extracts files from a ZIP archive to a directory
func (z *Zip) Decompress(ss *models.StreamWithSeek, outputPath string, args models.ArchiveInnerArgs, up func(float64)) error {
	zipReader, err := z.getReaderSingle(ss, args.ArchiveArgs)
	if err != nil {
		return err
	}

	innerPath := strings.TrimPrefix(args.InnerPath, "/")
	isDir := false

	// Check if we're extracting a single file or a directory
	if innerPath == "" {
		// Extract entire archive
		isDir = true
	} else {
		// Check if the specified path is a directory
		for _, zipFile := range zipReader.File {
			name := decodeName(zipFile.Name, args.ArchiveArgs.Encoding)
			if name == innerPath+"/" || strings.HasPrefix(name, innerPath+"/") {
				isDir = true
				break
			} else if name == innerPath {
				// Found exact file match
				if zipFile.IsEncrypted() {
					if args.ArchiveArgs.Password == "" {
						return apperrors.ErrPasswordRequired
					}
					zipFile.SetPassword(args.ArchiveArgs.Password)
				}

				// Extract single file
				return extractSingleFile(zipFile, outputPath, up)
			}
		}
	}

	if isDir {
		// Extract directory contents
		prefix := ""
		if innerPath != "" {
			prefix = innerPath + "/"
		}

		totalSize := int64(0)
		filesToExtract := make([]*zip.File, 0)

		// Calculate total size and collect files
		for _, zipFile := range zipReader.File {
			name := decodeName(zipFile.Name, args.ArchiveArgs.Encoding)

			if prefix == "" || name == prefix || strings.HasPrefix(name, prefix) {
				// Handle password if needed
				if zipFile.IsEncrypted() {
					if args.ArchiveArgs.Password == "" {
						return apperrors.ErrPasswordRequired
					}
					zipFile.SetPassword(args.ArchiveArgs.Password)
				}

				totalSize += int64(zipFile.UncompressedSize64)
				filesToExtract = append(filesToExtract, zipFile)
			}
		}

		// Extract files
		var extractedSize int64
		for _, file := range filesToExtract {
			name := decodeName(file.Name, args.ArchiveArgs.Encoding)

			// Get relative path
			relPath := name
			if prefix != "" {
				relPath = strings.TrimPrefix(name, prefix)
			}

			// Skip empty paths
			if relPath == "" {
				continue
			}

			targetPath := stdpath.Join(outputPath, relPath)

			info := file.FileInfo()
			if info.IsDir() {
				// Create directory
				err = os.MkdirAll(targetPath, 0755)
				if err != nil {
					return err
				}
			} else {
				// Create parent directories
				err = os.MkdirAll(stdpath.Dir(targetPath), 0755)
				if err != nil {
					return err
				}

				// Extract file
				err = extractFileToPath(file, targetPath)
				if err != nil {
					return filterZipError(err)
				}

				extractedSize += int64(file.UncompressedSize64)
				if totalSize > 0 {
					up(float64(extractedSize) / float64(totalSize) * 100)
				}
			}
		}

		return nil
	}

	return fmt.Errorf("path not found in archive: %s", innerPath)
}

// GetFS provides an fs.FS interface for the ZIP archive
func (z *Zip) GetFS(ss *models.StreamWithSeek, args models.ArchiveArgs) (fs.FS, error) {
	zipReader, err := z.getReaderSingle(ss, args)
	if err != nil {
		return nil, err
	}

	return &ZipFS{
		reader:   zipReader,
		encoding: args.Encoding,
	}, nil
}

// GetMetaMultipart handles metadata for multipart ZIP archives
func (z *Zip) GetMetaMultipart(streams []*models.StreamWithSeek, args models.ArchiveArgs) (models.ArchiveMeta, error) {
	// Check if this is a .zip + .z01/.z02/.z03 format
	if isZ01Format(streams) {
		log.Debug(context.Background(), "Detected .zip + .z01 format, using specialized reader")
		return z.getMetaWithZ01Reader(streams, args)
	}

	// For .zip.001/.zip.002 format, use simple concatenation
	log.Debug(context.Background(), "Using simple concatenation for .zip.001 format")
	return z.getMetaWithSimpleConcatenation(streams, args)
}

// getMetaWithZ01Reader handles .zip + .z01/.z02/.z03 format using specialized reader
func (z *Zip) getMetaWithZ01Reader(streams []*models.StreamWithSeek, args models.ArchiveArgs) (models.ArchiveMeta, error) {
	// Create specialized multipart reader
	multiPartReader, err := NewMultiPartZipReader(streams)
	if err != nil {
		return nil, fmt.Errorf("failed to create multipart reader: %w", err)
	}
	defer multiPartReader.Close()

	// Create ZIP reader from the multipart reader
	zipReader, err := zip.NewReader(multiPartReader, multiPartReader.Size())
	if err != nil {
		log.Debug(context.Background(), "Direct Z01 reading failed, trying temp file approach", "error", err)
		return z.getMetaMultipartWithTempFile(streams, args)
	}

	return z.createArchiveMetaFromZipReader(zipReader, args)
}

// getMetaWithSimpleConcatenation handles .zip.001/.zip.002 format with simple concatenation
func (z *Zip) getMetaWithSimpleConcatenation(streams []*models.StreamWithSeek, args models.ArchiveArgs) (models.ArchiveMeta, error) {
	// Use the existing MultiReaderAt for simple concatenation
	multiReaderAt, err := NewMultiReaderAt(streams)
	if err != nil {
		return nil, err
	}
	defer multiReaderAt.Close()

	// Create ZIP reader directly from MultiReaderAt
	zipReader, err := zip.NewReader(multiReaderAt, multiReaderAt.Size())
	if err != nil {
		// If direct reading fails, try the temp file approach
		log.Debug(context.Background(), "Direct concatenation reading failed, trying temp file approach", "error", err)
		return z.getMetaMultipartWithTempFile(streams, args)
	}

	return z.createArchiveMetaFromZipReader(zipReader, args)
}

// createArchiveMetaFromZipReader creates archive metadata from a ZIP reader
func (z *Zip) createArchiveMetaFromZipReader(zipReader *zip.Reader, args models.ArchiveArgs) (models.ArchiveMeta, error) {
	isEncrypted := false
	objects := make([]*models.Object, 0)

	for _, file := range zipReader.File {
		if file.IsEncrypted() {
			isEncrypted = true
			if args.Password != "" {
				file.SetPassword(args.Password)
			}
		}

		// Decode file name
		fileName := decodeName(file.Name, args.Encoding)

		obj := &models.Object{
			Name:     fileName,
			Size:     int64(file.UncompressedSize64),
			Modified: file.FileInfo().ModTime(),
			IsFolder: strings.HasSuffix(fileName, "/"),
		}
		objects = append(objects, obj)
	}

	// Convert to tree structure
	tree := make([]models.ObjTree, len(objects))
	for i, obj := range objects {
		tree[i] = &models.ObjectTree{Object: *obj}
	}

	return &models.ArchiveMetaInfo{
		Comment:   zipReader.Comment,
		Encrypted: isEncrypted,
		Tree:      tree,
	}, nil
}

// getMetaMultipartWithTempFile creates a temporary file and reads from it
func (z *Zip) getMetaMultipartWithTempFile(streams []*models.StreamWithSeek, args models.ArchiveArgs) (models.ArchiveMeta, error) {
	// Create temporary combined file
	tempFilePath, err := z.CreateTempFile(streams)
	if err != nil {
		return nil, err
	}
	defer os.Remove(tempFilePath) // Clean up

	// Open the temp file
	tempFile, err := os.Open(tempFilePath)
	if err != nil {
		return nil, err
	}
	defer tempFile.Close()

	// Get file info
	fileInfo, err := tempFile.Stat()
	if err != nil {
		return nil, err
	}

	// Create a stream from the temp file
	tempStream := &models.StreamWithSeek{
		ReadCloser: tempFile,
		Seeker:     tempFile,
		Size:       fileInfo.Size(),
		Name:       "combined.zip",
	}

	// Try to read with our ZIP handler
	return z.GetMeta(tempStream, args)
}

// ListMultipart handles listing for multipart ZIP archives
func (z *Zip) ListMultipart(streams []*models.StreamWithSeek, args models.ArchiveInnerArgs) ([]models.Obj, error) {
	// Check if this is a .zip + .z01/.z02/.z03 format
	if isZ01Format(streams) {
		log.Debug(context.Background(), "Using Z01 reader for listing")
		return z.listWithZ01Reader(streams, args)
	}

	// For .zip.001/.zip.002 format, use simple concatenation
	log.Debug(context.Background(), "Using simple concatenation for listing")
	return z.listWithSimpleConcatenation(streams, args)
}

// listWithZ01Reader handles listing with specialized Z01 reader
func (z *Zip) listWithZ01Reader(streams []*models.StreamWithSeek, args models.ArchiveInnerArgs) ([]models.Obj, error) {
	multiPartReader, err := NewMultiPartZipReader(streams)
	if err != nil {
		return nil, fmt.Errorf("failed to create multipart reader: %w", err)
	}
	defer multiPartReader.Close()

	// Create a StreamWithSeek wrapper
	multipartStream := &models.StreamWithSeek{
		ReadCloser: multiPartReader,
		Seeker:     multiPartReader,
		Size:       multiPartReader.Size(),
		Name:       args.ArchiveArgs.Filename,
	}

	return z.List(multipartStream, args)
}

// listWithSimpleConcatenation handles listing with simple concatenation
func (z *Zip) listWithSimpleConcatenation(streams []*models.StreamWithSeek, args models.ArchiveInnerArgs) ([]models.Obj, error) {
	multipartReader, err := z.GetMultipartReader(streams)
	if err != nil {
		return nil, err
	}
	defer multipartReader.Close()

	// Create a StreamWithSeek wrapper for the multipart reader
	multipartStream := &models.StreamWithSeek{
		ReadCloser: multipartReader,
		Seeker:     multipartReader,
		Size:       multipartReader.Size(),
		Name:       args.ArchiveArgs.Filename,
	}

	// Try the standard List method with the combined stream
	objects, err := z.List(multipartStream, args)
	if err != nil {
		// If direct reading fails, try the temp file approach
		log.Debug(context.Background(), "Direct multipart listing failed, trying temp file approach", "error", err)
		return z.listMultipartWithTempFile(streams, args)
	}

	return objects, nil
}

// listMultipartWithTempFile creates a temporary file and lists from it
func (z *Zip) listMultipartWithTempFile(streams []*models.StreamWithSeek, args models.ArchiveInnerArgs) ([]models.Obj, error) {
	// Create temporary combined file
	tempFilePath, err := z.CreateTempFile(streams)
	if err != nil {
		return nil, err
	}
	defer os.Remove(tempFilePath) // Clean up

	// Open the temp file
	tempFile, err := os.Open(tempFilePath)
	if err != nil {
		return nil, err
	}
	defer tempFile.Close()

	// Get file info
	fileInfo, err := tempFile.Stat()
	if err != nil {
		return nil, err
	}

	// Create a stream from the temp file
	tempStream := &models.StreamWithSeek{
		ReadCloser: tempFile,
		Seeker:     tempFile,
		Size:       fileInfo.Size(),
		Name:       "combined.zip",
	}

	// Try to list with our ZIP handler
	return z.List(tempStream, args)
}

// ExtractMultipart handles extraction from multipart ZIP archives
func (z *Zip) ExtractMultipart(streams []*models.StreamWithSeek, args models.ArchiveInnerArgs) (io.ReadCloser, int64, error) {
	// Check if this is a .zip + .z01/.z02/.z03 format
	if isZ01Format(streams) {
		log.Debug(context.Background(), "Using Z01 reader for extraction")
		return z.extractWithZ01Reader(streams, args)
	}

	// For .zip.001/.zip.002 format, use simple concatenation
	log.Debug(context.Background(), "Using simple concatenation for extraction")
	return z.extractWithSimpleConcatenation(streams, args)
}

// extractWithZ01Reader handles extraction with specialized Z01 reader
func (z *Zip) extractWithZ01Reader(streams []*models.StreamWithSeek, args models.ArchiveInnerArgs) (io.ReadCloser, int64, error) {
	multiPartReader, err := NewMultiPartZipReader(streams)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to create multipart reader: %w", err)
	}
	defer multiPartReader.Close()

	// Create a StreamWithSeek wrapper
	multipartStream := &models.StreamWithSeek{
		ReadCloser: multiPartReader,
		Seeker:     multiPartReader,
		Size:       multiPartReader.Size(),
		Name:       args.ArchiveArgs.Filename,
	}

	return z.Extract(multipartStream, args)
}

// extractWithSimpleConcatenation handles extraction with simple concatenation
func (z *Zip) extractWithSimpleConcatenation(streams []*models.StreamWithSeek, args models.ArchiveInnerArgs) (io.ReadCloser, int64, error) {
	// Use the MultipartReader to combine streams
	multipartReader, err := z.GetMultipartReader(streams)
	if err != nil {
		return nil, 0, err
	}

	// Create a StreamWithSeek wrapper for the multipart reader
	multipartStream := &models.StreamWithSeek{
		ReadCloser: multipartReader,
		Seeker:     multipartReader,
		Size:       multipartReader.Size(),
		Name:       args.ArchiveArgs.Filename,
	}

	// Use the standard Extract method with the combined stream
	return z.Extract(multipartStream, args)
}

// DecompressMultipart handles decompression for multipart ZIP archives
func (z *Zip) DecompressMultipart(streams []*models.StreamWithSeek, outputPath string, args models.ArchiveInnerArgs, up func(float64)) error {
	// Use the MultipartReader to combine streams
	multipartReader, err := z.GetMultipartReader(streams)
	if err != nil {
		return err
	}
	defer multipartReader.Close()

	// Create a StreamWithSeek wrapper for the multipart reader
	multipartStream := &models.StreamWithSeek{
		ReadCloser: multipartReader,
		Seeker:     multipartReader,
		Size:       multipartReader.Size(),
		Name:       args.ArchiveArgs.Filename,
	}

	// Use the standard Decompress method with the combined stream
	return z.Decompress(multipartStream, outputPath, args, up)
}

// GetFSMultipart provides an fs.FS interface for multipart ZIP archives
func (z *Zip) GetFSMultipart(streams []*models.StreamWithSeek, args models.ArchiveArgs) (fs.FS, error) {
	// Use the MultipartReader to combine streams
	multipartReader, err := z.GetMultipartReader(streams)
	if err != nil {
		return nil, err
	}

	// Create a StreamWithSeek wrapper for the multipart reader
	multipartStream := &models.StreamWithSeek{
		ReadCloser: multipartReader,
		Seeker:     multipartReader,
		Size:       multipartReader.Size(),
		Name:       args.Filename,
	}

	// Use the standard GetFS method with the combined stream
	return z.GetFS(multipartStream, args)
}

// Helper methods

// getReaderSingle creates a ZIP reader from a single stream
func (z *Zip) getReaderSingle(ss *models.StreamWithSeek, args models.ArchiveArgs) (*zip.Reader, error) {
	// Create a ReaderAt from the stream
	readerAt, err := stream.NewReadAtSeeker(ss, 0)
	if err != nil {
		return nil, err
	}

	// Create ZIP reader
	reader, err := zip.NewReader(readerAt, ss.GetSize())
	if err != nil {
		return nil, filterZipError(err)
	}

	return reader, nil
}

// Helper for extracting a single file
func extractSingleFile(file *zip.File, outputPath string, up func(float64)) error {
	rc, err := file.Open()
	if err != nil {
		return filterZipError(err)
	}
	defer rc.Close()

	// Create output file
	outFile, err := os.Create(outputPath)
	if err != nil {
		return err
	}
	defer outFile.Close()

	// Copy with progress
	return copyWithProgress(outFile, rc, int64(file.UncompressedSize64), up)
}

// Helper for extracting a file to a specific path
func extractFileToPath(file *zip.File, targetPath string) error {
	rc, err := file.Open()
	if err != nil {
		return filterZipError(err)
	}
	defer rc.Close()

	// Create output file
	outFile, err := os.Create(targetPath)
	if err != nil {
		return err
	}
	defer outFile.Close()

	// Copy file contents
	_, err = io.Copy(outFile, rc)
	return err
}

// copyWithProgress copies data with progress updates
func copyWithProgress(dst io.Writer, src io.Reader, totalSize int64, up func(float64)) error {
	buf := make([]byte, 32*1024) // 32KB buffer
	var written int64

	for {
		nr, er := src.Read(buf)
		if nr > 0 {
			nw, ew := dst.Write(buf[:nr])
			if nw > 0 {
				written += int64(nw)
				if totalSize > 0 {
					up(float64(written) / float64(totalSize) * 100)
				}
			}
			if ew != nil {
				return ew
			}
			if nr != nw {
				return io.ErrShortWrite
			}
		}
		if er != nil {
			if er == io.EOF {
				return nil
			}
			return er
		}
	}
}

// decodeName applies manual encoding to ZIP file names if specified
// We don't use automatic encoding detection, only manual encoding input
func decodeName(name string, encodingName string) string {
	// If no encoding is specified, return as-is
	if encodingName == "" {
		return name
	}

	// Try to decode with the specified encoding
	enc, err := toGolangEncoding(encodingName)
	if err == nil && enc != nil {
		dec := enc.NewDecoder()
		decodedName, err := dec.String(name)
		if err == nil {
			return decodedName
		}
	}

	// If decoding fails, return original name
	return name
}

// toGolangEncoding converts string encoding name to golang.org/x/text/encoding.Encoding
func toGolangEncoding(encName string) (encoding.Encoding, error) {
	if encName == "" {
		return nil, nil // nil often means UTF-8 or system default for libraries
	}
	switch strings.ToLower(encName) {
	case "shift-jis", "sjis", "shift_jis":
		return japanese.ShiftJIS, nil
	case "euc-jp":
		return japanese.EUCJP, nil
	case "gbk": // GBK is often handled by GB18030 which is a superset
		return simplifiedchinese.GB18030, nil
	case "gb18030":
		return simplifiedchinese.GB18030, nil
	case "big5":
		return traditionalchinese.Big5, nil
	case "euc-kr":
		return korean.EUCKR, nil
	case "utf-8", "utf8":
		return nil, nil // Explicitly UTF-8, often represented by nil TextEncoding
	default:
		return nil, fmt.Errorf("unsupported encoding: %s", encName)
	}
}

// filterZipError filters and standardizes ZIP-related errors
func filterZipError(err error) error {
	if err == nil {
		return nil
	}

	errMsg := err.Error()
	log.Debug(context.Background(), "filterZipError: analyzing error", "error", err)

	if strings.Contains(errMsg, "password") ||
		strings.Contains(errMsg, "encrypted") ||
		strings.Contains(errMsg, "decrypt") ||
		strings.Contains(errMsg, "authentication failed") {

		log.Debug(context.Background(), "filterZipError: detected password-related error")

		// Different archive tools use different error messages for wrong passwords
		if strings.Contains(errMsg, "wrong") ||
			strings.Contains(errMsg, "incorrect") ||
			strings.Contains(errMsg, "invalid") ||
			strings.Contains(errMsg, "bad") ||
			strings.Contains(errMsg, "authentication failed") {
			log.Debug(context.Background(), "filterZipError: detected invalid password error")
			return apperrors.ErrInvalidPassword
		}

		log.Debug(context.Background(), "filterZipError: detected password required error")
		return apperrors.ErrPasswordRequired
	}
	return err
}

// Make sure we implement all required interfaces
var _ tool.Tool = (*Zip)(nil)
var _ tool.MultipartTool = (*Zip)(nil)

func init() {
	tool.RegisterTool(&Zip{})
}
