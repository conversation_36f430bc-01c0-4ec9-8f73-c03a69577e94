package models

import (
	"io"
	"io/fs"
	"time"
)

// ArchiveArgs contains common arguments for archive operations
type ArchiveArgs struct {
	Password string
	Filename string
	Encoding string // Optional encoding name (e.g., "shift-jis", "euc-kr")
}

// ArchiveInnerArgs contains arguments for operations inside an archive
type ArchiveInnerArgs struct {
	ArchiveArgs
	InnerPath string
}

// ArchiveListArgs contains arguments for listing files in an archive
type ArchiveListArgs struct {
	ArchiveInnerArgs
	LinkArgs LinkArgs
	Refresh  bool
}

// LinkArgs contains arguments for generating links to files
type LinkArgs struct {
	IP     string
	Header map[string]string
}

// ArchiveDecompressArgs contains arguments for decompressing an archive
type ArchiveDecompressArgs struct {
	ArchiveInnerArgs
	Progress func(percent float64)
}

// Obj represents a file or directory object
type Obj interface {
	GetName() string
	GetSize() int64
	ModTime() time.Time
	IsDir() bool
	GetPath() string
}

// Object implements the Obj interface
type Object struct {
	Name     string
	Size     int64
	Modified time.Time
	IsFolder bool
	Path     string
}

func (o *Object) GetName() string     { return o.Name }
func (o *Object) GetSize() int64      { return o.Size }
func (o *Object) ModTime() time.Time  { return o.Modified }
func (o *Object) IsDir() bool         { return o.IsFolder }
func (o *Object) GetPath() string     { return o.Path }
func (o *Object) SetPath(path string) { o.Path = path }

// ObjTree represents a tree structure of objects
type ObjTree interface {
	GetObject() Obj
	GetChildren() []ObjTree
}

// ObjectTree implements the ObjTree interface
type ObjectTree struct {
	Object   Object
	Children []ObjTree
}

func (ot *ObjectTree) GetObject() Obj         { return &ot.Object }
func (ot *ObjectTree) GetChildren() []ObjTree { return ot.Children }

// ArchiveMeta represents metadata about an archive
type ArchiveMeta interface {
	GetComment() string
	IsEncrypted() bool
	GetTree() []ObjTree
}

// ArchiveMetaInfo implements the ArchiveMeta interface
type ArchiveMetaInfo struct {
	Comment   string
	Encrypted bool
	Tree      []ObjTree
}

func (a *ArchiveMetaInfo) GetComment() string { return a.Comment }
func (a *ArchiveMetaInfo) IsEncrypted() bool  { return a.Encrypted }
func (a *ArchiveMetaInfo) GetTree() []ObjTree { return a.Tree }

// ArchiveMetaProvider provides metadata about an archive
type ArchiveMetaProvider struct {
	ArchiveMeta
	Sort            *Sort
	Expiration      *time.Duration
	DriverProviding bool
}

// Sort represents sorting options
type Sort struct {
	OrderBy        string
	OrderDirection string
	ExtractFolder  int
}

// Stream interface for file streams
type Stream interface {
	io.ReadCloser
	io.Seeker
	Size() int64
}

// FileStream is a stream of a file
type FileStream struct {
	Ctx Context
	Obj Obj
}

// Context interface for operation context
type Context interface {
}

// SeekableStream is a stream that can be seeked
type SeekableStream struct {
	io.ReadCloser
	io.Seeker
	Size int64
}

// ArchiveTool interface for archive operations
type ArchiveTool interface {
	AcceptedExtensions() []string
	GetMeta(ss *SeekableStream, args ArchiveArgs) (ArchiveMeta, error)
	List(ss *SeekableStream, args ArchiveInnerArgs) ([]Obj, error)
	Extract(ss *SeekableStream, args ArchiveInnerArgs) (io.ReadCloser, int64, error)
	Decompress(ss *SeekableStream, outputPath string, args ArchiveInnerArgs, up func(float64)) error
	GetFS(ss *SeekableStream, args ArchiveArgs) (fs.FS, error)
}

// DirEntry is a directory entry
type DirEntry interface {
	fs.DirEntry
	FS() fs.FS
}

// Link represents a link to a resource
type Link struct {
	URL         string
	MFile       interface{}
	Header      map[string]string
	Size        int64
	Concurrency int
	PartSize    int64
}

// UpdateProgress is a function type for updating progress
type UpdateProgress func(float64)
