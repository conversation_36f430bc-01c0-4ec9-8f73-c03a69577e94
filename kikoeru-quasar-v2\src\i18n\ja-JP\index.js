// Japanese translations

export default {
  failed: '操作に失敗しました',
  success: '操作は成功しました',
  settings: '設定',

  // Admin profile page translations
  adminProfile: {
    title: '管理者プロフィール',
    username: 'ユーザー名',
    newPassword: '新しいパスワード',
    confirmPassword: 'パスワードの確認',
    passwordsMustMatch: 'パスワードが一致する必要があります',
    save: '変更を保存',
    updateSuccess: 'プロフィールが正常に更新されました',
    updateError: 'プロフィールの更新に失敗しました',
    loadError: 'プロフィールの読み込みに失敗しました',
    noChanges: '変更は検出されませんでした'
  },

  // Common text used across the application
  common: {
    confirm: '確認',
    cancel: 'キャンセル',
    delete: '削除',
    save: '保存',
    edit: '編集',
    create: '作成',
    add: '追加',
    remove: '削除',
    file: 'ファイル',
    next: '次へ',
    back: '戻る'
  },

  // Common validation messages
  validation: {
    required: 'このフィールドは必須です',
    minLength: '少なくとも{length}文字必要です',
    usernameMinLength: 'ユーザー名は{length}文字以上である必要があります',
    passwordMinLength: 'パスワードは{length}文字以上である必要があります',
    invalidEmail: '有効なメールアドレスを入力してください',
    passwordMismatch: 'パスワードが一致しません',
    playlistNameRequired: 'プレイリスト名を入力してください'
  },

  // Language switcher
  languageSwitcher: {
    language: '言語'
  },

  // Navigation items
  nav: {
    mediaLibrary: 'メディアライブラリ',
    myReviews: 'マイレビュー',
    playlists: 'プレイリスト',
    history: '再生履歴',
    circles: 'サークル',
    tags: 'タグ',
    voiceActors: '声優',
    randomListen: 'ランダム再生',
    sleepMode: 'スリープモード',
    adminPanel: '管理パネル',
    darkMode: 'ダークモード',
    lightMode: 'ライトモード',
    logout: 'ログアウト',
    login: 'ログイン'
  },

  // Dialog messages
  dialog: {
    logout: 'ログアウトしますか？',
    cancel: 'キャンセル',
    confirm: 'ログアウト',
    delete: '削除'
  },

  // Notifications
  notification: {
    darkModeEnabled: 'ダークモードに切り替えました',
    lightModeEnabled: 'ライトモードに切り替えました',
    loggedOut: 'ログアウトしました',
    randomPlayStart: '{count}個の音声をランダムに再生します',
    noPlayableAudio: '再生可能な音声が見つかりません',
    randomPlayError: 'ランダム作品の取得に失敗しました',
    loginSuccess: 'ログインに成功しました',
    loginFailed: 'ユーザー名またはパスワードが正しくありません',
    networkError: 'ネットワークエラー',
    registerSuccess: '登録に成功しました',
    registerSuccessLogin: '登録に成功しました！ログインしてください。',
    registerFailed: '登録に失敗しました: {status}',
    passwordChanged: 'パスワードを変更しました',
    emailSent: 'メールを送信しました',
    reviewSubmitted: 'レビューを送信しました',
    reviewSubmitFailed: 'レビューの送信に失敗しました',
    reviewDeleted: 'レビューを削除しました',
    reviewDeleteFailed: 'レビューの削除に失敗しました',
    playlistUpdated: 'プレイリストが更新されました',
    playlistUpdateFailed: 'プレイリストの更新に失敗しました',
    playlistDeleted: 'プレイリストが削除されました',
    playlistDeleteFailed: 'プレイリストの削除に失敗しました',
    loadFolderFailed: 'フォルダの内容の読み込みに失敗しました',
    downloadFailed: 'ダウンロードに失敗しました',
    openFileFailed: 'ファイルのオープンに失敗しました',
    loadListFailed: '{type}の読み込みに失敗しました',
    loadHistoryFailed: '再生履歴の読み込みに失敗しました',
    loadPlaylistFailed: 'プレイリストの読み込みに失敗しました',
    playlistItemRemoved: 'アイテムをプレイリストから削除しました',
    playlistItemRemoveFailed: 'プレイリストからのアイテムの削除に失敗しました',
    playlistOrderUpdated: 'プレイリストの順序を更新しました',
    playlistOrderUpdateFailed: 'プレイリストの順序の更新に失敗しました',
    searchWorksFailed: '作品の検索に失敗しました',
    workAlreadyInPlaylist: '作品は既にこのプレイリストにあります',
    playlistItemAdded: 'アイテムをプレイリストに追加しました',
    playlistItemAddFailed: 'プレイリストへのアイテムの追加に失敗しました',
    loadPlaylistsFailed: 'プレイリストの読み込みに失敗しました',
    playlistCreated: 'プレイリストが正常に作成されました',
    playlistCreateFailed: 'プレイリストの作成に失敗しました',
    loadReviewsFailed: 'レビューの読み込みに失敗しました',
    loadWorksFailed: '作品の読み込みに失敗しました',
    loadWorkDetailsFailed: '作品詳細の読み込みに失敗しました',
    adminLoadStoragesFailed: 'ストレージソースの読み込みに失敗しました',
    adminLoadDriverDefinitionsFailed: 'ドライバー定義の読み込みに失敗しました',
    adminDriversReloaded: 'ドライバーが正常にリロードされました',
    adminDriversReloadFailed: 'ドライバーのリロードに失敗しました',
    adminConnectionTestSuccess: '接続テストに成功しました',
    adminConnectionTestFailed: '接続テストに失敗しました',
    adminStorageDeleted: 'ストレージソースが削除されました',
    adminStorageDeleteFailed: 'ストレージソースの削除に失敗しました',
    adminLoadTasksFailed: 'タスクの読み込みに失敗しました',
    adminLoadStoragesForTasksFailed: 'タスクフォーム用のストレージソースの読み込みに失敗しました',
    adminScanTaskSubmitted: 'スキャンタスクが送信されました',
    adminScanTaskSubmitFailed: 'スキャンタスクの送信に失敗しました',
    adminScrapeAllTaskSubmitted: 'スクレイピングタスクが送信されました',
    adminScrapeAllTaskSubmitFailed: 'スクレイピングタスクの送信に失敗しました',
    adminTaskCancelled: 'タスクがキャンセルされました',
    adminTaskCancelFailed: 'タスクのキャンセルに失敗しました',
    adminSystemConfigSaved: 'システム設定が保存されました',
    adminSystemConfigSaveFailed: 'システム設定の保存に失敗しました',
    adminLoadCacheInfoFailed: 'キャッシュ情報の読み込みに失敗しました',
    adminCoverCacheCleared: 'カバーキャッシュがクリアされました',
    adminClearCoverCacheFailed: 'カバーキャッシュのクリアに失敗しました',
    adminFileCacheCleared: 'ファイルキャッシュがクリアされました',
    adminClearFileCacheFailed: 'ファイルキャッシュのクリアに失敗しました',
    adminAllCacheCleared: 'すべてのキャッシュがクリアされました',
    adminClearAllCacheFailed: 'すべてのキャッシュのクリアに失敗しました',
    adminWorksExported: '作品データが正常にエクスポートされました',
    adminExportWorksFailed: '作品データのエクスポートに失敗しました',
    adminUsersExported: 'ユーザーデータが正常にエクスポートされました',
    adminExportUsersFailed: 'ユーザーデータのエクスポートに失敗しました',
    adminDataImported: 'データが正常にインポートされました',
    adminImportDataFailed: 'データのインポートに失敗しました',
    adminLoadSystemInfoFailed: 'システム情報の読み込みに失敗しました',
    adminLoadScannerStatusFailed: 'スキャナーステータスの読み込みに失敗しました',
    adminScanSpecificStarted: '選択されたストレージのスキャンが開始されました',
    adminScanAllStarted: 'すべてのストレージのスキャンが開始されました',
    adminScanSpecificFailed: '選択されたストレージのスキャンの開始に失敗しました',
    adminScanAllFailed: 'すべてのストレージのスキャンの開始に失敗しました',
    subtitleUploaded: '字幕が正常にアップロードされました',
    multipleTitlesUploaded: '{count}個の字幕が正常にアップロードされました',
    uploadFailed: '字幕のアップロードに失敗しました',
    missingStorageInfo: 'ストレージ情報がありません',
    loadAudioFilesFailed: '音声ファイルの読み込みに失敗しました。もう一度お試しください。',
    encodingChanged: 'アーカイブのエンコーディングが変更されました'
  },

  // Auth related translations
  auth: {
    login: 'ログイン',
    register: '登録',
    username: 'ユーザー名',
    usernameOrEmail: 'ユーザー名またはメール',
    password: 'パスワード',
    confirmPassword: 'パスワード確認',
    email: 'メールアドレス',
    forgotPassword: 'パスワードを忘れた場合',
    resetPassword: 'パスワードリセット',
    sendResetEmail: 'リセットメールを送信',
    createAccount: '新規アカウント作成',
    adminLogin: '管理者ログインに成功しました！サイドバーに管理パネルが表示されています',
    registerNewAccount: '新規アカウント登録',
    alreadyHaveAccount: 'アカウントをお持ちですか？ログイン'
  },

  // Settings page
  settingsPage: {
    personalInfo: '個人情報',
    playbackSettings: '再生設定',
    interfaceSettings: 'インターフェース設定',
    username: 'ユーザー名',
    userGroup: 'ユーザーグループ',
    registrationDate: '登録日',
    lastUpdate: '最終更新',
    forwardJump: '早送り時間',
    rewindJump: '巻き戻し時間',
    seconds: '秒',
    guestBanner: 'ゲストとして閲覧中です。プレーヤー設定のみ表示されています。',
    emailSettings: 'メール設定',
    currentEmail: '現在のメール',
    notSet: '未設定',
    verified: '確認済み',
    unverified: '未確認',
    changeEmail: 'メールを変更',
    setEmail: 'メールを設定',
    resendVerification: '確認メールを再送信',
    unlinkEmail: 'メールの連携を解除',
    securitySettings: 'セキュリティ設定',
    currentPassword: '現在のパスワード',
    newPassword: '新しいパスワード',
    confirmNewPassword: '新しいパスワード（確認）',
    changePassword: 'パスワード変更',
    passwordRequirements: 'パスワードは6文字以上である必要があります'
  },

  // Work related translations
  work: {
    details: '詳細',
    tracks: 'トラック',
    reviews: 'レビュー',
    releasedAt: '発売日',
    circle: 'サークル',
    tags: 'タグ',
    vas: '声優',
    addToPlaylist: 'プレイリストに追加',
    createPlaylist: 'プレイリスト作成',
    writeReview: 'レビューを書く',
    noReviews: 'レビューはまだありません',
    yourRating: 'あなたの評価',
    rate: '評価する',
    playAll: 'すべて再生',
    averageRating: '平均',
    starRating: '星{count}つ',
    currency: '円',
    soldCount: '販売数',
    markProgress: '進捗をマーク',
    uploadSubtitle: '字幕をアップロード'
  },

  // Player related translations
  player: {
    play: '再生',
    pause: '一時停止',
    next: '次へ',
    previous: '前へ',
    lyrics: '歌詞',
    noLyrics: '歌詞はありません',
    currentlyPlaying: '再生中',
    queue: 'キュー',
    showQueue: '現在のプレイリストを表示',
    addToQueue: 'キューに追加',
    removeFromQueue: 'キューから削除',
    clearQueue: 'キューをクリア',
    hideCoverButtons: 'カバーボタンを隠す',
    swapSeekButtons: 'シーク/トラックボタンを入れ替え',
    openWorkDetails: '作品詳細を開く',
    rewind: '巻き戻し',
    forward: '早送り',
    playMode: '再生モード',
    reorder: '並び替え'
  },

  // Lyric dialog related translations
  lyric: {
    title: '歌詞',
    selectFile: '歌詞ファイルを選択',
    noLyricsFound: '歌詞ファイルが見つかりません',
    similarity: '類似度',
    selectLocalFile: 'ローカルファイルを選択',
    noLyrics: '歌詞がありません',
    noLyricsDesc: '歌詞を表示するには歌詞ファイルを選択してください',
    adjustTiming: 'タイミングオフセットを調整して歌詞と音声を同期させます',
    localSelectedFile: 'ローカル選択ファイル'
  },

  // Review related translations
  review: {
    myReview: '私のレビュー',
    deleteTag: 'タグを削除',
    confirmDeleteTag: 'このタグを削除してもよろしいですか？',
    progress: {
      wantToListen: '聴きたい',
      listening: '聴いている',
      listened: '聴いた',
      relistening: '再聴中',
      postponed: '保留中'
    },
    updatedAt: '更新日時',
    editReview: 'レビューを編集',
    deleteReview: 'レビューを削除',
    confirmDelete: {
      title: '削除の確認',
      message: 'このレビューを削除してもよろしいですか？'
    }
  },

  // Playlist related translations
  playlist: {
    create: 'プレイリスト作成',
    edit: 'プレイリスト編集',
    delete: 'プレイリスト削除',
    name: 'プレイリスト名',
    nameLabel: 'プレイリスト名',
    description: '説明',
    descriptionLabel: '説明 (任意)',
    visibility: '公開設定',
    visibilityLabel: '公開設定',
    public: '公開',
    private: '非公開',
    unlisted: '限定公開',
    itemCount: '{count} 件のアイテム',
    addTrack: 'トラックを追加',
    removeTrack: 'トラックを削除',
    moveTracks: 'トラックを移動',
    moveUp: '上へ移動',
    moveDown: '下へ移動',
    track: 'トラック',
    saveChanges: '変更を保存',
    myPlaylists: 'マイプレイリスト',
    createNew: '新規プレイリスト作成',
    emptyPlaylist: 'このプレイリストは空です',
    confirmDelete: 'このプレイリストを削除してもよろしいですか？',
    saveInProgress: 'プレイリスト保存機能は開発中です',
    confirmRemove: {
      title: '削除の確認',
      message: 'このアイテムをプレイリストから削除してもよろしいですか？'
    },
    confirmDeleteDialog: {
      title: '削除の確認',
      message: 'プレイリスト「{name}」を削除してもよろしいですか？この操作は元に戻せません。'
    },
    visibilityOptions: {
      private: '非公開',
      unlisted: '限定公開',
      public: '公開'
    },
    updatedAt: '{date}に更新',
    playAll: 'すべて再生',
    shufflePlay: 'シャッフル再生',
    editInfo: '情報を編集',
    content: 'プレイリストの内容',
    addItem: 'アイテムを追加',
    addItemsToPlaylist: 'アイテムをプレイリストに追加',
    searchWorks: '作品を検索',
    searchPlaceholder: '作品名またはオリジナルIDを入力',
    noResultsFound: '結果が見つかりません'
  },

  // Sleep mode related translations
  sleepMode: {
    title: 'スリープモード',
    setTime: 'スリープ時間を設定',
    cancel: 'タイマーをキャンセル',
    disabled: 'スリープモードを無効にしました',
    willStopAt: '{time}に再生を停止します'
  },

  // Work Tree specific translations
  worktree: {
    root: 'ルート',
    emptyFolder: '空のフォルダ',
    addToQueue: 'キューに追加',
    playNext: '次に再生',
    downloadFile: 'ファイルをダウンロード',
    passwordRequired: 'アーカイブパスワードが必要',
    enterPassword: 'パスワードを入力',
    selectEncoding: '文字エンコーディングを選択',
    selectEncodingTitle: 'アーカイブ文字エンコーディング',
    encoding: 'エンコード',
    zipEncodingOnly: '注意：エンコーディング選択はZIPアーカイブのみで動作します。RARと7zアーカイブはこの機能をサポートしていません。',
    encodingHelp: 'アーカイブ内のファイル名が文字化けする場合は、別のエンコーディングを試してください',
    encodingUTF8: 'UTF-8（デフォルト）',
    encodingShiftJIS: 'Shift-JIS（日本語）',
    encodingEUCJP: 'EUC-JP（日本語）',
    encodingGBK: 'GBK（簡体字中国語）',
    encodingBig5: 'Big5（繁体字中国語）',
    encodingEUCKR: 'EUC-KR（韓国語）'
  },

  // Error page translations
  error: {
    notFound: 'おっと、ここには何もありません...',
    goHome: 'ホームに戻る'
  },

  // List page translations
  list: {
    workCount: '{count} 作品',
    noData: 'データがありません',
    searchPlaceholder: '{type}を検索...',
    circle: 'サークル',
    tag: 'タグ',
    va: '声優'
  },

  // History related translations
  history: {
    title: '再生履歴',
    track: 'トラック',
    finished: '完了',
    playedAt: '再生日時',
    deleteRecord: '記録を削除',
    endOfList: 'リストの末尾',
    confirmDelete: {
      title: '削除の確認',
      message: 'この再生履歴を削除してもよろしいですか？'
    }
  },

  // Reviews Page specific translations
  reviewsPage: {
    sortBy: '並び替え',
    gridView: 'グリッド表示',
    listView: 'リスト表示',
    filterOptions: {
      all: 'すべて'
      // 「聴きたい」などの他のフィルターオプションは review.progress に既に存在します
    },
    sortOptions: {
      updatedDesc: '更新日時順（新しい順）',
      updatedAsc: '更新日時順（古い順）',
      ratingDesc: '評価順（高い順）',
      ratingAsc: '評価順（低い順）',
      releaseDesc: '発売日順（新しい順）',
      releaseAsc: '発売日順（古い順）'
    }
  },

  // Works Page specific translations
  worksPage: {
    searchResultTitle: '「{keyword}」の検索結果',
    circleWorksTitle: 'サークル「{circle}」の作品',
    tagWorksTitle: 'タグ「{tag}」の作品',
    vaWorksTitle: '声優「{va}」の作品',
    sortBy: '並び替え',
    gridView: 'グリッド表示',
    listView: 'リスト表示',
    showTags: 'タグを表示',
    hideTags: 'タグを非表示',
    detailView: '詳細表示',
    compactView: 'コンパクト表示',
    sortOptions: {
      releaseDesc: '発売日順（新しい順）',
      ratingDesc: '評価順（高い順）',
      releaseAsc: '発売日順（古い順）',
      salesDesc: '販売数順（多い順）',
      priceAsc: '価格順（安い順）',
      priceDesc: '価格順（高い順）',
      reviewsDesc: 'レビュー数順（多い順）',
      createdDesc: '追加日順（新しい順）'
    }
  },

  // Work Page specific translations
  workPage: {
    loading: '読み込み中...'
  },

  // Admin Storage Management Page
  adminStorage: {
    title: 'ストレージ管理',
    addStorage: 'ストレージソースを追加',
    reloadDrivers: 'ドライバーをリロード',
    driverHelp: 'ドライバーヘルプ',
    statusEnabled: '有効',
    statusDisabled: '無効',
    testConnection: '接続テスト',
    driverHelpTitle: 'ストレージドライバーヘルプ',
    driverInfo: {
      supportedStrategies: 'サポートされるダウンロード戦略',
      defaultStrategy: 'デフォルト',
      accessMethod: 'アクセス方法',
      proxyOnly: 'プロキシアクセスのみ',
      localOnly: 'ローカルアクセスのみ',
      configParams: '設定パラメーター',
      required: '必須',
      sensitive: '機密'
    },
    confirmDeleteDialog: {
      title: '削除の確認',
      message: 'ストレージソース「{remark}」を削除してもよろしいですか？この操作は元に戻せません。'
    },
    table: {
      order: '順序',
      remark: '備考',
      driver: 'ドライバー',
      downloadStrategy: 'ダウンロード戦略',
      status: 'ステータス',
      createdAt: '作成日時',
      actions: '操作'
    }
  },

  // Admin Task Management Page
  adminTasks: {
    title: 'タスク管理',
    scanLibrary: 'ライブラリをスキャン',
    scrapeAllWorks: 'すべての作品をスクレイプ',
    refreshTaskList: 'タスクリストを更新',
    viewDetails: '詳細を表示',
    cancelTask: 'タスクをキャンセル',
    storageIdLabel: 'ID',
    scanLibraryDialog: {
      title: 'ライブラリをスキャン',
      selectStorage: 'ストレージソースを選択',
      startScan: 'スキャンを開始'
    },
    scrapeAllDialog: {
      title: 'すべての作品をスクレイプ',
      forceUpdate: '既存のメタデータを強制更新',
      preferredLang: '優先言語',
      startScraping: 'スクレイピングを開始'
    },
    taskDetailDialog: {
      title: 'タスク詳細',
      taskId: 'タスクID',
      taskType: 'タスクタイプ',
      status: 'ステータス',
      progress: '進捗',
      message: 'メッセージ',
      errorMessage: 'エラーメッセージ',
      createdAt: '作成日時',
      startedAt: '開始日時',
      completedAt: '完了日時',
      payload: 'ペイロード',
      result: '結果'
    },
    table: {
      taskType: 'タスクタイプ',
      status: 'ステータス',
      progress: '進捗',
      message: 'メッセージ',
      createdAt: '作成日時',
      actions: '操作'
    },
    statusLabels: {
      pending: '保留中',
      running: '実行中',
      completed: '完了',
      failed: '失敗',
      cancelled: 'キャンセル済み'
    },
    languageOptions: {
      zh: '中国語',
      ja: '日本語',
      en: '英語'
    },
    validation: {
      selectStorage: 'ストレージソースを選択してください'
    }
  },

  // Dashboard - Advanced Settings Page
  dashboardAdvanced: {
    title: '詳細設定',
    systemConfig: {
      title: 'システム設定',
      siteName: 'サイト名',
      siteNameHint: 'ページタイトルに表示される名前',
      allowRegistration: 'ユーザー登録を許可',
      enableEmailFeatures: 'メール機能を有効にする',
      forceEmailVerification: '登録時にメール認証を強制する',
      saveButton: 'システム設定を保存'
    },
    cacheManagement: {
      title: 'キャッシュ管理',
      coverCache: 'カバーキャッシュ',
      fileCache: 'ファイルキャッシュ',
      allCache: 'すべてのキャッシュ',
      calculating: '計算中...',
      clearCoverCache: 'カバーキャッシュをクリア',
      clearFileCache: 'ファイルキャッシュをクリア',
      clearAllCache: 'すべてのキャッシュをクリア',
      clearAllDescription: 'ワンクリックですべてのキャッシュをクリア',
      refreshCacheInfo: 'キャッシュ情報を更新'
    },
    dataManagement: {
      title: 'データ管理',
      exportData: 'データのエクスポート',
      exportDescription: '作品データ、ユーザーデータなどをエクスポートします',
      exportWorks: '作品データをエクスポート',
      exportUsers: 'ユーザーデータをエクスポート',
      importData: 'データのインポート',
      importDescription: 'バックアップファイルからデータをインポートします',
      selectImportFile: 'インポートファイルを選択',
      importButton: 'データをインポート',
      noFileSelectedError: 'インポートするファイルを選択してください'
    },
    systemInfo: {
      title: 'システム情報',
      version: 'システムバージョン',
      fetching: '取得中...',
      dbStatus: 'データベースステータス',
      dbStatusHealthy: '正常',
      dbStatusError: 'エラー',
      totalWorks: '総作品数',
      totalWorksCount: '{count} 作品',
      totalUsers: '総ユーザー数',
      totalUsersCount: '{count} ユーザー',
      totalStorages: '総ストレージソース数',
      totalStoragesCount: '{count} ソース',
      refreshButton: 'システム情報を更新'
    }
  },

  // Dashboard - Scanner Page
  dashboardScanner: {
    title: 'スキャナー管理',
    unnamedStorage: '無名ストレージ',
    neverScanned: 'スキャン履歴なし',
    scannerStatus: {
      title: 'スキャナーステータス',
      currentStatus: '現在のステータス',
      currentScanningStorage: '現在スキャン中のストレージ',
      statusInfo: 'ステータス情報',
      lastError: '最後のエラー',
      scanProgressLabel: 'スキャン進捗',
      refreshStatus: 'ステータスを更新'
    },
    manualScan: {
      title: '手動スキャン',
      selectStorage: 'ストレージソースを選択',
      selectStorageHint: 'スキャンするストレージソースを選択してください',
      scanSelectedStorage: '選択したストレージをスキャン',
      scanAllStorages: 'すべてのストレージをスキャン'
    },
    storageList: {
      title: 'ストレージソースリスト',
      statusEnabled: '有効',
      statusDisabled: '無効',
      scanThisStorage: 'このストレージソースをスキャン',
      manageStorages: 'ストレージソースを管理',
      table: {
        remark: '備考',
        driver: 'ドライバー',
        status: 'ステータス',
        lastScannedAt: '最終スキャン日時',
        actions: '操作'
      }
    },
    scanConfirmDialog: {
      title: 'スキャンの確認',
      message: 'スキャンを開始してもよろしいですか？この処理には時間がかかる場合があります。',
      startScan: 'スキャンを開始'
    },
    statusLabels: {
      idle: 'アイドル',
      scanning: 'スキャン中',
      scraping: 'スクレイピング中',
      completed: '完了',
      failed: '失敗',
      error: 'エラー'
    }
  },

  // Dashboard - Home Page (Folders.vue is effectively the Dashboard Home)
  dashboardHome: {
    title: 'ダッシュボード',
    stats: {
      totalWorks: '総作品数',
      totalUsers: '総ユーザー数',
      totalStorages: 'ストレージソース',
      runningTasks: '実行中のタスク'
    },
    quickActions: {
      title: 'クイックアクション',
      scanLibrary: 'ライブラリをスキャン',
      manageStorages: 'ストレージ管理',
      userManagement: 'ユーザー管理',
      feedbackManagement: 'フィードバック管理'
    },
    recentTasks: {
      title: '最近のタスク',
      table: {
        taskType: 'タスクタイプ',
        status: 'ステータス',
        createdAt: '作成日時'
      }
    }
  },

  // Dashboard - User Management Page
  dashboardUserManage: {
    title: 'ユーザー管理',
    addUser: 'ユーザー追加',
    searchPlaceholder: 'ユーザー検索...',
    emailVerified: '検証済み',
    emailUnverified: '未検証',
    noEmail: 'メールなし',
    editUserTooltip: 'ユーザー編集',
    deleteUserTooltip: 'ユーザー削除',
    createUserDialog: {
      title: 'ユーザー追加',
      usernameLabel: 'ユーザー名',
      usernameRequired: 'ユーザー名を入力してください',
      emailLabel: 'メールアドレス (任意)',
      passwordLabel: 'パスワード',
      passwordMinLength: 'パスワードは6文字以上である必要があります',
      groupLabel: 'ユーザーグループ'
    },
    editUserDialog: {
      title: 'ユーザー編集',
      usernameLabel: 'ユーザー名',
      emailLabel: 'メールアドレス',
      groupLabel: 'ユーザーグループ',
      newPasswordLabel: '新しいパスワード (空の場合は変更なし)'
    },
    deleteConfirmDialog: {
      title: '削除確認',
      message: 'ユーザー「{username}」を削除してもよろしいですか？この操作は元に戻せません。'
    },
    table: {
      username: 'ユーザー名',
      email: 'メールアドレス',
      group: 'グループ',
      emailVerified: 'メール検証済み',
      createdAt: '作成日時',
      updatedAt: '更新日時',
      actions: '操作'
    },
    userGroups: {
      admin: '管理者',
      user: 'ユーザー'
    }
  },

  // Dashboard - Feedback Management Page
  dashboardFeedbackManage: {
    title: 'フィードバック管理',
    filterStatusLabel: 'ステータスで絞り込み',
    filterTypeLabel: 'タイプで絞り込み',
    anonymousUser: '匿名ユーザー',
    viewDetailTooltip: '詳細表示',
    updateStatusTooltip: 'ステータス更新',
    detailDialog: {
      title: 'フィードバック詳細',
      feedbackContentTitle: 'フィードバック内容',
      resolutionNotesTitle: '解決メモ',
      feedbackId: 'フィードバックID',
      type: 'タイプ',
      status: 'ステータス',
      submittedBy: '送信者',
      submittedAt: '送信日時',
      updatedAt: '更新日時',
      resolvedAt: '解決日時',
      resolvedBy: '解決者'
    },
    editDialog: {
      title: 'フィードバックステータス更新',
      statusLabel: 'ステータス',
      resolutionNotesLabel: '解決メモ (任意)'
    },
    table: {
      type: 'タイプ',
      status: 'ステータス',
      content: '内容',
      user: 'ユーザー',
      createdAt: '送信日時',
      actions: '操作'
    },
    feedbackStatus: {
      new: '新規',
      open: 'オープン',
      in_progress: '対応中',
      resolved: '解決済み',
      closed: 'クローズ',
      rejected: '拒否'
    },
    feedbackTypes: {
      bug: 'バグ報告',
      feature: '機能要望',
      improvement: '改善提案',
      comment: 'コメント',
      other: 'その他'
    },
    statusFilters: {
      all: 'すべてのステータス'
    },
    typeFilters: {
      all: 'すべてのタイプ'
    }
  },

  // Search related translations
  search: {
    placeholder: '検索...',
    advancedSearch: '詳細検索',
    close: '閉じる',
    ageCategory: '年齢カテゴリ',
    advancedFilters: '詳細フィルター',
    options: {
      addToFilter: 'フィルター',
      exclude: '除外',
      alwaysExclude: '常に除外',
      copyToClipboard: 'テキストをコピー',
      reverse: '検索を反転（フィルター/除外）'
    },
    ageOptions: {
      onlyAllAges: '⚪ 全年齢のみ',
      onlyR15: '🟠 R-15のみ',
      onlyAdult: '🔞 R-18のみ',
      allAgesAndR15: '🟡 全年齢 & R-15'
    },
    namespace: {
      tag: 'タグ検索',
      circle: 'サークル検索',
      va: '声優検索',
      rate: '評価でフィルター（より大きい）',
      price: '価格でフィルター（より大きい）',
      sell: '販売数でフィルター（より大きい）',
      age: '年齢カテゴリでフィルター',
      duration: '時間でフィルター（より長い）',
      lang: '言語でフィルター',
      '-tag': 'タグを除外',
      '-circle': 'サークルを除外',
      '-va': '声優を除外',
      '-age': '年齢カテゴリを除外',
      '-duration': '時間でフィルター（より短い）',
      '-lang': '言語を除外'
    },
    plainText: {
      filter: 'テキスト検索',
      exclude: 'テキスト検索（除外）'
    },
    selectOrEnter: '{type}を選択または入力',
    apply: '適用',
    exclude: '除外',
    cancel: 'キャンセル'
  },

  // Work card component
  workCard: {
    soldCount: '販売数',
    ageRating: {
      adult: 'R-18',
      r15: 'R-15',
      general: '全年齢'
    },
    dlsiteLink: 'DLsite',
    rating: '評価',
    commentCount: 'コメント'
  },

  // Subtitle upload specific translations
  subtitle: {
    upload: {
      step1Title: '字幕ファイルのアップロード',
      dragDropText: '字幕ファイルをここにドラッグ＆ドロップ',
      orText: 'または',
      selectFilesBtn: 'ファイルを選択',
      selectedFiles: '選択したファイル',
      step2Title: '一致の確認と編集',
      overallThreshold: '全体のしきい値',
      matchThreshold: '一致しきい値',
      selectAll: 'すべて選択',
      clearAll: 'すべてクリア',
      applyToAllFiles: 'すべてのファイルに適用',
      loadingAudioFiles: '音声ファイルを読み込み中...',
      noAudioFiles: '音声ファイルが見つかりません',
      noAudioFilesDesc: 'この字幕に一致する音声ファイルが見つかりません。',
      fetchAudioFiles: '音声ファイルを取得',
      searchTracksPlaceholder: 'トラックを検索...',
      step3Title: '確認とアップロード',
      uploadSummary: 'アップロードの概要',
      subtitleFiles: '字幕ファイル',
      description: '説明',
      descriptionPlaceholder: '任意の説明',
      visibility: '公開設定',
      public: '公開',
      selectedTracks: '選択したトラック',
      showSelectedTracks: '選択したトラックを表示',
      match: '一致',
      matches: '一致',
      noMatch: '一致なし',
      preview: '一致をプレビュー',
      removeMatch: '一致を削除',
      forceMatch: '一致を強制',
      matchPreviewTitle: '一致のプレビュー',
      settingsAppliedToAll: '設定がすべてのファイルに適用されました',
      thresholdApplied: 'しきい値 {threshold}% をすべてのファイルに適用しました',
      uploadBtn: 'アップロード',
      trackName: 'トラック名',
      matchPercentage: '一致率',
      actions: '操作'
    },
    notification: {
      subtitleUploaded: '字幕が正常にアップロードされました',
      multipleTitlesUploaded: '{count}個の字幕が正常にアップロードされました',
      uploadFailed: '字幕のアップロードに失敗しました',
      missingStorageInfo: 'ストレージ情報がありません',
      loadAudioFilesFailed: '音声ファイルの読み込みに失敗しました。もう一度お試しください。',
      encodingChanged: 'アーカイブのエンコーディングが変更されました'
    }
  }
}
