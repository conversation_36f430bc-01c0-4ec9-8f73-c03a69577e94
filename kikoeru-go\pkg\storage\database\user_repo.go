package database

import (
	"context"
	"database/sql"
	"errors"
	"fmt"

	"strings" // Added for List method's LIKE query

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"gorm.io/gorm"

	list_params_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/list_params" // Added import
)

// ListUsersAdminDbParams defines parameters for listing users in the repository layer for admin purposes.
type ListUsersAdminDbParams struct {
	list_params_dto.PaginationParams        // Embedded pagination and sorting
	Username                         string // Search term for username (uses LIKE)
	Email                            string // Search term for email (uses LIKE)
	Group                            string // Exact match for group
}

// UserRepository 定义用户数据仓库的接口
type UserRepository interface {
	Create(ctx context.Context, user *models.User) error
	GetByID(ctx context.Context, id string) (*models.User, error)
	GetByUsername(ctx context.Context, username string) (*models.User, error)
	GetByIdentifier(ctx context.Context, identifier string) (*models.User, error)
	GetByEmail(ctx context.Context, email string) (*models.User, error)
	GetByVerificationToken(ctx context.Context, token string) (*models.User, error)
	GetByResetPasswordToken(ctx context.Context, token string) (*models.User, error)
	Update(ctx context.Context, user *models.User) error
	List(ctx context.Context, params ListUsersAdminDbParams) (users []*models.User, totalCount int64, err error) // Added List
	DeleteByID(ctx context.Context, userID string) error                                                         // Added DeleteByID
	ClearPasswordResetToken(ctx context.Context, userID string) error
}

type userRepository struct {
	db *gorm.DB
}

func NewUserRepository(db *gorm.DB) UserRepository {
	return &userRepository{db: db}
}

func (r *userRepository) Create(ctx context.Context, user *models.User) error {
	if user == nil {
		return errors.New("user cannot be nil")
	}
	// Username is now unique. ID is primary key and set by BeforeCreate hook.
	// Check for existing username before creating.
	// Email uniqueness is handled by DB constraint but can be checked here too.
	if user.Username == "" {
		return errors.New("username cannot be empty for new user")
	}
	// BeforeCreate hook in models.User handles ID generation.
	// Create will fail if username or verified email is not unique due to DB constraints.
	result := r.db.WithContext(ctx).Create(user)
	if result.Error != nil {
		// TODO: Check for specific DB errors like unique constraint violation
		// and return a more specific error like service.ErrUserAlreadyExists or service.ErrEmailAlreadyExists
		return fmt.Errorf("failed to create user in DB: %w", result.Error)
	}
	return nil
}

func (r *userRepository) GetByID(ctx context.Context, id string) (*models.User, error) {
	var user models.User
	if id == "" {
		return nil, errors.New("user ID cannot be empty")
	}
	result := r.db.WithContext(ctx).Where("id = ?", id).First(&user)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, apperrors.ErrUserNotFound
		}
		return nil, result.Error
	}
	return &user, nil
}

func (r *userRepository) GetByUsername(ctx context.Context, username string) (*models.User, error) {
	var user models.User
	if username == "" {
		return nil, errors.New("username cannot be empty")
	}
	result := r.db.WithContext(ctx).Where("username = ?", username).First(&user)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, apperrors.ErrUserNotFound
		}
		return nil, result.Error
	}
	return &user, nil
}

func (r *userRepository) GetByIdentifier(ctx context.Context, identifier string) (*models.User, error) {
	var user models.User
	if identifier == "" {
		return nil, errors.New("identifier cannot be empty")
	}
	// Try by username first
	err := r.db.WithContext(ctx).Where("username = ?", identifier).First(&user).Error
	if err == nil {
		return &user, nil // Found by username
	}
	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("error querying user by username: %w", err) // Other DB error
	}
	// Try by email if not found by username
	err = r.db.WithContext(ctx).Where("email = ? AND email_verified = ?", identifier, true).First(&user).Error
	if err == nil {
		return &user, nil // Found by verified email
	}
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, apperrors.ErrUserNotFound
	}
	return nil, fmt.Errorf("error querying user by email: %w", err)
}

func (r *userRepository) GetByEmail(ctx context.Context, email string) (*models.User, error) {
	var user models.User
	if email == "" {
		return nil, errors.New("email cannot be empty for GetByEmail")
	}
	result := r.db.WithContext(ctx).Where("email = ?", email).First(&user)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, apperrors.ErrUserNotFound
		}
		return nil, result.Error
	}
	return &user, nil
}

func (r *userRepository) GetByVerificationToken(ctx context.Context, token string) (*models.User, error) {
	var user models.User
	if token == "" {
		return nil, errors.New("verification token cannot be empty")
	}
	result := r.db.WithContext(ctx).Where("verification_token = ?", token).First(&user)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, apperrors.ErrUserNotFound
		}
		return nil, result.Error
	}
	return &user, nil
}

func (r *userRepository) GetByResetPasswordToken(ctx context.Context, token string) (*models.User, error) {
	var user models.User
	if token == "" {
		return nil, errors.New("reset password token cannot be empty")
	}
	result := r.db.WithContext(ctx).Where("reset_password_token = ?", token).First(&user)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, apperrors.ErrUserNotFound
		}
		return nil, result.Error
	}
	return &user, nil
}

func (r *userRepository) Update(ctx context.Context, user *models.User) error {
	if user == nil || user.ID == "" { // Changed to check user.ID as primary key
		return errors.New("user for update must not be nil and must have an ID (primary key)")
	}
	result := r.db.WithContext(ctx).Save(user)
	return result.Error
}

// List retrieves a paginated and filtered list of users.
func (r *userRepository) List(ctx context.Context, params ListUsersAdminDbParams) (users []*models.User, totalCount int64, err error) {
	query := r.db.WithContext(ctx).Model(&models.User{})

	if params.Username != "" {
		// Case-insensitive search for username
		query = query.Where("LOWER(username) LIKE LOWER(?)", "%"+params.Username+"%")
	}
	if params.Email != "" {
		// Case-insensitive search for email
		query = query.Where("LOWER(email) LIKE LOWER(?)", "%"+params.Email+"%")
	}
	if params.Group != "" {
		query = query.Where("`group` = ?", params.Group) // Use backticks for 'group' as it's a reserved keyword in some SQL dialects
	}

	// Count total records matching filters
	if errCount := query.Count(&totalCount).Error; errCount != nil {
		return nil, 0, fmt.Errorf("failed to count users: %w", errCount)
	}

	// Apply sorting from embedded PaginationParams
	if params.SortBy != "" {
		// Basic validation for SortBy to prevent SQL injection if SortBy comes directly from user input without sanitization
		// For now, assume SortBy is a safe column name.
		order := params.SortBy
		if params.SortOrder != "" && (strings.ToLower(params.SortOrder) == "asc" || strings.ToLower(params.SortOrder) == "desc") {
			order += " " + strings.ToLower(params.SortOrder)
		} else {
			order += " asc" // Default to ascending if SortOrder is invalid or empty
		}
		query = query.Order(order)
	} else {
		query = query.Order("id asc") // Default sort if SortBy is not provided
	}

	// Apply pagination from embedded PaginationParams
	page := params.Page
	pageSize := params.PageSize
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20 // Default page size
	}
	if pageSize > 100 { // Max page size limit
		pageSize = 100
	}
	offset := (page - 1) * pageSize
	query = query.Offset(offset).Limit(pageSize)

	if errFind := query.Find(&users).Error; errFind != nil {
		return nil, 0, fmt.Errorf("failed to find users: %w", errFind)
	}

	return users, totalCount, nil
}

// DeleteByID removes a user from the database by their ID (string UUID).
func (r *userRepository) DeleteByID(ctx context.Context, userID string) error {
	if userID == "" {
		return errors.New("user ID cannot be empty for deletion")
	}

	// GORM's Delete with a primary key value will only delete if the record exists.
	// We check RowsAffected to confirm deletion.
	result := r.db.WithContext(ctx).Where("id = ?", userID).Delete(&models.User{})
	if result.Error != nil {
		return fmt.Errorf("failed to delete user %s: %w", userID, result.Error)
	}
	if result.RowsAffected == 0 {
		return apperrors.ErrUserNotFound
	}
	return nil
}

// ClearPasswordResetToken clears the password reset token and expiry for a user
func (r *userRepository) ClearPasswordResetToken(ctx context.Context, userID string) error {
	if userID == "" {
		return errors.New("user ID cannot be empty")
	}
	result := r.db.WithContext(ctx).Model(&models.User{}).
		Where("id = ?", userID).
		Updates(map[string]interface{}{
			"reset_password_token":            sql.NullString{Valid: false},
			"reset_password_token_expires_at": sql.NullTime{Valid: false},
		})
	if result.Error != nil {
		return fmt.Errorf("failed to clear password reset token: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return apperrors.ErrUserNotFound
	}
	return nil
}
