package iso9660

import (
	"io"
	"io/fs"
	"os"
	stdpath "path"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors"
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/archive/tool"
	"github.com/kdomanski/iso9660"
)

type ISO9660 struct {
}

func (t *ISO9660) AcceptedExtensions() []string {
	return []string{".iso"}
}

func (t *ISO9660) GetMeta(ss *models.StreamWithSeek, args models.ArchiveArgs) (models.ArchiveMeta, error) {
	return &models.ArchiveMetaInfo{
		Comment:   "",
		Encrypted: false,
	}, nil
}

func (t *ISO9660) List(ss *models.StreamWithSeek, args models.ArchiveInnerArgs) ([]models.Obj, error) {
	img, err := getImage(ss)
	if err != nil {
		return nil, err
	}
	dir, err := getObj(img, args.InnerPath)
	if err != nil {
		return nil, err
	}
	if !dir.IsDir() {
		return nil, apperrors.ErrNotADirectory
	}
	children, err := dir.GetChildren()
	if err != nil {
		return nil, err
	}
	ret := make([]models.Obj, 0, len(children))
	for _, child := range children {
		ret = append(ret, toModelObj(child))
	}
	return ret, nil
}

func (t *ISO9660) Extract(ss *models.StreamWithSeek, args models.ArchiveInnerArgs) (io.ReadCloser, int64, error) {
	img, err := getImage(ss)
	if err != nil {
		return nil, 0, err
	}
	obj, err := getObj(img, args.InnerPath)
	if err != nil {
		return nil, 0, err
	}
	if obj.IsDir() {
		return nil, 0, apperrors.ErrPathIsDir
	}
	return io.NopCloser(obj.Reader()), obj.Size(), nil
}

func (t *ISO9660) Decompress(ss *models.StreamWithSeek, outputPath string, args models.ArchiveInnerArgs, up func(float64)) error {
	img, err := getImage(ss)
	if err != nil {
		return err
	}
	obj, err := getObj(img, args.InnerPath)
	if err != nil {
		return err
	}
	if obj.IsDir() {
		if args.InnerPath != "/" {
			outputPath = stdpath.Join(outputPath, obj.Name())
			if err = os.MkdirAll(outputPath, 0700); err != nil {
				return err
			}
		}
		var children []*iso9660.File
		if children, err = obj.GetChildren(); err == nil {
			err = decompressAll(children, outputPath)
		}
	} else {
		err = decompress(obj, outputPath, up)
	}
	return err
}

// GetFS provides an fs.FS interface for the ISO archive
func (t *ISO9660) GetFS(ss *models.StreamWithSeek, args models.ArchiveArgs) (fs.FS, error) {
	// Get the ISO9660 image
	img, err := getImage(ss)
	if err != nil {
		return nil, err
	}

	// Return the ISO9660 filesystem implementation
	return &isoFS{
		image: img,
	}, nil
}

var _ tool.Tool = (*ISO9660)(nil)

func init() {
	tool.RegisterTool(&ISO9660{})
}
