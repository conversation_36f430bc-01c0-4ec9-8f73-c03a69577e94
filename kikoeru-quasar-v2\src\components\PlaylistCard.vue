<template>
  <q-card class="playlist-card">
    <router-link :to="`/playlist/${playlist.id}`">
      <q-img
        :src="playlistCoverUrl"
        :ratio="1"
        style="max-width: 300px;"
        transition="fade"
      >
        <div class="absolute-bottom bg-transparent">
          <div class="text-h6 text-white">{{ playlist.name }}</div>
          <div class="text-subtitle2 text-white">{{ t('playlist.itemCount', { count: playlist.item_count || 0 }) }}</div>
        </div>
      </q-img>
    </router-link>

    <q-card-section>
      <div class="text-subtitle1 ellipsis-2-lines">
        <router-link :to="`/playlist/${playlist.id}`" class="text-black">
          {{ playlist.name }}
        </router-link>
      </div>

      <div v-if="playlist.description" class="text-body2 text-grey-7 q-mt-sm ellipsis-2-lines">
        {{ playlist.description }}
      </div>

      <div class="row justify-between items-center q-mt-sm">
        <q-chip
          :color="visibilityColor(playlist.visibility)"
          text-color="white"
          dense
          size="sm"
        >
          {{ visibilityLabel(playlist.visibility) }}
        </q-chip>

        <span class="text-caption text-grey-6">
          {{ formatDate(playlist.updated_at) }}
        </span>
      </div>
    </q-card-section>

    <!-- 上下文菜单 -->
    <q-menu
      touch-position
      context-menu
      auto-close
      transition-show="jump-down"
      transition-hide="jump-up"
    >
      <q-list separator>
        <q-item clickable @click="editPlaylist">
          <q-item-section>{{ t('playlist.edit') }}</q-item-section>
        </q-item>

        <q-item clickable @click="confirmDelete">
          <q-item-section>{{ t('playlist.delete') }}</q-item-section>
        </q-item>
      </q-list>
    </q-menu>

    <!-- 删除确认对话框 -->
    <q-dialog v-model="showDeleteDialog" persistent>
      <q-card>
        <q-card-section>
          <div class="text-h6">{{ t('playlist.confirmDeleteDialog.title') }}</div>
          <div class="text-body2">{{ t('playlist.confirmDeleteDialog.message', { name: playlist.name }) }}</div>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="t('common.cancel')" v-close-popup />
          <q-btn flat :label="t('common.delete')" color="negative" @click="deletePlaylist" />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 编辑对话框 -->
    <q-dialog v-model="showEditDialog" persistent>
      <q-card style="min-width: 400px">
        <q-card-section>
          <div class="text-h6">{{ t('playlist.edit') }}</div>
        </q-card-section>

        <q-card-section class="q-pt-none">
          <q-input
            dense
            v-model="editForm.name"
            :label="t('playlist.nameLabel')"
            maxlength="100"
            counter
            :rules="[val => val && val.length > 0 || t('validation.playlistNameRequired')]"
          />

          <q-input
            dense
            v-model="editForm.description"
            :label="t('playlist.descriptionLabel')"
            type="textarea"
            rows="3"
            maxlength="500"
            counter
            class="q-mt-md"
          />

          <q-select
            dense
            v-model="editForm.visibility"
            :options="visibilityOptions"
            :label="t('playlist.visibilityLabel')"
            class="q-mt-md"
          />
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="t('common.cancel')" @click="cancelEdit" />
          <q-btn flat :label="t('common.save')" color="primary" @click="savePlaylist" :loading="saving" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-card>
</template>

<script setup>
import { ref, computed, getCurrentInstance } from 'vue'
import { useI18n } from 'vue-i18n'
import { useNotification } from '../composables/useNotification'

defineOptions({
  name: 'PlaylistCard'
})

const props = defineProps({
  playlist: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['refresh'])

const { t, locale } = useI18n()
const { proxy } = getCurrentInstance()
const { showSuccessNotification, showErrorNotification } = useNotification()

// Reactive data
const showDeleteDialog = ref(false)
const showEditDialog = ref(false)
const saving = ref(false)
const editForm = ref({
  name: '',
  description: '',
  visibility: 'private'
})

// Computed properties
const playlistCoverUrl = computed(() => {
  // 使用默认播放列表封面或第一个作品的封面
  return '/icons/playlist-default.png' // 可以后续实现更复杂的封面逻辑
})

const visibilityOptions = computed(() => [
  { label: t('playlist.visibilityOptions.private'), value: 'private' },
  { label: t('playlist.visibilityOptions.unlisted'), value: 'unlisted' },
  { label: t('playlist.visibilityOptions.public'), value: 'public' }
])

// Methods
const visibilityLabel = (visibility) => {
  const labels = {
    'private': t('playlist.visibilityOptions.private'),
    'unlisted': t('playlist.visibilityOptions.unlisted'),
    'public': t('playlist.visibilityOptions.public')
  }
  return labels[visibility] || visibility
}

const visibilityColor = (visibility) => {
  const colors = {
    'private': 'red',
    'unlisted': 'orange',
    'public': 'green'
  }
  return colors[visibility] || 'grey'
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString(locale.value, {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

const editPlaylist = () => {
  editForm.value = {
    name: props.playlist.name,
    description: props.playlist.description || '',
    visibility: props.playlist.visibility
  }
  showEditDialog.value = true
}

const cancelEdit = () => {
  showEditDialog.value = false
  editForm.value = {
    name: '',
    description: '',
    visibility: 'private'
  }
}

const savePlaylist = async () => {
  if (!editForm.value.name.trim()) {
    showErrorNotification(t('validation.playlistNameRequired'))
    return
  }

  saving.value = true
  try {
    await proxy.$api.put(`/api/v1/playlists/${props.playlist.id}`, editForm.value)
    showSuccessNotification(t('notification.playlistUpdated'))
    cancelEdit()
    emit('refresh')
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('notification.playlistUpdateFailed'))
  } finally {
    saving.value = false
  }
}

const confirmDelete = () => {
  showDeleteDialog.value = true
}

const deletePlaylist = async () => {
  try {
    await proxy.$api.delete(`/api/v1/playlists/${props.playlist.id}`)
    showDeleteDialog.value = false
    showSuccessNotification(t('notification.playlistDeleted'))
    emit('refresh')
  } catch (error) {
    showErrorNotification(error.response?.data?.error || error.message || t('notification.playlistDeleteFailed'))
  }
}


</script>

<!-- Styles moved to /src/css/components/PlaylistCard.scss -->
