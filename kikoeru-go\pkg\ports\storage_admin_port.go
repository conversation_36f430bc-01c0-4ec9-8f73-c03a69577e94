package ports

import (
	"context"

	dto_storage "github.com/Sakura-Byte/kikoeru-go/pkg/dto/storage" // Added DTO import
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/driver"
)

// CreateStorageSourceRequest and UpdateStorageSourceRequest are now defined in dto_storage package

// StorageAdminService defines the application service interface for managing storage sources.
// This interface is a "driven" port, implemented by the service layer and used by handlers.
type StorageAdminService interface {
	ListStorageSources(ctx context.Context) ([]*dto_storage.StorageSourceResponseDTO, error)
	GetStorageSourceByID(ctx context.Context, id uint) (*dto_storage.StorageSourceResponseDTO, error)
	CreateStorageSource(ctx context.Context, req dto_storage.CreateStorageSourceRequest) (*dto_storage.StorageSourceResponseDTO, error)
	UpdateStorageSource(ctx context.Context, id uint, req dto_storage.UpdateStorageSourceRequest) (*dto_storage.StorageSourceResponseDTO, error)
	DeleteStorageSource(ctx context.Context, id uint) error
	ReloadAllDrivers(ctx context.Context) error
	TestStorageSourceConnection(ctx context.Context, sourceConfig models.StorageSource) error
	GetDriverDefinitions(ctx context.Context) ([]driver.APIDriverDefinition, error)
	CountStorageSources() (int64, error)
}
