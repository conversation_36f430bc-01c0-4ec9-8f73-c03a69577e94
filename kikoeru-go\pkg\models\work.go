package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"
)

// RankItem represents a single entry in the rank list.
type RankItem struct {
	Term     string `json:"term"`
	Category string `json:"category"`
	Rank     int    `json:"rank"`
	RankDate string `json:"rank_date"`
}

// RankList is a custom type for storing a list of RankItem as JSON in the database.
type RankList []RankItem

// Scan implements the sql.Scanner interface.
func (rl *RankList) Scan(value interface{}) error {
	if value == nil {
		*rl = RankList{}
		return nil
	}

	switch v := value.(type) {
	case []byte:
		if len(v) == 0 {
			*rl = RankList{}
			return nil
		}
		return json.Unmarshal(v, rl)
	case string:
		if v == "" {
			*rl = RankList{}
			return nil
		}
		return json.Unmarshal([]byte(v), rl)
	default:
		return fmt.Errorf("unsupported type for RankList: %T", value)
	}
}

// Value implements the driver.Valuer interface.
func (rl RankList) Value() (driver.Value, error) {
	if len(rl) == 0 {
		return nil, nil
	}
	return json.Marshal(rl)
}

// Work represents a work in the database
type Work struct {
	ID              uint      `gorm:"primaryKey"`
	Title           string    `gorm:"not null"`
	CircleID        *string   `gorm:"index" json:"circle_id,omitempty"`
	OriginalID      *string   `gorm:"uniqueIndex"`
	StorageID       uint      `gorm:"index"`
	WorkType        *string   `gorm:"type:varchar(10)"`
	AgeRating       *string   `gorm:"type:varchar(10)"`
	NSFW            *bool     `gorm:"default:false"`
	ReleaseDate     *string   `gorm:"type:varchar(10)"`
	RateCount       *int64    `gorm:"default:0"`
	RateAverage2DP  *float64  `gorm:"column:rate_average_2dp;type:decimal(3,2)"`
	Rank            RankList  `gorm:"type:json"`
	LyricStatus     string    `gorm:"type:varchar(20);default:'none'"`
	Language        *string   `gorm:"type:varchar(10)"`
	Duration        *int64    `gorm:"default:0"`
	DlCount         *int64    `gorm:"default:0"`
	Price           *int64    `gorm:"default:0"`
	ReviewCount     *int64    `gorm:"default:0"`
	RateCountDetail *string   `gorm:"type:json"`
	PathInStorage   string    `gorm:"not null"`
	CreatedAt       time.Time `gorm:"autoCreateTime"`
	UpdatedAt       time.Time `gorm:"autoUpdateTime"`

	// Associations
	Circle *Circle `gorm:"foreignKey:CircleID"`
	Tags   []*Tag  `gorm:"many2many:r_work_tags"`
	VAs    []*VA   `gorm:"many2many:r_work_vas"`
}

// TableName returns the table name for the work model
func (Work) TableName() string {
	return "t_work"
}

// WorkTag is the join table model for Work and Tag many-to-many relationship.
// Exported to be used in repository layer for direct join table queries.
type WorkTag struct {
	WorkID uint   `gorm:"primaryKey"`
	TagID  string `gorm:"primaryKey"`
}

func (WorkTag) TableName() string {
	return "r_work_tags"
}

// WorkVA is the join table model for Work and VA many-to-many relationship.
// Exported to be used in repository layer for direct join table queries.
type WorkVA struct {
	WorkID uint   `gorm:"primaryKey"`
	VAID   string `gorm:"primaryKey"`
}

func (WorkVA) TableName() string {
	return "r_work_vas"
}

// Track struct has been removed as per refactor.md requirements.
// File and folder listing will be handled by the new FileSystemService and its API endpoints.
