package all_drivers

import (
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/driver"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/driver/google_drive"
	"github.com/Sakura-Byte/kikoeru-go/pkg/storage/driver/local"
	// Import other compiled-in drivers here
)

// RegisterAllDrivers registers all available compiled-in storage drivers
// with the provided DriverManager.
func RegisterAllDrivers(manager *driver.StorageDriverManager) {
	manager.RegisterDriver("local", local.NewLocal)
	manager.RegisterDriver("google_drive", google_drive.NewGoogleDrive)
	// Register other drivers here:
	// manager.RegisterDriver("another_driver", another_driver.NewDriver)
}
