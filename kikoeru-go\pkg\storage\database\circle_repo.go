package database

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"github.com/Sakura-Byte/kikoeru-go/pkg/apperrors" // Added for apperrors
	"github.com/Sakura-Byte/kikoeru-go/pkg/models"
	"gorm.io/gorm"

	circle_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/circle"     // Added circle_dto import
	metadata_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/metadata" // Added import
)

// Error definitions moved to kikoeru-go/pkg/apperrors/errors.go

type ListCirclesParams struct {
	Page     int    `form:"page,default=1"`
	PageSize int    `form:"page_size,default=30"`
	Name     string `form:"name"` // For searching by name
}

type CircleRepository interface {
	Create(ctx context.Context, circle *models.Circle) error
	GetByID(ctx context.Context, id string) (*models.Circle, error)
	GetByName(ctx context.Context, name string) (*models.Circle, error)
	GetOrCreateCircle(ctx context.Context, name string) (*models.Circle, error)
	List(ctx context.Context, params ListCirclesParams) (circles []*models.Circle, totalCount int64, err error)
	// ListAllWithWorkCount retrieves all circles with their associated work counts.
	ListAllWithWorkCount(ctx context.Context) (circles []*metadata_dto.CircleWithWorkCount, totalCount int64, err error) // Updated return type
	Update(ctx context.Context, circle *models.Circle) error
	Delete(ctx context.Context, id string) error
}

type circleRepository struct {
	db *gorm.DB
}

func NewCircleRepository(db *gorm.DB) CircleRepository {
	return &circleRepository{db: db}
}

func (r *circleRepository) Create(ctx context.Context, circle *models.Circle) error {
	if circle.Name == "" {
		return errors.New("circle name cannot be empty for creation")
	}
	if err := r.db.WithContext(ctx).Create(circle).Error; err != nil {
		if strings.Contains(err.Error(), "UNIQUE constraint failed") || errors.Is(err, gorm.ErrDuplicatedKey) {
			return fmt.Errorf("%w: name '%s'", apperrors.ErrCircleAlreadyExists, circle.Name) // Changed to apperrors
		}
		return fmt.Errorf("failed to create circle: %w", err)
	}
	return nil
}

func (r *circleRepository) GetByID(ctx context.Context, id string) (*models.Circle, error) {
	var circle models.Circle
	if err := r.db.WithContext(ctx).First(&circle, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, apperrors.ErrCircleNotFound // Changed to apperrors
		}
		return nil, fmt.Errorf("failed to get circle by ID %s: %w", id, err)
	}
	return &circle, nil
}

func (r *circleRepository) GetByName(ctx context.Context, name string) (*models.Circle, error) {
	var circle models.Circle
	if err := r.db.WithContext(ctx).Where("name = ?", name).First(&circle).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, apperrors.ErrCircleNotFound // Changed to apperrors
		}
		return nil, fmt.Errorf("failed to get circle by name '%s': %w", name, err)
	}
	return &circle, nil
}

func (r *circleRepository) GetOrCreateCircle(ctx context.Context, name string) (*models.Circle, error) {
	if name == "" {
		return nil, errors.New("circle name cannot be empty")
	}
	circle, err := r.GetByName(ctx, name)
	if err == nil {
		return circle, nil
	}
	if !errors.Is(err, apperrors.ErrCircleNotFound) { // Changed to apperrors
		return nil, err
	}
	newCircle := &models.Circle{Name: name}
	if errCreate := r.Create(ctx, newCircle); errCreate != nil {
		if errors.Is(errCreate, apperrors.ErrCircleAlreadyExists) { // Changed to apperrors
			return r.GetByName(ctx, name)
		}
		return nil, errCreate
	}
	return newCircle, nil
}

func (r *circleRepository) List(ctx context.Context, params ListCirclesParams) ([]*models.Circle, int64, error) {
	var circles []*models.Circle
	var totalCount int64
	query := r.db.WithContext(ctx).Model(&models.Circle{})
	if params.Name != "" {
		query = query.Where("LOWER(name) LIKE ?", "%"+strings.ToLower(params.Name)+"%")
	}
	if err := query.Count(&totalCount).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count circles: %w", err)
	}
	if totalCount == 0 {
		return []*models.Circle{}, 0, nil
	}
	if params.Page <= 0 {
		params.Page = 1
	}
	if params.PageSize <= 0 {
		params.PageSize = 30
	}
	if params.PageSize > 200 {
		params.PageSize = 200
	}
	offset := (params.Page - 1) * params.PageSize
	if err := query.Order("name asc").Offset(offset).Limit(params.PageSize).Find(&circles).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list circles: %w", err)
	}
	return circles, totalCount, nil
}

func (r *circleRepository) ListAllWithWorkCount(ctx context.Context) ([]*metadata_dto.CircleWithWorkCount, int64, error) { // Updated return type
	var results []*metadata_dto.CircleWithWorkCount // Updated variable type
	var totalCount int64

	// Count total circles
	if err := r.db.WithContext(ctx).Model(&models.Circle{}).Count(&totalCount).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count circles for work count: %w", err)
	}

	if totalCount == 0 {
		return []*metadata_dto.CircleWithWorkCount{}, 0, nil // Updated return type
	}

	// First query circles to get all data including timestamps
	var circles []*models.Circle
	if err := r.db.WithContext(ctx).Order("name asc").Find(&circles).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list circles: %w", err)
	}

	// Then create a map of circles by ID for quick lookup
	circleMap := make(map[string]*models.Circle)
	for _, circle := range circles {
		circleMap[circle.ID] = circle
	}

	// Define temp struct for scanning query results
	type CircleCount struct {
		ID        string `gorm:"column:id"`
		Name      string `gorm:"column:name"`
		WorkCount int64  `gorm:"column:work_count"`
	}

	// Query to get all circles with work counts
	var countResults []*CircleCount
	query := r.db.WithContext(ctx).Model(&models.Circle{}).
		Select("t_circle.id, t_circle.name, COUNT(t_work.id) as work_count").
		Joins("LEFT JOIN t_work ON t_work.circle_id = t_circle.id").
		Group("t_circle.id, t_circle.name").
		Order("t_circle.name asc")

	if err := query.Scan(&countResults).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list all circles with work count: %w", err)
	}

	// Combine the data
	results = make([]*metadata_dto.CircleWithWorkCount, len(countResults))
	for i, count := range countResults {
		// Get the circle with full data
		circle, exists := circleMap[count.ID]
		if !exists {
			continue
		}

		results[i] = &metadata_dto.CircleWithWorkCount{
			CircleDTO: circle_dto.CircleDTO{
				ID:        circle.ID,
				Name:      circle.Name,
				CreatedAt: circle.CreatedAt,
				UpdatedAt: circle.UpdatedAt,
			},
			WorkCount: count.WorkCount,
			I18n:      make(map[string]string),
		}
	}

	return results, totalCount, nil
}

func (r *circleRepository) Update(ctx context.Context, circle *models.Circle) error {
	if circle.ID == "" {
		return errors.New("cannot update circle with empty ID")
	}
	if circle.Name == "" {
		return errors.New("circle name cannot be empty for update")
	}
	if err := r.db.WithContext(ctx).Save(circle).Error; err != nil {
		if strings.Contains(err.Error(), "UNIQUE constraint failed") || errors.Is(err, gorm.ErrDuplicatedKey) {
			return fmt.Errorf("%w: name '%s'", apperrors.ErrCircleAlreadyExists, circle.Name) // Changed to apperrors
		}
		return fmt.Errorf("failed to update circle ID %s: %w", circle.ID, err)
	}
	return nil
}

func (r *circleRepository) Delete(ctx context.Context, id string) error {
	if id == "" {
		return errors.New("cannot delete circle with empty ID")
	}
	// When a circle is deleted, t_work.circle_id should ideally be set to NULL
	// if the foreign key constraint allows it (ON DELETE SET NULL).
	// If it's ON DELETE RESTRICT/NO ACTION, this delete will fail if works reference it.
	// If it's ON DELETE CASCADE, all works by this circle will be deleted (dangerous!).
	// Current schema for t_work.circle_id does not specify ON DELETE behavior, so it's DB default (often RESTRICT).
	// For now, this just attempts to delete the circle.
	// A more robust solution would be to check for referencing works or handle it in service layer.
	result := r.db.WithContext(ctx).Delete(&models.Circle{}, id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete circle ID %s: %w", id, result.Error)
	}
	if result.RowsAffected == 0 {
		return apperrors.ErrCircleNotFound // Changed to apperrors
	}
	return nil
}
