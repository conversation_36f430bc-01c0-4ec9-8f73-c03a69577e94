package auth

import (
	"crypto/rand"
	"encoding/hex"
)

// GenerateSecureRandomString 生成一个指定长度的安全随机十六进制字符串。
// 用于生成验证令牌、重置密码令牌等。
func GenerateSecureRandomString(length int) (string, error) {
	bytes := make([]byte, length)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// DefaultTokenLength 定义令牌中随机字节的默认长度，生成两倍长度的十六进制字符串。
const DefaultTokenLength = 32 // 生成 64 个字符的十六进制令牌
