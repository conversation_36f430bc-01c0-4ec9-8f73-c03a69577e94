package playlist

import (
	"database/sql"
	"time"

	work_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/work"
	// "github.com/Sakura-Byte/kikoeru-go/pkg/models" // Removed unused import
)

// CreatePlaylistRequest DTO for creating a playlist.
type CreatePlaylistRequest struct {
	Name        string `json:"name" binding:"required,min=1,max=100"`
	Description string `json:"description" binding:"max=500"`
	Visibility  string `json:"visibility" binding:"omitempty,oneof=private unlisted public"`
}

// UpdatePlaylistInfoRequest DTO for updating playlist information.
type UpdatePlaylistInfoRequest struct {
	Name        *string `json:"name" binding:"omitempty,min=1,max=100"`
	Description *string `json:"description" binding:"omitempty,max=500"`
	Visibility  *string `json:"visibility" binding:"omitempty,oneof=private unlisted public"`
}

// AddItemToPlaylistRequest DTO for adding an item to a playlist.
type AddItemToPlaylistRequest struct {
	WorkID    uint   `json:"work_id" binding:"required"`
	TrackPath string `json:"track_path"` // Optional, for specific track
}

// UpdatePlaylistItemOrdersRequest DTO for updating the order of items in a playlist.
type UpdatePlaylistItemOrdersRequest struct {
	ItemOrders map[uint]int `json:"item_orders" binding:"required"` // map[playlist_item_id]new_order
}

// PlaylistDTO represents a playlist in API responses.
type PlaylistDTO struct {
	ID          uint      `json:"id"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	UserID      string    `json:"user_id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Visibility  string    `json:"visibility"`
	ItemCount   int       `json:"item_count"`
}

// PlaylistItemDTO represents a playlist item in API responses.
type PlaylistItemDTO struct {
	ID         uint              `json:"id"`
	PlaylistID uint              `json:"playlist_id"`
	WorkID     uint              `json:"work_id"`
	TrackPath  sql.NullString    `json:"track_path,omitempty"`
	Order      int               `json:"order"`
	Work       *work_dto.WorkDTO `json:"work,omitempty"`
}

// PlaylistWithItemsDTO represents a playlist with its items for API responses.
type PlaylistWithItemsDTO struct {
	PlaylistDTO                    // Embeds PlaylistDTO
	Items       []*PlaylistItemDTO `json:"items"`
}
