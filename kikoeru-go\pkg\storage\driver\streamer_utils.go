package driver

import (
	"errors"
	"io"
	"time"
	// "net/http" // Not directly needed here, AlistHTTPRange is a local type
)

// concreteFileStreamer is a simple implementation of AlistFileStreamer for an in-memory byte slice
// or an existing io.ReadCloser, intended for uploads.
type concreteFileStreamer struct {
	name        string
	size        int64
	stream      io.ReadCloser // The underlying stream
	modTime     time.Time
	createTime  time.Time
	mimeType    string
	isDirVal    bool // Should always be false for a file streamer
	idVal       string
	pathVal     string
	hashInfoVal AlistHashInfo
	existObj    AlistObj
}

// NewConcreteFileStreamer creates a new AlistFileStreamer from an io.ReadCloser.
// The caller is responsible for ensuring the stream is valid and provides the correct size and MIME type.
func NewConcreteFileStreamer(name string, stream io.ReadCloser, size int64, mimeType string, modTime time.Time) AlistFileStreamer {
	ct := modTime // If creation time is not specifically known, use modification time or time.Now()
	if ct.IsZero() {
		ct = time.Now()
	}
	return &concreteFileStreamer{
		name:        name,
		size:        size,
		stream:      stream,
		mimeType:    mimeType,
		modTime:     modTime,
		createTime:  ct,
		isDirVal:    false,
		idVal:       "",              // No ID until stored
		pathVal:     name,            // Path is just the name for a new file being uploaded
		hashInfoVal: AlistHashInfo{}, // No hash info for a stream being uploaded
	}
}

// AlistObj interface methods
func (s *concreteFileStreamer) GetName() string        { return s.name }
func (s *concreteFileStreamer) GetSize() int64         { return s.size }
func (s *concreteFileStreamer) ModTime() time.Time     { return s.modTime }
func (s *concreteFileStreamer) CreateTime() time.Time  { return s.createTime }
func (s *concreteFileStreamer) IsDir() bool            { return s.isDirVal }
func (s *concreteFileStreamer) GetHash() AlistHashInfo { return s.hashInfoVal }
func (s *concreteFileStreamer) GetID() string          { return s.idVal }
func (s *concreteFileStreamer) GetPath() string        { return s.pathVal }

// io.Reader interface method
func (s *concreteFileStreamer) Read(p []byte) (n int, err error) {
	if s.stream == nil {
		return 0, io.EOF // Or some other error indicating stream is not available
	}
	return s.stream.Read(p)
}

// io.Closer interface method
func (s *concreteFileStreamer) Close() error {
	if s.stream != nil {
		return s.stream.Close()
	}
	return nil
}

// AlistFileStreamer specific methods
func (s *concreteFileStreamer) GetMimetype() string       { return s.mimeType }
func (s *concreteFileStreamer) NeedStore() bool           { return true }  // Typically true for uploads
func (s *concreteFileStreamer) IsForceStreamUpload() bool { return false } // Default, can be overridden if needed
func (s *concreteFileStreamer) GetExist() AlistObj        { return s.existObj }
func (s *concreteFileStreamer) SetExist(obj AlistObj)     { s.existObj = obj }
func (s *concreteFileStreamer) RangeRead(hr AlistHTTPRange) (io.Reader, error) {
	return nil, errors.New("RangeRead not supported for this basic streamer")
}

// Ensure concreteFileStreamer implements AlistFileStreamer
var _ AlistFileStreamer = (*concreteFileStreamer)(nil)
