package database

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"github.com/Sakura-Byte/kikoeru-go/pkg/config"
	"github.com/Sakura-Byte/kikoeru-go/pkg/log"
	"gorm.io/driver/mysql"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
)

// NewGormLogger 创建一个适配统一日志系统的 GORM logger
type NewGormLogger struct {
	level   logger.LogLevel
	slowSQL time.Duration
}

func (l *NewGormLogger) LogMode(level logger.LogLevel) logger.Interface {
	newlogger := *l
	newlogger.level = level
	return &newlogger
}

func (l *NewGormLogger) Info(ctx context.Context, msg string, data ...interface{}) {
	if l.level >= logger.Info {
		log.Info(ctx, msg, "data", data)
	}
}

func (l *NewGormLogger) Warn(ctx context.Context, msg string, data ...interface{}) {
	if l.level >= logger.Warn {
		log.Warn(ctx, msg, "data", data)
	}
}

func (l *NewGormLogger) Error(ctx context.Context, msg string, data ...interface{}) {
	if l.level >= logger.Error {
		log.Error(ctx, msg, "data", data)
	}
}

func (l *NewGormLogger) Trace(ctx context.Context, begin time.Time, fc func() (sql string, rowsAffected int64), err error) {
	if l.level <= logger.Silent {
		return
	}

	elapsed := time.Since(begin)
	sql, rows := fc()

	if err != nil && err != gorm.ErrRecordNotFound { // gorm.ErrRecordNotFound 不是一个真正的错误
		log.Error(ctx, "DB_TRACE_ERROR", "elapsed", elapsed, "rows", rows, "sql", sql, "error", err)
		return
	}

	if l.slowSQL > 0 && elapsed > l.slowSQL {
		log.Warn(ctx, "DB_TRACE_SLOW_SQL", "elapsed", elapsed, "rows", rows, "sql", sql)
		return
	}

	if l.level >= logger.Info { // 通常 Trace 对应 Debug
		log.Debug(ctx, "DB_TRACE", "elapsed", elapsed, "rows", rows, "sql", sql)
	}
}

// ConnectDB 根据配置初始化并返回一个 GORM 数据库实例
func ConnectDB(ctx context.Context, cfg config.DatabaseConfig, appPaths config.PathConfig) (*gorm.DB, error) {
	var dialector gorm.Dialector
	var err error

	gormLogLevel := logger.Warn // 默认 GORM 日志级别
	// TODO: 可以根据统一日志系统的级别来设置 GORM 日志级别
	// 目前使用默认的 Warn 级别

	gormLogger := &NewGormLogger{
		level:   gormLogLevel,
		slowSQL: 200 * time.Millisecond, // 慢 SQL 阈值
	}

	switch cfg.Driver {
	case "sqlite":
		// 确保 SQLite 数据库文件所在的目录存在
		dbPath := cfg.SQLite.Path
		if !filepath.IsAbs(dbPath) {
			// 如果是相对路径，则相对于应用的数据目录
			if appPaths.DataDir == "" {
				return nil, fmt.Errorf("database.sqlite.path is relative but paths.data_dir is not configured")
			}
			dbPath = filepath.Join(appPaths.DataDir, dbPath)
		}
		dbDir := filepath.Dir(dbPath)
		if err := os.MkdirAll(dbDir, 0750); err != nil {
			return nil, fmt.Errorf("failed to create SQLite directory %s: %w", dbDir, err)
		}
		log.Info(ctx, "Using SQLite database", "path", dbPath)
		dialector = sqlite.Open(dbPath)
	case "mysql":
		dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?%s",
			cfg.MySQL.User,
			cfg.MySQL.Password,
			cfg.MySQL.Host,
			cfg.MySQL.Port,
			cfg.MySQL.DBName,
			cfg.MySQL.Params,
		)
		log.Info(ctx, "Using MySQL database", "host", cfg.MySQL.Host, "port", cfg.MySQL.Port, "dbname", cfg.MySQL.DBName)
		dialector = mysql.Open(dsn)
	default:
		return nil, fmt.Errorf("unsupported database driver: %s", cfg.Driver)
	}

	db, err := gorm.Open(dialector, &gorm.Config{
		Logger: gormLogger,
		NamingStrategy: schema.NamingStrategy{
			// TablePrefix: "t_", // 如果需要表前缀
			SingularTable: false, // 使用复数表名，例如 "users" 而不是 "user"，但我们通过 TableName() 方法指定
		},
		DisableForeignKeyConstraintWhenMigrating: true, // 迁移时禁用外键，由迁移脚本处理
	})

	if err != nil {
		return nil, fmt.Errorf("failed to connect to database (%s): %w", cfg.Driver, err)
	}

	// 配置连接池 (GORM 使用 database/sql 的连接池)
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)
	if cfg.BusyTimeout > 0 && cfg.Driver == "sqlite" {
		// 对于 SQLite，可以设置 PRAGMA busy_timeout
		// GORM 的 SQLite 驱动可能已经处理了，或者可以通过 DSN 设置
		// 例如: "file.db?_busy_timeout=5000"
		// 这里我们也可以直接执行
		if err := db.Exec(fmt.Sprintf("PRAGMA busy_timeout = %d;", cfg.BusyTimeout)).Error; err != nil {
			log.Warn(ctx, "Failed to set PRAGMA busy_timeout for SQLite", "error", err)
		}
	}

	log.Info(ctx, "Database connection established successfully", "driver", cfg.Driver)
	return db, nil
}
