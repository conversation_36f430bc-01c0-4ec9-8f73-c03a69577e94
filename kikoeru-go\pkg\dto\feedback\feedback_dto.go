package feedback_dto

import (
	"time"

	user_dto "github.com/Sakura-Byte/kikoeru-go/pkg/dto/user"
)

// FeedbackSubmissionRequest DTO for submitting new feedback.
type FeedbackSubmissionRequest struct {
	Type    string `json:"type" binding:"required"` // e.g., "bug", "feature_request", "general"
	Subject string `json:"subject" binding:"required"`
	Message string `json:"message" binding:"required"`
	Email   string `json:"email"` // Optional, for anonymous users to provide contact info
}

// FeedbackResponse DTO for returning feedback details.
type FeedbackResponse struct {
	ID              uint                   `json:"id"`
	Category        string                 `json:"category"`                   // Renamed from Type
	Content         string                 `json:"content"`                    // Replaces Subject and Message
	Status          string                 `json:"status"`                     // e.g., "open", "closed", "in_progress"
	User            *user_dto.UserResponse `json:"user,omitempty"`             // Changed from SubmittedByUserID
	ResolutionNotes *string                `json:"resolution_notes,omitempty"` // Renamed from AdminNotes
	ResolvedBy      *string                `json:"resolved_by,omitempty"`      // Added
	CreatedAt       time.Time              `json:"created_at"`
	UpdatedAt       time.Time              `json:"updated_at"`
	ResolvedAt      *time.Time             `json:"resolved_at,omitempty"` // Renamed from ClosedAt
	// SubmitterIP removed, handle in service/handler if needed, not stored in DB model directly
}

// UpdateFeedbackRequest DTO for updating feedback by admin.
type UpdateFeedbackRequest struct {
	Status     *string `json:"status"`      // e.g., "open", "closed", "in_progress"
	AdminNotes *string `json:"admin_notes"` // Notes from admin
}
